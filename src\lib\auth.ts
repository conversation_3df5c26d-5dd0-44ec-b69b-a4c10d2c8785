import NextAuth from 'next-auth';
import CredentialsProvider from 'next-auth/providers/credentials';
import { DrizzleAdapter } from '@auth/drizzle-adapter';
import { db } from './db';
import { users } from './db/schema';
import { eq } from 'drizzle-orm';
import bcrypt from 'bcryptjs';

export const { handlers, auth, signIn, signOut } = NextAuth({
  // adapter: DrizzleAdapter(db), // Temporarily disabled for mock authentication
  session: { strategy: 'jwt' },
  providers: [
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        // Mock credentials for testing (temporary solution)
        const mockUsers = [
          {
            id: 'admin-1',
            email: '<EMAIL>',
            password: 'admin123',
            name: 'System Administrator',
            role: 'admin'
          },
          {
            id: 'checker-1',
            email: '<EMAIL>',
            password: 'checker123',
            name: 'Test Checker',
            role: 'test_checker'
          }
        ];

        // Find user by email
        const foundUser = mockUsers.find(user => user.email === credentials.email);

        if (!foundUser) {
          return null;
        }

        // Check password (in a real app, this would be hashed)
        if (foundUser.password !== credentials.password) {
          return null;
        }

        return {
          id: foundUser.id,
          email: foundUser.email,
          name: foundUser.name,
          role: foundUser.role,
        };
      },
    }),
  ],
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.role = user.role;
      }
      return token;
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.sub!;
        session.user.role = token.role as string;
      }
      return session;
    },
  },
  pages: {
    signIn: '/auth/signin',
  },
});
