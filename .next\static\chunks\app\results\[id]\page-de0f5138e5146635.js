(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[635],{3900:(e,s,a)=>{Promise.resolve().then(a.bind(a,742))},7364:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(7401).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},7508:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(7401).A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},8207:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(7401).A)("brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]])},1594:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(7401).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},3239:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(7401).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},6889:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(7401).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},4857:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(7401).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},4081:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(7401).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},7223:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(7401).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},6878:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(7401).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},1466:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(7401).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},6046:(e,s,a)=>{"use strict";var t=a(6658);a.o(t,"useParams")&&a.d(s,{useParams:function(){return t.useParams}}),a.o(t,"useRouter")&&a.d(s,{useRouter:function(){return t.useRouter}}),a.o(t,"useSearchParams")&&a.d(s,{useSearchParams:function(){return t.useSearchParams}})},742:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>S});var t=a(5155),l=a(2115),r=a(6046),c=a(8173),i=a.n(c),d=a(5565),n=a(6889),o=a(3239),x=a(7508),m=a(1594),h=a(7364),g=a(4081),u=a(4857),j=a(1466),N=a(7223),p=a(2423),b=a(8207);function f(e){let{scores:s,className:a=""}=e,l=[{name:"Listening",score:s.listening,color:"bg-blue-500"},{name:"Reading",score:s.reading,color:"bg-green-500"},{name:"Writing",score:s.writing,color:"bg-yellow-500"},{name:"Speaking",score:s.speaking,color:"bg-purple-500"}];return(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6 ".concat(a),children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-6",children:"Score Breakdown"}),(0,t.jsxs)("div",{className:"space-y-4",children:[l.map(e=>(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"w-20 text-sm font-medium text-gray-700",children:e.name}),(0,t.jsx)("div",{className:"flex-1 mx-4",children:(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-3",children:(0,t.jsx)("div",{className:"h-3 rounded-full ".concat(e.color," transition-all duration-500 ease-out"),style:{width:e.score?"".concat(e.score/9*100,"%"):"0%"}})})}),(0,t.jsx)("div",{className:"w-12 text-right",children:(0,t.jsx)("span",{className:"text-lg font-bold text-gray-900",children:e.score||"N/A"})})]},e.name)),s.overall&&(0,t.jsx)("div",{className:"mt-6 pt-4 border-t border-gray-200",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"w-20 text-sm font-medium text-gray-700",children:"Overall"}),(0,t.jsx)("div",{className:"flex-1 mx-4",children:(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-4",children:(0,t.jsx)("div",{className:"h-4 rounded-full bg-indigo-600 transition-all duration-500 ease-out",style:{width:"".concat(s.overall/9*100,"%")}})})}),(0,t.jsx)("div",{className:"w-12 text-right",children:(0,t.jsx)("span",{className:"text-xl font-bold text-indigo-600",children:s.overall})})]})})]})]})}var y=a(7401);let v=(0,y.A)("minus",[["path",{d:"M5 12h14",key:"1ays0h"}]]);var w=a(6878);let k=(0,y.A)("trending-down",[["path",{d:"M16 17h6v-6",key:"t6n2it"}],["path",{d:"m22 17-8.5-8.5-5 5L2 7",key:"x473p"}]]);function A(e){let{metrics:s,overallScore:a,className:l=""}=e,r=(e,s)=>e?e>s?{icon:w.A,color:"text-green-500",text:"Above Average"}:e<s?{icon:k,color:"text-red-500",text:"Below Average"}:{icon:v,color:"text-yellow-500",text:"Average"}:{icon:v,color:"text-gray-400",text:"N/A"},c=r(a,6.5);return(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6 ".concat(l),children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-6",children:"Performance Analysis"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"bg-gradient-to-br from-blue-50 to-indigo-100 rounded-lg p-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,t.jsx)("h4",{className:"text-sm font-medium text-gray-700",children:"Overall Performance"}),(0,t.jsx)(c.icon,{className:"h-5 w-5 ".concat(c.color)})]}),(0,t.jsx)("div",{className:"text-2xl font-bold text-indigo-600 mb-1",children:a||"N/A"}),(0,t.jsxs)("div",{className:"text-xs text-gray-600",children:["Global Average: ",6.5]}),(0,t.jsx)("div",{className:"text-xs font-medium ".concat(c.color),children:c.text})]}),(0,t.jsxs)("div",{className:"bg-gradient-to-br from-green-50 to-emerald-100 rounded-lg p-4",children:[(0,t.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:"Score Range"}),(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"text-lg font-bold text-green-600",children:s.highestScore||"N/A"}),(0,t.jsx)("div",{className:"text-xs text-gray-600",children:"Highest"})]}),(0,t.jsx)("div",{className:"text-gray-400",children:"-"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"text-lg font-bold text-red-600",children:s.lowestScore||"N/A"}),(0,t.jsx)("div",{className:"text-xs text-gray-600",children:"Lowest"})]})]}),(0,t.jsx)("div",{className:"mt-2",children:(0,t.jsxs)("div",{className:"text-sm text-gray-600",children:["Average: ",(0,t.jsx)("span",{className:"font-semibold",children:s.averageScore?s.averageScore.toFixed(1):"N/A"})]})})]})]}),(0,t.jsxs)("div",{className:"mt-6",children:[(0,t.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-4",children:"Module Performance vs Global Average"}),(0,t.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-3",children:Object.entries(s.scoreDistribution).map(e=>{let[s,a]=e,l=r(a,6.5);return(0,t.jsxs)("div",{className:"text-center p-3 bg-gray-50 rounded-lg",children:[(0,t.jsx)("div",{className:"text-xs font-medium text-gray-600 capitalize mb-1",children:s}),(0,t.jsx)("div",{className:"text-lg font-bold text-gray-900 mb-1",children:a||"N/A"}),(0,t.jsx)(l.icon,{className:"h-4 w-4 mx-auto ".concat(l.color)})]},s)})})]}),(0,t.jsxs)("div",{className:"mt-6 p-4 bg-yellow-50 rounded-lg",children:[(0,t.jsx)("h4",{className:"text-sm font-medium text-yellow-800 mb-2",children:"Performance Insights"}),(0,t.jsxs)("div",{className:"text-sm text-yellow-700",children:[a&&a>=7&&(0,t.jsx)("p",{children:"• Excellent performance! You're well above the global average."}),a&&a>=6&&a<7&&(0,t.jsx)("p",{children:"• Good performance! You're close to or at the global average."}),a&&a<6&&(0,t.jsx)("p",{children:"• There's room for improvement. Focus on your weaker modules."}),s.highestScore&&s.lowestScore&&(0,t.jsxs)("p",{children:["• Score consistency: ",(s.highestScore-s.lowestScore).toFixed(1)," band difference between highest and lowest modules."]})]})]})]})}function S(){let e=(0,r.useParams)().id,[s,a]=(0,l.useState)(null),[c,y]=(0,l.useState)(null),[v,w]=(0,l.useState)(!0),[k,S]=(0,l.useState)(""),[B,F]=(0,l.useState)(!1),M=(0,l.useCallback)(async()=>{try{let s=await fetch("/api/results/".concat(e));if(s.ok){let e=await s.json();a(e)}else{let e=await s.json();S(e.error||"Result not found")}}catch(e){console.error("Error fetching result:",e),S("Failed to load result")}finally{w(!1)}},[e]),L=(0,l.useCallback)(async()=>{if(null==s?void 0:s.aiFeedbackGenerated){F(!0);try{let s=await fetch("/api/feedback/".concat(e));if(s.ok){let e=await s.json();y(e)}}catch(e){console.error("Error fetching feedback:",e)}finally{F(!1)}}},[e,null==s?void 0:s.aiFeedbackGenerated]);(0,l.useEffect)(()=>{M()},[M]),(0,l.useEffect)(()=>{s&&L()},[L,s]);let R=async()=>{try{let a=await fetch("/api/certificate/".concat(e));if(a.ok){let t=await a.blob(),l=window.URL.createObjectURL(t),r=document.createElement("a");r.href=l,r.download="IELTS_Certificate_".concat(null==s?void 0:s.candidate.fullName.replace(/\s+/g,"_"),"_").concat(e,".pdf"),document.body.appendChild(r),r.click(),window.URL.revokeObjectURL(l),document.body.removeChild(r)}}catch(e){console.error("Certificate download error:",e),alert("An error occurred while downloading the certificate")}};return v?(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Loading your results..."})]})}):k||!s?(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center max-w-md mx-auto p-8",children:[(0,t.jsx)(m.A,{className:"h-16 w-16 text-red-400 mx-auto mb-4"}),(0,t.jsx)("h3",{className:"text-xl font-medium text-gray-900 mb-2",children:"Unable to Load Results"}),(0,t.jsx)("p",{className:"text-gray-600 mb-6",children:k||"Result not found"}),(0,t.jsxs)(i(),{href:"/search",className:"inline-flex items-center text-blue-600 hover:text-blue-700 font-medium",children:[(0,t.jsx)(h.A,{className:"h-4 w-4 mr-1"}),"Back to Search"]})]})}):(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100",children:[(0,t.jsx)("header",{className:"bg-white shadow-sm",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"flex justify-between items-center py-6",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsxs)(i(),{href:"/search",className:"flex items-center text-blue-600 hover:text-blue-700 mr-4",children:[(0,t.jsx)(h.A,{className:"h-5 w-5 mr-1"}),"Back to Search"]}),(0,t.jsx)(g.A,{className:"h-8 w-8 text-blue-600 mr-3"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"IELTS Test Results"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Official Test Report"})]})]}),s.certificateGenerated&&(0,t.jsxs)("button",{onClick:R,className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700",children:[(0,t.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Download Certificate"]})]})})}),(0,t.jsxs)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,t.jsxs)("div",{className:"lg:col-span-1 space-y-6",children:[(0,t.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,t.jsx)("h2",{className:"text-lg font-medium text-gray-900 mb-4",children:"Candidate Information"}),(0,t.jsxs)("div",{className:"flex items-start space-x-4",children:[s.candidate.photoUrl?(0,t.jsx)(d.default,{src:s.candidate.photoUrl,alt:s.candidate.fullName,width:80,height:80,className:"rounded-lg object-cover"}):(0,t.jsx)("div",{className:"w-20 h-20 bg-gray-200 rounded-lg flex items-center justify-center",children:(0,t.jsx)(j.A,{className:"h-8 w-8 text-gray-400"})}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("h4",{className:"text-xl font-semibold text-gray-900",children:s.candidate.fullName}),(0,t.jsxs)("div",{className:"mt-2 space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,t.jsx)(N.A,{className:"h-4 w-4 mr-2"}),s.candidate.nationality]}),(0,t.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,t.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Test Date: ",new Date(s.candidate.testDate).toLocaleDateString()]}),(0,t.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,t.jsx)(N.A,{className:"h-4 w-4 mr-2"}),"Test Center: ",s.candidate.testCenter]})]})]})]})]}),(0,t.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,t.jsx)("h2",{className:"text-lg font-medium text-gray-900 mb-4",children:"Result Status"}),(0,t.jsx)("div",{className:"flex items-center justify-between",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(e=>{switch(e){case"pending":return(0,t.jsx)(n.A,{className:"h-5 w-5 text-yellow-500"});case"completed":return(0,t.jsx)(o.A,{className:"h-5 w-5 text-green-500"});case"verified":return(0,t.jsx)(x.A,{className:"h-5 w-5 text-blue-500"});default:return(0,t.jsx)(m.A,{className:"h-5 w-5 text-gray-500"})}})(s.status),(0,t.jsx)("span",{className:"ml-3 px-3 py-1 rounded-full text-sm font-medium ".concat((e=>{switch(e){case"pending":return"text-yellow-800 bg-yellow-100";case"completed":return"text-green-800 bg-green-100";case"verified":return"text-blue-800 bg-blue-100";default:return"text-gray-800 bg-gray-100"}})(s.status)),children:s.status.charAt(0).toUpperCase()+s.status.slice(1)})]})}),(0,t.jsxs)("div",{className:"mt-4 text-sm text-gray-600",children:[(0,t.jsxs)("p",{children:["Result ID: ",s.id]}),s.certificateSerial&&(0,t.jsxs)("p",{children:["Certificate Serial: ",s.certificateSerial]}),(0,t.jsxs)("p",{children:["Generated: ",new Date(s.createdAt).toLocaleDateString()]})]})]}),(0,t.jsxs)("div",{className:"bg-gradient-to-br from-indigo-500 to-purple-600 shadow rounded-lg p-6 text-white text-center",children:[(0,t.jsx)("h2",{className:"text-lg font-medium mb-4",children:"Overall Band Score"}),(0,t.jsx)("div",{className:"text-5xl font-bold mb-2",children:s.overallBandScore||"N/A"}),(0,t.jsx)("p",{className:"text-indigo-100",children:"IELTS Band Score"}),s.overallBandScore&&(0,t.jsxs)("div",{className:"mt-4 text-sm",children:[s.overallBandScore>=8.5&&(0,t.jsx)("p",{children:"Expert User"}),s.overallBandScore>=7.5&&s.overallBandScore<8.5&&(0,t.jsx)("p",{children:"Very Good User"}),s.overallBandScore>=6.5&&s.overallBandScore<7.5&&(0,t.jsx)("p",{children:"Good User"}),s.overallBandScore>=5.5&&s.overallBandScore<6.5&&(0,t.jsx)("p",{children:"Modest User"}),s.overallBandScore<5.5&&(0,t.jsx)("p",{children:"Limited User"})]})]})]}),(0,t.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 xl:grid-cols-2 gap-6",children:[(0,t.jsx)(f,{scores:{listening:s.listeningBandScore,reading:s.readingBandScore,writing:s.writingBandScore,speaking:s.speakingBandScore,overall:s.overallBandScore}}),(0,t.jsx)(A,{metrics:s.performanceMetrics,overallScore:s.overallBandScore})]}),(0,t.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,t.jsx)("h2",{className:"text-lg font-medium text-gray-900 mb-6",children:"Detailed Score Breakdown"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,t.jsx)("h3",{className:"text-md font-semibold text-blue-600 mb-3",children:"Listening"}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Raw Score:"}),(0,t.jsxs)("span",{className:"font-medium",children:[s.listeningScore||"N/A","/40"]})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Band Score:"}),(0,t.jsx)("span",{className:"text-lg font-bold text-blue-600",children:s.listeningBandScore||"N/A"})]})]})]}),(0,t.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,t.jsx)("h3",{className:"text-md font-semibold text-green-600 mb-3",children:"Reading"}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Raw Score:"}),(0,t.jsxs)("span",{className:"font-medium",children:[s.readingScore||"N/A","/40"]})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Band Score:"}),(0,t.jsx)("span",{className:"text-lg font-bold text-green-600",children:s.readingBandScore||"N/A"})]})]})]}),(0,t.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,t.jsx)("h3",{className:"text-md font-semibold text-yellow-600 mb-3",children:"Writing"}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Task 1:"}),(0,t.jsxs)("span",{className:"font-medium",children:[s.writingTask1Score||"N/A","/9"]})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Task 2:"}),(0,t.jsxs)("span",{className:"font-medium",children:[s.writingTask2Score||"N/A","/9"]})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Band Score:"}),(0,t.jsx)("span",{className:"text-lg font-bold text-yellow-600",children:s.writingBandScore||"N/A"})]})]})]}),(0,t.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,t.jsx)("h3",{className:"text-md font-semibold text-purple-600 mb-3",children:"Speaking"}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Fluency:"}),(0,t.jsxs)("span",{className:"font-medium",children:[s.speakingFluencyScore||"N/A","/9"]})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Lexical:"}),(0,t.jsxs)("span",{className:"font-medium",children:[s.speakingLexicalScore||"N/A","/9"]})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Grammar:"}),(0,t.jsxs)("span",{className:"font-medium",children:[s.speakingGrammarScore||"N/A","/9"]})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Pronunciation:"}),(0,t.jsxs)("span",{className:"font-medium",children:[s.speakingPronunciationScore||"N/A","/9"]})]}),(0,t.jsxs)("div",{className:"flex justify-between border-t pt-2",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Band Score:"}),(0,t.jsx)("span",{className:"text-lg font-bold text-purple-600",children:s.speakingBandScore||"N/A"})]})]})]})]})]}),s.aiFeedbackGenerated&&(0,t.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,t.jsxs)("div",{className:"flex items-center mb-6",children:[(0,t.jsx)(b.A,{className:"h-6 w-6 text-purple-600 mr-2"}),(0,t.jsx)("h2",{className:"text-lg font-medium text-gray-900",children:"AI-Generated Feedback"})]}),B?(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Loading feedback..."})]}):c?(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[c.listeningFeedback&&(0,t.jsxs)("div",{className:"border-l-4 border-blue-500 pl-4",children:[(0,t.jsx)("h4",{className:"font-semibold text-blue-600 mb-2",children:"Listening Feedback"}),(0,t.jsx)("p",{className:"text-sm text-gray-700",children:c.listeningFeedback})]}),c.readingFeedback&&(0,t.jsxs)("div",{className:"border-l-4 border-green-500 pl-4",children:[(0,t.jsx)("h4",{className:"font-semibold text-green-600 mb-2",children:"Reading Feedback"}),(0,t.jsx)("p",{className:"text-sm text-gray-700",children:c.readingFeedback})]}),c.writingFeedback&&(0,t.jsxs)("div",{className:"border-l-4 border-yellow-500 pl-4",children:[(0,t.jsx)("h4",{className:"font-semibold text-yellow-600 mb-2",children:"Writing Feedback"}),(0,t.jsx)("p",{className:"text-sm text-gray-700",children:c.writingFeedback})]}),c.speakingFeedback&&(0,t.jsxs)("div",{className:"border-l-4 border-purple-500 pl-4",children:[(0,t.jsx)("h4",{className:"font-semibold text-purple-600 mb-2",children:"Speaking Feedback"}),(0,t.jsx)("p",{className:"text-sm text-gray-700",children:c.speakingFeedback})]})]}),c.overallFeedback&&(0,t.jsxs)("div",{className:"bg-gradient-to-r from-purple-50 to-indigo-50 rounded-lg p-4",children:[(0,t.jsx)("h4",{className:"font-semibold text-purple-600 mb-2",children:"Overall Assessment"}),(0,t.jsx)("p",{className:"text-gray-700",children:c.overallFeedback})]}),c.studyRecommendations&&(0,t.jsxs)("div",{className:"bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg p-4",children:[(0,t.jsx)("h4",{className:"font-semibold text-green-600 mb-2",children:"Study Recommendations"}),(0,t.jsx)("p",{className:"text-gray-700",children:c.studyRecommendations})]}),(0,t.jsxs)("div",{className:"text-xs text-gray-500 text-center",children:["Feedback generated on ",new Date(c.generatedAt).toLocaleDateString()]})]}):(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)(b.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-gray-600",children:"AI feedback is not available at this time."})]})]})]})]}),(0,t.jsxs)("div",{className:"mt-12 bg-white shadow rounded-lg p-6 text-center",children:[(0,t.jsxs)("div",{className:"flex items-center justify-center mb-4",children:[(0,t.jsx)(x.A,{className:"h-8 w-8 text-blue-600 mr-2"}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Official IELTS Test Report"})]}),(0,t.jsxs)("p",{className:"text-gray-600 mb-4",children:["This is an official IELTS test result. For verification purposes, please use the result ID: ",s.id]}),s.certificateSerial&&(0,t.jsxs)("p",{className:"text-sm text-gray-500",children:["Certificate Serial Number: ",s.certificateSerial]}),(0,t.jsxs)("div",{className:"mt-4 flex justify-center space-x-4",children:[(0,t.jsx)(i(),{href:"/search",className:"text-blue-600 hover:text-blue-700 text-sm font-medium",children:"Search Other Results"}),s.certificateSerial&&(0,t.jsx)(i(),{href:"/verify/".concat(s.certificateSerial),className:"text-green-600 hover:text-green-700 text-sm font-medium",children:"Verify Certificate"})]})]})]})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[8173,7311,8441,1517,7358],()=>s(3900)),_N_E=e.O()}]);