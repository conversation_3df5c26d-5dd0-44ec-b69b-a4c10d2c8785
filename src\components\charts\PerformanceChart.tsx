'use client';

import React from 'react';
import { TrendingUp, TrendingDown, Minus } from 'lucide-react';

interface PerformanceMetrics {
  averageScore: number | null;
  highestScore: number | null;
  lowestScore: number | null;
  scoreDistribution: {
    listening?: number | null;
    reading?: number | null;
    writing?: number | null;
    speaking?: number | null;
  };
}

interface PerformanceChartProps {
  metrics: PerformanceMetrics;
  overallScore?: number | null;
  className?: string;
}

export default function PerformanceChart({
  metrics,
  overallScore,
  className = ''
}: PerformanceChartProps) {
  const globalAverage = 6.5; // IELTS global average

  const getPerformanceIndicator = (score: number | null, average: number) => {
    if (!score) return { icon: Minus, color: 'text-gray-400', text: 'N/A' };

    if (score > average) {
      return { icon: TrendingUp, color: 'text-green-500', text: 'Above Average' };
    } else if (score < average) {
      return { icon: TrendingDown, color: 'text-red-500', text: 'Below Average' };
    } else {
      return { icon: Minus, color: 'text-yellow-500', text: 'Average' };
    }
  };

  const overallIndicator = getPerformanceIndicator(overallScore, globalAverage);

  return (
    <div className={`bg-white rounded-lg shadow-lg p-6 ${className}`}>
      <h3 className="text-lg font-semibold text-gray-900 mb-6">Performance Analysis</h3>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Overall Performance */}
        <div className="bg-gradient-to-br from-blue-50 to-indigo-100 rounded-lg p-4">
          <div className="flex items-center justify-between mb-2">
            <h4 className="text-sm font-medium text-gray-700">Overall Performance</h4>
            <overallIndicator.icon className={`h-5 w-5 ${overallIndicator.color}`} />
          </div>
          <div className="text-2xl font-bold text-indigo-600 mb-1">
            {overallScore || 'N/A'}
          </div>
          <div className="text-xs text-gray-600">
            Global Average: {globalAverage}
          </div>
          <div className={`text-xs font-medium ${overallIndicator.color}`}>
            {overallIndicator.text}
          </div>
        </div>

        {/* Score Range */}
        <div className="bg-gradient-to-br from-green-50 to-emerald-100 rounded-lg p-4">
          <h4 className="text-sm font-medium text-gray-700 mb-2">Score Range</h4>
          <div className="flex justify-between items-center">
            <div>
              <div className="text-lg font-bold text-green-600">
                {metrics.highestScore || 'N/A'}
              </div>
              <div className="text-xs text-gray-600">Highest</div>
            </div>
            <div className="text-gray-400">-</div>
            <div>
              <div className="text-lg font-bold text-red-600">
                {metrics.lowestScore || 'N/A'}
              </div>
              <div className="text-xs text-gray-600">Lowest</div>
            </div>
          </div>
          <div className="mt-2">
            <div className="text-sm text-gray-600">
              Average: <span className="font-semibold">
                {metrics.averageScore ? metrics.averageScore.toFixed(1) : 'N/A'}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Module Performance Comparison */}
      <div className="mt-6">
        <h4 className="text-sm font-medium text-gray-700 mb-4">Module Performance vs Global Average</h4>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
          {Object.entries(metrics.scoreDistribution).map(([module, score]) => {
            const indicator = getPerformanceIndicator(score, globalAverage);
            return (
              <div key={module} className="text-center p-3 bg-gray-50 rounded-lg">
                <div className="text-xs font-medium text-gray-600 capitalize mb-1">
                  {module}
                </div>
                <div className="text-lg font-bold text-gray-900 mb-1">
                  {score || 'N/A'}
                </div>
                <indicator.icon className={`h-4 w-4 mx-auto ${indicator.color}`} />
              </div>
            );
          })}
        </div>
      </div>

      {/* Performance Insights */}
      <div className="mt-6 p-4 bg-yellow-50 rounded-lg">
        <h4 className="text-sm font-medium text-yellow-800 mb-2">Performance Insights</h4>
        <div className="text-sm text-yellow-700">
          {overallScore && overallScore >= 7 && (
            <p>• Excellent performance! You&apos;re well above the global average.</p>
          )}
          {overallScore && overallScore >= 6 && overallScore < 7 && (
            <p>• Good performance! You&apos;re close to or at the global average.</p>
          )}
          {overallScore && overallScore < 6 && (
            <p>• There&apos;s room for improvement. Focus on your weaker modules.</p>
          )}
          {metrics.highestScore && metrics.lowestScore && (
            <p>• Score consistency: {(metrics.highestScore - metrics.lowestScore).toFixed(1)} band difference between highest and lowest modules.</p>
          )}
        </div>
      </div>
    </div>
  );
}
