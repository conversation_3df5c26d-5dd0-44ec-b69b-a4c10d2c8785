(()=>{var e={};e.id=3734,e.ids=[3734],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},2113:e=>{"use strict";e.exports=import("postgres")},77598:e=>{"use strict";e.exports=require("node:crypto")},13994:(e,t,s)=>{"use strict";s.a(e,async(e,r)=>{try{s.r(t),s.d(t,{patchFetch:()=>c,routeModule:()=>l,serverHooks:()=>g,workAsyncStorage:()=>u,workUnitAsyncStorage:()=>p});var a=s(42706),n=s(28203),i=s(45994),o=s(73487),d=e([o]);o=(d.then?(await d)():d)[0];let l=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/results/[id]/route",pathname:"/api/results/[id]",filename:"route",bundlePath:"app/api/results/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\api\\results\\[id]\\route.ts",nextConfigOutput:"",userland:o}),{workAsyncStorage:u,workUnitAsyncStorage:p,serverHooks:g}=l;function c(){return(0,i.patchFetch)({workAsyncStorage:u,workUnitAsyncStorage:p})}r()}catch(e){r(e)}})},96487:()=>{},78335:()=>{},73487:(e,t,s)=>{"use strict";s.a(e,async(e,r)=>{try{s.r(t),s.d(t,{GET:()=>c});var a=s(39187),n=s(62693),i=s(48590),o=s(47579),d=e([n]);async function c(e,{params:t}){try{let{id:e}=await t;if(!e||isNaN(Number(e)))return a.NextResponse.json({error:"Invalid result ID format"},{status:400});let s=await n.db.select({id:i.testResults.id,candidateId:i.testResults.candidateId,testDate:i.testResults.testDate,listeningScore:i.testResults.listeningScore,listeningBandScore:i.testResults.listeningBandScore,readingScore:i.testResults.readingScore,readingBandScore:i.testResults.readingBandScore,writingTask1Score:i.testResults.writingTask1Score,writingTask2Score:i.testResults.writingTask2Score,writingBandScore:i.testResults.writingBandScore,speakingFluencyScore:i.testResults.speakingFluencyScore,speakingLexicalScore:i.testResults.speakingLexicalScore,speakingGrammarScore:i.testResults.speakingGrammarScore,speakingPronunciationScore:i.testResults.speakingPronunciationScore,speakingBandScore:i.testResults.speakingBandScore,overallBandScore:i.testResults.overallBandScore,status:i.testResults.status,certificateSerial:i.testResults.certificateSerial,certificateGenerated:i.testResults.certificateGenerated,aiFeedbackGenerated:i.testResults.aiFeedbackGenerated,createdAt:i.testResults.createdAt,updatedAt:i.testResults.updatedAt,candidate:{fullName:i.candidates.fullName,nationality:i.candidates.nationality,testDate:i.candidates.testDate,testCenter:i.candidates.testCenter,photoUrl:i.candidates.photoUrl}}).from(i.testResults).innerJoin(i.candidates,(0,o.eq)(i.testResults.candidateId,i.candidates.id)).where((0,o.eq)(i.testResults.id,e)).limit(1);if(!s.length)return a.NextResponse.json({error:"Test result not found"},{status:404});let r=s[0];if("pending"===r.status)return a.NextResponse.json({error:"Test result is not yet available"},{status:403});let d=[r.listeningBandScore,r.readingBandScore,r.writingBandScore,r.speakingBandScore].filter(e=>null!==e),c={averageScore:d.length>0?d.reduce((e,t)=>e+t,0)/d.length:null,highestScore:d.length>0?Math.max(...d):null,lowestScore:d.length>0?Math.min(...d):null,scoreDistribution:{listening:r.listeningBandScore,reading:r.readingBandScore,writing:r.writingBandScore,speaking:r.speakingBandScore}};return a.NextResponse.json({...r,performanceMetrics:c})}catch(e){return console.error("Error fetching public test result:",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}n=(d.then?(await d)():d)[0],r()}catch(e){r(e)}})},62693:(e,t,s)=>{"use strict";s.a(e,async(e,r)=>{try{s.d(t,{db:()=>l});var a=s(10072),n=s(2113),i=s(48590),o=e([n,a]);[n,a]=o.then?(await o)():o;let d=process.env.DATABASE_URL,c=(0,n.default)(d,{prepare:!1}),l=(0,a.f)(c,{schema:i});r()}catch(e){r(e)}})},48590:(e,t,s)=>{"use strict";s.r(t),s.d(t,{accounts:()=>p,aiFeedback:()=>m,candidates:()=>k,sessions:()=>g,testResults:()=>f,users:()=>u,verificationTokens:()=>_});var r=s(87858),a=s(44799),n=s(32590),i=s(9848),o=s(70009),d=s(27390),c=s(32190),l=s(4502);let u=(0,r.cJ)("users",{id:(0,a.Qq)("id").primaryKey().$defaultFn(()=>(0,l.sX)()),name:(0,a.Qq)("name"),email:(0,a.Qq)("email").notNull().unique(),emailVerified:(0,n.vE)("emailVerified",{mode:"date"}),image:(0,a.Qq)("image"),password:(0,a.Qq)("password"),role:(0,a.Qq)("role",{enum:["admin","test_checker"]}).notNull().default("test_checker"),createdAt:(0,n.vE)("created_at").defaultNow().notNull(),updatedAt:(0,n.vE)("updated_at").defaultNow().notNull()}),p=(0,r.cJ)("accounts",{userId:(0,a.Qq)("userId").notNull().references(()=>u.id,{onDelete:"cascade"}),type:(0,a.Qq)("type").notNull(),provider:(0,a.Qq)("provider").notNull(),providerAccountId:(0,a.Qq)("providerAccountId").notNull(),refresh_token:(0,a.Qq)("refresh_token"),access_token:(0,a.Qq)("access_token"),expires_at:(0,i.nd)("expires_at"),token_type:(0,a.Qq)("token_type"),scope:(0,a.Qq)("scope"),id_token:(0,a.Qq)("id_token"),session_state:(0,a.Qq)("session_state")}),g=(0,r.cJ)("sessions",{sessionToken:(0,a.Qq)("sessionToken").primaryKey(),userId:(0,a.Qq)("userId").notNull().references(()=>u.id,{onDelete:"cascade"}),expires:(0,n.vE)("expires",{mode:"date"}).notNull()}),_=(0,r.cJ)("verificationTokens",{identifier:(0,a.Qq)("identifier").notNull(),token:(0,a.Qq)("token").notNull(),expires:(0,n.vE)("expires",{mode:"date"}).notNull()}),k=(0,r.cJ)("candidates",{id:(0,a.Qq)("id").primaryKey().$defaultFn(()=>(0,l.sX)()),fullName:(0,a.Qq)("full_name").notNull(),email:(0,a.Qq)("email").notNull().unique(),phoneNumber:(0,a.Qq)("phone_number").notNull(),dateOfBirth:(0,n.vE)("date_of_birth",{mode:"date"}).notNull(),nationality:(0,a.Qq)("nationality").notNull(),passportNumber:(0,a.Qq)("passport_number").notNull().unique(),testDate:(0,n.vE)("test_date",{mode:"date"}).notNull(),testCenter:(0,a.Qq)("test_center").notNull(),photoUrl:(0,a.Qq)("photo_url"),createdAt:(0,n.vE)("created_at").defaultNow().notNull(),updatedAt:(0,n.vE)("updated_at").defaultNow().notNull()}),f=(0,r.cJ)("test_results",{id:(0,a.Qq)("id").primaryKey().$defaultFn(()=>(0,l.sX)()),candidateId:(0,a.Qq)("candidate_id").notNull().references(()=>k.id,{onDelete:"cascade"}),listeningScore:(0,o._)("listening_score",{precision:3,scale:1}),listeningBandScore:(0,o._)("listening_band_score",{precision:2,scale:1}),readingScore:(0,o._)("reading_score",{precision:3,scale:1}),readingBandScore:(0,o._)("reading_band_score",{precision:2,scale:1}),writingTask1Score:(0,o._)("writing_task1_score",{precision:2,scale:1}),writingTask2Score:(0,o._)("writing_task2_score",{precision:2,scale:1}),writingBandScore:(0,o._)("writing_band_score",{precision:2,scale:1}),speakingFluencyScore:(0,o._)("speaking_fluency_score",{precision:2,scale:1}),speakingLexicalScore:(0,o._)("speaking_lexical_score",{precision:2,scale:1}),speakingGrammarScore:(0,o._)("speaking_grammar_score",{precision:2,scale:1}),speakingPronunciationScore:(0,o._)("speaking_pronunciation_score",{precision:2,scale:1}),speakingBandScore:(0,o._)("speaking_band_score",{precision:2,scale:1}),overallBandScore:(0,o._)("overall_band_score",{precision:2,scale:1}),status:(0,a.Qq)("status",{enum:["pending","completed","verified"]}).notNull().default("pending"),enteredBy:(0,a.Qq)("entered_by").references(()=>u.id),verifiedBy:(0,a.Qq)("verified_by").references(()=>u.id),certificateGenerated:(0,d.zM)("certificate_generated").default(!1),certificateSerial:(0,a.Qq)("certificate_serial").unique(),certificateUrl:(0,a.Qq)("certificate_url"),aiFeedbackGenerated:(0,d.zM)("ai_feedback_generated").default(!1),testDate:(0,n.vE)("test_date",{mode:"date"}),createdAt:(0,n.vE)("created_at").defaultNow().notNull(),updatedAt:(0,n.vE)("updated_at").defaultNow().notNull()}),m=(0,r.cJ)("ai_feedback",{id:(0,a.Qq)("id").primaryKey().$defaultFn(()=>(0,l.sX)()),testResultId:(0,a.Qq)("test_result_id").notNull().references(()=>f.id,{onDelete:"cascade"}),listeningFeedback:(0,a.Qq)("listening_feedback"),readingFeedback:(0,a.Qq)("reading_feedback"),writingFeedback:(0,a.Qq)("writing_feedback"),speakingFeedback:(0,a.Qq)("speaking_feedback"),overallFeedback:(0,a.Qq)("overall_feedback"),studyRecommendations:(0,a.Qq)("study_recommendations"),strengths:(0,c.Pq)("strengths").$type(),weaknesses:(0,c.Pq)("weaknesses").$type(),studyPlan:(0,c.Pq)("study_plan").$type(),generatedAt:(0,n.vE)("generated_at").defaultNow().notNull()})}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[638,5452,9757],()=>s(13994));module.exports=r})();