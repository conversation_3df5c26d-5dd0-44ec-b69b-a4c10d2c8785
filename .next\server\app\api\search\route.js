(()=>{var e={};e.id=6202,e.ids=[6202],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},2113:e=>{"use strict";e.exports=import("postgres")},77598:e=>{"use strict";e.exports=require("node:crypto")},57974:(e,t,a)=>{"use strict";a.a(e,async(e,s)=>{try{a.r(t),a.d(t,{patchFetch:()=>o,routeModule:()=>l,serverHooks:()=>_,workAsyncStorage:()=>u,workUnitAsyncStorage:()=>p});var r=a(42706),n=a(28203),i=a(45994),d=a(3699),c=e([d]);d=(c.then?(await c)():c)[0];let l=new r.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/search/route",pathname:"/api/search",filename:"route",bundlePath:"app/api/search/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\api\\search\\route.ts",nextConfigOutput:"",userland:d}),{workAsyncStorage:u,workUnitAsyncStorage:p,serverHooks:_}=l;function o(){return(0,i.patchFetch)({workAsyncStorage:u,workUnitAsyncStorage:p})}s()}catch(e){s(e)}})},96487:()=>{},78335:()=>{},3699:(e,t,a)=>{"use strict";a.a(e,async(e,s)=>{try{a.r(t),a.d(t,{POST:()=>o});var r=a(39187),n=a(62693),i=a(48590),d=a(47579),c=e([n]);async function o(e){try{let t;let{query:a,searchType:s}=await e.json();if(!a||!s)return r.NextResponse.json({error:"Query and search type are required"},{status:400});switch(s){case"name":t=await n.db.select({id:i.testResults.id,listeningBandScore:i.testResults.listeningBandScore,readingBandScore:i.testResults.readingBandScore,writingBandScore:i.testResults.writingBandScore,speakingBandScore:i.testResults.speakingBandScore,overallBandScore:i.testResults.overallBandScore,certificateGenerated:i.testResults.certificateGenerated,candidate:{fullName:i.candidates.fullName,testDate:i.candidates.testDate,testCenter:i.candidates.testCenter}}).from(i.testResults).innerJoin(i.candidates,(0,d.eq)(i.testResults.candidateId,i.candidates.id)).where((0,d.B3)(i.candidates.fullName,`%${a}%`));break;case"email":t=await n.db.select({id:i.testResults.id,listeningBandScore:i.testResults.listeningBandScore,readingBandScore:i.testResults.readingBandScore,writingBandScore:i.testResults.writingBandScore,speakingBandScore:i.testResults.speakingBandScore,overallBandScore:i.testResults.overallBandScore,certificateGenerated:i.testResults.certificateGenerated,candidate:{fullName:i.candidates.fullName,testDate:i.candidates.testDate,testCenter:i.candidates.testCenter}}).from(i.testResults).innerJoin(i.candidates,(0,d.eq)(i.testResults.candidateId,i.candidates.id)).where((0,d.eq)(i.candidates.email,a));break;case"passport":t=await n.db.select({id:i.testResults.id,listeningBandScore:i.testResults.listeningBandScore,readingBandScore:i.testResults.readingBandScore,writingBandScore:i.testResults.writingBandScore,speakingBandScore:i.testResults.speakingBandScore,overallBandScore:i.testResults.overallBandScore,certificateGenerated:i.testResults.certificateGenerated,candidate:{fullName:i.candidates.fullName,testDate:i.candidates.testDate,testCenter:i.candidates.testCenter}}).from(i.testResults).innerJoin(i.candidates,(0,d.eq)(i.testResults.candidateId,i.candidates.id)).where((0,d.eq)(i.candidates.passportNumber,a));break;case"certificate":t=await n.db.select({id:i.testResults.id,listeningBandScore:i.testResults.listeningBandScore,readingBandScore:i.testResults.readingBandScore,writingBandScore:i.testResults.writingBandScore,speakingBandScore:i.testResults.speakingBandScore,overallBandScore:i.testResults.overallBandScore,certificateGenerated:i.testResults.certificateGenerated,candidate:{fullName:i.candidates.fullName,testDate:i.candidates.testDate,testCenter:i.candidates.testCenter}}).from(i.testResults).innerJoin(i.candidates,(0,d.eq)(i.testResults.candidateId,i.candidates.id)).where((0,d.eq)(i.testResults.id,a));break;default:return r.NextResponse.json({error:"Invalid search type"},{status:400})}return r.NextResponse.json(t)}catch(e){return console.error("Search error:",e),r.NextResponse.json({error:"Internal server error"},{status:500})}}n=(c.then?(await c)():c)[0],s()}catch(e){s(e)}})},62693:(e,t,a)=>{"use strict";a.a(e,async(e,s)=>{try{a.d(t,{db:()=>l});var r=a(10072),n=a(2113),i=a(48590),d=e([n,r]);[n,r]=d.then?(await d)():d;let c=process.env.DATABASE_URL,o=(0,n.default)(c,{prepare:!1}),l=(0,r.f)(o,{schema:i});s()}catch(e){s(e)}})},48590:(e,t,a)=>{"use strict";a.r(t),a.d(t,{accounts:()=>p,aiFeedback:()=>m,candidates:()=>f,sessions:()=>_,testResults:()=>k,users:()=>u,verificationTokens:()=>g});var s=a(87858),r=a(44799),n=a(32590),i=a(9848),d=a(70009),c=a(27390),o=a(32190),l=a(4502);let u=(0,s.cJ)("users",{id:(0,r.Qq)("id").primaryKey().$defaultFn(()=>(0,l.sX)()),name:(0,r.Qq)("name"),email:(0,r.Qq)("email").notNull().unique(),emailVerified:(0,n.vE)("emailVerified",{mode:"date"}),image:(0,r.Qq)("image"),password:(0,r.Qq)("password"),role:(0,r.Qq)("role",{enum:["admin","test_checker"]}).notNull().default("test_checker"),createdAt:(0,n.vE)("created_at").defaultNow().notNull(),updatedAt:(0,n.vE)("updated_at").defaultNow().notNull()}),p=(0,s.cJ)("accounts",{userId:(0,r.Qq)("userId").notNull().references(()=>u.id,{onDelete:"cascade"}),type:(0,r.Qq)("type").notNull(),provider:(0,r.Qq)("provider").notNull(),providerAccountId:(0,r.Qq)("providerAccountId").notNull(),refresh_token:(0,r.Qq)("refresh_token"),access_token:(0,r.Qq)("access_token"),expires_at:(0,i.nd)("expires_at"),token_type:(0,r.Qq)("token_type"),scope:(0,r.Qq)("scope"),id_token:(0,r.Qq)("id_token"),session_state:(0,r.Qq)("session_state")}),_=(0,s.cJ)("sessions",{sessionToken:(0,r.Qq)("sessionToken").primaryKey(),userId:(0,r.Qq)("userId").notNull().references(()=>u.id,{onDelete:"cascade"}),expires:(0,n.vE)("expires",{mode:"date"}).notNull()}),g=(0,s.cJ)("verificationTokens",{identifier:(0,r.Qq)("identifier").notNull(),token:(0,r.Qq)("token").notNull(),expires:(0,n.vE)("expires",{mode:"date"}).notNull()}),f=(0,s.cJ)("candidates",{id:(0,r.Qq)("id").primaryKey().$defaultFn(()=>(0,l.sX)()),fullName:(0,r.Qq)("full_name").notNull(),email:(0,r.Qq)("email").notNull().unique(),phoneNumber:(0,r.Qq)("phone_number").notNull(),dateOfBirth:(0,n.vE)("date_of_birth",{mode:"date"}).notNull(),nationality:(0,r.Qq)("nationality").notNull(),passportNumber:(0,r.Qq)("passport_number").notNull().unique(),testDate:(0,n.vE)("test_date",{mode:"date"}).notNull(),testCenter:(0,r.Qq)("test_center").notNull(),photoUrl:(0,r.Qq)("photo_url"),createdAt:(0,n.vE)("created_at").defaultNow().notNull(),updatedAt:(0,n.vE)("updated_at").defaultNow().notNull()}),k=(0,s.cJ)("test_results",{id:(0,r.Qq)("id").primaryKey().$defaultFn(()=>(0,l.sX)()),candidateId:(0,r.Qq)("candidate_id").notNull().references(()=>f.id,{onDelete:"cascade"}),listeningScore:(0,d._)("listening_score",{precision:3,scale:1}),listeningBandScore:(0,d._)("listening_band_score",{precision:2,scale:1}),readingScore:(0,d._)("reading_score",{precision:3,scale:1}),readingBandScore:(0,d._)("reading_band_score",{precision:2,scale:1}),writingTask1Score:(0,d._)("writing_task1_score",{precision:2,scale:1}),writingTask2Score:(0,d._)("writing_task2_score",{precision:2,scale:1}),writingBandScore:(0,d._)("writing_band_score",{precision:2,scale:1}),speakingFluencyScore:(0,d._)("speaking_fluency_score",{precision:2,scale:1}),speakingLexicalScore:(0,d._)("speaking_lexical_score",{precision:2,scale:1}),speakingGrammarScore:(0,d._)("speaking_grammar_score",{precision:2,scale:1}),speakingPronunciationScore:(0,d._)("speaking_pronunciation_score",{precision:2,scale:1}),speakingBandScore:(0,d._)("speaking_band_score",{precision:2,scale:1}),overallBandScore:(0,d._)("overall_band_score",{precision:2,scale:1}),status:(0,r.Qq)("status",{enum:["pending","completed","verified"]}).notNull().default("pending"),enteredBy:(0,r.Qq)("entered_by").references(()=>u.id),verifiedBy:(0,r.Qq)("verified_by").references(()=>u.id),certificateGenerated:(0,c.zM)("certificate_generated").default(!1),certificateSerial:(0,r.Qq)("certificate_serial").unique(),certificateUrl:(0,r.Qq)("certificate_url"),aiFeedbackGenerated:(0,c.zM)("ai_feedback_generated").default(!1),testDate:(0,n.vE)("test_date",{mode:"date"}),createdAt:(0,n.vE)("created_at").defaultNow().notNull(),updatedAt:(0,n.vE)("updated_at").defaultNow().notNull()}),m=(0,s.cJ)("ai_feedback",{id:(0,r.Qq)("id").primaryKey().$defaultFn(()=>(0,l.sX)()),testResultId:(0,r.Qq)("test_result_id").notNull().references(()=>k.id,{onDelete:"cascade"}),listeningFeedback:(0,r.Qq)("listening_feedback"),readingFeedback:(0,r.Qq)("reading_feedback"),writingFeedback:(0,r.Qq)("writing_feedback"),speakingFeedback:(0,r.Qq)("speaking_feedback"),overallFeedback:(0,r.Qq)("overall_feedback"),studyRecommendations:(0,r.Qq)("study_recommendations"),strengths:(0,o.Pq)("strengths").$type(),weaknesses:(0,o.Pq)("weaknesses").$type(),studyPlan:(0,o.Pq)("study_plan").$type(),generatedAt:(0,n.vE)("generated_at").defaultNow().notNull()})}};var t=require("../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),s=t.X(0,[638,5452,9757],()=>a(57974));module.exports=s})();