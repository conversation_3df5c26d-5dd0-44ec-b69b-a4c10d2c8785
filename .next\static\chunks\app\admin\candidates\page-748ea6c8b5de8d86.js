(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8997],{62:(e,t,s)=>{Promise.resolve().then(s.bind(s,4842))},4857:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(7401).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},2598:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(7401).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},5525:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(7401).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},6462:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(7401).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},853:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(7401).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},6764:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(7401).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},7517:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(7401).A)("user-plus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},4842:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>j});var a=s(5155),r=s(2115),l=s(8173),d=s.n(l),i=s(5565),c=s(7517),n=s(853),x=s(5525),h=s(7401);let m=(0,h.A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);var o=s(4857),u=s(6462);let y=(0,h.A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]]);var p=s(2423),b=s(2598),g=s(6764);function j(){let[e,t]=(0,r.useState)([]),[s,l]=(0,r.useState)(!0),[h,j]=(0,r.useState)(""),[N,f]=(0,r.useState)(1),[v,w]=(0,r.useState)(1),[k,A]=(0,r.useState)([]),C=(0,r.useCallback)(async()=>{l(!0);try{let e=new URLSearchParams({page:N.toString(),limit:"20",search:h}),s=await fetch("/api/admin/candidates?".concat(e));if(s.ok){let e=await s.json();t(e.candidates),w(Math.ceil(e.total/20))}}catch(e){console.error("Error fetching candidates:",e)}finally{l(!1)}},[N,h,20]);(0,r.useEffect)(()=>{C()},[C]);let M=e=>{A(t=>t.includes(e)?t.filter(t=>t!==e):[...t,e])},S=async()=>{if(confirm("Are you sure you want to delete ".concat(k.length," candidate(s)?")))try{(await fetch("/api/admin/candidates/bulk-delete",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({candidateIds:k})})).ok&&(A([]),C())}catch(e){console.error("Error deleting candidates:",e)}};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Candidates"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Manage test candidates and their information"})]}),(0,a.jsxs)(d(),{href:"/admin/candidates/new",className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700",children:[(0,a.jsx)(c.A,{className:"h-4 w-4 mr-2"}),"Add Candidate"]})]}),(0,a.jsx)("div",{className:"bg-white shadow rounded-lg p-6",children:(0,a.jsxs)("form",{onSubmit:e=>{e.preventDefault(),f(1),C()},className:"flex gap-4",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(n.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,a.jsx)("input",{type:"text",placeholder:"Search by name, email, or passport number...",value:h,onChange:e=>j(e.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]})}),(0,a.jsx)("button",{type:"submit",className:"px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700",children:"Search"}),(0,a.jsxs)("button",{type:"button",className:"px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50",children:[(0,a.jsx)(x.A,{className:"h-4 w-4 mr-2 inline"}),"Filters"]})]})}),k.length>0&&(0,a.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("span",{className:"text-sm text-blue-700",children:[k.length," candidate(s) selected"]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)("button",{onClick:S,className:"px-3 py-1 text-sm font-medium text-red-700 bg-red-100 rounded hover:bg-red-200",children:[(0,a.jsx)(m,{className:"h-4 w-4 mr-1 inline"}),"Delete"]}),(0,a.jsxs)("button",{className:"px-3 py-1 text-sm font-medium text-blue-700 bg-blue-100 rounded hover:bg-blue-200",children:[(0,a.jsx)(o.A,{className:"h-4 w-4 mr-1 inline"}),"Export"]})]})]})}),(0,a.jsx)("div",{className:"bg-white shadow rounded-lg overflow-hidden",children:s?(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left",children:(0,a.jsx)("input",{type:"checkbox",checked:k.length===e.length&&e.length>0,onChange:()=>{k.length===e.length?A([]):A(e.map(e=>e.id))},className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"})}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Candidate"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Contact"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Test Details"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Registration"}),(0,a.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:e.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsx)("input",{type:"checkbox",checked:k.includes(e.id),onChange:()=>M(e.id),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"})}),(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[e.photoUrl&&(0,a.jsx)(i.default,{className:"h-10 w-10 rounded-full mr-4",src:e.photoUrl,alt:e.fullName,width:40,height:40}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.fullName}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.passportNumber})]})]})}),(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsxs)("div",{className:"text-sm text-gray-900",children:[(0,a.jsxs)("div",{className:"flex items-center mb-1",children:[(0,a.jsx)(u.A,{className:"h-4 w-4 mr-1 text-gray-400"}),e.email]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(y,{className:"h-4 w-4 mr-1 text-gray-400"}),e.phoneNumber]})]})}),(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsxs)("div",{className:"text-sm text-gray-900",children:[(0,a.jsxs)("div",{className:"flex items-center mb-1",children:[(0,a.jsx)(p.A,{className:"h-4 w-4 mr-1 text-gray-400"}),new Date(e.testDate).toLocaleDateString()]}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.testCenter})]})}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-500",children:new Date(e.createdAt).toLocaleDateString()}),(0,a.jsx)("td",{className:"px-6 py-4 text-right text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex justify-end gap-2",children:[(0,a.jsx)(d(),{href:"/admin/candidates/".concat(e.id),className:"text-blue-600 hover:text-blue-900",children:(0,a.jsx)(b.A,{className:"h-4 w-4"})}),(0,a.jsx)(d(),{href:"/admin/candidates/".concat(e.id,"/edit"),className:"text-green-600 hover:text-green-900",children:(0,a.jsx)(g.A,{className:"h-4 w-4"})}),(0,a.jsx)("button",{className:"text-red-600 hover:text-red-900",children:(0,a.jsx)(m,{className:"h-4 w-4"})})]})})]},e.id))})]})}),v>1&&(0,a.jsx)("div",{className:"bg-white px-4 py-3 border-t border-gray-200 sm:px-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"text-sm text-gray-700",children:["Page ",N," of ",v]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)("button",{onClick:()=>f(e=>Math.max(1,e-1)),disabled:1===N,className:"px-3 py-1 text-sm border border-gray-300 rounded disabled:opacity-50",children:"Previous"}),(0,a.jsx)("button",{onClick:()=>f(e=>Math.min(v,e+1)),disabled:N===v,className:"px-3 py-1 text-sm border border-gray-300 rounded disabled:opacity-50",children:"Next"})]})]})})]})})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[8173,7311,8441,1517,7358],()=>t(62)),_N_E=e.O()}]);