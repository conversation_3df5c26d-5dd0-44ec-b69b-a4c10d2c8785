(()=>{var e={};e.id=9684,e.ids=[9684],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},55511:e=>{"use strict";e.exports=require("crypto")},2113:e=>{"use strict";e.exports=import("postgres")},77598:e=>{"use strict";e.exports=require("node:crypto")},82404:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{patchFetch:()=>l,routeModule:()=>c,serverHooks:()=>g,workAsyncStorage:()=>u,workUnitAsyncStorage:()=>p});var a=r(42706),n=r(28203),i=r(45994),o=r(309),d=e([o]);o=(d.then?(await d)():d)[0];let c=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/checker/results/route",pathname:"/api/checker/results",filename:"route",bundlePath:"app/api/checker/results/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\api\\checker\\results\\route.ts",nextConfigOutput:"",userland:o}),{workAsyncStorage:u,workUnitAsyncStorage:p,serverHooks:g}=c;function l(){return(0,i.patchFetch)({workAsyncStorage:u,workUnitAsyncStorage:p})}s()}catch(e){s(e)}})},96487:()=>{},78335:()=>{},309:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{GET:()=>u,POST:()=>c});var a=r(39187),n=r(37702),i=r(62693),o=r(48590),d=r(47579),l=e([n,i]);async function c(e){try{let t=await (0,n.j2)();if(!t)return a.NextResponse.json({error:"Unauthorized"},{status:401});let r=await e.json();if(!r.candidateId)return a.NextResponse.json({error:"Candidate ID is required"},{status:400});if(!(await i.db.select().from(o.candidates).where((0,d.eq)(o.candidates.id,r.candidateId)).limit(1)).length)return a.NextResponse.json({error:"Candidate not found"},{status:404});if((await i.db.select().from(o.testResults).where((0,d.eq)(o.testResults.candidateId,r.candidateId)).limit(1)).length>0)return a.NextResponse.json({error:"Test results already exist for this candidate"},{status:409});let s={candidateId:r.candidateId,listeningScore:r.listeningScore?parseFloat(r.listeningScore):null,listeningBandScore:r.listeningBandScore?parseFloat(r.listeningBandScore):null,readingScore:r.readingScore?parseFloat(r.readingScore):null,readingBandScore:r.readingBandScore?parseFloat(r.readingBandScore):null,writingTask1Score:r.writingTask1Score?parseFloat(r.writingTask1Score):null,writingTask2Score:r.writingTask2Score?parseFloat(r.writingTask2Score):null,writingBandScore:r.writingBandScore?parseFloat(r.writingBandScore):null,speakingFluencyScore:r.speakingFluencyScore?parseFloat(r.speakingFluencyScore):null,speakingLexicalScore:r.speakingLexicalScore?parseFloat(r.speakingLexicalScore):null,speakingGrammarScore:r.speakingGrammarScore?parseFloat(r.speakingGrammarScore):null,speakingPronunciationScore:r.speakingPronunciationScore?parseFloat(r.speakingPronunciationScore):null,speakingBandScore:r.speakingBandScore?parseFloat(r.speakingBandScore):null,overallBandScore:r.overallBandScore?parseFloat(r.overallBandScore):null,status:"pending",enteredBy:t.user?.id},l=await i.db.insert(o.testResults).values(s).returning();return a.NextResponse.json(l[0],{status:201})}catch(e){return console.error("Error creating test result:",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}async function u(e){try{let t=await (0,n.j2)();if(!t)return a.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:r}=new URL(e.url),s=parseInt(r.get("page")||"1"),l=parseInt(r.get("limit")||"20"),c=r.get("status"),u=(s-1)*l,p=t.user?.id,g=(0,d.eq)(o.testResults.enteredBy,p);c&&(g=(0,d.eq)(o.testResults.enteredBy,p));let _=await i.db.select({id:o.testResults.id,candidateId:o.testResults.candidateId,listeningBandScore:o.testResults.listeningBandScore,readingBandScore:o.testResults.readingBandScore,writingBandScore:o.testResults.writingBandScore,speakingBandScore:o.testResults.speakingBandScore,overallBandScore:o.testResults.overallBandScore,status:o.testResults.status,createdAt:o.testResults.createdAt,candidate:{fullName:o.candidates.fullName,passportNumber:o.candidates.passportNumber,testDate:o.candidates.testDate}}).from(o.testResults).innerJoin(o.candidates,(0,d.eq)(o.testResults.candidateId,o.candidates.id)).where(g).limit(l).offset(u).orderBy(o.testResults.createdAt);return a.NextResponse.json({results:_,page:s,limit:l})}catch(e){return console.error("Error fetching test results:",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}[n,i]=l.then?(await l)():l,s()}catch(e){s(e)}})},37702:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.d(t,{Y9:()=>u,j2:()=>p});var a=r(32221),n=r(31648),i=r(62693),o=r(48590),d=r(47579),l=r(34926),c=e([i]);i=(c.then?(await c)():c)[0];let{handlers:u,auth:p,signIn:g,signOut:_}=(0,a.Ay)({session:{strategy:"jwt"},providers:[(0,n.A)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;try{let t=await i.db.select().from(o.users).where((0,d.eq)(o.users.email,e.email)).limit(1);if(0===t.length)return null;let r=t[0];if(!r.password||!await l.Ay.compare(e.password,r.password))return null;return{id:r.id,email:r.email,name:r.name,role:r.role}}catch(e){return console.error("Authentication error:",e),null}}})],callbacks:{jwt:async({token:e,user:t})=>(t&&(e.role=t.role),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role),e)},pages:{signIn:"/auth/signin"}});s()}catch(e){s(e)}})},62693:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.d(t,{db:()=>c});var a=r(10072),n=r(2113),i=r(48590),o=e([n,a]);[n,a]=o.then?(await o)():o;let d=process.env.DATABASE_URL,l=(0,n.default)(d,{prepare:!1}),c=(0,a.f)(l,{schema:i});s()}catch(e){s(e)}})},48590:(e,t,r)=>{"use strict";r.r(t),r.d(t,{accounts:()=>p,aiFeedback:()=>f,candidates:()=>k,sessions:()=>g,testResults:()=>m,users:()=>u,verificationTokens:()=>_});var s=r(87858),a=r(44799),n=r(32590),i=r(9848),o=r(70009),d=r(27390),l=r(32190),c=r(4502);let u=(0,s.cJ)("users",{id:(0,a.Qq)("id").primaryKey().$defaultFn(()=>(0,c.sX)()),name:(0,a.Qq)("name"),email:(0,a.Qq)("email").notNull().unique(),emailVerified:(0,n.vE)("emailVerified",{mode:"date"}),image:(0,a.Qq)("image"),password:(0,a.Qq)("password"),role:(0,a.Qq)("role",{enum:["admin","test_checker"]}).notNull().default("test_checker"),createdAt:(0,n.vE)("created_at").defaultNow().notNull(),updatedAt:(0,n.vE)("updated_at").defaultNow().notNull()}),p=(0,s.cJ)("accounts",{userId:(0,a.Qq)("userId").notNull().references(()=>u.id,{onDelete:"cascade"}),type:(0,a.Qq)("type").notNull(),provider:(0,a.Qq)("provider").notNull(),providerAccountId:(0,a.Qq)("providerAccountId").notNull(),refresh_token:(0,a.Qq)("refresh_token"),access_token:(0,a.Qq)("access_token"),expires_at:(0,i.nd)("expires_at"),token_type:(0,a.Qq)("token_type"),scope:(0,a.Qq)("scope"),id_token:(0,a.Qq)("id_token"),session_state:(0,a.Qq)("session_state")}),g=(0,s.cJ)("sessions",{sessionToken:(0,a.Qq)("sessionToken").primaryKey(),userId:(0,a.Qq)("userId").notNull().references(()=>u.id,{onDelete:"cascade"}),expires:(0,n.vE)("expires",{mode:"date"}).notNull()}),_=(0,s.cJ)("verificationTokens",{identifier:(0,a.Qq)("identifier").notNull(),token:(0,a.Qq)("token").notNull(),expires:(0,n.vE)("expires",{mode:"date"}).notNull()}),k=(0,s.cJ)("candidates",{id:(0,a.Qq)("id").primaryKey().$defaultFn(()=>(0,c.sX)()),fullName:(0,a.Qq)("full_name").notNull(),email:(0,a.Qq)("email").notNull().unique(),phoneNumber:(0,a.Qq)("phone_number").notNull(),dateOfBirth:(0,n.vE)("date_of_birth",{mode:"date"}).notNull(),nationality:(0,a.Qq)("nationality").notNull(),passportNumber:(0,a.Qq)("passport_number").notNull().unique(),testDate:(0,n.vE)("test_date",{mode:"date"}).notNull(),testCenter:(0,a.Qq)("test_center").notNull(),photoUrl:(0,a.Qq)("photo_url"),createdAt:(0,n.vE)("created_at").defaultNow().notNull(),updatedAt:(0,n.vE)("updated_at").defaultNow().notNull()}),m=(0,s.cJ)("test_results",{id:(0,a.Qq)("id").primaryKey().$defaultFn(()=>(0,c.sX)()),candidateId:(0,a.Qq)("candidate_id").notNull().references(()=>k.id,{onDelete:"cascade"}),listeningScore:(0,o._)("listening_score",{precision:3,scale:1}),listeningBandScore:(0,o._)("listening_band_score",{precision:2,scale:1}),readingScore:(0,o._)("reading_score",{precision:3,scale:1}),readingBandScore:(0,o._)("reading_band_score",{precision:2,scale:1}),writingTask1Score:(0,o._)("writing_task1_score",{precision:2,scale:1}),writingTask2Score:(0,o._)("writing_task2_score",{precision:2,scale:1}),writingBandScore:(0,o._)("writing_band_score",{precision:2,scale:1}),speakingFluencyScore:(0,o._)("speaking_fluency_score",{precision:2,scale:1}),speakingLexicalScore:(0,o._)("speaking_lexical_score",{precision:2,scale:1}),speakingGrammarScore:(0,o._)("speaking_grammar_score",{precision:2,scale:1}),speakingPronunciationScore:(0,o._)("speaking_pronunciation_score",{precision:2,scale:1}),speakingBandScore:(0,o._)("speaking_band_score",{precision:2,scale:1}),overallBandScore:(0,o._)("overall_band_score",{precision:2,scale:1}),status:(0,a.Qq)("status",{enum:["pending","completed","verified"]}).notNull().default("pending"),enteredBy:(0,a.Qq)("entered_by").references(()=>u.id),verifiedBy:(0,a.Qq)("verified_by").references(()=>u.id),certificateGenerated:(0,d.zM)("certificate_generated").default(!1),certificateSerial:(0,a.Qq)("certificate_serial").unique(),certificateUrl:(0,a.Qq)("certificate_url"),aiFeedbackGenerated:(0,d.zM)("ai_feedback_generated").default(!1),testDate:(0,n.vE)("test_date",{mode:"date"}),createdAt:(0,n.vE)("created_at").defaultNow().notNull(),updatedAt:(0,n.vE)("updated_at").defaultNow().notNull()}),f=(0,s.cJ)("ai_feedback",{id:(0,a.Qq)("id").primaryKey().$defaultFn(()=>(0,c.sX)()),testResultId:(0,a.Qq)("test_result_id").notNull().references(()=>m.id,{onDelete:"cascade"}),listeningFeedback:(0,a.Qq)("listening_feedback"),readingFeedback:(0,a.Qq)("reading_feedback"),writingFeedback:(0,a.Qq)("writing_feedback"),speakingFeedback:(0,a.Qq)("speaking_feedback"),overallFeedback:(0,a.Qq)("overall_feedback"),studyRecommendations:(0,a.Qq)("study_recommendations"),strengths:(0,l.Pq)("strengths").$type(),weaknesses:(0,l.Pq)("weaknesses").$type(),studyPlan:(0,l.Pq)("study_plan").$type(),generatedAt:(0,n.vE)("generated_at").defaultNow().notNull()})}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[638,5452,9757,4681],()=>r(82404));module.exports=s})();