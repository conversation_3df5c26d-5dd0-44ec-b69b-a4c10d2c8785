(()=>{var e={};e.id=5413,e.ids=[5413],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},55511:e=>{"use strict";e.exports=require("crypto")},29021:e=>{"use strict";e.exports=require("fs")},79748:e=>{"use strict";e.exports=require("fs/promises")},33873:e=>{"use strict";e.exports=require("path")},2113:e=>{"use strict";e.exports=import("postgres")},77598:e=>{"use strict";e.exports=require("node:crypto")},22526:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{patchFetch:()=>c,routeModule:()=>d,serverHooks:()=>_,workAsyncStorage:()=>u,workUnitAsyncStorage:()=>p});var a=r(42706),i=r(28203),n=r(45994),o=r(10930),l=e([o]);o=(l.then?(await l)():l)[0];let d=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/upload/route",pathname:"/api/upload",filename:"route",bundlePath:"app/api/upload/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\api\\upload\\route.ts",nextConfigOutput:"",userland:o}),{workAsyncStorage:u,workUnitAsyncStorage:p,serverHooks:_}=d;function c(){return(0,n.patchFetch)({workAsyncStorage:u,workUnitAsyncStorage:p})}s()}catch(e){s(e)}})},96487:()=>{},78335:()=>{},10930:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{DELETE:()=>u,POST:()=>d});var a=r(39187),i=r(37702),n=r(79748),o=r(33873),l=r(29021),c=e([i]);async function d(e){try{if(!await (0,i.j2)())return a.NextResponse.json({error:"Unauthorized"},{status:401});let t=await e.formData(),r=t.get("file"),s=t.get("type");if(!r)return a.NextResponse.json({error:"No file provided"},{status:400});let c={photo:["image/jpeg","image/jpg","image/png","image/webp"],document:["application/pdf","image/jpeg","image/jpg","image/png"]},d=c[s]||c.photo;if(!d.includes(r.type))return a.NextResponse.json({error:`Invalid file type. Allowed types: ${d.join(", ")}`},{status:400});if(r.size>5242880)return a.NextResponse.json({error:"File size too large. Maximum size is 5MB"},{status:400});let u=(0,o.join)(process.cwd(),"public","uploads",s);(0,l.existsSync)(u)||await (0,n.mkdir)(u,{recursive:!0});let p=Date.now(),_=Math.random().toString(36).substring(2,15),f=r.name.split(".").pop(),m=`${p}_${_}.${f}`,g=(0,o.join)(u,m),y=await r.arrayBuffer(),q=Buffer.from(y);await (0,n.writeFile)(g,q);let k=`/uploads/${s}/${m}`;return a.NextResponse.json({success:!0,url:k,fileName:m,originalName:r.name,size:r.size,type:r.type})}catch(e){return console.error("File upload error:",e),a.NextResponse.json({error:"Failed to upload file"},{status:500})}}async function u(e){try{if(!await (0,i.j2)())return a.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:t}=new URL(e.url),s=t.get("url");if(!s)return a.NextResponse.json({error:"File URL is required"},{status:400});let n=(0,o.join)(process.cwd(),"public",s);if((0,l.existsSync)(n)){let{unlink:e}=await Promise.resolve().then(r.t.bind(r,79748,23));await e(n)}return a.NextResponse.json({success:!0,message:"File deleted successfully"})}catch(e){return console.error("File deletion error:",e),a.NextResponse.json({error:"Failed to delete file"},{status:500})}}i=(c.then?(await c)():c)[0],s()}catch(e){s(e)}})},37702:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.d(t,{Y9:()=>u,j2:()=>p});var a=r(32221),i=r(31648),n=r(62693),o=r(48590),l=r(47579),c=r(34926),d=e([n]);n=(d.then?(await d)():d)[0];let{handlers:u,auth:p,signIn:_,signOut:f}=(0,a.Ay)({session:{strategy:"jwt"},providers:[(0,i.A)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;try{let t=await n.db.select().from(o.users).where((0,l.eq)(o.users.email,e.email)).limit(1);if(0===t.length)return null;let r=t[0];if(!r.password||!await c.Ay.compare(e.password,r.password))return null;return{id:r.id,email:r.email,name:r.name,role:r.role}}catch(e){return console.error("Authentication error:",e),null}}})],callbacks:{jwt:async({token:e,user:t})=>(t&&(e.role=t.role),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role),e)},pages:{signIn:"/auth/signin"}});s()}catch(e){s(e)}})},62693:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.d(t,{db:()=>d});var a=r(10072),i=r(2113),n=r(48590),o=e([i,a]);[i,a]=o.then?(await o)():o;let l=process.env.DATABASE_URL,c=(0,i.default)(l,{prepare:!1}),d=(0,a.f)(c,{schema:n});s()}catch(e){s(e)}})},48590:(e,t,r)=>{"use strict";r.r(t),r.d(t,{accounts:()=>p,aiFeedback:()=>y,candidates:()=>m,sessions:()=>_,testResults:()=>g,users:()=>u,verificationTokens:()=>f});var s=r(87858),a=r(44799),i=r(32590),n=r(9848),o=r(70009),l=r(27390),c=r(32190),d=r(4502);let u=(0,s.cJ)("users",{id:(0,a.Qq)("id").primaryKey().$defaultFn(()=>(0,d.sX)()),name:(0,a.Qq)("name"),email:(0,a.Qq)("email").notNull().unique(),emailVerified:(0,i.vE)("emailVerified",{mode:"date"}),image:(0,a.Qq)("image"),password:(0,a.Qq)("password"),role:(0,a.Qq)("role",{enum:["admin","test_checker"]}).notNull().default("test_checker"),createdAt:(0,i.vE)("created_at").defaultNow().notNull(),updatedAt:(0,i.vE)("updated_at").defaultNow().notNull()}),p=(0,s.cJ)("accounts",{userId:(0,a.Qq)("userId").notNull().references(()=>u.id,{onDelete:"cascade"}),type:(0,a.Qq)("type").notNull(),provider:(0,a.Qq)("provider").notNull(),providerAccountId:(0,a.Qq)("providerAccountId").notNull(),refresh_token:(0,a.Qq)("refresh_token"),access_token:(0,a.Qq)("access_token"),expires_at:(0,n.nd)("expires_at"),token_type:(0,a.Qq)("token_type"),scope:(0,a.Qq)("scope"),id_token:(0,a.Qq)("id_token"),session_state:(0,a.Qq)("session_state")}),_=(0,s.cJ)("sessions",{sessionToken:(0,a.Qq)("sessionToken").primaryKey(),userId:(0,a.Qq)("userId").notNull().references(()=>u.id,{onDelete:"cascade"}),expires:(0,i.vE)("expires",{mode:"date"}).notNull()}),f=(0,s.cJ)("verificationTokens",{identifier:(0,a.Qq)("identifier").notNull(),token:(0,a.Qq)("token").notNull(),expires:(0,i.vE)("expires",{mode:"date"}).notNull()}),m=(0,s.cJ)("candidates",{id:(0,a.Qq)("id").primaryKey().$defaultFn(()=>(0,d.sX)()),fullName:(0,a.Qq)("full_name").notNull(),email:(0,a.Qq)("email").notNull().unique(),phoneNumber:(0,a.Qq)("phone_number").notNull(),dateOfBirth:(0,i.vE)("date_of_birth",{mode:"date"}).notNull(),nationality:(0,a.Qq)("nationality").notNull(),passportNumber:(0,a.Qq)("passport_number").notNull().unique(),testDate:(0,i.vE)("test_date",{mode:"date"}).notNull(),testCenter:(0,a.Qq)("test_center").notNull(),photoUrl:(0,a.Qq)("photo_url"),createdAt:(0,i.vE)("created_at").defaultNow().notNull(),updatedAt:(0,i.vE)("updated_at").defaultNow().notNull()}),g=(0,s.cJ)("test_results",{id:(0,a.Qq)("id").primaryKey().$defaultFn(()=>(0,d.sX)()),candidateId:(0,a.Qq)("candidate_id").notNull().references(()=>m.id,{onDelete:"cascade"}),listeningScore:(0,o._)("listening_score",{precision:3,scale:1}),listeningBandScore:(0,o._)("listening_band_score",{precision:2,scale:1}),readingScore:(0,o._)("reading_score",{precision:3,scale:1}),readingBandScore:(0,o._)("reading_band_score",{precision:2,scale:1}),writingTask1Score:(0,o._)("writing_task1_score",{precision:2,scale:1}),writingTask2Score:(0,o._)("writing_task2_score",{precision:2,scale:1}),writingBandScore:(0,o._)("writing_band_score",{precision:2,scale:1}),speakingFluencyScore:(0,o._)("speaking_fluency_score",{precision:2,scale:1}),speakingLexicalScore:(0,o._)("speaking_lexical_score",{precision:2,scale:1}),speakingGrammarScore:(0,o._)("speaking_grammar_score",{precision:2,scale:1}),speakingPronunciationScore:(0,o._)("speaking_pronunciation_score",{precision:2,scale:1}),speakingBandScore:(0,o._)("speaking_band_score",{precision:2,scale:1}),overallBandScore:(0,o._)("overall_band_score",{precision:2,scale:1}),status:(0,a.Qq)("status",{enum:["pending","completed","verified"]}).notNull().default("pending"),enteredBy:(0,a.Qq)("entered_by").references(()=>u.id),verifiedBy:(0,a.Qq)("verified_by").references(()=>u.id),certificateGenerated:(0,l.zM)("certificate_generated").default(!1),certificateSerial:(0,a.Qq)("certificate_serial").unique(),certificateUrl:(0,a.Qq)("certificate_url"),aiFeedbackGenerated:(0,l.zM)("ai_feedback_generated").default(!1),testDate:(0,i.vE)("test_date",{mode:"date"}),createdAt:(0,i.vE)("created_at").defaultNow().notNull(),updatedAt:(0,i.vE)("updated_at").defaultNow().notNull()}),y=(0,s.cJ)("ai_feedback",{id:(0,a.Qq)("id").primaryKey().$defaultFn(()=>(0,d.sX)()),testResultId:(0,a.Qq)("test_result_id").notNull().references(()=>g.id,{onDelete:"cascade"}),listeningFeedback:(0,a.Qq)("listening_feedback"),readingFeedback:(0,a.Qq)("reading_feedback"),writingFeedback:(0,a.Qq)("writing_feedback"),speakingFeedback:(0,a.Qq)("speaking_feedback"),overallFeedback:(0,a.Qq)("overall_feedback"),studyRecommendations:(0,a.Qq)("study_recommendations"),strengths:(0,c.Pq)("strengths").$type(),weaknesses:(0,c.Pq)("weaknesses").$type(),studyPlan:(0,c.Pq)("study_plan").$type(),generatedAt:(0,i.vE)("generated_at").defaultNow().notNull()})}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[638,5452,9757,4681],()=>r(22526));module.exports=s})();