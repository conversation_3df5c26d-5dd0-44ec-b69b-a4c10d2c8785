(()=>{var e={};e.id=7618,e.ids=[7618],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},39568:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>d.a,__next_app__:()=>m,pages:()=>o,routeModule:()=>x,tree:()=>c});var r=s(70260),a=s(28203),i=s(25155),d=s.n(i),l=s(67292),n={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>l[e]);s.d(t,n);let c=["",{children:["admin",{children:["reports",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,76573)),"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\admin\\reports\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,96038)),"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\admin\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,71354)),"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],o=["C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\admin\\reports\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/reports/page",pathname:"/admin/reports",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},97032:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,13219,23)),Promise.resolve().then(s.t.bind(s,34863,23)),Promise.resolve().then(s.t.bind(s,25155,23)),Promise.resolve().then(s.t.bind(s,40802,23)),Promise.resolve().then(s.t.bind(s,9350,23)),Promise.resolve().then(s.t.bind(s,48530,23)),Promise.resolve().then(s.t.bind(s,88921,23))},60584:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,66959,23)),Promise.resolve().then(s.t.bind(s,33875,23)),Promise.resolve().then(s.t.bind(s,88903,23)),Promise.resolve().then(s.t.bind(s,57174,23)),Promise.resolve().then(s.t.bind(s,84178,23)),Promise.resolve().then(s.t.bind(s,87190,23)),Promise.resolve().then(s.t.bind(s,61365,23))},71315:(e,t,s)=>{Promise.resolve().then(s.bind(s,70452))},8267:(e,t,s)=>{Promise.resolve().then(s.bind(s,98805))},67305:(e,t,s)=>{Promise.resolve().then(s.bind(s,96038))},57577:(e,t,s)=>{Promise.resolve().then(s.bind(s,53760))},43023:(e,t,s)=>{Promise.resolve().then(s.bind(s,76573))},51271:(e,t,s)=>{Promise.resolve().then(s.bind(s,50841))},41680:(e,t,s)=>{"use strict";s.d(t,{A:()=>m});var r=s(58009);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,s)=>s?s.toUpperCase():t.toLowerCase()),d=e=>{let t=i(e);return t.charAt(0).toUpperCase()+t.slice(1)},l=(...e)=>e.filter((e,t,s)=>!!e&&""!==e.trim()&&s.indexOf(e)===t).join(" ").trim(),n=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=(0,r.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:s=2,absoluteStrokeWidth:a,className:i="",children:d,iconNode:o,...m},x)=>(0,r.createElement)("svg",{ref:x,...c,width:t,height:t,stroke:e,strokeWidth:a?24*Number(s)/Number(t):s,className:l("lucide",i),...!d&&!n(m)&&{"aria-hidden":"true"},...m},[...o.map(([e,t])=>(0,r.createElement)(e,t)),...Array.isArray(d)?d:[d]])),m=(e,t)=>{let s=(0,r.forwardRef)(({className:s,...i},n)=>(0,r.createElement)(o,{ref:n,iconNode:t,className:l(`lucide-${a(d(e))}`,`lucide-${e}`,s),...i}));return s.displayName=d(e),s}},43464:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(41680).A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},45723:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(41680).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},79660:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(41680).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},46583:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(41680).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},4643:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(41680).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},19473:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(41680).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},61075:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(41680).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},87137:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(41680).A)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},30722:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(41680).A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},92557:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(41680).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},16873:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(41680).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},80832:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(41680).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},69855:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(41680).A)("user-plus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},64977:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(41680).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},79334:(e,t,s)=>{"use strict";var r=s(58686);s.o(r,"useParams")&&s.d(t,{useParams:function(){return r.useParams}}),s.o(r,"useRouter")&&s.d(t,{useRouter:function(){return r.useRouter}}),s.o(r,"useSearchParams")&&s.d(t,{useSearchParams:function(){return r.useSearchParams}})},53760:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>y});var r=s(45512),a=s(98805),i=s(79334);s(58009);var d=s(28531),l=s.n(d),n=s(87137),c=s(64977),o=s(69855),m=s(79660),x=s(16873),h=s(61075);let p=(0,s(41680).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);var u=s(30722);function y({children:e}){let{data:t,status:s}=(0,a.wV)();if((0,i.useRouter)(),"loading"===s)return(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})});if(!t||t.user?.role!=="admin")return null;let d=[{name:"Dashboard",href:"/admin",icon:n.A},{name:"Candidates",href:"/admin/candidates",icon:c.A},{name:"Add Candidate",href:"/admin/candidates/new",icon:o.A},{name:"Test Results",href:"/admin/results",icon:m.A},{name:"Advanced Search",href:"/admin/search",icon:x.A},{name:"Reports",href:"/admin/reports",icon:h.A},{name:"Settings",href:"/admin/settings",icon:p}];return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)("div",{className:"fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg",children:(0,r.jsxs)("div",{className:"flex flex-col h-full",children:[(0,r.jsxs)("div",{className:"flex items-center px-6 py-4 border-b border-gray-200",children:[(0,r.jsx)(h.A,{className:"h-8 w-8 text-blue-600 mr-3"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-lg font-semibold text-gray-900",children:"IELTS Admin"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Certification System"})]})]}),(0,r.jsx)("nav",{className:"flex-1 px-4 py-6 space-y-2",children:d.map(e=>{let t=e.icon;return(0,r.jsxs)(l(),{href:e.href,className:"flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-100 hover:text-gray-900 group",children:[(0,r.jsx)(t,{className:"h-5 w-5 mr-3 text-gray-400 group-hover:text-gray-500"}),e.name]},e.name)})}),(0,r.jsxs)("div",{className:"border-t border-gray-200 p-4",children:[(0,r.jsx)("div",{className:"flex items-center mb-3",children:(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900",children:t.user?.name}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:t.user?.email}),(0,r.jsx)("p",{className:"text-xs text-blue-600 font-medium",children:"Administrator"})]})}),(0,r.jsxs)("button",{onClick:()=>(0,a.CI)({callbackUrl:"/"}),className:"flex items-center w-full px-3 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-100 hover:text-gray-900",children:[(0,r.jsx)(u.A,{className:"h-4 w-4 mr-3"}),"Sign out"]})]})]})}),(0,r.jsx)("div",{className:"pl-64",children:(0,r.jsx)("main",{className:"py-6",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:e})})})]})}},50841:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>u});var r=s(45512),a=s(58009),i=s(92557),d=s(19473),l=s(64977),n=s(61075),c=s(80832),o=s(43464),m=s(79660),x=s(45723),h=s(4643),p=s(46583);function u(){let[e,t]=(0,a.useState)(null),[s,u]=(0,a.useState)(!0),[y,f]=(0,a.useState)({from:new Date(Date.now()-2592e6).toISOString().split("T")[0],to:new Date().toISOString().split("T")[0]}),v=(0,a.useCallback)(async()=>{u(!0);try{let e=await fetch("/api/admin/reports",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(y)});if(e.ok){let s=await e.json();t(s)}}catch(e){console.error("Error fetching report data:",e)}finally{u(!1)}},[y]),g=async()=>{try{let e=await fetch("/api/admin/reports/export",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({...y,format:"pdf"})});if(e.ok){let t=await e.blob(),s=window.URL.createObjectURL(t),r=document.createElement("a");r.href=s,r.download=`ielts_report_${y.from}_to_${y.to}.pdf`,document.body.appendChild(r),r.click(),window.URL.revokeObjectURL(s),document.body.removeChild(r)}}catch(e){console.error("Export error:",e)}};return s?(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Reports & Analytics"}),(0,r.jsx)("p",{className:"text-gray-600",children:"System performance and statistics"})]}),(0,r.jsxs)("div",{className:"flex gap-3",children:[(0,r.jsxs)("button",{onClick:v,className:"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50",children:[(0,r.jsx)(i.A,{className:"h-4 w-4 mr-2"}),"Refresh"]}),(0,r.jsxs)("button",{onClick:g,className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700",children:[(0,r.jsx)(d.A,{className:"h-4 w-4 mr-2"}),"Export Report"]})]})]}),(0,r.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Report Period"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"dateFrom",className:"block text-sm font-medium text-gray-700 mb-2",children:"From Date"}),(0,r.jsx)("input",{type:"date",id:"dateFrom",value:y.from,onChange:e=>f(t=>({...t,from:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"dateTo",className:"block text-sm font-medium text-gray-700 mb-2",children:"To Date"}),(0,r.jsx)("input",{type:"date",id:"dateTo",value:y.to,onChange:e=>f(t=>({...t,to:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]})]})]}),e&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,r.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,r.jsx)("div",{className:"p-5",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(l.A,{className:"h-8 w-8 text-blue-600"})}),(0,r.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,r.jsxs)("dl",{children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Total Candidates"}),(0,r.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:e.overview.totalCandidates.toLocaleString()})]})})]})})}),(0,r.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,r.jsx)("div",{className:"p-5",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(n.A,{className:"h-8 w-8 text-green-600"})}),(0,r.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,r.jsxs)("dl",{children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Test Results"}),(0,r.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:e.overview.totalResults.toLocaleString()})]})})]})})}),(0,r.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,r.jsx)("div",{className:"p-5",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(c.A,{className:"h-8 w-8 text-purple-600"})}),(0,r.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,r.jsxs)("dl",{children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Average Band Score"}),(0,r.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:e.overview.averageBandScore.toFixed(1)})]})})]})})}),(0,r.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,r.jsx)("div",{className:"p-5",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(o.A,{className:"h-8 w-8 text-yellow-600"})}),(0,r.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,r.jsxs)("dl",{children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Certificates Generated"}),(0,r.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:e.overview.certificatesGenerated.toLocaleString()})]})})]})})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-medium text-gray-900 mb-4 flex items-center",children:[(0,r.jsx)(m.A,{className:"h-5 w-5 mr-2"}),"Band Score Distribution"]}),(0,r.jsx)("div",{className:"space-y-3",children:e.bandScoreDistribution.map(e=>(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsxs)("div",{className:"w-16 text-sm font-medium text-gray-900",children:["Band ",e.score]}),(0,r.jsx)("div",{className:"flex-1 mx-4",children:(0,r.jsx)("div",{className:"bg-gray-200 rounded-full h-2",children:(0,r.jsx)("div",{className:"bg-blue-600 h-2 rounded-full",style:{width:`${e.percentage}%`}})})}),(0,r.jsxs)("div",{className:"w-16 text-sm text-gray-600 text-right",children:[e.count," (",e.percentage.toFixed(1),"%)"]})]},e.score))})]}),(0,r.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Test Center Performance"}),(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase",children:"Center"}),(0,r.jsx)("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase",children:"Candidates"}),(0,r.jsx)("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase",children:"Avg Score"})]})}),(0,r.jsx)("tbody",{className:"divide-y divide-gray-200",children:e.testCenterStats.map((e,t)=>(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{className:"px-4 py-2 text-sm text-gray-900",children:e.center.replace("IELTS Test Center - ","")}),(0,r.jsx)("td",{className:"px-4 py-2 text-sm text-gray-600",children:e.candidates}),(0,r.jsx)("td",{className:"px-4 py-2 text-sm text-gray-600",children:e.avgScore.toFixed(1)})]},t))})]})})]})]}),(0,r.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-medium text-gray-900 mb-4 flex items-center",children:[(0,r.jsx)(x.A,{className:"h-5 w-5 mr-2"}),"Monthly Trends"]}),(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase",children:"Month"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase",children:"New Candidates"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase",children:"Test Results"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase",children:"Average Score"})]})}),(0,r.jsx)("tbody",{className:"divide-y divide-gray-200",children:e.monthlyStats.map((e,t)=>(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{className:"px-6 py-4 text-sm font-medium text-gray-900",children:e.month}),(0,r.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:e.candidates}),(0,r.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:e.results}),(0,r.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:e.avgScore.toFixed(1)})]},t))})]})})]}),(0,r.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-medium text-gray-900 mb-4 flex items-center",children:[(0,r.jsx)(h.A,{className:"h-5 w-5 mr-2"}),"Recent Activity (Last 7 Days)"]}),(0,r.jsx)("div",{className:"space-y-3",children:e.recentActivity.map((e,t)=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(x.A,{className:"h-4 w-4 text-gray-400 mr-3"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-900",children:new Date(e.date).toLocaleDateString()})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-600",children:[(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(l.A,{className:"h-4 w-4 mr-1"}),e.candidates," candidates"]}),(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(p.A,{className:"h-4 w-4 mr-1"}),e.results," results"]})]})]},t))})]})]})]})}},96038:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\admin\\layout.tsx","default")},76573:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\reports\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\admin\\reports\\page.tsx","default")},71354:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n,metadata:()=>l});var r=s(62740),a=s(85041),i=s.n(a),d=s(70452);s(61135);let l={title:"IELTS Certification System",description:"Professional IELTS test result management and certification system"};function n({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsx)("body",{className:i().className,children:(0,r.jsx)(d.SessionProvider,{children:e})})})}},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(88077);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},61135:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[638,8338,2367],()=>s(39568));module.exports=r})();