(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4619],{4012:(e,t,s)=>{Promise.resolve().then(s.bind(s,4398))},9136:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(7401).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},4857:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(7401).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},2598:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(7401).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},4081:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(7401).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},5525:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(7401).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},7223:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(7401).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},1773:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(7401).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},853:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(7401).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},6764:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(7401).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},1466:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(7401).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},4398:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>f});var a=s(5155),l=s(2115),r=s(8173),n=s.n(r),d=s(5565),i=s(4857),c=s(853),o=s(5525),u=s(1773),m=s(9136),h=s(1466),x=s(4081),b=s(2423),p=s(7223),y=s(2598),g=s(6764);function f(){let[e,t]=(0,l.useState)({query:"",searchType:"all",testCenter:"",testDateFrom:"",testDateTo:"",nationality:"",hasResults:"all",resultStatus:"all",bandScoreMin:"",bandScoreMax:""}),[s,r]=(0,l.useState)([]),[f,j]=(0,l.useState)(!1),[v,N]=(0,l.useState)(!1),[w,S]=(0,l.useState)(!1),[k,A]=(0,l.useState)(0),C=(e,s)=>{t(t=>({...t,[e]:s}))},T=async t=>{null==t||t.preventDefault(),j(!0),N(!0);try{let t=await fetch("/api/admin/search",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(t.ok){let e=await t.json();r(e.results),A(e.total)}else r([]),A(0)}catch(e){console.error("Search error:",e),r([]),A(0)}finally{j(!1)}},M=async()=>{try{let t=await fetch("/api/admin/export",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({filters:e,format:"csv"})});if(t.ok){let e=await t.blob(),s=window.URL.createObjectURL(e),a=document.createElement("a");a.href=s,a.download="ielts_search_results_".concat(new Date().toISOString().split("T")[0],".csv"),document.body.appendChild(a),a.click(),window.URL.revokeObjectURL(s),document.body.removeChild(a)}}catch(e){console.error("Export error:",e)}};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Advanced Search"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Search and filter candidates and test results"})]}),s.length>0&&(0,a.jsxs)("button",{onClick:M,className:"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50",children:[(0,a.jsx)(i.A,{className:"h-4 w-4 mr-2"}),"Export Results"]})]}),(0,a.jsx)("div",{className:"bg-white shadow rounded-lg p-6",children:(0,a.jsxs)("form",{onSubmit:T,className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"searchType",className:"block text-sm font-medium text-gray-700 mb-2",children:"Search Type"}),(0,a.jsxs)("select",{id:"searchType",value:e.searchType,onChange:e=>C("searchType",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,a.jsx)("option",{value:"all",children:"All Fields"}),(0,a.jsx)("option",{value:"name",children:"Full Name"}),(0,a.jsx)("option",{value:"email",children:"Email"}),(0,a.jsx)("option",{value:"passport",children:"Passport Number"})]})]}),(0,a.jsxs)("div",{className:"md:col-span-2",children:[(0,a.jsx)("label",{htmlFor:"query",className:"block text-sm font-medium text-gray-700 mb-2",children:"Search Query"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(c.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,a.jsx)("input",{type:"text",id:"query",value:e.query,onChange:e=>C("query",e.target.value),placeholder:"Enter search term...",className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between pt-4 border-t border-gray-200",children:[(0,a.jsxs)("button",{type:"button",onClick:()=>S(!w),className:"inline-flex items-center text-sm text-blue-600 hover:text-blue-700",children:[(0,a.jsx)(o.A,{className:"h-4 w-4 mr-1"}),w?"Hide":"Show"," Advanced Filters"]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)("button",{type:"button",onClick:()=>{t({query:"",searchType:"all",testCenter:"",testDateFrom:"",testDateTo:"",nationality:"",hasResults:"all",resultStatus:"all",bandScoreMin:"",bandScoreMax:""}),r([]),N(!1)},className:"px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50",children:[(0,a.jsx)(u.A,{className:"h-4 w-4 mr-2 inline"}),"Clear"]}),(0,a.jsx)("button",{type:"submit",disabled:f,className:"px-6 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50",children:f?"Searching...":"Search"})]})]}),w&&(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 pt-4 border-t border-gray-200",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"testCenter",className:"block text-sm font-medium text-gray-700 mb-2",children:"Test Center"}),(0,a.jsxs)("select",{id:"testCenter",value:e.testCenter,onChange:e=>C("testCenter",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,a.jsx)("option",{value:"",children:"All Centers"}),["IELTS Test Center - London","IELTS Test Center - Manchester","IELTS Test Center - Birmingham","IELTS Test Center - Edinburgh","IELTS Test Center - Cardiff","IELTS Test Center - Belfast","IELTS Test Center - Dublin","IELTS Test Center - Online"].map(e=>(0,a.jsx)("option",{value:e,children:e},e))]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"testDateFrom",className:"block text-sm font-medium text-gray-700 mb-2",children:"Test Date From"}),(0,a.jsx)("input",{type:"date",id:"testDateFrom",value:e.testDateFrom,onChange:e=>C("testDateFrom",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"testDateTo",className:"block text-sm font-medium text-gray-700 mb-2",children:"Test Date To"}),(0,a.jsx)("input",{type:"date",id:"testDateTo",value:e.testDateTo,onChange:e=>C("testDateTo",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"nationality",className:"block text-sm font-medium text-gray-700 mb-2",children:"Nationality"}),(0,a.jsx)("input",{type:"text",id:"nationality",value:e.nationality,onChange:e=>C("nationality",e.target.value),placeholder:"e.g., British, American",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"hasResults",className:"block text-sm font-medium text-gray-700 mb-2",children:"Has Test Results"}),(0,a.jsxs)("select",{id:"hasResults",value:e.hasResults,onChange:e=>C("hasResults",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,a.jsx)("option",{value:"all",children:"All Candidates"}),(0,a.jsx)("option",{value:"yes",children:"With Results"}),(0,a.jsx)("option",{value:"no",children:"Without Results"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"resultStatus",className:"block text-sm font-medium text-gray-700 mb-2",children:"Result Status"}),(0,a.jsxs)("select",{id:"resultStatus",value:e.resultStatus,onChange:e=>C("resultStatus",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,a.jsx)("option",{value:"all",children:"All Statuses"}),(0,a.jsx)("option",{value:"pending",children:"Pending"}),(0,a.jsx)("option",{value:"completed",children:"Completed"}),(0,a.jsx)("option",{value:"verified",children:"Verified"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"bandScoreMin",className:"block text-sm font-medium text-gray-700 mb-2",children:"Min Band Score"}),(0,a.jsx)("input",{type:"number",id:"bandScoreMin",value:e.bandScoreMin,onChange:e=>C("bandScoreMin",e.target.value),min:"1",max:"9",step:"0.5",placeholder:"1.0",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"bandScoreMax",className:"block text-sm font-medium text-gray-700 mb-2",children:"Max Band Score"}),(0,a.jsx)("input",{type:"number",id:"bandScoreMax",value:e.bandScoreMax,onChange:e=>C("bandScoreMax",e.target.value),min:"1",max:"9",step:"0.5",placeholder:"9.0",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]})]})]})}),v&&(0,a.jsxs)("div",{className:"bg-white shadow rounded-lg",children:[(0,a.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("h3",{className:"text-lg font-medium text-gray-900",children:["Search Results ",k>0&&"(".concat(k,")")]}),s.length>0&&(0,a.jsxs)("div",{className:"flex items-center text-sm text-gray-500",children:[(0,a.jsx)(m.A,{className:"h-4 w-4 mr-1"}),s.filter(e=>e.hasResults).length," with results"]})]})}),f?(0,a.jsxs)("div",{className:"flex items-center justify-center py-12",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),(0,a.jsx)("span",{className:"ml-2 text-gray-600",children:"Searching..."})]}):s.length>0?(0,a.jsx)("div",{className:"divide-y divide-gray-200",children:s.map(e=>(0,a.jsx)("div",{className:"p-6 hover:bg-gray-50",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[e.photoUrl?(0,a.jsx)(d.default,{className:"h-12 w-12 rounded-full object-cover",src:e.photoUrl,alt:e.fullName,width:48,height:48}):(0,a.jsx)("div",{className:"h-12 w-12 rounded-full bg-gray-200 flex items-center justify-center",children:(0,a.jsx)(h.A,{className:"h-6 w-6 text-gray-400"})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h4",{className:"text-lg font-medium text-gray-900",children:e.fullName}),(0,a.jsxs)("div",{className:"mt-1 flex items-center space-x-4 text-sm text-gray-500",children:[(0,a.jsxs)("span",{className:"flex items-center",children:[(0,a.jsx)(x.A,{className:"h-4 w-4 mr-1"}),e.passportNumber]}),(0,a.jsxs)("span",{className:"flex items-center",children:[(0,a.jsx)(b.A,{className:"h-4 w-4 mr-1"}),new Date(e.testDate).toLocaleDateString()]}),(0,a.jsxs)("span",{className:"flex items-center",children:[(0,a.jsx)(p.A,{className:"h-4 w-4 mr-1"}),e.testCenter]})]}),(0,a.jsxs)("div",{className:"mt-1 text-sm text-gray-600",children:[e.email," • ",e.nationality]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[e.hasResults&&e.result?(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("p",{className:"font-semibold text-gray-900",children:["Band: ",e.result.overallBandScore||"N/A"]}),(0,a.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat("completed"===e.result.status?"bg-green-100 text-green-800":"pending"===e.result.status?"bg-yellow-100 text-yellow-800":"bg-blue-100 text-blue-800"),children:e.result.status})]}):(0,a.jsx)("span",{className:"text-sm text-gray-500",children:"No results"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(n(),{href:"/admin/candidates/".concat(e.id),className:"text-blue-600 hover:text-blue-900",title:"View Details",children:(0,a.jsx)(y.A,{className:"h-4 w-4"})}),(0,a.jsx)(n(),{href:"/admin/candidates/".concat(e.id,"/edit"),className:"text-green-600 hover:text-green-900",title:"Edit Candidate",children:(0,a.jsx)(g.A,{className:"h-4 w-4"})})]})]})]})},e.id))}):(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(c.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No results found"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Try adjusting your search criteria or filters."})]})]})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[8173,7311,8441,1517,7358],()=>t(4012)),_N_E=e.O()}]);