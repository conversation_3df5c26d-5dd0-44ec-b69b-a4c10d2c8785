{"/_not-found/page": "app/_not-found/page.js", "/api/admin/candidates/route": "app/api/admin/candidates/route.js", "/api/admin/dashboard/route": "app/api/admin/dashboard/route.js", "/api/admin/export/route": "app/api/admin/export/route.js", "/api/admin/reports/route": "app/api/admin/reports/route.js", "/api/admin/results/export/route": "app/api/admin/results/export/route.js", "/api/admin/results/route": "app/api/admin/results/route.js", "/api/admin/search/route": "app/api/admin/search/route.js", "/api/ai/generate-feedback/route": "app/api/ai/generate-feedback/route.js", "/api/ai/save-feedback/route": "app/api/ai/save-feedback/route.js", "/api/certificate/[id]/route": "app/api/certificate/[id]/route.js", "/api/certificate/verify/[serial]/route": "app/api/certificate/verify/[serial]/route.js", "/api/checker/candidates/[id]/route": "app/api/checker/candidates/[id]/route.js", "/api/checker/candidates/search/route": "app/api/checker/candidates/search/route.js", "/api/checker/dashboard/route": "app/api/checker/dashboard/route.js", "/api/checker/results/[id]/route": "app/api/checker/results/[id]/route.js", "/api/checker/results/route": "app/api/checker/results/route.js", "/api/checker/results/search/route": "app/api/checker/results/search/route.js", "/api/feedback/[resultId]/route": "app/api/feedback/[resultId]/route.js", "/api/results/[id]/route": "app/api/results/[id]/route.js", "/api/search/route": "app/api/search/route.js", "/api/upload/route": "app/api/upload/route.js", "/favicon.ico/route": "app/favicon.ico/route.js", "/api/auth/[...nextauth]/route": "app/api/auth/[...nextauth]/route.js", "/auth/signin/page": "app/auth/signin/page.js", "/page": "app/page.js", "/search/page": "app/search/page.js", "/verify/[serial]/page": "app/verify/[serial]/page.js", "/results/[id]/page": "app/results/[id]/page.js", "/verify/page": "app/verify/page.js", "/admin/candidates/new/page": "app/admin/candidates/new/page.js", "/admin/candidates/page": "app/admin/candidates/page.js", "/admin/results/page": "app/admin/results/page.js", "/dashboard/results/[id]/edit/page": "app/dashboard/results/[id]/edit/page.js", "/admin/page": "app/admin/page.js", "/admin/reports/page": "app/admin/reports/page.js", "/dashboard/feedback/page": "app/dashboard/feedback/page.js", "/dashboard/results/list/page": "app/dashboard/results/list/page.js", "/dashboard/results/page": "app/dashboard/results/page.js", "/dashboard/results/[id]/page": "app/dashboard/results/[id]/page.js", "/admin/search/page": "app/admin/search/page.js", "/dashboard/page": "app/dashboard/page.js", "/dashboard/search/page": "app/dashboard/search/page.js"}