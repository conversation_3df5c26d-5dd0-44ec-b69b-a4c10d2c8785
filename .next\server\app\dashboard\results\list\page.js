(()=>{var e={};e.id=5429,e.ids=[5429],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},93788:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>h,pages:()=>o,routeModule:()=>m,tree:()=>c});var r=s(70260),a=s(28203),l=s(25155),i=s.n(l),d=s(67292),n={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>d[e]);s.d(t,n);let c=["",{children:["dashboard",{children:["results",{children:["list",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,33502)),"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\dashboard\\results\\list\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,33405)),"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,71354)),"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],o=["C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\dashboard\\results\\list\\page.tsx"],h={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/results/list/page",pathname:"/dashboard/results/list",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},97032:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,13219,23)),Promise.resolve().then(s.t.bind(s,34863,23)),Promise.resolve().then(s.t.bind(s,25155,23)),Promise.resolve().then(s.t.bind(s,40802,23)),Promise.resolve().then(s.t.bind(s,9350,23)),Promise.resolve().then(s.t.bind(s,48530,23)),Promise.resolve().then(s.t.bind(s,88921,23))},60584:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,66959,23)),Promise.resolve().then(s.t.bind(s,33875,23)),Promise.resolve().then(s.t.bind(s,88903,23)),Promise.resolve().then(s.t.bind(s,57174,23)),Promise.resolve().then(s.t.bind(s,84178,23)),Promise.resolve().then(s.t.bind(s,87190,23)),Promise.resolve().then(s.t.bind(s,61365,23))},71315:(e,t,s)=>{Promise.resolve().then(s.bind(s,70452))},8267:(e,t,s)=>{Promise.resolve().then(s.bind(s,98805))},63508:(e,t,s)=>{Promise.resolve().then(s.bind(s,33405))},16244:(e,t,s)=>{Promise.resolve().then(s.bind(s,12361))},63941:(e,t,s)=>{Promise.resolve().then(s.bind(s,33502))},57861:(e,t,s)=>{Promise.resolve().then(s.bind(s,84042))},41680:(e,t,s)=>{"use strict";s.d(t,{A:()=>h});var r=s(58009);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,s)=>s?s.toUpperCase():t.toLowerCase()),i=e=>{let t=l(e);return t.charAt(0).toUpperCase()+t.slice(1)},d=(...e)=>e.filter((e,t,s)=>!!e&&""!==e.trim()&&s.indexOf(e)===t).join(" ").trim(),n=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=(0,r.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:s=2,absoluteStrokeWidth:a,className:l="",children:i,iconNode:o,...h},m)=>(0,r.createElement)("svg",{ref:m,...c,width:t,height:t,stroke:e,strokeWidth:a?24*Number(s)/Number(t):s,className:d("lucide",l),...!i&&!n(h)&&{"aria-hidden":"true"},...h},[...o.map(([e,t])=>(0,r.createElement)(e,t)),...Array.isArray(i)?i:[i]])),h=(e,t)=>{let s=(0,r.forwardRef)(({className:s,...l},n)=>(0,r.createElement)(o,{ref:n,iconNode:t,className:d(`lucide-${a(i(e))}`,`lucide-${e}`,s),...l}));return s.displayName=i(e),s}},78397:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(41680).A)("brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]])},79660:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(41680).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},45037:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(41680).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},46583:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(41680).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},94172:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(41680).A)("clipboard-list",[["rect",{width:"8",height:"4",x:"8",y:"2",rx:"1",ry:"1",key:"tgr4d6"}],["path",{d:"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2",key:"116196"}],["path",{d:"M12 11h4",key:"1jrz19"}],["path",{d:"M12 16h4",key:"n85exb"}],["path",{d:"M8 11h.01",key:"1dfujw"}],["path",{d:"M8 16h.01",key:"18s6g9"}]])},4643:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(41680).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},21956:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(41680).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},61075:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(41680).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},87137:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(41680).A)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},30722:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(41680).A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},18741:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(41680).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},16873:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(41680).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},19904:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(41680).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},79334:(e,t,s)=>{"use strict";var r=s(58686);s.o(r,"useParams")&&s.d(t,{useParams:function(){return r.useParams}}),s.o(r,"useRouter")&&s.d(t,{useRouter:function(){return r.useRouter}}),s.o(r,"useSearchParams")&&s.d(t,{useSearchParams:function(){return r.useSearchParams}})},12361:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>p});var r=s(45512),a=s(98805),l=s(79334);s(58009);var i=s(28531),d=s.n(i),n=s(87137),c=s(16873),o=s(94172),h=s(79660),m=s(78397),x=s(61075),u=s(30722);function p({children:e}){let{data:t,status:s}=(0,a.wV)();if((0,l.useRouter)(),"loading"===s)return(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})});if(!t)return null;let i=[{name:"Dashboard",href:"/dashboard",icon:n.A},{name:"Search Candidates",href:"/dashboard/search",icon:c.A},{name:"Enter Results",href:"/dashboard/results",icon:o.A},{name:"Test Results",href:"/dashboard/results/list",icon:h.A},{name:"AI Feedback",href:"/dashboard/feedback",icon:m.A}];return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)("div",{className:"fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg",children:(0,r.jsxs)("div",{className:"flex flex-col h-full",children:[(0,r.jsxs)("div",{className:"flex items-center px-6 py-4 border-b border-gray-200",children:[(0,r.jsx)(x.A,{className:"h-8 w-8 text-blue-600 mr-3"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-lg font-semibold text-gray-900",children:"IELTS Checker"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Test Results Portal"})]})]}),(0,r.jsx)("nav",{className:"flex-1 px-4 py-6 space-y-2",children:i.map(e=>{let t=e.icon;return(0,r.jsxs)(d(),{href:e.href,className:"flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-100 hover:text-gray-900 group",children:[(0,r.jsx)(t,{className:"h-5 w-5 mr-3 text-gray-400 group-hover:text-gray-500"}),e.name]},e.name)})}),(0,r.jsxs)("div",{className:"border-t border-gray-200 p-4",children:[(0,r.jsx)("div",{className:"flex items-center mb-3",children:(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900",children:t.user?.name}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:t.user?.email}),(0,r.jsx)("p",{className:"text-xs text-green-600 font-medium",children:"Test Checker"})]})}),(0,r.jsxs)("button",{onClick:()=>(0,a.CI)({callbackUrl:"/"}),className:"flex items-center w-full px-3 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-100 hover:text-gray-900",children:[(0,r.jsx)(u.A,{className:"h-4 w-4 mr-3"}),"Sign out"]})]})]})}),(0,r.jsx)("div",{className:"pl-64",children:(0,r.jsx)("main",{className:"py-6",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:e})})})]})}},84042:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>p});var r=s(45512),a=s(58009),l=s(28531),i=s.n(l),d=s(46583),n=s(4643),c=s(45037),o=s(18741),h=s(21956),m=s(19904),x=s(79660),u=s(16873);function p(){let[e,t]=(0,a.useState)([]),[s,l]=(0,a.useState)(!0),[p]=(0,a.useState)(1),[y,f]=(0,a.useState)("all");(0,a.useCallback)(async()=>{l(!0);try{let e=new URLSearchParams({page:p.toString(),limit:"20"});"all"!==y&&e.append("status",y);let s=await fetch(`/api/checker/results?${e}`);if(s.ok){let e=await s.json();t(e.results)}}catch(e){console.error("Error fetching results:",e)}finally{l(!1)}},[p,y,20]);let g=e=>{switch(e){case"completed":return(0,r.jsx)(d.A,{className:"h-5 w-5 text-green-500"});case"pending":return(0,r.jsx)(n.A,{className:"h-5 w-5 text-yellow-500"});default:return(0,r.jsx)(c.A,{className:"h-5 w-5 text-red-500"})}},b=e=>{let t="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium";switch(e){case"completed":return`${t} bg-green-100 text-green-800`;case"pending":return`${t} bg-yellow-100 text-yellow-800`;default:return`${t} bg-red-100 text-red-800`}};return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Test Results"}),(0,r.jsx)("p",{className:"text-gray-600",children:"View and manage entered test results"})]}),(0,r.jsxs)(i(),{href:"/dashboard/results",className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700",children:[(0,r.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"Enter New Results"]})]}),(0,r.jsx)("div",{className:"bg-white shadow rounded-lg p-6",children:(0,r.jsxs)("div",{className:"flex gap-4 items-center",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"statusFilter",className:"block text-sm font-medium text-gray-700 mb-2",children:"Filter by Status"}),(0,r.jsxs)("select",{id:"statusFilter",value:y,onChange:e=>f(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,r.jsx)("option",{value:"all",children:"All Results"}),(0,r.jsx)("option",{value:"pending",children:"Pending"}),(0,r.jsx)("option",{value:"completed",children:"Completed"}),(0,r.jsx)("option",{value:"verified",children:"Verified"})]})]}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Quick Stats"}),(0,r.jsxs)("div",{className:"flex gap-4 text-sm",children:[(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(n.A,{className:"h-4 w-4 text-yellow-500 mr-1"}),"Pending: ",e.filter(e=>"pending"===e.status).length]}),(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(d.A,{className:"h-4 w-4 text-green-500 mr-1"}),"Completed: ",e.filter(e=>"completed"===e.status).length]})]})]})]})}),(0,r.jsx)("div",{className:"bg-white shadow rounded-lg overflow-hidden",children:s?(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Candidate"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Test Date"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Scores"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Overall"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Entered"}),(0,r.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:e.map(e=>(0,r.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,r.jsx)("td",{className:"px-6 py-4",children:(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.candidate.fullName}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:e.candidate.passportNumber})]})}),(0,r.jsx)("td",{className:"px-6 py-4 text-sm text-gray-900",children:new Date(e.candidate.testDate).toLocaleDateString()}),(0,r.jsx)("td",{className:"px-6 py-4",children:(0,r.jsxs)("div",{className:"flex space-x-2 text-xs",children:[(0,r.jsxs)("span",{className:"bg-blue-100 text-blue-800 px-2 py-1 rounded",children:["L: ",e.listeningBandScore||"N/A"]}),(0,r.jsxs)("span",{className:"bg-green-100 text-green-800 px-2 py-1 rounded",children:["R: ",e.readingBandScore||"N/A"]}),(0,r.jsxs)("span",{className:"bg-purple-100 text-purple-800 px-2 py-1 rounded",children:["W: ",e.writingBandScore||"N/A"]}),(0,r.jsxs)("span",{className:"bg-red-100 text-red-800 px-2 py-1 rounded",children:["S: ",e.speakingBandScore||"N/A"]})]})}),(0,r.jsx)("td",{className:"px-6 py-4",children:(0,r.jsx)("div",{className:"text-lg font-semibold text-gray-900",children:e.overallBandScore||"N/A"})}),(0,r.jsx)("td",{className:"px-6 py-4",children:(0,r.jsxs)("div",{className:"flex items-center",children:[g(e.status),(0,r.jsx)("span",{className:`ml-2 ${b(e.status)}`,children:e.status})]})}),(0,r.jsx)("td",{className:"px-6 py-4 text-sm text-gray-500",children:new Date(e.createdAt).toLocaleDateString()}),(0,r.jsx)("td",{className:"px-6 py-4 text-right text-sm font-medium",children:(0,r.jsxs)("div",{className:"flex justify-end gap-2",children:[(0,r.jsx)(i(),{href:`/dashboard/results/${e.id}`,className:"text-blue-600 hover:text-blue-900",title:"View Details",children:(0,r.jsx)(h.A,{className:"h-4 w-4"})}),(0,r.jsx)(i(),{href:`/dashboard/results/${e.id}/edit`,className:"text-green-600 hover:text-green-900",title:"Edit Results",children:(0,r.jsx)(m.A,{className:"h-4 w-4"})})]})})]},e.id))})]})}),0===e.length&&!s&&(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)(x.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No results found"}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"all"===y?"You haven't entered any test results yet.":`No results with status "${y}" found.`}),(0,r.jsxs)(i(),{href:"/dashboard/results",className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700",children:[(0,r.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"Enter First Result"]})]})]})}),(0,r.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-blue-900 mb-4",children:"Quick Actions"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)(i(),{href:"/dashboard/search",className:"flex items-center p-4 bg-white rounded-lg shadow hover:shadow-md transition-shadow",children:[(0,r.jsx)(u.A,{className:"h-8 w-8 text-blue-600 mr-4"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900",children:"Search Candidates"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Find candidates to enter results"})]})]}),(0,r.jsxs)(i(),{href:"/dashboard/results",className:"flex items-center p-4 bg-white rounded-lg shadow hover:shadow-md transition-shadow",children:[(0,r.jsx)(o.A,{className:"h-8 w-8 text-green-600 mr-4"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900",children:"Enter New Results"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Add test scores for a candidate"})]})]}),(0,r.jsxs)(i(),{href:"/dashboard/feedback",className:"flex items-center p-4 bg-white rounded-lg shadow hover:shadow-md transition-shadow",children:[(0,r.jsx)(x.A,{className:"h-8 w-8 text-purple-600 mr-4"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900",children:"Generate Feedback"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Create AI-powered feedback"})]})]})]})]})]})}},33405:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\dashboard\\layout.tsx","default")},33502:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\list\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\dashboard\\results\\list\\page.tsx","default")},71354:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n,metadata:()=>d});var r=s(62740),a=s(85041),l=s.n(a),i=s(70452);s(61135);let d={title:"IELTS Certification System",description:"Professional IELTS test result management and certification system"};function n({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsx)("body",{className:l().className,children:(0,r.jsx)(i.SessionProvider,{children:e})})})}},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(88077);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},61135:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[638,8338,2367],()=>s(93788));module.exports=r})();