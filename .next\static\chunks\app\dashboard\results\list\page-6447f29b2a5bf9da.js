(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5429],{6515:(e,t,s)=>{Promise.resolve().then(s.bind(s,7036))},7401:(e,t,s)=>{"use strict";s.d(t,{A:()=>h});var a=s(2115);let r=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,s)=>s?s.toUpperCase():t.toLowerCase()),d=e=>{let t=l(e);return t.charAt(0).toUpperCase()+t.slice(1)},c=function(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return t.filter((e,t,s)=>!!e&&""!==e.trim()&&s.indexOf(e)===t).join(" ").trim()},i=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var n={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let x=(0,a.forwardRef)((e,t)=>{let{color:s="currentColor",size:r=24,strokeWidth:l=2,absoluteStrokeWidth:d,className:x="",children:h,iconNode:m,...o}=e;return(0,a.createElement)("svg",{ref:t,...n,width:r,height:r,stroke:s,strokeWidth:d?24*Number(l)/Number(r):l,className:c("lucide",x),...!h&&!i(o)&&{"aria-hidden":"true"},...o},[...m.map(e=>{let[t,s]=e;return(0,a.createElement)(t,s)}),...Array.isArray(h)?h:[h]])}),h=(e,t)=>{let s=(0,a.forwardRef)((s,l)=>{let{className:i,...n}=s;return(0,a.createElement)(x,{ref:l,iconNode:t,className:c("lucide-".concat(r(d(e))),"lucide-".concat(e),i),...n})});return s.displayName=d(e),s}},9136:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(7401).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},1594:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(7401).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},3239:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(7401).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},6889:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(7401).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},2598:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(7401).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},3473:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(7401).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},853:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(7401).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},6764:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(7401).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},7036:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>p});var a=s(5155),r=s(2115),l=s(8173),d=s.n(l),c=s(3239),i=s(6889),n=s(1594),x=s(3473),h=s(2598),m=s(6764),o=s(9136),u=s(853);function p(){let[e,t]=(0,r.useState)([]),[s,l]=(0,r.useState)(!0),[p]=(0,r.useState)(1),[g,y]=(0,r.useState)("all"),j=(0,r.useCallback)(async()=>{l(!0);try{let e=new URLSearchParams({page:p.toString(),limit:"20"});"all"!==g&&e.append("status",g);let s=await fetch("/api/checker/results?".concat(e));if(s.ok){let e=await s.json();t(e.results)}}catch(e){console.error("Error fetching results:",e)}finally{l(!1)}},[p,g,20]);(0,r.useEffect)(()=>{j()},[j]);let f=e=>{switch(e){case"completed":return(0,a.jsx)(c.A,{className:"h-5 w-5 text-green-500"});case"pending":return(0,a.jsx)(i.A,{className:"h-5 w-5 text-yellow-500"});default:return(0,a.jsx)(n.A,{className:"h-5 w-5 text-red-500"})}},N=e=>{let t="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium";switch(e){case"completed":return"".concat(t," bg-green-100 text-green-800");case"pending":return"".concat(t," bg-yellow-100 text-yellow-800");default:return"".concat(t," bg-red-100 text-red-800")}};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Test Results"}),(0,a.jsx)("p",{className:"text-gray-600",children:"View and manage entered test results"})]}),(0,a.jsxs)(d(),{href:"/dashboard/results",className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700",children:[(0,a.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Enter New Results"]})]}),(0,a.jsx)("div",{className:"bg-white shadow rounded-lg p-6",children:(0,a.jsxs)("div",{className:"flex gap-4 items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"statusFilter",className:"block text-sm font-medium text-gray-700 mb-2",children:"Filter by Status"}),(0,a.jsxs)("select",{id:"statusFilter",value:g,onChange:e=>y(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,a.jsx)("option",{value:"all",children:"All Results"}),(0,a.jsx)("option",{value:"pending",children:"Pending"}),(0,a.jsx)("option",{value:"completed",children:"Completed"}),(0,a.jsx)("option",{value:"verified",children:"Verified"})]})]}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Quick Stats"}),(0,a.jsxs)("div",{className:"flex gap-4 text-sm",children:[(0,a.jsxs)("span",{className:"flex items-center",children:[(0,a.jsx)(i.A,{className:"h-4 w-4 text-yellow-500 mr-1"}),"Pending: ",e.filter(e=>"pending"===e.status).length]}),(0,a.jsxs)("span",{className:"flex items-center",children:[(0,a.jsx)(c.A,{className:"h-4 w-4 text-green-500 mr-1"}),"Completed: ",e.filter(e=>"completed"===e.status).length]})]})]})]})}),(0,a.jsx)("div",{className:"bg-white shadow rounded-lg overflow-hidden",children:s?(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Candidate"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Test Date"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Scores"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Overall"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Entered"}),(0,a.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:e.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.candidate.fullName}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.candidate.passportNumber})]})}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-900",children:new Date(e.candidate.testDate).toLocaleDateString()}),(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsxs)("div",{className:"flex space-x-2 text-xs",children:[(0,a.jsxs)("span",{className:"bg-blue-100 text-blue-800 px-2 py-1 rounded",children:["L: ",e.listeningBandScore||"N/A"]}),(0,a.jsxs)("span",{className:"bg-green-100 text-green-800 px-2 py-1 rounded",children:["R: ",e.readingBandScore||"N/A"]}),(0,a.jsxs)("span",{className:"bg-purple-100 text-purple-800 px-2 py-1 rounded",children:["W: ",e.writingBandScore||"N/A"]}),(0,a.jsxs)("span",{className:"bg-red-100 text-red-800 px-2 py-1 rounded",children:["S: ",e.speakingBandScore||"N/A"]})]})}),(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsx)("div",{className:"text-lg font-semibold text-gray-900",children:e.overallBandScore||"N/A"})}),(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[f(e.status),(0,a.jsx)("span",{className:"ml-2 ".concat(N(e.status)),children:e.status})]})}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-500",children:new Date(e.createdAt).toLocaleDateString()}),(0,a.jsx)("td",{className:"px-6 py-4 text-right text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex justify-end gap-2",children:[(0,a.jsx)(d(),{href:"/dashboard/results/".concat(e.id),className:"text-blue-600 hover:text-blue-900",title:"View Details",children:(0,a.jsx)(h.A,{className:"h-4 w-4"})}),(0,a.jsx)(d(),{href:"/dashboard/results/".concat(e.id,"/edit"),className:"text-green-600 hover:text-green-900",title:"Edit Results",children:(0,a.jsx)(m.A,{className:"h-4 w-4"})})]})})]},e.id))})]})}),0===e.length&&!s&&(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(o.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No results found"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"all"===g?"You haven't entered any test results yet.":'No results with status "'.concat(g,'" found.')}),(0,a.jsxs)(d(),{href:"/dashboard/results",className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700",children:[(0,a.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Enter First Result"]})]})]})}),(0,a.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-blue-900 mb-4",children:"Quick Actions"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)(d(),{href:"/dashboard/search",className:"flex items-center p-4 bg-white rounded-lg shadow hover:shadow-md transition-shadow",children:[(0,a.jsx)(u.A,{className:"h-8 w-8 text-blue-600 mr-4"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900",children:"Search Candidates"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Find candidates to enter results"})]})]}),(0,a.jsxs)(d(),{href:"/dashboard/results",className:"flex items-center p-4 bg-white rounded-lg shadow hover:shadow-md transition-shadow",children:[(0,a.jsx)(x.A,{className:"h-8 w-8 text-green-600 mr-4"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900",children:"Enter New Results"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Add test scores for a candidate"})]})]}),(0,a.jsxs)(d(),{href:"/dashboard/feedback",className:"flex items-center p-4 bg-white rounded-lg shadow hover:shadow-md transition-shadow",children:[(0,a.jsx)(o.A,{className:"h-8 w-8 text-purple-600 mr-4"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900",children:"Generate Feedback"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Create AI-powered feedback"})]})]})]})]})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[8173,8441,1517,7358],()=>t(6515)),_N_E=e.O()}]);