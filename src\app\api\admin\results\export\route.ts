import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { db } from '@/lib/db';
import { testResults, candidates, users } from '@/lib/db/schema';
import { eq, desc, like, or, and } from 'drizzle-orm';

export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    if (session.user?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Forbidden - Admin access required' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const search = searchParams.get('search');

    // Build where conditions
    let whereConditions: any[] = [];

    if (status && status !== 'all') {
      whereConditions.push(eq(testResults.status, status as any));
    }

    if (search) {
      whereConditions.push(
        or(
          like(candidates.fullName, `%${search}%`),
          like(candidates.email, `%${search}%`),
          like(candidates.passportNumber, `%${search}%`)
        )
      );
    }

    const whereClause = whereConditions.length > 0 ? and(...whereConditions) : undefined;

    // Get all results matching the criteria
    const results = await db
      .select({
        id: testResults.id,
        candidateId: testResults.candidateId,
        listeningScore: testResults.listeningScore,
        listeningBandScore: testResults.listeningBandScore,
        readingScore: testResults.readingScore,
        readingBandScore: testResults.readingBandScore,
        writingTask1Score: testResults.writingTask1Score,
        writingTask2Score: testResults.writingTask2Score,
        writingBandScore: testResults.writingBandScore,
        speakingFluencyScore: testResults.speakingFluencyScore,
        speakingLexicalScore: testResults.speakingLexicalScore,
        speakingGrammarScore: testResults.speakingGrammarScore,
        speakingPronunciationScore: testResults.speakingPronunciationScore,
        speakingBandScore: testResults.speakingBandScore,
        overallBandScore: testResults.overallBandScore,
        status: testResults.status,
        certificateGenerated: testResults.certificateGenerated,
        certificateSerial: testResults.certificateSerial,
        createdAt: testResults.createdAt,
        updatedAt: testResults.updatedAt,
        candidate: {
          fullName: candidates.fullName,
          email: candidates.email,
          phoneNumber: candidates.phoneNumber,
          passportNumber: candidates.passportNumber,
          nationality: candidates.nationality,
          dateOfBirth: candidates.dateOfBirth,
          testDate: candidates.testDate,
          testCenter: candidates.testCenter,
        },
        checker: {
          name: users.name,
          email: users.email,
        }
      })
      .from(testResults)
      .leftJoin(candidates, eq(testResults.candidateId, candidates.id))
      .leftJoin(users, eq(testResults.enteredBy, users.id))
      .where(whereClause)
      .orderBy(desc(testResults.createdAt));

    // Generate CSV content
    const csvHeaders = [
      'Result ID',
      'Candidate Name',
      'Email',
      'Phone',
      'Passport Number',
      'Nationality',
      'Date of Birth',
      'Test Date',
      'Test Center',
      'Listening Raw Score',
      'Listening Band Score',
      'Reading Raw Score',
      'Reading Band Score',
      'Writing Task 1 Score',
      'Writing Task 2 Score',
      'Writing Band Score',
      'Speaking Fluency Score',
      'Speaking Lexical Score',
      'Speaking Grammar Score',
      'Speaking Pronunciation Score',
      'Speaking Band Score',
      'Overall Band Score',
      'Status',
      'Certificate Generated',
      'Certificate Serial',
      'Entered By',
      'Checker Email',
      'Created Date',
      'Updated Date'
    ];

    const csvRows = results.map(result => [
      result.id,
      result.candidate.fullName || '',
      result.candidate.email || '',
      result.candidate.phoneNumber || '',
      result.candidate.passportNumber || '',
      result.candidate.nationality || '',
      result.candidate.dateOfBirth || '',
      result.candidate.testDate || '',
      result.candidate.testCenter || '',
      result.listeningScore || '',
      result.listeningBandScore || '',
      result.readingScore || '',
      result.readingBandScore || '',
      result.writingTask1Score || '',
      result.writingTask2Score || '',
      result.writingBandScore || '',
      result.speakingFluencyScore || '',
      result.speakingLexicalScore || '',
      result.speakingGrammarScore || '',
      result.speakingPronunciationScore || '',
      result.speakingBandScore || '',
      result.overallBandScore || '',
      result.status,
      result.certificateGenerated ? 'Yes' : 'No',
      result.certificateSerial || '',
      result.checker.name || '',
      result.checker.email || '',
      new Date(result.createdAt).toISOString(),
      new Date(result.updatedAt).toISOString()
    ]);

    // Escape CSV values and handle commas/quotes
    const escapeCsvValue = (value: any): string => {
      const str = String(value || '');
      if (str.includes(',') || str.includes('"') || str.includes('\n')) {
        return `"${str.replace(/"/g, '""')}"`;
      }
      return str;
    };

    const csvContent = [
      csvHeaders.map(escapeCsvValue).join(','),
      ...csvRows.map(row => row.map(escapeCsvValue).join(','))
    ].join('\n');

    // Return CSV file
    return new NextResponse(csvContent, {
      status: 200,
      headers: {
        'Content-Type': 'text/csv',
        'Content-Disposition': `attachment; filename="ielts_results_${new Date().toISOString().split('T')[0]}.csv"`,
      },
    });

  } catch (error) {
    console.error('Error exporting results:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
