(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7618],{2209:(e,t,s)=>{Promise.resolve().then(s.bind(s,9409))},7401:(e,t,s)=>{"use strict";s.d(t,{A:()=>m});var a=s(2115);let r=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,s)=>s?s.toUpperCase():t.toLowerCase()),d=e=>{let t=l(e);return t.charAt(0).toUpperCase()+t.slice(1)},i=function(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return t.filter((e,t,s)=>!!e&&""!==e.trim()&&s.indexOf(e)===t).join(" ").trim()},c=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var n={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let x=(0,a.forwardRef)((e,t)=>{let{color:s="currentColor",size:r=24,strokeWidth:l=2,absoluteStrokeWidth:d,className:x="",children:m,iconNode:o,...h}=e;return(0,a.createElement)("svg",{ref:t,...n,width:r,height:r,stroke:s,strokeWidth:d?24*Number(l)/Number(r):l,className:i("lucide",x),...!m&&!c(h)&&{"aria-hidden":"true"},...h},[...o.map(e=>{let[t,s]=e;return(0,a.createElement)(t,s)}),...Array.isArray(m)?m:[m]])}),m=(e,t)=>{let s=(0,a.forwardRef)((s,l)=>{let{className:c,...n}=s;return(0,a.createElement)(x,{ref:l,iconNode:t,className:i("lucide-".concat(r(d(e))),"lucide-".concat(e),c),...n})});return s.displayName=d(e),s}},7508:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(7401).A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},2423:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(7401).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},9136:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(7401).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},3239:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(7401).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},6889:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(7401).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},4857:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(7401).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},4081:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(7401).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},1773:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(7401).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},6878:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(7401).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},2823:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(7401).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},9409:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>u});var a=s(5155),r=s(2115),l=s(1773),d=s(4857),i=s(2823),c=s(4081),n=s(6878),x=s(7508),m=s(9136),o=s(2423),h=s(6889),p=s(3239);function u(){let[e,t]=(0,r.useState)(null),[s,u]=(0,r.useState)(!0),[y,g]=(0,r.useState)({from:new Date(Date.now()-2592e6).toISOString().split("T")[0],to:new Date().toISOString().split("T")[0]}),f=(0,r.useCallback)(async()=>{u(!0);try{let e=await fetch("/api/admin/reports",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(y)});if(e.ok){let s=await e.json();t(s)}}catch(e){console.error("Error fetching report data:",e)}finally{u(!1)}},[y]);(0,r.useEffect)(()=>{f()},[f]);let j=async()=>{try{let e=await fetch("/api/admin/reports/export",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({...y,format:"pdf"})});if(e.ok){let t=await e.blob(),s=window.URL.createObjectURL(t),a=document.createElement("a");a.href=s,a.download="ielts_report_".concat(y.from,"_to_").concat(y.to,".pdf"),document.body.appendChild(a),a.click(),window.URL.revokeObjectURL(s),document.body.removeChild(a)}}catch(e){console.error("Export error:",e)}};return s?(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Reports & Analytics"}),(0,a.jsx)("p",{className:"text-gray-600",children:"System performance and statistics"})]}),(0,a.jsxs)("div",{className:"flex gap-3",children:[(0,a.jsxs)("button",{onClick:f,className:"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50",children:[(0,a.jsx)(l.A,{className:"h-4 w-4 mr-2"}),"Refresh"]}),(0,a.jsxs)("button",{onClick:j,className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700",children:[(0,a.jsx)(d.A,{className:"h-4 w-4 mr-2"}),"Export Report"]})]})]}),(0,a.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Report Period"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"dateFrom",className:"block text-sm font-medium text-gray-700 mb-2",children:"From Date"}),(0,a.jsx)("input",{type:"date",id:"dateFrom",value:y.from,onChange:e=>g(t=>({...t,from:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"dateTo",className:"block text-sm font-medium text-gray-700 mb-2",children:"To Date"}),(0,a.jsx)("input",{type:"date",id:"dateTo",value:y.to,onChange:e=>g(t=>({...t,to:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]})]})]}),e&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,a.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,a.jsx)("div",{className:"p-5",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)(i.A,{className:"h-8 w-8 text-blue-600"})}),(0,a.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,a.jsxs)("dl",{children:[(0,a.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Total Candidates"}),(0,a.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:e.overview.totalCandidates.toLocaleString()})]})})]})})}),(0,a.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,a.jsx)("div",{className:"p-5",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)(c.A,{className:"h-8 w-8 text-green-600"})}),(0,a.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,a.jsxs)("dl",{children:[(0,a.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Test Results"}),(0,a.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:e.overview.totalResults.toLocaleString()})]})})]})})}),(0,a.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,a.jsx)("div",{className:"p-5",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)(n.A,{className:"h-8 w-8 text-purple-600"})}),(0,a.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,a.jsxs)("dl",{children:[(0,a.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Average Band Score"}),(0,a.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:e.overview.averageBandScore.toFixed(1)})]})})]})})}),(0,a.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,a.jsx)("div",{className:"p-5",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)(x.A,{className:"h-8 w-8 text-yellow-600"})}),(0,a.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,a.jsxs)("dl",{children:[(0,a.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Certificates Generated"}),(0,a.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:e.overview.certificatesGenerated.toLocaleString()})]})})]})})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,a.jsxs)("h3",{className:"text-lg font-medium text-gray-900 mb-4 flex items-center",children:[(0,a.jsx)(m.A,{className:"h-5 w-5 mr-2"}),"Band Score Distribution"]}),(0,a.jsx)("div",{className:"space-y-3",children:e.bandScoreDistribution.map(e=>(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsxs)("div",{className:"w-16 text-sm font-medium text-gray-900",children:["Band ",e.score]}),(0,a.jsx)("div",{className:"flex-1 mx-4",children:(0,a.jsx)("div",{className:"bg-gray-200 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-blue-600 h-2 rounded-full",style:{width:"".concat(e.percentage,"%")}})})}),(0,a.jsxs)("div",{className:"w-16 text-sm text-gray-600 text-right",children:[e.count," (",e.percentage.toFixed(1),"%)"]})]},e.score))})]}),(0,a.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Test Center Performance"}),(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase",children:"Center"}),(0,a.jsx)("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase",children:"Candidates"}),(0,a.jsx)("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase",children:"Avg Score"})]})}),(0,a.jsx)("tbody",{className:"divide-y divide-gray-200",children:e.testCenterStats.map((e,t)=>(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-4 py-2 text-sm text-gray-900",children:e.center.replace("IELTS Test Center - ","")}),(0,a.jsx)("td",{className:"px-4 py-2 text-sm text-gray-600",children:e.candidates}),(0,a.jsx)("td",{className:"px-4 py-2 text-sm text-gray-600",children:e.avgScore.toFixed(1)})]},t))})]})})]})]}),(0,a.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,a.jsxs)("h3",{className:"text-lg font-medium text-gray-900 mb-4 flex items-center",children:[(0,a.jsx)(o.A,{className:"h-5 w-5 mr-2"}),"Monthly Trends"]}),(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase",children:"Month"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase",children:"New Candidates"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase",children:"Test Results"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase",children:"Average Score"})]})}),(0,a.jsx)("tbody",{className:"divide-y divide-gray-200",children:e.monthlyStats.map((e,t)=>(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 text-sm font-medium text-gray-900",children:e.month}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:e.candidates}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:e.results}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-600",children:e.avgScore.toFixed(1)})]},t))})]})})]}),(0,a.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,a.jsxs)("h3",{className:"text-lg font-medium text-gray-900 mb-4 flex items-center",children:[(0,a.jsx)(h.A,{className:"h-5 w-5 mr-2"}),"Recent Activity (Last 7 Days)"]}),(0,a.jsx)("div",{className:"space-y-3",children:e.recentActivity.map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(o.A,{className:"h-4 w-4 text-gray-400 mr-3"}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-900",children:new Date(e.date).toLocaleDateString()})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-600",children:[(0,a.jsxs)("span",{className:"flex items-center",children:[(0,a.jsx)(i.A,{className:"h-4 w-4 mr-1"}),e.candidates," candidates"]}),(0,a.jsxs)("span",{className:"flex items-center",children:[(0,a.jsx)(p.A,{className:"h-4 w-4 mr-1"}),e.results," results"]})]})]},t))})]})]})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[8441,1517,7358],()=>t(2209)),_N_E=e.O()}]);