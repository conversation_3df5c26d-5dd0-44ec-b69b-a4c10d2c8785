(()=>{var e={};e.id=8675,e.ids=[8675],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},41814:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>m,pages:()=>o,routeModule:()=>x,tree:()=>d});var r=s(70260),i=s(28203),a=s(25155),l=s.n(a),n=s(67292),c={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);s.d(t,c);let d=["",{children:["verify",{children:["[serial]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,34312)),"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\verify\\[serial]\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,71354)),"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],o=["C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\verify\\[serial]\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/verify/[serial]/page",pathname:"/verify/[serial]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},97032:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,13219,23)),Promise.resolve().then(s.t.bind(s,34863,23)),Promise.resolve().then(s.t.bind(s,25155,23)),Promise.resolve().then(s.t.bind(s,40802,23)),Promise.resolve().then(s.t.bind(s,9350,23)),Promise.resolve().then(s.t.bind(s,48530,23)),Promise.resolve().then(s.t.bind(s,88921,23))},60584:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,66959,23)),Promise.resolve().then(s.t.bind(s,33875,23)),Promise.resolve().then(s.t.bind(s,88903,23)),Promise.resolve().then(s.t.bind(s,57174,23)),Promise.resolve().then(s.t.bind(s,84178,23)),Promise.resolve().then(s.t.bind(s,87190,23)),Promise.resolve().then(s.t.bind(s,61365,23))},71315:(e,t,s)=>{Promise.resolve().then(s.bind(s,70452))},8267:(e,t,s)=>{Promise.resolve().then(s.bind(s,98805))},70024:(e,t,s)=>{Promise.resolve().then(s.bind(s,34312))},56872:(e,t,s)=>{Promise.resolve().then(s.bind(s,28554))},41680:(e,t,s)=>{"use strict";s.d(t,{A:()=>m});var r=s(58009);let i=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),a=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,s)=>s?s.toUpperCase():t.toLowerCase()),l=e=>{let t=a(e);return t.charAt(0).toUpperCase()+t.slice(1)},n=(...e)=>e.filter((e,t,s)=>!!e&&""!==e.trim()&&s.indexOf(e)===t).join(" ").trim(),c=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=(0,r.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:s=2,absoluteStrokeWidth:i,className:a="",children:l,iconNode:o,...m},x)=>(0,r.createElement)("svg",{ref:x,...d,width:t,height:t,stroke:e,strokeWidth:i?24*Number(s)/Number(t):s,className:n("lucide",a),...!l&&!c(m)&&{"aria-hidden":"true"},...m},[...o.map(([e,t])=>(0,r.createElement)(e,t)),...Array.isArray(l)?l:[l]])),m=(e,t)=>{let s=(0,r.forwardRef)(({className:s,...a},c)=>(0,r.createElement)(o,{ref:c,iconNode:t,className:n(`lucide-${i(l(e))}`,`lucide-${e}`,s),...a}));return s.displayName=l(e),s}},35668:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(41680).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},43464:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(41680).A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},45723:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(41680).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},45037:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(41680).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},46583:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(41680).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},61075:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(41680).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},48857:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(41680).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},70384:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(41680).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},87798:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(41680).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},79334:(e,t,s)=>{"use strict";var r=s(58686);s.o(r,"useParams")&&s.d(t,{useParams:function(){return r.useParams}}),s.o(r,"useRouter")&&s.d(t,{useRouter:function(){return r.useRouter}}),s.o(r,"useSearchParams")&&s.d(t,{useSearchParams:function(){return r.useSearchParams}})},28554:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>y});var r=s(45512),i=s(58009),a=s(79334),l=s(28531),n=s.n(l),c=s(35668),d=s(70384),o=s(46583);let m=(0,s(41680).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]);var x=s(43464),h=s(87798),p=s(48857),u=s(45723),f=s(61075),v=s(45037);function y(){let e=(0,a.useParams)().serial,[t,s]=(0,i.useState)(null),[l,y]=(0,i.useState)(!0);return((0,i.useCallback)(async()=>{try{let t=await fetch(`/api/certificate/verify/${e}`),r=await t.json();t.ok,s(r)}catch(e){console.error("Error verifying certificate:",e),s({valid:!1,error:"Network error",message:"Unable to connect to verification service"})}finally{y(!1)}},[e]),l)?(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Verifying certificate..."})]})}):(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100",children:[(0,r.jsx)("header",{className:"bg-white shadow-sm",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsx)("div",{className:"flex justify-between items-center py-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsxs)(n(),{href:"/search",className:"flex items-center text-blue-600 hover:text-blue-700 mr-4",children:[(0,r.jsx)(c.A,{className:"h-5 w-5 mr-1"}),"Back to Search"]}),(0,r.jsx)(d.A,{className:"h-8 w-8 text-blue-600 mr-3"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Certificate Verification"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Verify IELTS Certificate Authenticity"})]})]})})})}),(0,r.jsxs)("main",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-lg p-8 mb-8",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"mb-6",children:t?.valid?(0,r.jsx)(o.A,{className:"h-16 w-16 text-green-500 mx-auto"}):(0,r.jsx)(m,{className:"h-16 w-16 text-red-500 mx-auto"})}),(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:t?.valid?"Certificate Verified":"Verification Failed"}),(0,r.jsx)("div",{className:`inline-flex items-center px-4 py-2 rounded-full text-sm font-medium ${t?.valid?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:t?.valid?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"Valid Certificate"]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(m,{className:"h-4 w-4 mr-2"}),"Invalid Certificate"]})}),(0,r.jsxs)("p",{className:"mt-4 text-gray-600",children:["Serial Number: ",(0,r.jsx)("span",{className:"font-mono font-semibold",children:e})]}),(0,r.jsx)("p",{className:"mt-2 text-sm text-gray-500",children:t?.verification?.message||t?.message})]})}),t?.valid&&t.certificate&&(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-8",children:[(0,r.jsxs)("div",{className:"flex items-center mb-6",children:[(0,r.jsx)(x.A,{className:"h-6 w-6 text-blue-600 mr-2"}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900",children:"Certificate Details"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("h4",{className:"text-lg font-medium text-gray-900 border-b pb-2",children:"Candidate Information"}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(h.A,{className:"h-5 w-5 text-gray-400 mr-3"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Full Name"}),(0,r.jsx)("p",{className:"font-semibold",children:t.certificate.candidateName})]})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(p.A,{className:"h-5 w-5 text-gray-400 mr-3"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Nationality"}),(0,r.jsx)("p",{className:"font-semibold",children:t.certificate.nationality})]})]})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("h4",{className:"text-lg font-medium text-gray-900 border-b pb-2",children:"Test Information"}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(u.A,{className:"h-5 w-5 text-gray-400 mr-3"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Test Date"}),(0,r.jsx)("p",{className:"font-semibold",children:new Date(t.certificate.testDate).toLocaleDateString()})]})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(p.A,{className:"h-5 w-5 text-gray-400 mr-3"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Test Center"}),(0,r.jsx)("p",{className:"font-semibold",children:t.certificate.testCenter})]})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(f.A,{className:"h-5 w-5 text-gray-400 mr-3"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Overall Band Score"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-indigo-600",children:t.certificate.overallBandScore||"N/A"})]})]})]})]}),(0,r.jsxs)("div",{className:"mt-8 pt-6 border-t border-gray-200",children:[(0,r.jsx)("h4",{className:"text-lg font-medium text-gray-900 mb-4",children:"Certificate Metadata"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-gray-600",children:"Certificate Serial"}),(0,r.jsx)("p",{className:"font-mono font-semibold",children:t.certificate.serial})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-gray-600",children:"Result ID"}),(0,r.jsx)("p",{className:"font-mono font-semibold",children:t.certificate.resultId})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-gray-600",children:"Issue Date"}),(0,r.jsx)("p",{className:"font-semibold",children:new Date(t.certificate.issueDate).toLocaleDateString()})]})]})]}),(0,r.jsx)("div",{className:"mt-8 flex justify-center space-x-4",children:(0,r.jsxs)(n(),{href:`/results/${t.certificate.resultId}`,className:"inline-flex items-center px-4 py-2 border border-blue-300 text-sm font-medium rounded-md text-blue-700 bg-blue-50 hover:bg-blue-100",children:[(0,r.jsx)(f.A,{className:"h-4 w-4 mr-2"}),"View Full Results"]})})]}),!t?.valid&&(0,r.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-6",children:[(0,r.jsxs)("div",{className:"flex items-center mb-4",children:[(0,r.jsx)(v.A,{className:"h-6 w-6 text-red-600 mr-2"}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-red-800",children:"Verification Failed"})]}),(0,r.jsx)("p",{className:"text-red-700 mb-4",children:t?.message||"The certificate could not be verified."}),(0,r.jsxs)("div",{className:"text-sm text-red-600",children:[(0,r.jsx)("p",{children:"Possible reasons:"}),(0,r.jsxs)("ul",{className:"list-disc list-inside mt-2 space-y-1",children:[(0,r.jsx)("li",{children:"Invalid or incorrect serial number"}),(0,r.jsx)("li",{children:"Certificate has not been generated"}),(0,r.jsx)("li",{children:"Test result is still pending"}),(0,r.jsx)("li",{children:"Certificate has been revoked"})]})]})]}),(0,r.jsxs)("div",{className:"mt-12 text-center",children:[(0,r.jsx)("p",{className:"text-gray-600 text-sm",children:"For additional verification or support, please contact the IELTS administration office."}),(0,r.jsx)("div",{className:"mt-4",children:(0,r.jsx)(n(),{href:"/search",className:"text-blue-600 hover:text-blue-700 text-sm font-medium",children:"Search for Results"})})]})]})]})}},71354:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c,metadata:()=>n});var r=s(62740),i=s(85041),a=s.n(i),l=s(70452);s(61135);let n={title:"IELTS Certification System",description:"Professional IELTS test result management and certification system"};function c({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsx)("body",{className:a().className,children:(0,r.jsx)(l.SessionProvider,{children:e})})})}},34312:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\verify\\\\[serial]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\verify\\[serial]\\page.tsx","default")},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});var r=s(88077);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},61135:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[638,8338,2367],()=>s(41814));module.exports=r})();