(()=>{var e={};e.id=5614,e.ids=[5614],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},2113:e=>{"use strict";e.exports=import("postgres")},77598:e=>{"use strict";e.exports=require("node:crypto")},36624:(e,t,a)=>{"use strict";a.a(e,async(e,r)=>{try{a.r(t),a.d(t,{patchFetch:()=>c,routeModule:()=>l,serverHooks:()=>k,workAsyncStorage:()=>u,workUnitAsyncStorage:()=>p});var s=a(42706),n=a(28203),i=a(45994),d=a(78695),o=e([d]);d=(o.then?(await o)():o)[0];let l=new s.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/feedback/[resultId]/route",pathname:"/api/feedback/[resultId]",filename:"route",bundlePath:"app/api/feedback/[resultId]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\api\\feedback\\[resultId]\\route.ts",nextConfigOutput:"",userland:d}),{workAsyncStorage:u,workUnitAsyncStorage:p,serverHooks:k}=l;function c(){return(0,i.patchFetch)({workAsyncStorage:u,workUnitAsyncStorage:p})}r()}catch(e){r(e)}})},96487:()=>{},78335:()=>{},78695:(e,t,a)=>{"use strict";a.a(e,async(e,r)=>{try{a.r(t),a.d(t,{GET:()=>c});var s=a(39187),n=a(62693),i=a(48590),d=a(47579),o=e([n]);async function c(e,{params:t}){try{let e=t.resultId;if(!e||isNaN(Number(e)))return s.NextResponse.json({error:"Invalid result ID format"},{status:400});let a=await n.db.select({id:i.testResults.id,status:i.testResults.status,aiFeedbackGenerated:i.testResults.aiFeedbackGenerated}).from(i.testResults).where((0,d.eq)(i.testResults.id,e)).limit(1);if(!a.length)return s.NextResponse.json({error:"Test result not found"},{status:404});if("pending"===a[0].status)return s.NextResponse.json({error:"Test result is not yet available"},{status:403});if(!a[0].aiFeedbackGenerated)return s.NextResponse.json({error:"AI feedback has not been generated for this result"},{status:404});let r=await n.db.select({id:i.aiFeedback.id,testResultId:i.aiFeedback.testResultId,listeningFeedback:i.aiFeedback.listeningFeedback,readingFeedback:i.aiFeedback.readingFeedback,writingFeedback:i.aiFeedback.writingFeedback,speakingFeedback:i.aiFeedback.speakingFeedback,overallFeedback:i.aiFeedback.overallFeedback,studyRecommendations:i.aiFeedback.studyRecommendations,generatedAt:i.aiFeedback.generatedAt}).from(i.aiFeedback).where((0,d.eq)(i.aiFeedback.testResultId,e)).limit(1);if(!r.length)return s.NextResponse.json({error:"AI feedback not found"},{status:404});return s.NextResponse.json(r[0])}catch(e){return console.error("Error fetching public AI feedback:",e),s.NextResponse.json({error:"Internal server error"},{status:500})}}n=(o.then?(await o)():o)[0],r()}catch(e){r(e)}})},62693:(e,t,a)=>{"use strict";a.a(e,async(e,r)=>{try{a.d(t,{db:()=>l});var s=a(10072),n=a(2113),i=a(48590),d=e([n,s]);[n,s]=d.then?(await d)():d;let o=process.env.DATABASE_URL,c=(0,n.default)(o,{prepare:!1}),l=(0,s.f)(c,{schema:i});r()}catch(e){r(e)}})},48590:(e,t,a)=>{"use strict";a.r(t),a.d(t,{accounts:()=>p,aiFeedback:()=>b,candidates:()=>f,sessions:()=>k,testResults:()=>g,users:()=>u,verificationTokens:()=>_});var r=a(87858),s=a(44799),n=a(32590),i=a(9848),d=a(70009),o=a(27390),c=a(32190),l=a(4502);let u=(0,r.cJ)("users",{id:(0,s.Qq)("id").primaryKey().$defaultFn(()=>(0,l.sX)()),name:(0,s.Qq)("name"),email:(0,s.Qq)("email").notNull().unique(),emailVerified:(0,n.vE)("emailVerified",{mode:"date"}),image:(0,s.Qq)("image"),password:(0,s.Qq)("password"),role:(0,s.Qq)("role",{enum:["admin","test_checker"]}).notNull().default("test_checker"),createdAt:(0,n.vE)("created_at").defaultNow().notNull(),updatedAt:(0,n.vE)("updated_at").defaultNow().notNull()}),p=(0,r.cJ)("accounts",{userId:(0,s.Qq)("userId").notNull().references(()=>u.id,{onDelete:"cascade"}),type:(0,s.Qq)("type").notNull(),provider:(0,s.Qq)("provider").notNull(),providerAccountId:(0,s.Qq)("providerAccountId").notNull(),refresh_token:(0,s.Qq)("refresh_token"),access_token:(0,s.Qq)("access_token"),expires_at:(0,i.nd)("expires_at"),token_type:(0,s.Qq)("token_type"),scope:(0,s.Qq)("scope"),id_token:(0,s.Qq)("id_token"),session_state:(0,s.Qq)("session_state")}),k=(0,r.cJ)("sessions",{sessionToken:(0,s.Qq)("sessionToken").primaryKey(),userId:(0,s.Qq)("userId").notNull().references(()=>u.id,{onDelete:"cascade"}),expires:(0,n.vE)("expires",{mode:"date"}).notNull()}),_=(0,r.cJ)("verificationTokens",{identifier:(0,s.Qq)("identifier").notNull(),token:(0,s.Qq)("token").notNull(),expires:(0,n.vE)("expires",{mode:"date"}).notNull()}),f=(0,r.cJ)("candidates",{id:(0,s.Qq)("id").primaryKey().$defaultFn(()=>(0,l.sX)()),fullName:(0,s.Qq)("full_name").notNull(),email:(0,s.Qq)("email").notNull().unique(),phoneNumber:(0,s.Qq)("phone_number").notNull(),dateOfBirth:(0,n.vE)("date_of_birth",{mode:"date"}).notNull(),nationality:(0,s.Qq)("nationality").notNull(),passportNumber:(0,s.Qq)("passport_number").notNull().unique(),testDate:(0,n.vE)("test_date",{mode:"date"}).notNull(),testCenter:(0,s.Qq)("test_center").notNull(),photoUrl:(0,s.Qq)("photo_url"),createdAt:(0,n.vE)("created_at").defaultNow().notNull(),updatedAt:(0,n.vE)("updated_at").defaultNow().notNull()}),g=(0,r.cJ)("test_results",{id:(0,s.Qq)("id").primaryKey().$defaultFn(()=>(0,l.sX)()),candidateId:(0,s.Qq)("candidate_id").notNull().references(()=>f.id,{onDelete:"cascade"}),listeningScore:(0,d._)("listening_score",{precision:3,scale:1}),listeningBandScore:(0,d._)("listening_band_score",{precision:2,scale:1}),readingScore:(0,d._)("reading_score",{precision:3,scale:1}),readingBandScore:(0,d._)("reading_band_score",{precision:2,scale:1}),writingTask1Score:(0,d._)("writing_task1_score",{precision:2,scale:1}),writingTask2Score:(0,d._)("writing_task2_score",{precision:2,scale:1}),writingBandScore:(0,d._)("writing_band_score",{precision:2,scale:1}),speakingFluencyScore:(0,d._)("speaking_fluency_score",{precision:2,scale:1}),speakingLexicalScore:(0,d._)("speaking_lexical_score",{precision:2,scale:1}),speakingGrammarScore:(0,d._)("speaking_grammar_score",{precision:2,scale:1}),speakingPronunciationScore:(0,d._)("speaking_pronunciation_score",{precision:2,scale:1}),speakingBandScore:(0,d._)("speaking_band_score",{precision:2,scale:1}),overallBandScore:(0,d._)("overall_band_score",{precision:2,scale:1}),status:(0,s.Qq)("status",{enum:["pending","completed","verified"]}).notNull().default("pending"),enteredBy:(0,s.Qq)("entered_by").references(()=>u.id),verifiedBy:(0,s.Qq)("verified_by").references(()=>u.id),certificateGenerated:(0,o.zM)("certificate_generated").default(!1),certificateSerial:(0,s.Qq)("certificate_serial").unique(),certificateUrl:(0,s.Qq)("certificate_url"),aiFeedbackGenerated:(0,o.zM)("ai_feedback_generated").default(!1),testDate:(0,n.vE)("test_date",{mode:"date"}),createdAt:(0,n.vE)("created_at").defaultNow().notNull(),updatedAt:(0,n.vE)("updated_at").defaultNow().notNull()}),b=(0,r.cJ)("ai_feedback",{id:(0,s.Qq)("id").primaryKey().$defaultFn(()=>(0,l.sX)()),testResultId:(0,s.Qq)("test_result_id").notNull().references(()=>g.id,{onDelete:"cascade"}),listeningFeedback:(0,s.Qq)("listening_feedback"),readingFeedback:(0,s.Qq)("reading_feedback"),writingFeedback:(0,s.Qq)("writing_feedback"),speakingFeedback:(0,s.Qq)("speaking_feedback"),overallFeedback:(0,s.Qq)("overall_feedback"),studyRecommendations:(0,s.Qq)("study_recommendations"),strengths:(0,c.Pq)("strengths").$type(),weaknesses:(0,c.Pq)("weaknesses").$type(),studyPlan:(0,c.Pq)("study_plan").$type(),generatedAt:(0,n.vE)("generated_at").defaultNow().notNull()})}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[638,5452,9757],()=>a(36624));module.exports=r})();