(()=>{var e={};e.id=2908,e.ids=[2908],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},55511:e=>{"use strict";e.exports=require("crypto")},2113:e=>{"use strict";e.exports=import("postgres")},77598:e=>{"use strict";e.exports=require("node:crypto")},1614:(e,t,a)=>{"use strict";a.a(e,async(e,s)=>{try{a.r(t),a.d(t,{patchFetch:()=>c,routeModule:()=>l,serverHooks:()=>m,workAsyncStorage:()=>u,workUnitAsyncStorage:()=>p});var r=a(42706),i=a(28203),n=a(45994),o=a(98893),d=e([o]);o=(d.then?(await d)():d)[0];let l=new r.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/ai/save-feedback/route",pathname:"/api/ai/save-feedback",filename:"route",bundlePath:"app/api/ai/save-feedback/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\api\\ai\\save-feedback\\route.ts",nextConfigOutput:"",userland:o}),{workAsyncStorage:u,workUnitAsyncStorage:p,serverHooks:m}=l;function c(){return(0,n.patchFetch)({workAsyncStorage:u,workUnitAsyncStorage:p})}s()}catch(e){s(e)}})},96487:()=>{},78335:()=>{},98893:(e,t,a)=>{"use strict";a.a(e,async(e,s)=>{try{a.r(t),a.d(t,{POST:()=>l});var r=a(39187),i=a(37702),n=a(62693),o=a(48590),d=a(47579),c=e([i,n]);async function l(e){try{if(!await (0,i.j2)())return r.NextResponse.json({error:"Unauthorized"},{status:401});let{resultId:t,feedback:a}=await e.json();if(!t||!a)return r.NextResponse.json({error:"Result ID and feedback are required"},{status:400});if((await n.db.select().from(o.aiFeedback).where((0,d.eq)(o.aiFeedback.testResultId,t)).limit(1)).length>0){let e=await n.db.update(o.aiFeedback).set({overallFeedback:a.overallFeedback||a.overallAssessment,listeningFeedback:a.listeningFeedback||a.specificRecommendations?.listening,readingFeedback:a.readingFeedback||a.specificRecommendations?.reading,writingFeedback:a.writingFeedback||a.specificRecommendations?.writing,speakingFeedback:a.speakingFeedback||a.specificRecommendations?.speaking,studyRecommendations:a.studyRecommendations,strengths:a.strengths,weaknesses:a.weaknesses||a.areasForImprovement,studyPlan:a.studyPlan}).where((0,d.eq)(o.aiFeedback.testResultId,t)).returning();return r.NextResponse.json(e[0])}{let e=await n.db.insert(o.aiFeedback).values({testResultId:t,overallFeedback:a.overallFeedback||a.overallAssessment,listeningFeedback:a.listeningFeedback||a.specificRecommendations?.listening,readingFeedback:a.readingFeedback||a.specificRecommendations?.reading,writingFeedback:a.writingFeedback||a.specificRecommendations?.writing,speakingFeedback:a.speakingFeedback||a.specificRecommendations?.speaking,studyRecommendations:a.studyRecommendations,strengths:a.strengths,weaknesses:a.weaknesses||a.areasForImprovement,studyPlan:a.studyPlan}).returning();return r.NextResponse.json(e[0],{status:201})}}catch(e){return console.error("Error saving AI feedback:",e),r.NextResponse.json({error:"Failed to save feedback"},{status:500})}}[i,n]=c.then?(await c)():c,s()}catch(e){s(e)}})},37702:(e,t,a)=>{"use strict";a.a(e,async(e,s)=>{try{a.d(t,{Y9:()=>u,j2:()=>p});var r=a(32221),i=a(31648),n=a(62693),o=a(48590),d=a(47579),c=a(34926),l=e([n]);n=(l.then?(await l)():l)[0];let{handlers:u,auth:p,signIn:m,signOut:k}=(0,r.Ay)({session:{strategy:"jwt"},providers:[(0,i.A)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;try{let t=await n.db.select().from(o.users).where((0,d.eq)(o.users.email,e.email)).limit(1);if(0===t.length)return null;let a=t[0];if(!a.password||!await c.Ay.compare(e.password,a.password))return null;return{id:a.id,email:a.email,name:a.name,role:a.role}}catch(e){return console.error("Authentication error:",e),null}}})],callbacks:{jwt:async({token:e,user:t})=>(t&&(e.id=t.id,e.role=t.role,e.email=t.email,e.name=t.name),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.id,e.user.role=t.role,e.user.email=t.email,e.user.name=t.name),e)},pages:{signIn:"/auth/signin"}});s()}catch(e){s(e)}})},62693:(e,t,a)=>{"use strict";a.a(e,async(e,s)=>{try{a.d(t,{db:()=>l});var r=a(10072),i=a(2113),n=a(48590),o=e([i,r]);[i,r]=o.then?(await o)():o;let d=process.env.DATABASE_URL,c=(0,i.default)(d,{prepare:!1}),l=(0,r.f)(c,{schema:n});s()}catch(e){s(e)}})},48590:(e,t,a)=>{"use strict";a.r(t),a.d(t,{accounts:()=>p,aiFeedback:()=>f,candidates:()=>g,sessions:()=>m,testResults:()=>_,users:()=>u,verificationTokens:()=>k});var s=a(87858),r=a(44799),i=a(32590),n=a(9848),o=a(70009),d=a(27390),c=a(32190),l=a(4502);let u=(0,s.cJ)("users",{id:(0,r.Qq)("id").primaryKey().$defaultFn(()=>(0,l.sX)()),name:(0,r.Qq)("name"),email:(0,r.Qq)("email").notNull().unique(),emailVerified:(0,i.vE)("emailVerified",{mode:"date"}),image:(0,r.Qq)("image"),password:(0,r.Qq)("password"),role:(0,r.Qq)("role",{enum:["admin","test_checker"]}).notNull().default("test_checker"),createdAt:(0,i.vE)("created_at").defaultNow().notNull(),updatedAt:(0,i.vE)("updated_at").defaultNow().notNull()}),p=(0,s.cJ)("accounts",{userId:(0,r.Qq)("userId").notNull().references(()=>u.id,{onDelete:"cascade"}),type:(0,r.Qq)("type").notNull(),provider:(0,r.Qq)("provider").notNull(),providerAccountId:(0,r.Qq)("providerAccountId").notNull(),refresh_token:(0,r.Qq)("refresh_token"),access_token:(0,r.Qq)("access_token"),expires_at:(0,n.nd)("expires_at"),token_type:(0,r.Qq)("token_type"),scope:(0,r.Qq)("scope"),id_token:(0,r.Qq)("id_token"),session_state:(0,r.Qq)("session_state")}),m=(0,s.cJ)("sessions",{sessionToken:(0,r.Qq)("sessionToken").primaryKey(),userId:(0,r.Qq)("userId").notNull().references(()=>u.id,{onDelete:"cascade"}),expires:(0,i.vE)("expires",{mode:"date"}).notNull()}),k=(0,s.cJ)("verificationTokens",{identifier:(0,r.Qq)("identifier").notNull(),token:(0,r.Qq)("token").notNull(),expires:(0,i.vE)("expires",{mode:"date"}).notNull()}),g=(0,s.cJ)("candidates",{id:(0,r.Qq)("id").primaryKey().$defaultFn(()=>(0,l.sX)()),fullName:(0,r.Qq)("full_name").notNull(),email:(0,r.Qq)("email").notNull().unique(),phoneNumber:(0,r.Qq)("phone_number").notNull(),dateOfBirth:(0,i.vE)("date_of_birth",{mode:"date"}).notNull(),nationality:(0,r.Qq)("nationality").notNull(),passportNumber:(0,r.Qq)("passport_number").notNull().unique(),testDate:(0,i.vE)("test_date",{mode:"date"}).notNull(),testCenter:(0,r.Qq)("test_center").notNull(),photoUrl:(0,r.Qq)("photo_url"),createdAt:(0,i.vE)("created_at").defaultNow().notNull(),updatedAt:(0,i.vE)("updated_at").defaultNow().notNull()}),_=(0,s.cJ)("test_results",{id:(0,r.Qq)("id").primaryKey().$defaultFn(()=>(0,l.sX)()),candidateId:(0,r.Qq)("candidate_id").notNull().references(()=>g.id,{onDelete:"cascade"}),listeningScore:(0,o._)("listening_score",{precision:3,scale:1}),listeningBandScore:(0,o._)("listening_band_score",{precision:2,scale:1}),readingScore:(0,o._)("reading_score",{precision:3,scale:1}),readingBandScore:(0,o._)("reading_band_score",{precision:2,scale:1}),writingTask1Score:(0,o._)("writing_task1_score",{precision:2,scale:1}),writingTask2Score:(0,o._)("writing_task2_score",{precision:2,scale:1}),writingBandScore:(0,o._)("writing_band_score",{precision:2,scale:1}),speakingFluencyScore:(0,o._)("speaking_fluency_score",{precision:2,scale:1}),speakingLexicalScore:(0,o._)("speaking_lexical_score",{precision:2,scale:1}),speakingGrammarScore:(0,o._)("speaking_grammar_score",{precision:2,scale:1}),speakingPronunciationScore:(0,o._)("speaking_pronunciation_score",{precision:2,scale:1}),speakingBandScore:(0,o._)("speaking_band_score",{precision:2,scale:1}),overallBandScore:(0,o._)("overall_band_score",{precision:2,scale:1}),status:(0,r.Qq)("status",{enum:["pending","completed","verified"]}).notNull().default("pending"),enteredBy:(0,r.Qq)("entered_by").references(()=>u.id),verifiedBy:(0,r.Qq)("verified_by").references(()=>u.id),certificateGenerated:(0,d.zM)("certificate_generated").default(!1),certificateSerial:(0,r.Qq)("certificate_serial").unique(),certificateUrl:(0,r.Qq)("certificate_url"),aiFeedbackGenerated:(0,d.zM)("ai_feedback_generated").default(!1),testDate:(0,i.vE)("test_date",{mode:"date"}),createdAt:(0,i.vE)("created_at").defaultNow().notNull(),updatedAt:(0,i.vE)("updated_at").defaultNow().notNull()}),f=(0,s.cJ)("ai_feedback",{id:(0,r.Qq)("id").primaryKey().$defaultFn(()=>(0,l.sX)()),testResultId:(0,r.Qq)("test_result_id").notNull().references(()=>_.id,{onDelete:"cascade"}),listeningFeedback:(0,r.Qq)("listening_feedback"),readingFeedback:(0,r.Qq)("reading_feedback"),writingFeedback:(0,r.Qq)("writing_feedback"),speakingFeedback:(0,r.Qq)("speaking_feedback"),overallFeedback:(0,r.Qq)("overall_feedback"),studyRecommendations:(0,r.Qq)("study_recommendations"),strengths:(0,c.Pq)("strengths").$type(),weaknesses:(0,c.Pq)("weaknesses").$type(),studyPlan:(0,c.Pq)("study_plan").$type(),generatedAt:(0,i.vE)("generated_at").defaultNow().notNull()})}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),s=t.X(0,[638,5452,9757,4681],()=>a(1614));module.exports=s})();