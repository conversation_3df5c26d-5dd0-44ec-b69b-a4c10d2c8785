(()=>{var e={};e.id=2908,e.ids=[2908],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},55511:e=>{"use strict";e.exports=require("crypto")},2113:e=>{"use strict";e.exports=import("postgres")},77598:e=>{"use strict";e.exports=require("node:crypto")},1614:(e,t,s)=>{"use strict";s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{patchFetch:()=>d,routeModule:()=>l,serverHooks:()=>m,workAsyncStorage:()=>u,workUnitAsyncStorage:()=>p});var r=s(42706),n=s(28203),i=s(45994),o=s(98893),c=e([o]);o=(c.then?(await c)():c)[0];let l=new r.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/ai/save-feedback/route",pathname:"/api/ai/save-feedback",filename:"route",bundlePath:"app/api/ai/save-feedback/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\api\\ai\\save-feedback\\route.ts",nextConfigOutput:"",userland:o}),{workAsyncStorage:u,workUnitAsyncStorage:p,serverHooks:m}=l;function d(){return(0,i.patchFetch)({workAsyncStorage:u,workUnitAsyncStorage:p})}a()}catch(e){a(e)}})},96487:()=>{},78335:()=>{},98893:(e,t,s)=>{"use strict";s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{POST:()=>l});var r=s(39187),n=s(37702),i=s(62693),o=s(48590),c=s(47579),d=e([n,i]);async function l(e){try{let t=await (0,n.j2)();if(!t)return r.NextResponse.json({error:"Unauthorized"},{status:401});let{resultId:s,feedback:a}=await e.json();if(!s||!a)return r.NextResponse.json({error:"Result ID and feedback are required"},{status:400});if((await i.db.select().from(o.aiFeedback).where((0,c.eq)(o.aiFeedback.testResultId,s)).limit(1)).length>0){let e=await i.db.update(o.aiFeedback).set({overallAssessment:a.overallAssessment,strengths:a.strengths,areasForImprovement:a.areasForImprovement,listeningRecommendations:a.specificRecommendations.listening,readingRecommendations:a.specificRecommendations.reading,writingRecommendations:a.specificRecommendations.writing,speakingRecommendations:a.specificRecommendations.speaking,studyPlan:a.studyPlan,nextSteps:a.nextSteps,updatedAt:new Date}).where((0,c.eq)(o.aiFeedback.testResultId,s)).returning();return r.NextResponse.json(e[0])}{let e=await i.db.insert(o.aiFeedback).values({testResultId:s,overallAssessment:a.overallAssessment,strengths:a.strengths,areasForImprovement:a.areasForImprovement,listeningRecommendations:a.specificRecommendations.listening,readingRecommendations:a.specificRecommendations.reading,writingRecommendations:a.specificRecommendations.writing,speakingRecommendations:a.specificRecommendations.speaking,studyPlan:a.studyPlan,nextSteps:a.nextSteps,generatedBy:t.user?.id}).returning();return r.NextResponse.json(e[0],{status:201})}}catch(e){return console.error("Error saving AI feedback:",e),r.NextResponse.json({error:"Failed to save feedback"},{status:500})}}[n,i]=d.then?(await d)():d,a()}catch(e){a(e)}})},37702:(e,t,s)=>{"use strict";s.a(e,async(e,a)=>{try{s.d(t,{Y9:()=>u,j2:()=>p});var r=s(32221),n=s(31648),i=s(62693),o=s(48590),c=s(47579),d=s(34926),l=e([i]);i=(l.then?(await l)():l)[0];let{handlers:u,auth:p,signIn:m,signOut:_}=(0,r.Ay)({session:{strategy:"jwt"},providers:[(0,n.A)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;try{let t=await i.db.select().from(o.users).where((0,c.eq)(o.users.email,e.email)).limit(1);if(0===t.length)return null;let s=t[0];if(!s.password||!await d.Ay.compare(e.password,s.password))return null;return{id:s.id,email:s.email,name:s.name,role:s.role}}catch(e){return console.error("Authentication error:",e),null}}})],callbacks:{jwt:async({token:e,user:t})=>(t&&(e.role=t.role),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role),e)},pages:{signIn:"/auth/signin"}});a()}catch(e){a(e)}})},62693:(e,t,s)=>{"use strict";s.a(e,async(e,a)=>{try{s.d(t,{db:()=>l});var r=s(10072),n=s(2113),i=s(48590),o=e([n,r]);[n,r]=o.then?(await o)():o;let c=process.env.DATABASE_URL,d=(0,n.default)(c,{prepare:!1}),l=(0,r.f)(d,{schema:i});a()}catch(e){a(e)}})},48590:(e,t,s)=>{"use strict";s.r(t),s.d(t,{accounts:()=>p,aiFeedback:()=>k,candidates:()=>g,sessions:()=>m,testResults:()=>f,users:()=>u,verificationTokens:()=>_});var a=s(87858),r=s(44799),n=s(32590),i=s(9848),o=s(70009),c=s(27390),d=s(32190),l=s(4502);let u=(0,a.cJ)("users",{id:(0,r.Qq)("id").primaryKey().$defaultFn(()=>(0,l.sX)()),name:(0,r.Qq)("name"),email:(0,r.Qq)("email").notNull().unique(),emailVerified:(0,n.vE)("emailVerified",{mode:"date"}),image:(0,r.Qq)("image"),password:(0,r.Qq)("password"),role:(0,r.Qq)("role",{enum:["admin","test_checker"]}).notNull().default("test_checker"),createdAt:(0,n.vE)("created_at").defaultNow().notNull(),updatedAt:(0,n.vE)("updated_at").defaultNow().notNull()}),p=(0,a.cJ)("accounts",{userId:(0,r.Qq)("userId").notNull().references(()=>u.id,{onDelete:"cascade"}),type:(0,r.Qq)("type").notNull(),provider:(0,r.Qq)("provider").notNull(),providerAccountId:(0,r.Qq)("providerAccountId").notNull(),refresh_token:(0,r.Qq)("refresh_token"),access_token:(0,r.Qq)("access_token"),expires_at:(0,i.nd)("expires_at"),token_type:(0,r.Qq)("token_type"),scope:(0,r.Qq)("scope"),id_token:(0,r.Qq)("id_token"),session_state:(0,r.Qq)("session_state")}),m=(0,a.cJ)("sessions",{sessionToken:(0,r.Qq)("sessionToken").primaryKey(),userId:(0,r.Qq)("userId").notNull().references(()=>u.id,{onDelete:"cascade"}),expires:(0,n.vE)("expires",{mode:"date"}).notNull()}),_=(0,a.cJ)("verificationTokens",{identifier:(0,r.Qq)("identifier").notNull(),token:(0,r.Qq)("token").notNull(),expires:(0,n.vE)("expires",{mode:"date"}).notNull()}),g=(0,a.cJ)("candidates",{id:(0,r.Qq)("id").primaryKey().$defaultFn(()=>(0,l.sX)()),fullName:(0,r.Qq)("full_name").notNull(),email:(0,r.Qq)("email").notNull().unique(),phoneNumber:(0,r.Qq)("phone_number").notNull(),dateOfBirth:(0,n.vE)("date_of_birth",{mode:"date"}).notNull(),nationality:(0,r.Qq)("nationality").notNull(),passportNumber:(0,r.Qq)("passport_number").notNull().unique(),testDate:(0,n.vE)("test_date",{mode:"date"}).notNull(),testCenter:(0,r.Qq)("test_center").notNull(),photoUrl:(0,r.Qq)("photo_url"),createdAt:(0,n.vE)("created_at").defaultNow().notNull(),updatedAt:(0,n.vE)("updated_at").defaultNow().notNull()}),f=(0,a.cJ)("test_results",{id:(0,r.Qq)("id").primaryKey().$defaultFn(()=>(0,l.sX)()),candidateId:(0,r.Qq)("candidate_id").notNull().references(()=>g.id,{onDelete:"cascade"}),listeningScore:(0,o._)("listening_score",{precision:3,scale:1}),listeningBandScore:(0,o._)("listening_band_score",{precision:2,scale:1}),readingScore:(0,o._)("reading_score",{precision:3,scale:1}),readingBandScore:(0,o._)("reading_band_score",{precision:2,scale:1}),writingTask1Score:(0,o._)("writing_task1_score",{precision:2,scale:1}),writingTask2Score:(0,o._)("writing_task2_score",{precision:2,scale:1}),writingBandScore:(0,o._)("writing_band_score",{precision:2,scale:1}),speakingFluencyScore:(0,o._)("speaking_fluency_score",{precision:2,scale:1}),speakingLexicalScore:(0,o._)("speaking_lexical_score",{precision:2,scale:1}),speakingGrammarScore:(0,o._)("speaking_grammar_score",{precision:2,scale:1}),speakingPronunciationScore:(0,o._)("speaking_pronunciation_score",{precision:2,scale:1}),speakingBandScore:(0,o._)("speaking_band_score",{precision:2,scale:1}),overallBandScore:(0,o._)("overall_band_score",{precision:2,scale:1}),status:(0,r.Qq)("status",{enum:["pending","completed","verified"]}).notNull().default("pending"),enteredBy:(0,r.Qq)("entered_by").references(()=>u.id),verifiedBy:(0,r.Qq)("verified_by").references(()=>u.id),certificateGenerated:(0,c.zM)("certificate_generated").default(!1),certificateSerial:(0,r.Qq)("certificate_serial").unique(),certificateUrl:(0,r.Qq)("certificate_url"),aiFeedbackGenerated:(0,c.zM)("ai_feedback_generated").default(!1),testDate:(0,n.vE)("test_date",{mode:"date"}),createdAt:(0,n.vE)("created_at").defaultNow().notNull(),updatedAt:(0,n.vE)("updated_at").defaultNow().notNull()}),k=(0,a.cJ)("ai_feedback",{id:(0,r.Qq)("id").primaryKey().$defaultFn(()=>(0,l.sX)()),testResultId:(0,r.Qq)("test_result_id").notNull().references(()=>f.id,{onDelete:"cascade"}),listeningFeedback:(0,r.Qq)("listening_feedback"),readingFeedback:(0,r.Qq)("reading_feedback"),writingFeedback:(0,r.Qq)("writing_feedback"),speakingFeedback:(0,r.Qq)("speaking_feedback"),overallFeedback:(0,r.Qq)("overall_feedback"),studyRecommendations:(0,r.Qq)("study_recommendations"),strengths:(0,d.Pq)("strengths").$type(),weaknesses:(0,d.Pq)("weaknesses").$type(),studyPlan:(0,d.Pq)("study_plan").$type(),generatedAt:(0,n.vE)("generated_at").defaultNow().notNull()})}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[638,5452,9757,4681],()=>s(1614));module.exports=a})();