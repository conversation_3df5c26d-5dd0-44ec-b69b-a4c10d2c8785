(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7378],{2218:(e,t,s)=>{Promise.resolve().then(s.bind(s,5435))},7364:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(7401).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},3467:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(7401).A)("book-open",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},8207:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(7401).A)("brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]])},9136:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(7401).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},1594:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(7401).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},3239:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(7401).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},6889:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(7401).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},4857:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(7401).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},4081:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(7401).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},8178:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(7401).A)("headphones",[["path",{d:"M3 14h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-7a9 9 0 0 1 18 0v7a2 2 0 0 1-2 2h-1a2 2 0 0 1-2-2v-3a2 2 0 0 1 2-2h3",key:"1xhozi"}]])},7223:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(7401).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},6164:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(7401).A)("mic",[["path",{d:"M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z",key:"131961"}],["path",{d:"M19 10v2a7 7 0 0 1-14 0v-2",key:"1vc78b"}],["line",{x1:"12",x2:"12",y1:"19",y2:"22",key:"x3vr5v"}]])},6005:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(7401).A)("pen-tool",[["path",{d:"M15.707 21.293a1 1 0 0 1-1.414 0l-1.586-1.586a1 1 0 0 1 0-1.414l5.586-5.586a1 1 0 0 1 1.414 0l1.586 1.586a1 1 0 0 1 0 1.414z",key:"nt11vn"}],["path",{d:"m18 13-1.375-6.874a1 1 0 0 0-.746-.776L3.235 2.028a1 1 0 0 0-1.207 1.207L5.35 15.879a1 1 0 0 0 .776.746L13 18",key:"15qc1e"}],["path",{d:"m2.3 2.3 7.286 7.286",key:"1wuzzi"}],["circle",{cx:"11",cy:"11",r:"2",key:"xmgehs"}]])},6764:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(7401).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},1466:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(7401).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},6046:(e,t,s)=>{"use strict";var a=s(6658);s.o(a,"useParams")&&s.d(t,{useParams:function(){return a.useParams}}),s.o(a,"useRouter")&&s.d(t,{useRouter:function(){return a.useRouter}}),s.o(a,"useSearchParams")&&s.d(t,{useSearchParams:function(){return a.useSearchParams}})},5435:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>A});var a=s(5155),r=s(2115),l=s(5565),c=s(6046),d=s(8173),i=s.n(d),n=s(3239),x=s(6889),m=s(1594),o=s(7364),h=s(6764),g=s(4857),u=s(1466),p=s(4081),y=s(2423),j=s(7223),f=s(8178),N=s(3467),b=s(6005),w=s(6164),v=s(9136),k=s(8207);function A(){let e=(0,c.useParams)().id,[t,s]=(0,r.useState)(null),[d,A]=(0,r.useState)(!0),[S,M]=(0,r.useState)(""),[C,L]=(0,r.useState)(!1),R=(0,r.useCallback)(async()=>{try{let t=await fetch("/api/checker/results/".concat(e));if(t.ok){let e=await t.json();s(e)}else M("Result not found")}catch(e){console.error("Error fetching result:",e),M("Failed to load result")}finally{A(!1)}},[e]);(0,r.useEffect)(()=>{e&&R()},[e,R]);let B=async()=>{if(t){L(!0);try{let a=await fetch("/api/certificate/".concat(e),{method:"POST"});if(a.ok){let a=await fetch("/api/certificate/".concat(e));if(a.ok){let r=await a.blob(),l=window.URL.createObjectURL(r),c=document.createElement("a");c.href=l,c.download="IELTS_Certificate_".concat(t.candidate.fullName.replace(/\s+/g,"_"),"_").concat(e,".pdf"),document.body.appendChild(c),c.click(),window.URL.revokeObjectURL(l),document.body.removeChild(c),s(e=>e?{...e,certificateGenerated:!0}:null)}}else{let e=await a.json();alert("Failed to generate certificate: ".concat(e.error))}}catch(e){console.error("Certificate generation error:",e),alert("An error occurred while generating the certificate")}finally{L(!1)}}},E=async()=>{try{let s=await fetch("/api/certificate/".concat(e));if(s.ok){let a=await s.blob(),r=window.URL.createObjectURL(a),l=document.createElement("a");l.href=r,l.download="IELTS_Certificate_".concat(null==t?void 0:t.candidate.fullName.replace(/\s+/g,"_"),"_").concat(e,".pdf"),document.body.appendChild(l),l.click(),window.URL.revokeObjectURL(r),document.body.removeChild(l)}}catch(e){console.error("Certificate download error:",e),alert("An error occurred while downloading the certificate")}};return d?(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):S||!t?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(m.A,{className:"h-12 w-12 text-red-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Error Loading Result"}),(0,a.jsx)("p",{className:"text-gray-600",children:S||"Result not found"}),(0,a.jsxs)(i(),{href:"/dashboard/results/list",className:"mt-4 inline-flex items-center text-blue-600 hover:text-blue-700",children:[(0,a.jsx)(o.A,{className:"h-4 w-4 mr-1"}),"Back to Results"]})]}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(i(),{href:"/dashboard/results/list",className:"mr-4 p-2 text-gray-400 hover:text-gray-600",children:(0,a.jsx)(o.A,{className:"h-5 w-5"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Test Result Details"}),(0,a.jsx)("p",{className:"text-gray-600",children:"View complete IELTS test results"})]})]}),(0,a.jsxs)("div",{className:"flex gap-3",children:[(0,a.jsxs)(i(),{href:"/dashboard/results/".concat(e,"/edit"),className:"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50",children:[(0,a.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Edit Results"]}),t.certificateGenerated?(0,a.jsxs)("button",{onClick:E,className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700",children:[(0,a.jsx)(g.A,{className:"h-4 w-4 mr-2"}),"Download Certificate"]}):(0,a.jsx)("button",{onClick:B,disabled:C||"pending"===t.status,className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50",children:C?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Generating..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(g.A,{className:"h-4 w-4 mr-2"}),"Generate Certificate"]})})]})]}),(0,a.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,a.jsxs)("h3",{className:"text-lg font-medium text-gray-900 mb-4 flex items-center",children:[(0,a.jsx)(u.A,{className:"h-5 w-5 mr-2"}),"Candidate Information"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[t.candidate.photoUrl?(0,a.jsx)(l.default,{className:"h-16 w-16 rounded-full object-cover",src:t.candidate.photoUrl,alt:t.candidate.fullName,width:64,height:64}):(0,a.jsx)("div",{className:"h-16 w-16 rounded-full bg-gray-200 flex items-center justify-center",children:(0,a.jsx)(u.A,{className:"h-8 w-8 text-gray-400"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-xl font-semibold text-gray-900",children:t.candidate.fullName}),(0,a.jsx)("p",{className:"text-gray-600",children:t.candidate.email}),(0,a.jsx)("p",{className:"text-gray-600",children:t.candidate.phoneNumber})]})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,a.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Passport: ",t.candidate.passportNumber]}),(0,a.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,a.jsx)(y.A,{className:"h-4 w-4 mr-2"}),"Test Date: ",new Date(t.candidate.testDate).toLocaleDateString()]}),(0,a.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,a.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"Test Center: ",t.candidate.testCenter]})]})]})]}),(0,a.jsx)("div",{className:"bg-white shadow rounded-lg p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(e=>{switch(e){case"completed":return(0,a.jsx)(n.A,{className:"h-5 w-5 text-green-500"});case"pending":return(0,a.jsx)(x.A,{className:"h-5 w-5 text-yellow-500"});default:return(0,a.jsx)(m.A,{className:"h-5 w-5 text-red-500"})}})(t.status),(0,a.jsx)("span",{className:"ml-3 ".concat((e=>{let t="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium";switch(e){case"completed":return"".concat(t," bg-green-100 text-green-800");case"pending":return"".concat(t," bg-yellow-100 text-yellow-800");default:return"".concat(t," bg-red-100 text-red-800")}})(t.status)),children:t.status.charAt(0).toUpperCase()+t.status.slice(1)})]}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:["Entered: ",new Date(t.createdAt).toLocaleDateString(),t.updatedAt!==t.createdAt&&(0,a.jsxs)("span",{className:"ml-2",children:["• Updated: ",new Date(t.updatedAt).toLocaleDateString()]})]})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,a.jsxs)("h3",{className:"text-lg font-medium text-gray-900 mb-4 flex items-center",children:[(0,a.jsx)(f.A,{className:"h-5 w-5 mr-2 text-blue-600"}),"Listening"]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Raw Score"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[t.listeningScore||"N/A","/40"]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Band Score"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-blue-600",children:t.listeningBandScore||"N/A"})]})]})]}),(0,a.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,a.jsxs)("h3",{className:"text-lg font-medium text-gray-900 mb-4 flex items-center",children:[(0,a.jsx)(N.A,{className:"h-5 w-5 mr-2 text-green-600"}),"Reading"]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Raw Score"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[t.readingScore||"N/A","/40"]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Band Score"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-green-600",children:t.readingBandScore||"N/A"})]})]})]}),(0,a.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,a.jsxs)("h3",{className:"text-lg font-medium text-gray-900 mb-4 flex items-center",children:[(0,a.jsx)(b.A,{className:"h-5 w-5 mr-2 text-purple-600"}),"Writing"]}),(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Task 1"}),(0,a.jsx)("p",{className:"text-xl font-bold text-gray-900",children:t.writingTask1Score||"N/A"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Task 2"}),(0,a.jsx)("p",{className:"text-xl font-bold text-gray-900",children:t.writingTask2Score||"N/A"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Band Score"}),(0,a.jsx)("p",{className:"text-xl font-bold text-purple-600",children:t.writingBandScore||"N/A"})]})]})]}),(0,a.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,a.jsxs)("h3",{className:"text-lg font-medium text-gray-900 mb-4 flex items-center",children:[(0,a.jsx)(w.A,{className:"h-5 w-5 mr-2 text-red-600"}),"Speaking"]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 mb-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"Fluency & Coherence"}),(0,a.jsx)("p",{className:"text-lg font-bold text-gray-900",children:t.speakingFluencyScore||"N/A"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"Lexical Resource"}),(0,a.jsx)("p",{className:"text-lg font-bold text-gray-900",children:t.speakingLexicalScore||"N/A"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"Grammar & Accuracy"}),(0,a.jsx)("p",{className:"text-lg font-bold text-gray-900",children:t.speakingGrammarScore||"N/A"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"Pronunciation"}),(0,a.jsx)("p",{className:"text-lg font-bold text-gray-900",children:t.speakingPronunciationScore||"N/A"})]})]}),(0,a.jsxs)("div",{className:"border-t pt-4",children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Overall Band Score"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-red-600",children:t.speakingBandScore||"N/A"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-gradient-to-br from-blue-50 to-indigo-100 shadow rounded-lg p-8 text-center",children:[(0,a.jsxs)("h3",{className:"text-lg font-medium text-gray-900 mb-4 flex items-center justify-center",children:[(0,a.jsx)(v.A,{className:"h-5 w-5 mr-2 text-indigo-600"}),"Overall Band Score"]}),(0,a.jsx)("div",{className:"text-6xl font-bold text-indigo-600 mb-2",children:t.overallBandScore||"N/A"}),(0,a.jsx)("p",{className:"text-gray-600",children:"IELTS Band Score"})]}),(0,a.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Actions"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)(i(),{href:"/dashboard/feedback/generate?resultId=".concat(e),className:"w-full flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700",children:[(0,a.jsx)(k.A,{className:"h-4 w-4 mr-2"}),"Generate AI Feedback"]}),t.certificateGenerated?(0,a.jsxs)("button",{onClick:E,className:"w-full flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50",children:[(0,a.jsx)(g.A,{className:"h-4 w-4 mr-2"}),"Download Certificate"]}):(0,a.jsx)("button",{onClick:B,disabled:C||"pending"===t.status,className:"w-full flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50",children:C?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600 mr-2"}),"Generating..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(g.A,{className:"h-4 w-4 mr-2"}),"Generate Certificate"]})}),(0,a.jsxs)(i(),{href:"/dashboard/results/".concat(e,"/edit"),className:"w-full flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50",children:[(0,a.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Edit Results"]})]})]}),(0,a.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Score Summary"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Listening"}),(0,a.jsx)("span",{className:"font-medium",children:t.listeningBandScore||"N/A"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Reading"}),(0,a.jsx)("span",{className:"font-medium",children:t.readingBandScore||"N/A"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Writing"}),(0,a.jsx)("span",{className:"font-medium",children:t.writingBandScore||"N/A"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Speaking"}),(0,a.jsx)("span",{className:"font-medium",children:t.speakingBandScore||"N/A"})]}),(0,a.jsxs)("div",{className:"border-t pt-3 flex justify-between",children:[(0,a.jsx)("span",{className:"font-medium text-gray-900",children:"Overall"}),(0,a.jsx)("span",{className:"font-bold text-indigo-600",children:t.overallBandScore||"N/A"})]})]})]})]})]})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[8173,7311,8441,1517,7358],()=>t(2218)),_N_E=e.O()}]);