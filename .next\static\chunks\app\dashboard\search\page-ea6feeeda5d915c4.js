(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8382],{8987:(e,s,t)=>{Promise.resolve().then(t.bind(t,2247))},4836:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(7401).A)("clipboard-list",[["rect",{width:"8",height:"4",x:"8",y:"2",rx:"1",ry:"1",key:"tgr4d6"}],["path",{d:"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2",key:"116196"}],["path",{d:"M12 11h4",key:"1jrz19"}],["path",{d:"M12 16h4",key:"n85exb"}],["path",{d:"M8 11h.01",key:"1dfujw"}],["path",{d:"M8 16h.01",key:"18s6g9"}]])},2598:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(7401).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},4081:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(7401).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},7223:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(7401).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},3473:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(7401).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},853:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(7401).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},1466:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(7401).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},2247:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>y});var a=t(5155),r=t(2115),l=t(8173),d=t.n(l),c=t(5565),i=t(853),n=t(1466),h=t(4081),m=t(2423),o=t(7223),x=t(2598),u=t(3473),p=t(4836);function y(){let[e,s]=(0,r.useState)(""),[t,l]=(0,r.useState)("name"),[y,g]=(0,r.useState)([]),[b,j]=(0,r.useState)(!1),[f,N]=(0,r.useState)(!1),v=async s=>{if(s.preventDefault(),e.trim()){j(!0),N(!0);try{let s=await fetch("/api/checker/candidates/search",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({query:e,searchType:t})});if(s.ok){let e=await s.json();g(e)}else g([])}catch(e){console.error("Search error:",e),g([])}finally{j(!1)}}};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Search Candidates"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Find candidates to enter or view test results"})]}),(0,a.jsx)("div",{className:"bg-white shadow rounded-lg p-6",children:(0,a.jsx)("form",{onSubmit:v,className:"space-y-4",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"searchType",className:"block text-sm font-medium text-gray-700 mb-2",children:"Search by"}),(0,a.jsxs)("select",{id:"searchType",value:t,onChange:e=>l(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,a.jsx)("option",{value:"name",children:"Full Name"}),(0,a.jsx)("option",{value:"email",children:"Email Address"}),(0,a.jsx)("option",{value:"passport",children:"Passport Number"})]})]}),(0,a.jsxs)("div",{className:"md:col-span-2",children:[(0,a.jsx)("label",{htmlFor:"searchQuery",className:"block text-sm font-medium text-gray-700 mb-2",children:"Search term"}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsxs)("div",{className:"relative flex-1",children:[(0,a.jsx)(i.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,a.jsx)("input",{type:"text",id:"searchQuery",value:e,onChange:e=>s(e.target.value),placeholder:"name"===t?"Enter candidate name...":"email"===t?"Enter email address...":"Enter passport number...",className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-l-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500",required:!0})]}),(0,a.jsx)("button",{type:"submit",disabled:b,className:"px-6 py-2 border border-transparent text-sm font-medium rounded-r-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50",children:b?"Searching...":"Search"})]})]})]})})}),f&&(0,a.jsxs)("div",{className:"bg-white shadow rounded-lg",children:[(0,a.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,a.jsxs)("h3",{className:"text-lg font-medium text-gray-900",children:["Search Results ",y.length>0&&"(".concat(y.length,")")]})}),b?(0,a.jsxs)("div",{className:"flex items-center justify-center py-12",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),(0,a.jsx)("span",{className:"ml-2 text-gray-600",children:"Searching candidates..."})]}):y.length>0?(0,a.jsx)("div",{className:"divide-y divide-gray-200",children:y.map(e=>(0,a.jsx)("div",{className:"p-6 hover:bg-gray-50",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[e.photoUrl?(0,a.jsx)(c.default,{className:"h-12 w-12 rounded-full object-cover",src:e.photoUrl,alt:e.fullName,width:48,height:48}):(0,a.jsx)("div",{className:"h-12 w-12 rounded-full bg-gray-200 flex items-center justify-center",children:(0,a.jsx)(n.A,{className:"h-6 w-6 text-gray-400"})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h4",{className:"text-lg font-medium text-gray-900",children:e.fullName}),(0,a.jsxs)("div",{className:"mt-1 flex items-center space-x-4 text-sm text-gray-500",children:[(0,a.jsxs)("span",{className:"flex items-center",children:[(0,a.jsx)(h.A,{className:"h-4 w-4 mr-1"}),e.passportNumber]}),(0,a.jsxs)("span",{className:"flex items-center",children:[(0,a.jsx)(m.A,{className:"h-4 w-4 mr-1"}),"Test: ",new Date(e.testDate).toLocaleDateString()]}),(0,a.jsxs)("span",{className:"flex items-center",children:[(0,a.jsx)(o.A,{className:"h-4 w-4 mr-1"}),e.testCenter]})]}),(0,a.jsxs)("div",{className:"mt-1 text-sm text-gray-600",children:[e.email," • ",e.phoneNumber]})]})]}),(0,a.jsx)("div",{className:"flex items-center space-x-3",children:e.hasResults?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800",children:"Results Available"}),(0,a.jsxs)(d(),{href:"/dashboard/results/".concat(e.resultId),className:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50",children:[(0,a.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"View Results"]})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800",children:"No Results"}),(0,a.jsxs)(d(),{href:"/dashboard/results/new?candidateId=".concat(e.id),className:"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700",children:[(0,a.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Enter Results"]})]})})]})},e.id))}):(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(i.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No candidates found"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Try adjusting your search criteria or check the spelling."})]})]}),!f&&(0,a.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-blue-900 mb-4",children:"Quick Actions"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)(d(),{href:"/dashboard/results/list",className:"flex items-center p-4 bg-white rounded-lg shadow hover:shadow-md transition-shadow",children:[(0,a.jsx)(p.A,{className:"h-8 w-8 text-blue-600 mr-4"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900",children:"View All Results"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Browse all entered test results"})]})]}),(0,a.jsxs)(d(),{href:"/dashboard/results",className:"flex items-center p-4 bg-white rounded-lg shadow hover:shadow-md transition-shadow",children:[(0,a.jsx)(u.A,{className:"h-8 w-8 text-green-600 mr-4"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900",children:"Enter New Results"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Add test results for a candidate"})]})]})]})]})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[8173,7311,8441,1517,7358],()=>s(8987)),_N_E=e.O()}]);