(()=>{var e={};e.id=8382,e.ids=[8382],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},5250:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>h,pages:()=>c,routeModule:()=>m,tree:()=>o});var r=t(70260),a=t(28203),i=t(25155),l=t.n(i),d=t(67292),n={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>d[e]);t.d(s,n);let o=["",{children:["dashboard",{children:["search",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,76433)),"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\dashboard\\search\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,33405)),"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,71354)),"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\dashboard\\search\\page.tsx"],h={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/search/page",pathname:"/dashboard/search",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},97032:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,13219,23)),Promise.resolve().then(t.t.bind(t,34863,23)),Promise.resolve().then(t.t.bind(t,25155,23)),Promise.resolve().then(t.t.bind(t,40802,23)),Promise.resolve().then(t.t.bind(t,9350,23)),Promise.resolve().then(t.t.bind(t,48530,23)),Promise.resolve().then(t.t.bind(t,88921,23))},60584:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,66959,23)),Promise.resolve().then(t.t.bind(t,33875,23)),Promise.resolve().then(t.t.bind(t,88903,23)),Promise.resolve().then(t.t.bind(t,57174,23)),Promise.resolve().then(t.t.bind(t,84178,23)),Promise.resolve().then(t.t.bind(t,87190,23)),Promise.resolve().then(t.t.bind(t,61365,23))},71315:(e,s,t)=>{Promise.resolve().then(t.bind(t,70452))},8267:(e,s,t)=>{Promise.resolve().then(t.bind(t,98805))},63508:(e,s,t)=>{Promise.resolve().then(t.bind(t,33405))},16244:(e,s,t)=>{Promise.resolve().then(t.bind(t,12361))},39085:(e,s,t)=>{Promise.resolve().then(t.bind(t,76433))},86349:(e,s,t)=>{Promise.resolve().then(t.bind(t,94101))},78397:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(41680).A)("brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]])},79660:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(41680).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},94172:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(41680).A)("clipboard-list",[["rect",{width:"8",height:"4",x:"8",y:"2",rx:"1",ry:"1",key:"tgr4d6"}],["path",{d:"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2",key:"116196"}],["path",{d:"M12 11h4",key:"1jrz19"}],["path",{d:"M12 16h4",key:"n85exb"}],["path",{d:"M8 11h.01",key:"1dfujw"}],["path",{d:"M8 16h.01",key:"18s6g9"}]])},21956:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(41680).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},87137:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(41680).A)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},30722:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(41680).A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},48857:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(41680).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},18741:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(41680).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},16873:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(41680).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},87798:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(41680).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},12361:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>p});var r=t(45512),a=t(98805),i=t(79334);t(58009);var l=t(28531),d=t.n(l),n=t(87137),o=t(16873),c=t(94172),h=t(79660),m=t(78397),x=t(61075),u=t(30722);function p({children:e}){let{data:s,status:t}=(0,a.wV)();if((0,i.useRouter)(),"loading"===t)return(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})});if(!s)return null;let l=[{name:"Dashboard",href:"/dashboard",icon:n.A},{name:"Search Candidates",href:"/dashboard/search",icon:o.A},{name:"Enter Results",href:"/dashboard/results",icon:c.A},{name:"Test Results",href:"/dashboard/results/list",icon:h.A},{name:"AI Feedback",href:"/dashboard/feedback",icon:m.A}];return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)("div",{className:"fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg",children:(0,r.jsxs)("div",{className:"flex flex-col h-full",children:[(0,r.jsxs)("div",{className:"flex items-center px-6 py-4 border-b border-gray-200",children:[(0,r.jsx)(x.A,{className:"h-8 w-8 text-blue-600 mr-3"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-lg font-semibold text-gray-900",children:"IELTS Checker"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Test Results Portal"})]})]}),(0,r.jsx)("nav",{className:"flex-1 px-4 py-6 space-y-2",children:l.map(e=>{let s=e.icon;return(0,r.jsxs)(d(),{href:e.href,className:"flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-100 hover:text-gray-900 group",children:[(0,r.jsx)(s,{className:"h-5 w-5 mr-3 text-gray-400 group-hover:text-gray-500"}),e.name]},e.name)})}),(0,r.jsxs)("div",{className:"border-t border-gray-200 p-4",children:[(0,r.jsx)("div",{className:"flex items-center mb-3",children:(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900",children:s.user?.name}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:s.user?.email}),(0,r.jsx)("p",{className:"text-xs text-green-600 font-medium",children:"Test Checker"})]})}),(0,r.jsxs)("button",{onClick:()=>(0,a.CI)({callbackUrl:"/"}),className:"flex items-center w-full px-3 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-100 hover:text-gray-900",children:[(0,r.jsx)(u.A,{className:"h-4 w-4 mr-3"}),"Sign out"]})]})]})}),(0,r.jsx)("div",{className:"pl-64",children:(0,r.jsx)("main",{className:"py-6",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:e})})})]})}},94101:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>f});var r=t(45512),a=t(58009),i=t(28531),l=t.n(i),d=t(45103),n=t(16873),o=t(87798),c=t(61075),h=t(45723),m=t(48857),x=t(21956),u=t(18741),p=t(94172);function f(){let[e,s]=(0,a.useState)(""),[t,i]=(0,a.useState)("name"),[f,b]=(0,a.useState)([]),[y,g]=(0,a.useState)(!1),[v,j]=(0,a.useState)(!1),N=async s=>{if(s.preventDefault(),e.trim()){g(!0),j(!0);try{let s=await fetch("/api/checker/candidates/search",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({query:e,searchType:t})});if(s.ok){let e=await s.json();b(e)}else b([])}catch(e){console.error("Search error:",e),b([])}finally{g(!1)}}};return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Search Candidates"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Find candidates to enter or view test results"})]}),(0,r.jsx)("div",{className:"bg-white shadow rounded-lg p-6",children:(0,r.jsx)("form",{onSubmit:N,className:"space-y-4",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"searchType",className:"block text-sm font-medium text-gray-700 mb-2",children:"Search by"}),(0,r.jsxs)("select",{id:"searchType",value:t,onChange:e=>i(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,r.jsx)("option",{value:"name",children:"Full Name"}),(0,r.jsx)("option",{value:"email",children:"Email Address"}),(0,r.jsx)("option",{value:"passport",children:"Passport Number"})]})]}),(0,r.jsxs)("div",{className:"md:col-span-2",children:[(0,r.jsx)("label",{htmlFor:"searchQuery",className:"block text-sm font-medium text-gray-700 mb-2",children:"Search term"}),(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsxs)("div",{className:"relative flex-1",children:[(0,r.jsx)(n.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,r.jsx)("input",{type:"text",id:"searchQuery",value:e,onChange:e=>s(e.target.value),placeholder:"name"===t?"Enter candidate name...":"email"===t?"Enter email address...":"Enter passport number...",className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-l-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500",required:!0})]}),(0,r.jsx)("button",{type:"submit",disabled:y,className:"px-6 py-2 border border-transparent text-sm font-medium rounded-r-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50",children:y?"Searching...":"Search"})]})]})]})})}),v&&(0,r.jsxs)("div",{className:"bg-white shadow rounded-lg",children:[(0,r.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,r.jsxs)("h3",{className:"text-lg font-medium text-gray-900",children:["Search Results ",f.length>0&&`(${f.length})`]})}),y?(0,r.jsxs)("div",{className:"flex items-center justify-center py-12",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),(0,r.jsx)("span",{className:"ml-2 text-gray-600",children:"Searching candidates..."})]}):f.length>0?(0,r.jsx)("div",{className:"divide-y divide-gray-200",children:f.map(e=>(0,r.jsx)("div",{className:"p-6 hover:bg-gray-50",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[e.photoUrl?(0,r.jsx)(d.default,{className:"h-12 w-12 rounded-full object-cover",src:e.photoUrl,alt:e.fullName,width:48,height:48}):(0,r.jsx)("div",{className:"h-12 w-12 rounded-full bg-gray-200 flex items-center justify-center",children:(0,r.jsx)(o.A,{className:"h-6 w-6 text-gray-400"})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h4",{className:"text-lg font-medium text-gray-900",children:e.fullName}),(0,r.jsxs)("div",{className:"mt-1 flex items-center space-x-4 text-sm text-gray-500",children:[(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(c.A,{className:"h-4 w-4 mr-1"}),e.passportNumber]}),(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(h.A,{className:"h-4 w-4 mr-1"}),"Test: ",new Date(e.testDate).toLocaleDateString()]}),(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(m.A,{className:"h-4 w-4 mr-1"}),e.testCenter]})]}),(0,r.jsxs)("div",{className:"mt-1 text-sm text-gray-600",children:[e.email," • ",e.phoneNumber]})]})]}),(0,r.jsx)("div",{className:"flex items-center space-x-3",children:e.hasResults?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800",children:"Results Available"}),(0,r.jsxs)(l(),{href:`/dashboard/results/${e.resultId}`,className:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50",children:[(0,r.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"View Results"]})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800",children:"No Results"}),(0,r.jsxs)(l(),{href:`/dashboard/results/new?candidateId=${e.id}`,className:"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700",children:[(0,r.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Enter Results"]})]})})]})},e.id))}):(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)(n.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No candidates found"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Try adjusting your search criteria or check the spelling."})]})]}),!v&&(0,r.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-blue-900 mb-4",children:"Quick Actions"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)(l(),{href:"/dashboard/results/list",className:"flex items-center p-4 bg-white rounded-lg shadow hover:shadow-md transition-shadow",children:[(0,r.jsx)(p.A,{className:"h-8 w-8 text-blue-600 mr-4"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900",children:"View All Results"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Browse all entered test results"})]})]}),(0,r.jsxs)(l(),{href:"/dashboard/results",className:"flex items-center p-4 bg-white rounded-lg shadow hover:shadow-md transition-shadow",children:[(0,r.jsx)(u.A,{className:"h-8 w-8 text-green-600 mr-4"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900",children:"Enter New Results"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Add test results for a candidate"})]})]})]})]})]})}},33405:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\dashboard\\layout.tsx","default")},76433:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\search\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\dashboard\\search\\page.tsx","default")},71354:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n,metadata:()=>d});var r=t(62740),a=t(85041),i=t.n(a),l=t(70452);t(61135);let d={title:"IELTS Certification System",description:"Professional IELTS test result management and certification system"};function n({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsx)("body",{className:i().className,children:(0,r.jsx)(l.SessionProvider,{children:e})})})}},70440:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});var r=t(88077);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},61135:()=>{}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[638,8338,2367,8324],()=>t(5250));module.exports=r})();