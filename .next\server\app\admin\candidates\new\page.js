(()=>{var e={};e.id=4604,e.ids=[4604],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},38962:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>h,tree:()=>o});var r=s(70260),a=s(28203),n=s(25155),i=s.n(n),l=s(67292),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);s.d(t,d);let o=["",{children:["admin",{children:["candidates",{children:["new",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,76515)),"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\admin\\candidates\\new\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,96038)),"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\admin\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,71354)),"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\admin\\candidates\\new\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/candidates/new/page",pathname:"/admin/candidates/new",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},97032:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,13219,23)),Promise.resolve().then(s.t.bind(s,34863,23)),Promise.resolve().then(s.t.bind(s,25155,23)),Promise.resolve().then(s.t.bind(s,40802,23)),Promise.resolve().then(s.t.bind(s,9350,23)),Promise.resolve().then(s.t.bind(s,48530,23)),Promise.resolve().then(s.t.bind(s,88921,23))},60584:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,66959,23)),Promise.resolve().then(s.t.bind(s,33875,23)),Promise.resolve().then(s.t.bind(s,88903,23)),Promise.resolve().then(s.t.bind(s,57174,23)),Promise.resolve().then(s.t.bind(s,84178,23)),Promise.resolve().then(s.t.bind(s,87190,23)),Promise.resolve().then(s.t.bind(s,61365,23))},71315:(e,t,s)=>{Promise.resolve().then(s.bind(s,70452))},8267:(e,t,s)=>{Promise.resolve().then(s.bind(s,98805))},5544:(e,t,s)=>{Promise.resolve().then(s.bind(s,76515))},92392:(e,t,s)=>{Promise.resolve().then(s.bind(s,22282))},67305:(e,t,s)=>{Promise.resolve().then(s.bind(s,96038))},57577:(e,t,s)=>{Promise.resolve().then(s.bind(s,53760))},35668:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(41680).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},79660:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(41680).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},87137:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(41680).A)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},30722:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(41680).A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},8866:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(41680).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},33680:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(41680).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},16873:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(41680).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},69855:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(41680).A)("user-plus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},87798:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(41680).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},64977:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(41680).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},22282:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>j});var r=s(45512),a=s(58009),n=s(79334),i=s(28531),l=s.n(i),d=s(35668),o=s(87798),c=s(8866),m=s(45723),h=s(41680);let u=(0,h.A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]]);var x=s(33680),p=s(61075);let f=(0,h.A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),b=(0,h.A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]),y=(0,h.A)("image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]]);var g=s(45103);function v({type:e,onUpload:t,onRemove:s,currentFile:n,accept:i,maxSize:l=5,className:d="",disabled:o=!1}){let[c,m]=(0,a.useState)(!1),[h,x]=(0,a.useState)(!1),[v,j]=(0,a.useState)(""),N=(0,a.useRef)(null),w=async s=>{if(!o){j(""),m(!0);try{if(s.size>1048576*l)throw Error(`File size must be less than ${l}MB`);let r=new FormData;r.append("file",s),r.append("type",e);let a=await fetch("/api/upload",{method:"POST",body:r});if(!a.ok){let e=await a.json();throw Error(e.error||"Upload failed")}let n=await a.json();t(n.url)}catch(e){console.error("Upload error:",e),j(e instanceof Error?e.message:"Upload failed")}finally{m(!1)}}},k=async()=>{if(n&&!o)try{await fetch(`/api/upload?url=${encodeURIComponent(n)}`,{method:"DELETE"}),s?.()}catch(e){console.error("Remove error:",e)}};return n?(0,r.jsx)("div",{className:`relative ${d}`,children:(0,r.jsx)("div",{className:"border-2 border-gray-200 rounded-lg p-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:["photo"===e?(0,r.jsx)("div",{className:"relative",children:(0,r.jsx)(g.default,{src:n,alt:"Uploaded file",className:"h-16 w-16 object-cover rounded-lg",width:64,height:64})}):(0,r.jsx)("div",{className:"h-16 w-16 bg-gray-100 rounded-lg flex items-center justify-center",children:(0,r.jsx)(p.A,{className:"h-8 w-8 text-gray-400"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"File uploaded"}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"Click remove to change file"})]})]}),!o&&(0,r.jsx)("button",{onClick:k,className:"p-1 text-gray-400 hover:text-red-500 transition-colors",title:"Remove file",children:(0,r.jsx)(f,{className:"h-5 w-5"})})]})})}):(0,r.jsxs)("div",{className:`relative ${d}`,children:[(0,r.jsx)("input",{ref:N,type:"file",accept:i||("photo"===e?"image/jpeg,image/jpg,image/png,image/webp":"application/pdf,image/jpeg,image/jpg,image/png"),onChange:e=>{let t=e.target.files?.[0];t&&w(t)},className:"hidden",disabled:o}),(0,r.jsx)("div",{onClick:()=>{o||N.current?.click()},onDrop:e=>{if(e.preventDefault(),x(!1),o)return;let t=e.dataTransfer.files?.[0];t&&w(t)},onDragOver:e=>{e.preventDefault(),o||x(!0)},onDragLeave:e=>{e.preventDefault(),x(!1)},className:`
          border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-all
          ${h?"border-blue-400 bg-blue-50":"border-gray-300 hover:border-gray-400"}
          ${o?"opacity-50 cursor-not-allowed":""}
          ${v?"border-red-300 bg-red-50":""}
        `,children:c?(0,r.jsxs)("div",{className:"flex flex-col items-center",children:[(0,r.jsx)(b,{className:"h-12 w-12 text-blue-500 animate-spin mb-4"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Uploading..."})]}):(0,r.jsxs)("div",{className:"flex flex-col items-center",children:["photo"===e?(0,r.jsx)(y,{className:"h-12 w-12 text-gray-400 mb-4"}):(0,r.jsx)(u,{className:"h-12 w-12 text-gray-400 mb-4"}),(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900 mb-2",children:"photo"===e?"Upload Photo":"Upload Document"}),(0,r.jsx)("p",{className:"text-xs text-gray-500 mb-2",children:"Drag and drop or click to select"}),(0,r.jsx)("p",{className:"text-xs text-gray-400",children:"photo"===e?"PNG, JPG, WEBP up to 5MB":"PDF, PNG, JPG up to 5MB"})]})}),v&&(0,r.jsx)("p",{className:"mt-2 text-sm text-red-600",children:v})]})}function j(){let e=(0,n.useRouter)(),[t,s]=(0,a.useState)(!1),[i,h]=(0,a.useState)(""),[p,f]=(0,a.useState)({fullName:"",email:"",phoneNumber:"",dateOfBirth:"",nationality:"",passportNumber:"",testDate:"",testCenter:"",photoUrl:""}),b=e=>{let{name:t,value:s}=e.target;f(e=>({...e,[t]:s}))},y=async t=>{t.preventDefault(),s(!0),h("");try{let t=await fetch("/api/admin/candidates",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(p)});if(t.ok)e.push("/admin/candidates");else{let e=await t.json();h(e.error||"Failed to create candidate")}}catch{h("An error occurred. Please try again.")}finally{s(!1)}};return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)("div",{className:"flex items-center justify-between",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(l(),{href:"/admin/candidates",className:"mr-4 p-2 text-gray-400 hover:text-gray-600",children:(0,r.jsx)(d.A,{className:"h-5 w-5"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Add New Candidate"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Register a new test candidate"})]})]})}),(0,r.jsx)("div",{className:"bg-white shadow rounded-lg",children:(0,r.jsxs)("form",{onSubmit:y,className:"p-6 space-y-6",children:[i&&(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm",children:i}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("h3",{className:"text-lg font-medium text-gray-900 mb-4 flex items-center",children:[(0,r.jsx)(o.A,{className:"h-5 w-5 mr-2"}),"Personal Information"]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"fullName",className:"block text-sm font-medium text-gray-700 mb-2",children:"Full Name *"}),(0,r.jsx)("input",{type:"text",id:"fullName",name:"fullName",value:p.fullName,onChange:b,required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter full name as on passport"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"dateOfBirth",className:"block text-sm font-medium text-gray-700 mb-2",children:"Date of Birth *"}),(0,r.jsx)("input",{type:"date",id:"dateOfBirth",name:"dateOfBirth",value:p.dateOfBirth,onChange:b,required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"nationality",className:"block text-sm font-medium text-gray-700 mb-2",children:"Nationality *"}),(0,r.jsx)("input",{type:"text",id:"nationality",name:"nationality",value:p.nationality,onChange:b,required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"e.g., British, American, etc."})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"passportNumber",className:"block text-sm font-medium text-gray-700 mb-2",children:"Passport Number *"}),(0,r.jsx)("input",{type:"text",id:"passportNumber",name:"passportNumber",value:p.passportNumber,onChange:b,required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter passport number"})]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("h3",{className:"text-lg font-medium text-gray-900 mb-4 flex items-center",children:[(0,r.jsx)(c.A,{className:"h-5 w-5 mr-2"}),"Contact Information"]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address *"}),(0,r.jsx)("input",{type:"email",id:"email",name:"email",value:p.email,onChange:b,required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"<EMAIL>"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"phoneNumber",className:"block text-sm font-medium text-gray-700 mb-2",children:"Phone Number *"}),(0,r.jsx)("input",{type:"tel",id:"phoneNumber",name:"phoneNumber",value:p.phoneNumber,onChange:b,required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"+44 ************"})]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("h3",{className:"text-lg font-medium text-gray-900 mb-4 flex items-center",children:[(0,r.jsx)(m.A,{className:"h-5 w-5 mr-2"}),"Test Information"]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"testDate",className:"block text-sm font-medium text-gray-700 mb-2",children:"Test Date *"}),(0,r.jsx)("input",{type:"date",id:"testDate",name:"testDate",value:p.testDate,onChange:b,required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"testCenter",className:"block text-sm font-medium text-gray-700 mb-2",children:"Test Center *"}),(0,r.jsxs)("select",{id:"testCenter",name:"testCenter",value:p.testCenter,onChange:b,required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,r.jsx)("option",{value:"",children:"Select test center"}),["IELTS Test Center - London","IELTS Test Center - Manchester","IELTS Test Center - Birmingham","IELTS Test Center - Edinburgh","IELTS Test Center - Cardiff","IELTS Test Center - Belfast","IELTS Test Center - Dublin","IELTS Test Center - Online"].map(e=>(0,r.jsx)("option",{value:e,children:e},e))]})]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("h3",{className:"text-lg font-medium text-gray-900 mb-4 flex items-center",children:[(0,r.jsx)(u,{className:"h-5 w-5 mr-2"}),"Candidate Photo"]}),(0,r.jsx)(v,{type:"photo",onUpload:e=>f(t=>({...t,photoUrl:e})),onRemove:()=>f(e=>({...e,photoUrl:""})),currentFile:p.photoUrl,className:"w-full"})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-4 pt-6 border-t border-gray-200",children:[(0,r.jsx)(l(),{href:"/admin/candidates",className:"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50",children:"Cancel"}),(0,r.jsx)("button",{type:"submit",disabled:t,className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50",children:t?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Creating..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Create Candidate"]})})]})]})})]})}},53760:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>f});var r=s(45512),a=s(98805),n=s(79334);s(58009);var i=s(28531),l=s.n(i),d=s(87137),o=s(64977),c=s(69855),m=s(79660),h=s(16873),u=s(61075);let x=(0,s(41680).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);var p=s(30722);function f({children:e}){let{data:t,status:s}=(0,a.wV)();if((0,n.useRouter)(),"loading"===s)return(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})});if(!t||t.user?.role!=="admin")return null;let i=[{name:"Dashboard",href:"/admin",icon:d.A},{name:"Candidates",href:"/admin/candidates",icon:o.A},{name:"Add Candidate",href:"/admin/candidates/new",icon:c.A},{name:"Test Results",href:"/admin/results",icon:m.A},{name:"Advanced Search",href:"/admin/search",icon:h.A},{name:"Reports",href:"/admin/reports",icon:u.A},{name:"Settings",href:"/admin/settings",icon:x}];return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)("div",{className:"fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg",children:(0,r.jsxs)("div",{className:"flex flex-col h-full",children:[(0,r.jsxs)("div",{className:"flex items-center px-6 py-4 border-b border-gray-200",children:[(0,r.jsx)(u.A,{className:"h-8 w-8 text-blue-600 mr-3"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-lg font-semibold text-gray-900",children:"IELTS Admin"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Certification System"})]})]}),(0,r.jsx)("nav",{className:"flex-1 px-4 py-6 space-y-2",children:i.map(e=>{let t=e.icon;return(0,r.jsxs)(l(),{href:e.href,className:"flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-100 hover:text-gray-900 group",children:[(0,r.jsx)(t,{className:"h-5 w-5 mr-3 text-gray-400 group-hover:text-gray-500"}),e.name]},e.name)})}),(0,r.jsxs)("div",{className:"border-t border-gray-200 p-4",children:[(0,r.jsx)("div",{className:"flex items-center mb-3",children:(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900",children:t.user?.name}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:t.user?.email}),(0,r.jsx)("p",{className:"text-xs text-blue-600 font-medium",children:"Administrator"})]})}),(0,r.jsxs)("button",{onClick:()=>(0,a.CI)({callbackUrl:"/"}),className:"flex items-center w-full px-3 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-100 hover:text-gray-900",children:[(0,r.jsx)(p.A,{className:"h-4 w-4 mr-3"}),"Sign out"]})]})]})}),(0,r.jsx)("div",{className:"pl-64",children:(0,r.jsx)("main",{className:"py-6",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:e})})})]})}},76515:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\candidates\\\\new\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\admin\\candidates\\new\\page.tsx","default")},96038:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\admin\\layout.tsx","default")},71354:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d,metadata:()=>l});var r=s(62740),a=s(85041),n=s.n(a),i=s(70452);s(61135);let l={title:"IELTS Certification System",description:"Professional IELTS test result management and certification system"};function d({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsx)("body",{className:n().className,children:(0,r.jsx)(i.SessionProvider,{children:e})})})}},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(88077);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},61135:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[638,8338,2367,8324],()=>s(38962));module.exports=r})();