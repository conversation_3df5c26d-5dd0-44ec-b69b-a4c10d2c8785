"use strict";exports.id=4681,exports.ids=[4681],exports.modules={46347:(e,t,r)=>{function n(){throw Error("`forbidden()` is experimental and only allowed to be enabled when `experimental.authInterrupts` is enabled.")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"forbidden",{enumerable:!0,get:function(){return n}}),r(26003).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},26003:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HTTPAccessErrorStatus:function(){return r},HTTP_ERROR_FALLBACK_ERROR_CODE:function(){return a},getAccessFallbackErrorTypeByStatus:function(){return s},getAccessFallbackHTTPStatus:function(){return o},isHTTPAccessFallbackError:function(){return i}});let r={NOT_FOUND:404,FORBIDDEN:403,UNAUTHORIZED:401},n=new Set(Object.values(r)),a="NEXT_HTTP_ERROR_FALLBACK";function i(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[t,r]=e.digest.split(";");return t===a&&n.has(Number(r))}function o(e){return Number(e.digest.split(";")[1])}function s(e){switch(e){case 401:return"unauthorized";case 403:return"forbidden";case 404:return"not-found";default:return}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},11271:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isNextRouterError",{enumerable:!0,get:function(){return i}});let n=r(26003),a=r(23543);function i(e){return(0,a.isRedirectError)(e)||(0,n.isHTTPAccessFallbackError)(e)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},67359:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return d},RedirectType:function(){return a.RedirectType},forbidden:function(){return o.forbidden},notFound:function(){return i.notFound},permanentRedirect:function(){return n.permanentRedirect},redirect:function(){return n.redirect},unauthorized:function(){return s.unauthorized},unstable_rethrow:function(){return c.unstable_rethrow}});let n=r(26552),a=r(23543),i=r(39274),o=r(46347),s=r(10590),c=r(51370);class l extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class d extends URLSearchParams{append(){throw new l}delete(){throw new l}set(){throw new l}sort(){throw new l}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},39274:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"notFound",{enumerable:!0,get:function(){return a}});let n=""+r(26003).HTTP_ERROR_FALLBACK_ERROR_CODE+";404";function a(){let e=Error(n);throw e.digest=n,e}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},23543:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{REDIRECT_ERROR_CODE:function(){return a},RedirectType:function(){return i},isRedirectError:function(){return o}});let n=r(11541),a="NEXT_REDIRECT";var i=function(e){return e.push="push",e.replace="replace",e}({});function o(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let t=e.digest.split(";"),[r,i]=t,o=t.slice(2,-2).join(";"),s=Number(t.at(-2));return r===a&&("replace"===i||"push"===i)&&"string"==typeof o&&!isNaN(s)&&s in n.RedirectStatusCode}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},11541:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RedirectStatusCode",{enumerable:!0,get:function(){return r}});var r=function(e){return e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect",e}({});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},26552:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getRedirectError:function(){return o},getRedirectStatusCodeFromError:function(){return u},getRedirectTypeFromError:function(){return d},getURLFromRedirectError:function(){return l},permanentRedirect:function(){return c},redirect:function(){return s}});let n=r(19121),a=r(11541),i=r(23543);function o(e,t,r){void 0===r&&(r=a.RedirectStatusCode.TemporaryRedirect);let n=Error(i.REDIRECT_ERROR_CODE);return n.digest=i.REDIRECT_ERROR_CODE+";"+t+";"+e+";"+r+";",n}function s(e,t){let r=n.actionAsyncStorage.getStore();throw o(e,t||((null==r?void 0:r.isAction)?i.RedirectType.push:i.RedirectType.replace),a.RedirectStatusCode.TemporaryRedirect)}function c(e,t){throw void 0===t&&(t=i.RedirectType.replace),o(e,t,a.RedirectStatusCode.PermanentRedirect)}function l(e){return(0,i.isRedirectError)(e)?e.digest.split(";").slice(2,-2).join(";"):null}function d(e){if(!(0,i.isRedirectError)(e))throw Error("Not a redirect error");return e.digest.split(";",2)[1]}function u(e){if(!(0,i.isRedirectError)(e))throw Error("Not a redirect error");return Number(e.digest.split(";").at(-2))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},10590:(e,t,r)=>{function n(){throw Error("`unauthorized()` is experimental and only allowed to be used when `experimental.authInterrupts` is enabled.")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unauthorized",{enumerable:!0,get:function(){return n}}),r(26003).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},51370:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return function e(t){if((0,o.isNextRouterError)(t)||(0,i.isBailoutToCSRError)(t)||(0,n.isDynamicUsageError)(t)||(0,a.isPostpone)(t))throw t;t instanceof Error&&"cause"in t&&e(t.cause)}}});let n=r(62349),a=r(67418),i=r(40627),o=r(11271);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},62349:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isDynamicUsageError",{enumerable:!0,get:function(){return s}});let n=r(42490),a=r(40627),i=r(11271),o=r(10436),s=e=>(0,n.isDynamicServerError)(e)||(0,a.isBailoutToCSRError)(e)||(0,i.isNextRouterError)(e)||(0,o.isDynamicPostpone)(e)},37301:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createDedupedByCallsiteServerErrorLoggerDev",{enumerable:!0,get:function(){return c}});let n=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=a(void 0);if(r&&r.has(e))return r.get(e);var n={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&Object.prototype.hasOwnProperty.call(e,o)){var s=i?Object.getOwnPropertyDescriptor(e,o):null;s&&(s.get||s.set)?Object.defineProperty(n,o,s):n[o]=e[o]}return n.default=e,r&&r.set(e,n),n}(r(76301));function a(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(a=function(e){return e?r:t})(e)}let i={current:null},o="function"==typeof n.cache?n.cache:e=>e,s=console.warn;function c(e){return function(...t){s(e(...t))}}o(e=>{try{s(i.current)}finally{i.current=null}})},67418:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isPostpone",{enumerable:!0,get:function(){return n}});let r=Symbol.for("react.postpone");function n(e){return"object"==typeof e&&null!==e&&e.$$typeof===r}},97200:(e,t,r)=>{Object.defineProperty(t,"U",{enumerable:!0,get:function(){return f}});let n=r(46620),a=r(9181),i=r(29294),o=r(63033),s=r(10436),c=r(82312),l=r(60457),d=r(37301),u=(r(676),r(24982));function f(){let e="cookies",t=i.workAsyncStorage.getStore(),r=o.workUnitAsyncStorage.getStore();if(t){if(r&&"after"===r.phase&&!(0,u.isRequestAPICallableInsideAfter)())throw Error(`Route ${t.route} used "cookies" inside "after(...)". This is not supported. If you need this data inside an "after" callback, use "cookies" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`);if(t.forceStatic)return h(n.RequestCookiesAdapter.seal(new a.RequestCookies(new Headers({}))));if(r){if("cache"===r.type)throw Error(`Route ${t.route} used "cookies" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "cookies" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`);if("unstable-cache"===r.type)throw Error(`Route ${t.route} used "cookies" inside a function cached with "unstable_cache(...)". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "cookies" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`)}if(t.dynamicShouldError)throw new c.StaticGenBailoutError(`Route ${t.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`cookies\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`);if(r){if("prerender"===r.type)return function(e,t){let r=p.get(t);if(r)return r;let n=(0,l.makeHangingPromise)(t.renderSignal,"`cookies()`");return p.set(t,n),Object.defineProperties(n,{[Symbol.iterator]:{value:function(){let r="`cookies()[Symbol.iterator]()`",n=b(e,r);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(e,r,n,t)}},size:{get(){let r="`cookies().size`",n=b(e,r);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(e,r,n,t)}},get:{value:function(){let r;r=0==arguments.length?"`cookies().get()`":`\`cookies().get(${y(arguments[0])})\``;let n=b(e,r);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(e,r,n,t)}},getAll:{value:function(){let r;r=0==arguments.length?"`cookies().getAll()`":`\`cookies().getAll(${y(arguments[0])})\``;let n=b(e,r);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(e,r,n,t)}},has:{value:function(){let r;r=0==arguments.length?"`cookies().has()`":`\`cookies().has(${y(arguments[0])})\``;let n=b(e,r);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(e,r,n,t)}},set:{value:function(){let r;if(0==arguments.length)r="`cookies().set()`";else{let e=arguments[0];r=e?`\`cookies().set(${y(e)}, ...)\``:"`cookies().set(...)`"}let n=b(e,r);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(e,r,n,t)}},delete:{value:function(){let r;r=0==arguments.length?"`cookies().delete()`":1==arguments.length?`\`cookies().delete(${y(arguments[0])})\``:`\`cookies().delete(${y(arguments[0])}, ...)\``;let n=b(e,r);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(e,r,n,t)}},clear:{value:function(){let r="`cookies().clear()`",n=b(e,r);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(e,r,n,t)}},toString:{value:function(){let r="`cookies().toString()`",n=b(e,r);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(e,r,n,t)}}}),n}(t.route,r);"prerender-ppr"===r.type?(0,s.postponeWithTracking)(t.route,e,r.dynamicTracking):"prerender-legacy"===r.type&&(0,s.throwToInterruptStaticGeneration)(e,t,r)}(0,s.trackDynamicDataInDynamicRender)(t,r)}let d=(0,o.getExpectedRequestStore)(e);return h((0,n.areCookiesMutableInCurrentPhase)(d)?d.userspaceMutableCookies:d.cookies)}let p=new WeakMap;function h(e){let t=p.get(e);if(t)return t;let r=Promise.resolve(e);return p.set(e,r),Object.defineProperties(r,{[Symbol.iterator]:{value:e[Symbol.iterator]?e[Symbol.iterator].bind(e):m.bind(e)},size:{get:()=>e.size},get:{value:e.get.bind(e)},getAll:{value:e.getAll.bind(e)},has:{value:e.has.bind(e)},set:{value:e.set.bind(e)},delete:{value:e.delete.bind(e)},clear:{value:"function"==typeof e.clear?e.clear.bind(e):x.bind(e,r)},toString:{value:e.toString.bind(e)}}),r}function y(e){return"object"==typeof e&&null!==e&&"string"==typeof e.name?`'${e.name}'`:"string"==typeof e?`'${e}'`:"..."}function b(e,t){let r=e?`Route "${e}" `:"This route ";return Error(`${r}used ${t}. \`cookies()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`)}function m(){return this.getAll().map(e=>[e.name,e]).values()}function x(e){for(let e of this.getAll())this.delete(e.name);return e}(0,d.createDedupedByCallsiteServerErrorLoggerDev)(b)},46250:(e,t,r)=>{let n=r(63033),a=r(29294),i=r(10436),o=r(37301),s=r(82312),c=r(42490);new WeakMap;class l{constructor(e){this._provider=e}get isEnabled(){return null!==this._provider&&this._provider.isEnabled}enable(){d("draftMode().enable()"),null!==this._provider&&this._provider.enable()}disable(){d("draftMode().disable()"),null!==this._provider&&this._provider.disable()}}function d(e){let t=a.workAsyncStorage.getStore(),r=n.workUnitAsyncStorage.getStore();if(t){if(r){if("cache"===r.type)throw Error(`Route ${t.route} used "${e}" inside "use cache". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`);if("unstable-cache"===r.type)throw Error(`Route ${t.route} used "${e}" inside a function cached with "unstable_cache(...)". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`);if("after"===r.phase)throw Error(`Route ${t.route} used "${e}" inside \`after\`. The enabled status of draftMode can be read inside \`after\` but you cannot enable or disable draftMode. See more info here: https://nextjs.org/docs/app/api-reference/functions/after`)}if(t.dynamicShouldError)throw new s.StaticGenBailoutError(`Route ${t.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`);if(r){if("prerender"===r.type){let n=Error(`Route ${t.route} used ${e} without first calling \`await connection()\`. See more info here: https://nextjs.org/docs/messages/next-prerender-sync-headers`);(0,i.abortAndThrowOnSynchronousRequestDataAccess)(t.route,e,n,r)}else if("prerender-ppr"===r.type)(0,i.postponeWithTracking)(t.route,e,r.dynamicTracking);else if("prerender-legacy"===r.type){r.revalidate=0;let n=new c.DynamicServerError(`Route ${t.route} couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`);throw t.dynamicUsageDescription=e,t.dynamicUsageStack=n.stack,n}}}}(0,o.createDedupedByCallsiteServerErrorLoggerDev)(function(e,t){let r=e?`Route "${e}" `:"This route ";return Error(`${r}used ${t}. \`draftMode()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`)})},83009:(e,t,r)=>{Object.defineProperty(t,"b",{enumerable:!0,get:function(){return u}});let n=r(9785),a=r(29294),i=r(63033),o=r(10436),s=r(82312),c=r(60457),l=r(37301),d=(r(676),r(24982));function u(){let e=a.workAsyncStorage.getStore(),t=i.workUnitAsyncStorage.getStore();if(e){if(t&&"after"===t.phase&&!(0,d.isRequestAPICallableInsideAfter)())throw Error(`Route ${e.route} used "headers" inside "after(...)". This is not supported. If you need this data inside an "after" callback, use "headers" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`);if(e.forceStatic)return p(n.HeadersAdapter.seal(new Headers({})));if(t){if("cache"===t.type)throw Error(`Route ${e.route} used "headers" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`);if("unstable-cache"===t.type)throw Error(`Route ${e.route} used "headers" inside a function cached with "unstable_cache(...)". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`)}if(e.dynamicShouldError)throw new s.StaticGenBailoutError(`Route ${e.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`headers\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`);if(t){if("prerender"===t.type)return function(e,t){let r=f.get(t);if(r)return r;let n=(0,c.makeHangingPromise)(t.renderSignal,"`headers()`");return f.set(t,n),Object.defineProperties(n,{append:{value:function(){let r=`\`headers().append(${h(arguments[0])}, ...)\``,n=y(e,r);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(e,r,n,t)}},delete:{value:function(){let r=`\`headers().delete(${h(arguments[0])})\``,n=y(e,r);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(e,r,n,t)}},get:{value:function(){let r=`\`headers().get(${h(arguments[0])})\``,n=y(e,r);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(e,r,n,t)}},has:{value:function(){let r=`\`headers().has(${h(arguments[0])})\``,n=y(e,r);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(e,r,n,t)}},set:{value:function(){let r=`\`headers().set(${h(arguments[0])}, ...)\``,n=y(e,r);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(e,r,n,t)}},getSetCookie:{value:function(){let r="`headers().getSetCookie()`",n=y(e,r);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(e,r,n,t)}},forEach:{value:function(){let r="`headers().forEach(...)`",n=y(e,r);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(e,r,n,t)}},keys:{value:function(){let r="`headers().keys()`",n=y(e,r);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(e,r,n,t)}},values:{value:function(){let r="`headers().values()`",n=y(e,r);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(e,r,n,t)}},entries:{value:function(){let r="`headers().entries()`",n=y(e,r);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(e,r,n,t)}},[Symbol.iterator]:{value:function(){let r="`headers()[Symbol.iterator]()`",n=y(e,r);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(e,r,n,t)}}}),n}(e.route,t);"prerender-ppr"===t.type?(0,o.postponeWithTracking)(e.route,"headers",t.dynamicTracking):"prerender-legacy"===t.type&&(0,o.throwToInterruptStaticGeneration)("headers",e,t)}(0,o.trackDynamicDataInDynamicRender)(e,t)}return p((0,i.getExpectedRequestStore)("headers").headers)}let f=new WeakMap;function p(e){let t=f.get(e);if(t)return t;let r=Promise.resolve(e);return f.set(e,r),Object.defineProperties(r,{append:{value:e.append.bind(e)},delete:{value:e.delete.bind(e)},get:{value:e.get.bind(e)},has:{value:e.has.bind(e)},set:{value:e.set.bind(e)},getSetCookie:{value:e.getSetCookie.bind(e)},forEach:{value:e.forEach.bind(e)},keys:{value:e.keys.bind(e)},values:{value:e.values.bind(e)},entries:{value:e.entries.bind(e)},[Symbol.iterator]:{value:e[Symbol.iterator].bind(e)}}),r}function h(e){return"string"==typeof e?`'${e}'`:"..."}function y(e,t){let r=e?`Route "${e}" `:"This route ";return Error(`${r}used ${t}. \`headers()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`)}(0,l.createDedupedByCallsiteServerErrorLoggerDev)(y)},9785:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HeadersAdapter:function(){return i},ReadonlyHeadersError:function(){return a}});let n=r(20614);class a extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new a}}class i extends Headers{constructor(e){super(),this.headers=new Proxy(e,{get(t,r,a){if("symbol"==typeof r)return n.ReflectAdapter.get(t,r,a);let i=r.toLowerCase(),o=Object.keys(e).find(e=>e.toLowerCase()===i);if(void 0!==o)return n.ReflectAdapter.get(t,o,a)},set(t,r,a,i){if("symbol"==typeof r)return n.ReflectAdapter.set(t,r,a,i);let o=r.toLowerCase(),s=Object.keys(e).find(e=>e.toLowerCase()===o);return n.ReflectAdapter.set(t,s??r,a,i)},has(t,r){if("symbol"==typeof r)return n.ReflectAdapter.has(t,r);let a=r.toLowerCase(),i=Object.keys(e).find(e=>e.toLowerCase()===a);return void 0!==i&&n.ReflectAdapter.has(t,i)},deleteProperty(t,r){if("symbol"==typeof r)return n.ReflectAdapter.deleteProperty(t,r);let a=r.toLowerCase(),i=Object.keys(e).find(e=>e.toLowerCase()===a);return void 0===i||n.ReflectAdapter.deleteProperty(t,i)}})}static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"append":case"delete":case"set":return a.callable;default:return n.ReflectAdapter.get(e,t,r)}}})}merge(e){return Array.isArray(e)?e.join(", "):e}static from(e){return e instanceof Headers?e:new i(e)}append(e,t){let r=this.headers[e];"string"==typeof r?this.headers[e]=[r,t]:Array.isArray(r)?r.push(t):this.headers[e]=t}delete(e){delete this.headers[e]}get(e){let t=this.headers[e];return void 0!==t?this.merge(t):null}has(e){return void 0!==this.headers[e]}set(e,t){this.headers[e]=t}forEach(e,t){for(let[r,n]of this.entries())e.call(t,n,r,this)}*entries(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase(),r=this.get(t);yield[t,r]}}*keys(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase();yield t}}*values(){for(let e of Object.keys(this.headers)){let t=this.get(e);yield t}}[Symbol.iterator](){return this.entries()}}},46620:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{MutableRequestCookiesAdapter:function(){return f},ReadonlyRequestCookiesError:function(){return s},RequestCookiesAdapter:function(){return c},appendMutableCookies:function(){return u},areCookiesMutableInCurrentPhase:function(){return h},getModifiedCookieValues:function(){return d},responseCookiesToRequestCookies:function(){return b},wrapWithMutableAccessCheck:function(){return p}});let n=r(9181),a=r(20614),i=r(29294),o=r(63033);class s extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#options")}static callable(){throw new s}}class c{static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"clear":case"delete":case"set":return s.callable;default:return a.ReflectAdapter.get(e,t,r)}}})}}let l=Symbol.for("next.mutated.cookies");function d(e){let t=e[l];return t&&Array.isArray(t)&&0!==t.length?t:[]}function u(e,t){let r=d(t);if(0===r.length)return!1;let a=new n.ResponseCookies(e),i=a.getAll();for(let e of r)a.set(e);for(let e of i)a.set(e);return!0}class f{static wrap(e,t){let r=new n.ResponseCookies(new Headers);for(let t of e.getAll())r.set(t);let o=[],s=new Set,c=()=>{let e=i.workAsyncStorage.getStore();if(e&&(e.pathWasRevalidated=!0),o=r.getAll().filter(e=>s.has(e.name)),t){let e=[];for(let t of o){let r=new n.ResponseCookies(new Headers);r.set(t),e.push(r.toString())}t(e)}},d=new Proxy(r,{get(e,t,r){switch(t){case l:return o;case"delete":return function(...t){s.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.delete(...t),d}finally{c()}};case"set":return function(...t){s.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.set(...t),d}finally{c()}};default:return a.ReflectAdapter.get(e,t,r)}}});return d}}function p(e){let t=new Proxy(e,{get(e,r,n){switch(r){case"delete":return function(...r){return y("cookies().delete"),e.delete(...r),t};case"set":return function(...r){return y("cookies().set"),e.set(...r),t};default:return a.ReflectAdapter.get(e,r,n)}}});return t}function h(e){return"action"===e.phase}function y(e){if(!h((0,o.getExpectedRequestStore)(e)))throw new s}function b(e){let t=new n.RequestCookies(new Headers);for(let r of e.getAll())t.set(r);return t}},40627:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{BailoutToCSRError:function(){return n},isBailoutToCSRError:function(){return a}});let r="BAILOUT_TO_CLIENT_SIDE_RENDERING";class n extends Error{constructor(e){super("Bail out to client-side rendering: "+e),this.reason=e,this.digest=r}}function a(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===r}},31648:(e,t,r)=>{r.d(t,{A:()=>n});function n(e){return{id:"credentials",name:"Credentials",type:"credentials",credentials:{},authorize:()=>null,options:e}}},34926:(e,t,r)=>{r.d(t,{Ay:()=>E});var n=r(55511),a=null;function i(e,t){if("number"!=typeof(e=e||m))throw Error("Illegal arguments: "+typeof e+", "+typeof t);e<4?e=4:e>31&&(e=31);var r=[];return r.push("$2b$"),e<10&&r.push("0"),r.push(e.toString()),r.push("$"),r.push(h(function(e){try{return crypto.getRandomValues(new Uint8Array(e))}catch{}try{return n.randomBytes(e)}catch{}if(!a)throw Error("Neither WebCryptoAPI nor a crypto module is available. Use bcrypt.setRandomFallback to set an alternative");return a(e)}(b),b)),r.join("")}function o(e,t,r){if("function"==typeof t&&(r=t,t=void 0),"function"==typeof e&&(r=e,e=void 0),void 0===e)e=m;else if("number"!=typeof e)throw Error("illegal arguments: "+typeof e);function n(t){d(function(){try{t(null,i(e))}catch(e){t(e)}})}if(!r)return new Promise(function(e,t){n(function(r,n){if(r){t(r);return}e(n)})});if("function"!=typeof r)throw Error("Illegal callback: "+typeof r);n(r)}function s(e,t){if(void 0===t&&(t=m),"number"==typeof t&&(t=i(t)),"string"!=typeof e||"string"!=typeof t)throw Error("Illegal arguments: "+typeof e+", "+typeof t);return S(e,t)}function c(e,t,r,n){function a(r){"string"==typeof e&&"number"==typeof t?o(t,function(t,a){S(e,a,r,n)}):"string"==typeof e&&"string"==typeof t?S(e,t,r,n):d(r.bind(this,Error("Illegal arguments: "+typeof e+", "+typeof t)))}if(!r)return new Promise(function(e,t){a(function(r,n){if(r){t(r);return}e(n)})});if("function"!=typeof r)throw Error("Illegal callback: "+typeof r);a(r)}function l(e,t){for(var r=e.length^t.length,n=0;n<e.length;++n)r|=e.charCodeAt(n)^t.charCodeAt(n);return 0===r}var d="undefined"!=typeof process&&process&&"function"==typeof process.nextTick?"function"==typeof setImmediate?setImmediate:process.nextTick:setTimeout;function u(e){for(var t=0,r=0,n=0;n<e.length;++n)(r=e.charCodeAt(n))<128?t+=1:r<2048?t+=2:(64512&r)==55296&&(64512&e.charCodeAt(n+1))==56320?(++n,t+=4):t+=3;return t}var f="./ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),p=[-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,0,1,54,55,56,57,58,59,60,61,62,63,-1,-1,-1,-1,-1,-1,-1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,-1,-1,-1,-1,-1,-1,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,-1,-1,-1,-1,-1];function h(e,t){var r,n,a=0,i=[];if(t<=0||t>e.length)throw Error("Illegal len: "+t);for(;a<t;){if(r=255&e[a++],i.push(f[r>>2&63]),r=(3&r)<<4,a>=t||(r|=(n=255&e[a++])>>4&15,i.push(f[63&r]),r=(15&n)<<2,a>=t)){i.push(f[63&r]);break}r|=(n=255&e[a++])>>6&3,i.push(f[63&r]),i.push(f[63&n])}return i.join("")}function y(e,t){var r,n,a,i,o,s=0,c=e.length,l=0,d=[];if(t<=0)throw Error("Illegal len: "+t);for(;s<c-1&&l<t&&(r=(o=e.charCodeAt(s++))<p.length?p[o]:-1,n=(o=e.charCodeAt(s++))<p.length?p[o]:-1,-1!=r&&-1!=n)&&(i=r<<2>>>0|(48&n)>>4,d.push(String.fromCharCode(i)),!(++l>=t||s>=c||-1==(a=(o=e.charCodeAt(s++))<p.length?p[o]:-1)||(i=(15&n)<<4>>>0|(60&a)>>2,d.push(String.fromCharCode(i)),++l>=t||s>=c)));)i=(3&a)<<6>>>0|((o=e.charCodeAt(s++))<p.length?p[o]:-1),d.push(String.fromCharCode(i)),++l;var u=[];for(s=0;s<l;s++)u.push(d[s].charCodeAt(0));return u}var b=16,m=10,x=[0x243f6a88,0x85a308d3,0x13198a2e,0x3707344,0xa4093822,0x299f31d0,0x82efa98,0xec4e6c89,0x452821e6,0x38d01377,0xbe5466cf,0x34e90c6c,0xc0ac29b7,0xc97c50dd,0x3f84d5b5,0xb5470917,0x9216d5d9,0x8979fb1b],g=[0xd1310ba6,0x98dfb5ac,0x2ffd72db,0xd01adfb7,0xb8e1afed,0x6a267e96,0xba7c9045,0xf12c7f99,0x24a19947,0xb3916cf7,0x801f2e2,0x858efc16,0x636920d8,0x71574e69,0xa458fea3,0xf4933d7e,0xd95748f,0x728eb658,0x718bcd58,0x82154aee,0x7b54a41d,0xc25a59b5,0x9c30d539,0x2af26013,0xc5d1b023,0x286085f0,0xca417918,0xb8db38ef,0x8e79dcb0,0x603a180e,0x6c9e0e8b,0xb01e8a3e,0xd71577c1,0xbd314b27,0x78af2fda,0x55605c60,0xe65525f3,0xaa55ab94,0x57489862,0x63e81440,0x55ca396a,0x2aab10b6,0xb4cc5c34,0x1141e8ce,0xa15486af,0x7c72e993,0xb3ee1411,0x636fbc2a,0x2ba9c55d,0x741831f6,0xce5c3e16,0x9b87931e,0xafd6ba33,0x6c24cf5c,0x7a325381,0x28958677,0x3b8f4898,0x6b4bb9af,0xc4bfe81b,0x66282193,0x61d809cc,0xfb21a991,0x487cac60,0x5dec8032,0xef845d5d,0xe98575b1,0xdc262302,0xeb651b88,0x23893e81,0xd396acc5,0xf6d6ff3,0x83f44239,0x2e0b4482,0xa4842004,0x69c8f04a,0x9e1f9b5e,0x21c66842,0xf6e96c9a,0x670c9c61,0xabd388f0,0x6a51a0d2,0xd8542f68,0x960fa728,0xab5133a3,0x6eef0b6c,0x137a3be4,0xba3bf050,0x7efb2a98,0xa1f1651d,0x39af0176,0x66ca593e,0x82430e88,0x8cee8619,0x456f9fb4,0x7d84a5c3,0x3b8b5ebe,0xe06f75d8,0x85c12073,0x401a449f,0x56c16aa6,0x4ed3aa62,0x363f7706,0x1bfedf72,0x429b023d,0x37d0d724,0xd00a1248,0xdb0fead3,0x49f1c09b,0x75372c9,0x80991b7b,0x25d479d8,0xf6e8def7,0xe3fe501a,0xb6794c3b,0x976ce0bd,0x4c006ba,0xc1a94fb6,0x409f60c4,0x5e5c9ec2,0x196a2463,0x68fb6faf,0x3e6c53b5,0x1339b2eb,0x3b52ec6f,0x6dfc511f,0x9b30952c,0xcc814544,0xaf5ebd09,0xbee3d004,0xde334afd,0x660f2807,0x192e4bb3,0xc0cba857,0x45c8740f,0xd20b5f39,0xb9d3fbdb,0x5579c0bd,0x1a60320a,0xd6a100c6,0x402c7279,0x679f25fe,0xfb1fa3cc,0x8ea5e9f8,0xdb3222f8,0x3c7516df,0xfd616b15,0x2f501ec8,0xad0552ab,0x323db5fa,0xfd238760,0x53317b48,0x3e00df82,0x9e5c57bb,0xca6f8ca0,0x1a87562e,0xdf1769db,0xd542a8f6,0x287effc3,0xac6732c6,0x8c4f5573,0x695b27b0,0xbbca58c8,0xe1ffa35d,0xb8f011a0,0x10fa3d98,0xfd2183b8,0x4afcb56c,0x2dd1d35b,0x9a53e479,0xb6f84565,0xd28e49bc,0x4bfb9790,0xe1ddf2da,0xa4cb7e33,0x62fb1341,0xcee4c6e8,0xef20cada,0x36774c01,0xd07e9efe,0x2bf11fb4,0x95dbda4d,0xae909198,0xeaad8e71,0x6b93d5a0,0xd08ed1d0,0xafc725e0,0x8e3c5b2f,0x8e7594b7,0x8ff6e2fb,0xf2122b64,0x8888b812,0x900df01c,0x4fad5ea0,0x688fc31c,0xd1cff191,0xb3a8c1ad,0x2f2f2218,0xbe0e1777,0xea752dfe,0x8b021fa1,0xe5a0cc0f,0xb56f74e8,0x18acf3d6,0xce89e299,0xb4a84fe0,0xfd13e0b7,0x7cc43b81,0xd2ada8d9,0x165fa266,0x80957705,0x93cc7314,0x211a1477,0xe6ad2065,0x77b5fa86,0xc75442f5,0xfb9d35cf,0xebcdaf0c,0x7b3e89a0,0xd6411bd3,0xae1e7e49,2428461,0x2071b35e,0x226800bb,0x57b8e0af,0x2464369b,0xf009b91e,0x5563911d,0x59dfa6aa,0x78c14389,0xd95a537f,0x207d5ba2,0x2e5b9c5,0x83260376,0x6295cfa9,0x11c81968,0x4e734a41,0xb3472dca,0x7b14a94a,0x1b510052,0x9a532915,0xd60f573f,0xbc9bc6e4,0x2b60a476,0x81e67400,0x8ba6fb5,0x571be91f,0xf296ec6b,0x2a0dd915,0xb6636521,0xe7b9f9b6,0xff34052e,0xc5855664,0x53b02d5d,0xa99f8fa1,0x8ba4799,0x6e85076a,0x4b7a70e9,0xb5b32944,0xdb75092e,0xc4192623,290971e4,0x49a7df7d,0x9cee60b8,0x8fedb266,0xecaa8c71,0x699a17ff,0x5664526c,0xc2b19ee1,0x193602a5,0x75094c29,0xa0591340,0xe4183a3e,0x3f54989a,0x5b429d65,0x6b8fe4d6,0x99f73fd6,0xa1d29c07,0xefe830f5,0x4d2d38e6,0xf0255dc1,0x4cdd2086,0x8470eb26,0x6382e9c6,0x21ecc5e,0x9686b3f,0x3ebaefc9,0x3c971814,0x6b6a70a1,0x687f3584,0x52a0e286,0xb79c5305,0xaa500737,0x3e07841c,0x7fdeae5c,0x8e7d44ec,0x5716f2b8,0xb03ada37,0xf0500c0d,0xf01c1f04,0x200b3ff,0xae0cf51a,0x3cb574b2,0x25837a58,0xdc0921bd,0xd19113f9,0x7ca92ff6,0x94324773,0x22f54701,0x3ae5e581,0x37c2dadc,0xc8b57634,0x9af3dda7,0xa9446146,0xfd0030e,0xecc8c73e,0xa4751e41,0xe238cd99,0x3bea0e2f,0x3280bba1,0x183eb331,0x4e548b38,0x4f6db908,0x6f420d03,0xf60a04bf,0x2cb81290,0x24977c79,0x5679b072,0xbcaf89af,0xde9a771f,0xd9930810,0xb38bae12,0xdccf3f2e,0x5512721f,0x2e6b7124,0x501adde6,0x9f84cd87,0x7a584718,0x7408da17,0xbc9f9abc,0xe94b7d8c,0xec7aec3a,0xdb851dfa,0x63094366,0xc464c3d2,0xef1c1847,0x3215d908,0xdd433b37,0x24c2ba16,0x12a14d43,0x2a65c451,0x50940002,0x133ae4dd,0x71dff89e,0x10314e55,0x81ac77d6,0x5f11199b,0x43556f1,0xd7a3c76b,0x3c11183b,0x5924a509,0xf28fe6ed,0x97f1fbfa,0x9ebabf2c,0x1e153c6e,0x86e34570,0xeae96fb1,0x860e5e0a,0x5a3e2ab3,0x771fe71c,0x4e3d06fa,0x2965dcb9,0x99e71d0f,0x803e89d6,0x5266c825,0x2e4cc978,0x9c10b36a,0xc6150eba,0x94e2ea78,0xa5fc3c53,0x1e0a2df4,0xf2f74ea7,0x361d2b3d,0x1939260f,0x19c27960,0x5223a708,0xf71312b6,0xebadfe6e,0xeac31f66,0xe3bc4595,0xa67bc883,0xb17f37d1,0x18cff28,0xc332ddef,0xbe6c5aa5,0x65582185,0x68ab9802,0xeecea50f,0xdb2f953b,0x2aef7dad,0x5b6e2f84,0x1521b628,0x29076170,0xecdd4775,0x619f1510,0x13cca830,0xeb61bd96,0x334fe1e,0xaa0363cf,0xb5735c90,0x4c70a239,0xd59e9e0b,0xcbaade14,0xeecc86bc,0x60622ca7,0x9cab5cab,0xb2f3846e,0x648b1eaf,0x19bdf0ca,0xa02369b9,0x655abb50,0x40685a32,0x3c2ab4b3,0x319ee9d5,0xc021b8f7,0x9b540b19,0x875fa099,0x95f7997e,0x623d7da8,0xf837889a,0x97e32d77,0x11ed935f,0x16681281,0xe358829,0xc7e61fd6,0x96dedfa1,0x7858ba99,0x57f584a5,0x1b227263,0x9b83c3ff,0x1ac24696,0xcdb30aeb,0x532e3054,0x8fd948e4,0x6dbc3128,0x58ebf2ef,0x34c6ffea,0xfe28ed61,0xee7c3c73,0x5d4a14d9,0xe864b7e3,0x42105d14,0x203e13e0,0x45eee2b6,0xa3aaabea,0xdb6c4f15,0xfacb4fd0,0xc742f442,0xef6abbb5,0x654f3b1d,0x41cd2105,0xd81e799e,0x86854dc7,0xe44b476a,0x3d816250,0xcf62a1f2,0x5b8d2646,0xfc8883a0,0xc1c7b6a3,0x7f1524c3,0x69cb7492,0x47848a0b,0x5692b285,0x95bbf00,0xad19489d,0x1462b174,0x23820e00,0x58428d2a,0xc55f5ea,0x1dadf43e,0x233f7061,0x3372f092,0x8d937e41,0xd65fecf1,0x6c223bdb,0x7cde3759,0xcbee7460,0x4085f2a7,0xce77326e,0xa6078084,0x19f8509e,0xe8efd855,0x61d99735,0xa969a7aa,0xc50c06c2,0x5a04abfc,0x800bcadc,0x9e447a2e,0xc3453484,0xfdd56705,0xe1e9ec9,0xdb73dbd3,0x105588cd,0x675fda79,0xe3674340,0xc5c43465,0x713e38d8,0x3d28f89e,0xf16dff20,0x153e21e7,0x8fb03d4a,0xe6e39f2b,0xdb83adf7,0xe93d5a68,0x948140f7,0xf64c261c,0x94692934,0x411520f7,0x7602d4f7,0xbcf46b2e,0xd4a20068,0xd4082471,0x3320f46a,0x43b7d4b7,0x500061af,0x1e39f62e,0x97244546,0x14214f74,0xbf8b8840,0x4d95fc1d,0x96b591af,0x70f4ddd3,0x66a02f45,0xbfbc09ec,0x3bd9785,0x7fac6dd0,0x31cb8504,0x96eb27b3,0x55fd3941,0xda2547e6,0xabca0a9a,0x28507825,0x530429f4,0xa2c86da,0xe9b66dfb,0x68dc1462,0xd7486900,0x680ec0a4,0x27a18dee,0x4f3ffea2,0xe887ad8c,0xb58ce006,0x7af4d6b6,0xaace1e7c,0xd3375fec,0xce78a399,0x406b2a42,0x20fe9e35,0xd9f385b9,0xee39d7ab,0x3b124e8b,0x1dc9faf7,0x4b6d1856,0x26a36631,0xeae397b2,0x3a6efa74,0xdd5b4332,0x6841e7f7,0xca7820fb,0xfb0af54e,0xd8feb397,0x454056ac,0xba489527,0x55533a3a,0x20838d87,0xfe6ba9b7,0xd096954b,0x55a867bc,0xa1159a58,0xcca92963,0x99e1db33,0xa62a4a56,0x3f3125f9,0x5ef47e1c,0x9029317c,0xfdf8e802,0x4272f70,0x80bb155c,0x5282ce3,0x95c11548,0xe4c66d22,0x48c1133f,0xc70f86dc,0x7f9c9ee,0x41041f0f,0x404779a4,0x5d886e17,0x325f51eb,0xd59bc0d1,0xf2bcc18f,0x41113564,0x257b7834,0x602a9c60,0xdff8e8a3,0x1f636c1b,0xe12b4c2,0x2e1329e,0xaf664fd1,0xcad18115,0x6b2395e0,0x333e92e1,0x3b240b62,0xeebeb922,0x85b2a20e,0xe6ba0d99,0xde720c8c,0x2da2f728,0xd0127845,0x95b794fd,0x647d0862,0xe7ccf5f0,0x5449a36f,0x877d48fa,0xc39dfd27,0xf33e8d1e,0xa476341,0x992eff74,0x3a6f6eab,0xf4f8fd37,0xa812dc60,0xa1ebddf8,0x991be14c,0xdb6e6b0d,0xc67b5510,0x6d672c37,0x2765d43b,0xdcd0e804,0xf1290dc7,0xcc00ffa3,0xb5390f92,0x690fed0b,0x667b9ffb,0xcedb7d9c,0xa091cf0b,0xd9155ea3,0xbb132f88,0x515bad24,0x7b9479bf,0x763bd6eb,0x37392eb3,0xcc115979,0x8026e297,0xf42e312d,0x6842ada7,0xc66a2b3b,0x12754ccc,0x782ef11c,0x6a124237,0xb79251e7,0x6a1bbe6,0x4bfb6350,0x1a6b1018,0x11caedfa,0x3d25bdd8,0xe2e1c3c9,0x44421659,0xa121386,0xd90cec6e,0xd5abea2a,0x64af674e,0xda86a85f,0xbebfe988,0x64e4c3fe,0x9dbc8057,0xf0f7c086,0x60787bf8,0x6003604d,0xd1fd8346,0xf6381fb0,0x7745ae04,0xd736fccc,0x83426b33,0xf01eab71,0xb0804187,0x3c005e5f,0x77a057be,0xbde8ae24,0x55464299,0xbf582e61,0x4e58f48f,0xf2ddfda2,0xf474ef38,0x8789bdc2,0x5366f9c3,0xc8b38e74,0xb475f255,0x46fcd9b9,0x7aeb2661,0x8b1ddf84,0x846a0e79,0x915f95e2,0x466e598e,0x20b45770,0x8cd55591,0xc902de4c,0xb90bace1,0xbb8205d0,0x11a86248,0x7574a99e,0xb77f19b6,0xe0a9dc09,0x662d09a1,0xc4324633,0xe85a1f02,0x9f0be8c,0x4a99a025,0x1d6efe10,0x1ab93d1d,0xba5a4df,0xa186f20f,0x2868f169,0xdcb7da83,0x573906fe,0xa1e2ce9b,0x4fcd7f52,0x50115e01,0xa70683fa,0xa002b5c4,0xde6d027,0x9af88c27,0x773f8641,0xc3604c06,0x61a806b5,0xf0177a28,0xc0f586e0,6314154,0x30dc7d62,0x11e69ed7,0x2338ea63,0x53c2dd94,0xc2c21634,0xbbcbee56,0x90bcb6de,0xebfc7da1,0xce591d76,0x6f05e409,0x4b7c0188,0x39720a3d,0x7c927c24,0x86e3725f,0x724d9db9,0x1ac15bb4,0xd39eb8fc,0xed545578,0x8fca5b5,0xd83d7cd3,0x4dad0fc4,0x1e50ef5e,0xb161e6f8,0xa28514d9,0x6c51133c,0x6fd5c7e7,0x56e14ec4,0x362abfce,0xddc6c837,0xd79a3234,0x92638212,0x670efa8e,0x406000e0,0x3a39ce37,0xd3faf5cf,0xabc27737,0x5ac52d1b,0x5cb0679e,0x4fa33742,0xd3822740,0x99bc9bbe,0xd5118e9d,0xbf0f7315,0xd62d1c7e,0xc700c47b,0xb78c1b6b,0x21a19045,0xb26eb1be,0x6a366eb4,0x5748ab2f,0xbc946e79,0xc6a376d2,0x6549c2c8,0x530ff8ee,0x468dde7d,0xd5730a1d,0x4cd04dc6,0x2939bbdb,0xa9ba4650,0xac9526e8,0xbe5ee304,0xa1fad5f0,0x6a2d519a,0x63ef8ce2,0x9a86ee22,0xc089c2b8,0x43242ef6,0xa51e03aa,0x9cf2d0a4,0x83c061ba,0x9be96a4d,0x8fe51550,0xba645bd6,0x2826a2f9,0xa73a3ae1,0x4ba99586,0xef5562e9,0xc72fefd3,0xf752f7da,0x3f046f69,0x77fa0a59,0x80e4a915,0x87b08601,0x9b09e6ad,0x3b3ee593,0xe990fd5a,0x9e34d797,0x2cf0b7d9,0x22b8b51,0x96d5ac3a,0x17da67d,0xd1cf3ed6,0x7c7d2d28,0x1f9f25cf,0xadf2b89b,0x5ad6b472,0x5a88f54c,0xe029ac71,0xe019a5e6,0x47b0acfd,0xed93fa9b,0xe8d3c48d,0x283b57cc,0xf8d56629,0x79132e28,0x785f0191,0xed756055,0xf7960e44,0xe3d35e8c,0x15056dd4,0x88f46dba,0x3a16125,0x564f0bd,0xc3eb9e15,0x3c9057a2,0x97271aec,0xa93a072a,0x1b3f6d9b,0x1e6321f5,0xf59c66fb,0x26dcf319,0x7533d928,0xb155fdf5,0x3563482,0x8aba3cbb,0x28517711,0xc20ad9f8,0xabcc5167,0xccad925f,0x4de81751,0x3830dc8e,0x379d5862,0x9320f991,0xea7a90c2,0xfb3e7bce,0x5121ce64,0x774fbe32,0xa8b6e37e,0xc3293d46,0x48de5369,0x6413e680,0xa2ae0810,0xdd6db224,0x69852dfd,0x9072166,0xb39a460a,0x6445c0dd,0x586cdecf,0x1c20c8ae,0x5bbef7dd,0x1b588d40,0xccd2017f,0x6bb4e3bb,0xdda26a7e,0x3a59ff45,0x3e350a44,0xbcb4cdd5,0x72eacea8,0xfa6484bb,0x8d6612ae,0xbf3c6f47,0xd29be463,0x542f5d9e,0xaec2771b,0xf64e6370,0x740e0d8d,0xe75b1357,0xf8721671,0xaf537d5d,0x4040cb08,0x4eb4e2cc,0x34d2466a,0x115af84,3786409e3,0x95983a1d,0x6b89fb4,0xce6ea048,0x6f3f3b82,0x3520ab82,0x11a1d4b,0x277227f8,0x611560b1,0xe7933fdc,0xbb3a792b,0x344525bd,0xa08839e1,0x51ce794b,0x2f32c9b7,0xa01fbac9,0xe01cc87e,0xbcc7d1f6,0xcf0111c3,0xa1e8aac7,0x1a908749,0xd44fbd9a,0xd0dadecb,0xd50ada38,0x339c32a,0xc6913667,0x8df9317c,0xe0b12b4f,0xf79e59b7,0x43f5bb3a,0xf2d519ff,0x27d9459c,0xbf97222c,0x15e6fc2a,0xf91fc71,0x9b941525,0xfae59361,0xceb69ceb,0xc2a86459,0x12baa8d1,0xb6c1075e,0xe3056a0c,0x10d25065,0xcb03a442,0xe0ec6e0e,0x1698db3b,0x4c98a0be,0x3278e964,0x9f1f9532,0xe0d392df,0xd3a0342b,0x8971f21e,0x1b0a7441,0x4ba3348c,0xc5be7120,0xc37632d8,0xdf359f8d,0x9b992f2e,0xe60b6f47,0xfe3f11d,0xe54cda54,0x1edad891,0xce6279cf,0xcd3e7e6f,0x1618b166,0xfd2c1d05,0x848fd2c5,0xf6fb2299,0xf523f357,0xa6327623,0x93a83531,0x56cccd02,0xacf08162,0x5a75ebb5,0x6e163697,0x88d273cc,0xde966292,0x81b949d0,0x4c50901b,0x71c65614,0xe6c6c7bd,0x327a140a,0x45e1d006,0xc3f27b9a,0xc9aa53fd,0x62a80f00,0xbb25bfe2,0x35bdd2f6,0x71126905,0xb2040222,0xb6cbcf7c,0xcd769c2b,0x53113ec0,0x1640e3d3,0x38abbd60,0x2547adf0,0xba38209c,0xf746ce76,0x77afa1c5,0x20756060,0x85cbfe4e,0x8ae88dd8,0x7aaaf9b0,0x4cf9aa7e,0x1948c25c,0x2fb8a8c,0x1c36ae4,0xd6ebe1f9,0x90d4f869,0xa65cdea0,0x3f09252d,0xc208e69f,0xb74e6132,0xce77e25b,0x578fdfe3,0x3ac372e6],w=[0x4f727068,0x65616e42,0x65686f6c,0x64657253,0x63727944,0x6f756274];function _(e,t,r,n){var a=e[t],i=e[t+1];return a^=r[0],i^=(n[a>>>24]+n[256|a>>16&255]^n[512|a>>8&255])+n[768|255&a]^r[1],a^=(n[i>>>24]+n[256|i>>16&255]^n[512|i>>8&255])+n[768|255&i]^r[2],i^=(n[a>>>24]+n[256|a>>16&255]^n[512|a>>8&255])+n[768|255&a]^r[3],a^=(n[i>>>24]+n[256|i>>16&255]^n[512|i>>8&255])+n[768|255&i]^r[4],i^=(n[a>>>24]+n[256|a>>16&255]^n[512|a>>8&255])+n[768|255&a]^r[5],a^=(n[i>>>24]+n[256|i>>16&255]^n[512|i>>8&255])+n[768|255&i]^r[6],i^=(n[a>>>24]+n[256|a>>16&255]^n[512|a>>8&255])+n[768|255&a]^r[7],a^=(n[i>>>24]+n[256|i>>16&255]^n[512|i>>8&255])+n[768|255&i]^r[8],i^=(n[a>>>24]+n[256|a>>16&255]^n[512|a>>8&255])+n[768|255&a]^r[9],a^=(n[i>>>24]+n[256|i>>16&255]^n[512|i>>8&255])+n[768|255&i]^r[10],i^=(n[a>>>24]+n[256|a>>16&255]^n[512|a>>8&255])+n[768|255&a]^r[11],a^=(n[i>>>24]+n[256|i>>16&255]^n[512|i>>8&255])+n[768|255&i]^r[12],i^=(n[a>>>24]+n[256|a>>16&255]^n[512|a>>8&255])+n[768|255&a]^r[13],a^=(n[i>>>24]+n[256|i>>16&255]^n[512|i>>8&255])+n[768|255&i]^r[14],i^=(n[a>>>24]+n[256|a>>16&255]^n[512|a>>8&255])+n[768|255&a]^r[15],a^=(n[i>>>24]+n[256|i>>16&255]^n[512|i>>8&255])+n[768|255&i]^r[16],e[t]=i^r[17],e[t+1]=a,e}function v(e,t){for(var r=0,n=0;r<4;++r)n=n<<8|255&e[t],t=(t+1)%e.length;return{key:n,offp:t}}function k(e,t,r){for(var n,a=0,i=[0,0],o=t.length,s=r.length,c=0;c<o;c++)a=(n=v(e,a)).offp,t[c]=t[c]^n.key;for(c=0;c<o;c+=2)i=_(i,0,t,r),t[c]=i[0],t[c+1]=i[1];for(c=0;c<s;c+=2)i=_(i,0,t,r),r[c]=i[0],r[c+1]=i[1]}function A(e,t,r,n,a){var i,o,s=w.slice(),c=s.length;if(r<4||r>31){if(o=Error("Illegal number of rounds (4-31): "+r),n){d(n.bind(this,o));return}throw o}if(t.length!==b){if(o=Error("Illegal salt length: "+t.length+" != "+b),n){d(n.bind(this,o));return}throw o}r=1<<r>>>0;var l,u,f,p=0;function h(){if(a&&a(p/r),p<r)for(var i=Date.now();p<r&&(p+=1,k(e,l,u),k(t,l,u),!(Date.now()-i>100)););else{for(p=0;p<64;p++)for(f=0;f<c>>1;f++)_(s,f<<1,l,u);var o=[];for(p=0;p<c;p++)o.push((s[p]>>24&255)>>>0),o.push((s[p]>>16&255)>>>0),o.push((s[p]>>8&255)>>>0),o.push((255&s[p])>>>0);return n?void n(null,o):o}n&&d(h)}if("function"==typeof Int32Array?(l=new Int32Array(x),u=new Int32Array(g)):(l=x.slice(),u=g.slice()),function(e,t,r,n){for(var a,i=0,o=[0,0],s=r.length,c=n.length,l=0;l<s;l++)i=(a=v(t,i)).offp,r[l]=r[l]^a.key;for(l=0,i=0;l<s;l+=2)i=(a=v(e,i)).offp,o[0]^=a.key,i=(a=v(e,i)).offp,o[1]^=a.key,o=_(o,0,r,n),r[l]=o[0],r[l+1]=o[1];for(l=0;l<c;l+=2)i=(a=v(e,i)).offp,o[0]^=a.key,i=(a=v(e,i)).offp,o[1]^=a.key,o=_(o,0,r,n),n[l]=o[0],n[l+1]=o[1]}(t,e,l,u),void 0!==n)h();else for(;;)if(void 0!==(i=h()))return i||[]}function S(e,t,r,n){if("string"!=typeof e||"string"!=typeof t){if(a=Error("Invalid string / salt: Not a string"),r){d(r.bind(this,a));return}throw a}if("$"!==t.charAt(0)||"2"!==t.charAt(1)){if(a=Error("Invalid salt version: "+t.substring(0,2)),r){d(r.bind(this,a));return}throw a}if("$"===t.charAt(2))i="\0",o=3;else{if("a"!==(i=t.charAt(2))&&"b"!==i&&"y"!==i||"$"!==t.charAt(3)){if(a=Error("Invalid salt revision: "+t.substring(2,4)),r){d(r.bind(this,a));return}throw a}o=4}if(t.charAt(o+2)>"$"){if(a=Error("Missing salt rounds"),r){d(r.bind(this,a));return}throw a}var a,i,o,s=10*parseInt(t.substring(o,o+1),10)+parseInt(t.substring(o+1,o+2),10),c=t.substring(o+3,o+25),l=function(e){for(var t,r,n=0,a=Array(u(e)),i=0,o=e.length;i<o;++i)(t=e.charCodeAt(i))<128?a[n++]=t:(t<2048?a[n++]=t>>6|192:((64512&t)==55296&&(64512&(r=e.charCodeAt(i+1)))==56320?(t=65536+((1023&t)<<10)+(1023&r),++i,a[n++]=t>>18|240,a[n++]=t>>12&63|128):a[n++]=t>>12|224,a[n++]=t>>6&63|128),a[n++]=63&t|128);return a}(e+=i>="a"?"\0":""),f=y(c,b);function p(e){var t=[];return t.push("$2"),i>="a"&&t.push(i),t.push("$"),s<10&&t.push("0"),t.push(s.toString()),t.push("$"),t.push(h(f,f.length)),t.push(h(e,4*w.length-1)),t.join("")}if(void 0===r)return p(A(l,f,s));A(l,f,s,function(e,t){e?r(e,null):r(null,p(t))},n)}let E={setRandomFallback:function(e){a=e},genSaltSync:i,genSalt:o,hashSync:s,hash:c,compareSync:function(e,t){if("string"!=typeof e||"string"!=typeof t)throw Error("Illegal arguments: "+typeof e+", "+typeof t);return 60===t.length&&l(s(e,t.substring(0,t.length-31)),t)},compare:function(e,t,r,n){function a(r){if("string"!=typeof e||"string"!=typeof t){d(r.bind(this,Error("Illegal arguments: "+typeof e+", "+typeof t)));return}if(60!==t.length){d(r.bind(this,null,!1));return}c(e,t.substring(0,29),function(e,n){e?r(e):r(null,l(n,t))},n)}if(!r)return new Promise(function(e,t){a(function(r,n){if(r){t(r);return}e(n)})});if("function"!=typeof r)throw Error("Illegal callback: "+typeof r);a(r)},getRounds:function(e){if("string"!=typeof e)throw Error("Illegal arguments: "+typeof e);return parseInt(e.split("$")[2],10)},getSalt:function(e){if("string"!=typeof e)throw Error("Illegal arguments: "+typeof e);if(60!==e.length)throw Error("Illegal hash length: "+e.length+" != 60");return e.substring(0,29)},truncates:function(e){if("string"!=typeof e)throw Error("Illegal arguments: "+typeof e);return u(e)>72},encodeBase64:function(e,t){return h(e,t)},decodeBase64:function(e,t){return y(e,t)}}},32221:(e,t,r)=>{let n,a,i,o,s;r.d(t,{Ay:()=>i8});var c={};r.r(c),r.d(c,{q:()=>tB,l:()=>tF});var l=function(e,t,r,n,a){if("m"===n)throw TypeError("Private method is not writable");if("a"===n&&!a)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!a:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?a.call(e,r):a?a.value=r:t.set(e,r),r},d=function(e,t,r,n){if("a"===r&&!n)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!n:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===r?n:"a"===r?n.call(e):n?n.value:t.get(e)};function u(e){let t=e?"__Secure-":"";return{sessionToken:{name:`${t}authjs.session-token`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},callbackUrl:{name:`${t}authjs.callback-url`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},csrfToken:{name:`${e?"__Host-":""}authjs.csrf-token`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},pkceCodeVerifier:{name:`${t}authjs.pkce.code_verifier`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e,maxAge:900}},state:{name:`${t}authjs.state`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e,maxAge:900}},nonce:{name:`${t}authjs.nonce`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},webauthnChallenge:{name:`${t}authjs.challenge`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e,maxAge:900}}}}class f{constructor(e,t,r){if(rx.add(this),rg.set(this,{}),rw.set(this,void 0),r_.set(this,void 0),l(this,r_,r,"f"),l(this,rw,e,"f"),!t)return;let{name:n}=e;for(let[e,r]of Object.entries(t))e.startsWith(n)&&r&&(d(this,rg,"f")[e]=r)}get value(){return Object.keys(d(this,rg,"f")).sort((e,t)=>parseInt(e.split(".").pop()||"0")-parseInt(t.split(".").pop()||"0")).map(e=>d(this,rg,"f")[e]).join("")}chunk(e,t){let r=d(this,rx,"m",rk).call(this);for(let n of d(this,rx,"m",rv).call(this,{name:d(this,rw,"f").name,value:e,options:{...d(this,rw,"f").options,...t}}))r[n.name]=n;return Object.values(r)}clean(){return Object.values(d(this,rx,"m",rk).call(this))}}rg=new WeakMap,rw=new WeakMap,r_=new WeakMap,rx=new WeakSet,rv=function(e){let t=Math.ceil(e.value.length/3936);if(1===t)return d(this,rg,"f")[e.name]=e.value,[e];let r=[];for(let n=0;n<t;n++){let t=`${e.name}.${n}`,a=e.value.substr(3936*n,3936);r.push({...e,name:t,value:a}),d(this,rg,"f")[t]=a}return d(this,r_,"f").debug("CHUNKING_SESSION_COOKIE",{message:"Session cookie exceeds allowed 4096 bytes.",emptyCookieSize:160,valueSize:e.value.length,chunks:r.map(e=>e.value.length+160)}),r},rk=function(){let e={};for(let t in d(this,rg,"f"))delete d(this,rg,"f")?.[t],e[t]={name:t,value:"",options:{...d(this,rw,"f").options,maxAge:0}};return e};class p extends Error{constructor(e,t){e instanceof Error?super(void 0,{cause:{err:e,...e.cause,...t}}):"string"==typeof e?(t instanceof Error&&(t={err:t,...t.cause}),super(e,t)):super(void 0,e),this.name=this.constructor.name,this.type=this.constructor.type??"AuthError",this.kind=this.constructor.kind??"error",Error.captureStackTrace?.(this,this.constructor);let r=`https://errors.authjs.dev#${this.type.toLowerCase()}`;this.message+=`${this.message?". ":""}Read more at ${r}`}}class h extends p{}h.kind="signIn";class y extends p{}y.type="AdapterError";class b extends p{}b.type="AccessDenied";class m extends p{}m.type="CallbackRouteError";class x extends p{}x.type="ErrorPageLoop";class g extends p{}g.type="EventError";class w extends p{}w.type="InvalidCallbackUrl";class _ extends h{constructor(){super(...arguments),this.code="credentials"}}_.type="CredentialsSignin";class v extends p{}v.type="InvalidEndpoints";class k extends p{}k.type="InvalidCheck";class A extends p{}A.type="JWTSessionError";class S extends p{}S.type="MissingAdapter";class E extends p{}E.type="MissingAdapterMethods";class T extends p{}T.type="MissingAuthorize";class R extends p{}R.type="MissingSecret";class C extends h{}C.type="OAuthAccountNotLinked";class P extends h{}P.type="OAuthCallbackError";class O extends p{}O.type="OAuthProfileParseError";class U extends p{}U.type="SessionTokenError";class $ extends h{}$.type="OAuthSignInError";class I extends h{}I.type="EmailSignInError";class j extends p{}j.type="SignOutError";class H extends p{}H.type="UnknownAction";class D extends p{}D.type="UnsupportedStrategy";class W extends p{}W.type="InvalidProvider";class K extends p{}K.type="UntrustedHost";class L extends p{}L.type="Verification";class M extends h{}M.type="MissingCSRF";let N=new Set(["CredentialsSignin","OAuthAccountNotLinked","OAuthCallbackError","AccessDenied","Verification","MissingCSRF","AccountNotLinked","WebAuthnVerificationError"]);class J extends p{}J.type="DuplicateConditionalUI";class B extends p{}B.type="MissingWebAuthnAutocomplete";class q extends p{}q.type="WebAuthnVerificationError";class z extends h{}z.type="AccountNotLinked";class F extends p{}F.type="ExperimentalFeatureNotEnabled";let V=!1;function G(e,t){try{return/^https?:/.test(new URL(e,e.startsWith("/")?t:void 0).protocol)}catch{return!1}}let X=!1,Y=!1,Z=!1,Q=["createVerificationToken","useVerificationToken","getUserByEmail"],ee=["createUser","getUser","getUserByEmail","getUserByAccount","updateUser","linkAccount","createSession","getSessionAndUser","updateSession","deleteSession"],et=["createUser","getUser","linkAccount","getAccount","getAuthenticator","createAuthenticator","listAuthenticatorsByUserId","updateAuthenticatorCounter"];var er=r(55511);let en=(e,t,r,n,a)=>{let i=parseInt(e.substr(3),10)>>3||20,o=(0,er.createHmac)(e,r.byteLength?r:new Uint8Array(i)).update(t).digest(),s=Math.ceil(a/i),c=new Uint8Array(i*s+n.byteLength+1),l=0,d=0;for(let t=1;t<=s;t++)c.set(n,d),c[d+n.byteLength]=t,c.set((0,er.createHmac)(e,o).update(c.subarray(l,d+n.byteLength+1)).digest(),d),l=d,d+=i;return c.slice(0,a)};"function"!=typeof er.hkdf||process.versions.electron||(n=async(...e)=>new Promise((t,r)=>{er.hkdf(...e,(e,n)=>{e?r(e):t(new Uint8Array(n))})}));let ea=async(e,t,r,a,i)=>(n||en)(e,t,r,a,i);function ei(e,t){if("string"==typeof e)return new TextEncoder().encode(e);if(!(e instanceof Uint8Array))throw TypeError(`"${t}"" must be an instance of Uint8Array or a string`);return e}async function eo(e,t,r,n,a){return ea(function(e){switch(e){case"sha256":case"sha384":case"sha512":case"sha1":return e;default:throw TypeError('unsupported "digest" value')}}(e),function(e){let t=ei(e,"ikm");if(!t.byteLength)throw TypeError('"ikm" must be at least one byte in length');return t}(t),ei(r,"salt"),function(e){let t=ei(e,"info");if(t.byteLength>1024)throw TypeError('"info" must not contain more than 1024 bytes');return t}(n),function(e,t){if("number"!=typeof e||!Number.isInteger(e)||e<1)throw TypeError('"keylen" must be a positive integer');if(e>255*(parseInt(t.substr(3),10)>>3||20))throw TypeError('"keylen" too large');return e}(a,e))}let es=async(e,t)=>{let r=`SHA-${e.slice(-3)}`;return new Uint8Array(await crypto.subtle.digest(r,t))},ec=new TextEncoder,el=new TextDecoder;function ed(...e){let t=new Uint8Array(e.reduce((e,{length:t})=>e+t,0)),r=0;for(let n of e)t.set(n,r),r+=n.length;return t}function eu(e,t,r){if(t<0||t>=0x100000000)throw RangeError(`value must be >= 0 and <= ${0x100000000-1}. Received ${t}`);e.set([t>>>24,t>>>16,t>>>8,255&t],r)}function ef(e){let t=Math.floor(e/0x100000000),r=new Uint8Array(8);return eu(r,t,0),eu(r,e%0x100000000,4),r}function ep(e){let t=new Uint8Array(4);return eu(t,e),t}function eh(e){if(Uint8Array.fromBase64)return Uint8Array.fromBase64("string"==typeof e?e:el.decode(e),{alphabet:"base64url"});let t=e;t instanceof Uint8Array&&(t=el.decode(t)),t=t.replace(/-/g,"+").replace(/_/g,"/").replace(/\s/g,"");try{return function(e){if(Uint8Array.fromBase64)return Uint8Array.fromBase64(e);let t=atob(e),r=new Uint8Array(t.length);for(let e=0;e<t.length;e++)r[e]=t.charCodeAt(e);return r}(t)}catch{throw TypeError("The input to be decoded is not correctly encoded.")}}function ey(e){let t=e;return("string"==typeof t&&(t=ec.encode(t)),Uint8Array.prototype.toBase64)?t.toBase64({alphabet:"base64url",omitPadding:!0}):(function(e){if(Uint8Array.prototype.toBase64)return e.toBase64();let t=[];for(let r=0;r<e.length;r+=32768)t.push(String.fromCharCode.apply(null,e.subarray(r,r+32768)));return btoa(t.join(""))})(t).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}class eb extends Error{static code="ERR_JOSE_GENERIC";code="ERR_JOSE_GENERIC";constructor(e,t){super(e,t),this.name=this.constructor.name,Error.captureStackTrace?.(this,this.constructor)}}class em extends eb{static code="ERR_JWT_CLAIM_VALIDATION_FAILED";code="ERR_JWT_CLAIM_VALIDATION_FAILED";claim;reason;payload;constructor(e,t,r="unspecified",n="unspecified"){super(e,{cause:{claim:r,reason:n,payload:t}}),this.claim=r,this.reason=n,this.payload=t}}class ex extends eb{static code="ERR_JWT_EXPIRED";code="ERR_JWT_EXPIRED";claim;reason;payload;constructor(e,t,r="unspecified",n="unspecified"){super(e,{cause:{claim:r,reason:n,payload:t}}),this.claim=r,this.reason=n,this.payload=t}}class eg extends eb{static code="ERR_JOSE_ALG_NOT_ALLOWED";code="ERR_JOSE_ALG_NOT_ALLOWED"}class ew extends eb{static code="ERR_JOSE_NOT_SUPPORTED";code="ERR_JOSE_NOT_SUPPORTED"}class e_ extends eb{static code="ERR_JWE_DECRYPTION_FAILED";code="ERR_JWE_DECRYPTION_FAILED";constructor(e="decryption operation failed",t){super(e,t)}}class ev extends eb{static code="ERR_JWE_INVALID";code="ERR_JWE_INVALID"}class ek extends eb{static code="ERR_JWT_INVALID";code="ERR_JWT_INVALID"}class eA extends eb{static code="ERR_JWK_INVALID";code="ERR_JWK_INVALID"}class eS extends eb{[Symbol.asyncIterator];static code="ERR_JWKS_MULTIPLE_MATCHING_KEYS";code="ERR_JWKS_MULTIPLE_MATCHING_KEYS";constructor(e="multiple matching keys found in the JSON Web Key Set",t){super(e,t)}}function eE(e){if(!eT(e))throw Error("CryptoKey instance expected")}function eT(e){return e?.[Symbol.toStringTag]==="CryptoKey"}function eR(e){return e?.[Symbol.toStringTag]==="KeyObject"}let eC=e=>eT(e)||eR(e),eP=e=>{if(!function(e){return"object"==typeof e&&null!==e}(e)||"[object Object]"!==Object.prototype.toString.call(e))return!1;if(null===Object.getPrototypeOf(e))return!0;let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t};function eO(e){return eP(e)&&"string"==typeof e.kty}function eU(e,t,...r){if((r=r.filter(Boolean)).length>2){let t=r.pop();e+=`one of type ${r.join(", ")}, or ${t}.`}else 2===r.length?e+=`one of type ${r[0]} or ${r[1]}.`:e+=`of type ${r[0]}.`;return null==t?e+=` Received ${t}`:"function"==typeof t&&t.name?e+=` Received function ${t.name}`:"object"==typeof t&&null!=t&&t.constructor?.name&&(e+=` Received an instance of ${t.constructor.name}`),e}let e$=(e,...t)=>eU("Key must be ",e,...t);function eI(e,t,...r){return eU(`Key for the ${e} algorithm must be `,t,...r)}async function ej(e){if(eR(e)){if("secret"!==e.type)return e.export({format:"jwk"});e=e.export()}if(e instanceof Uint8Array)return{kty:"oct",k:ey(e)};if(!eT(e))throw TypeError(e$(e,"CryptoKey","KeyObject","Uint8Array"));if(!e.extractable)throw TypeError("non-extractable CryptoKey cannot be exported as a JWK");let{ext:t,key_ops:r,alg:n,use:a,...i}=await crypto.subtle.exportKey("jwk",e);return i}async function eH(e){return ej(e)}let eD=(e,t)=>{if("string"!=typeof e||!e)throw new eA(`${t} missing or invalid`)};async function eW(e,t){let r,n;if(eO(e))r=e;else if(eC(e))r=await eH(e);else throw TypeError(e$(e,"CryptoKey","KeyObject","JSON Web Key"));if("sha256"!==(t??="sha256")&&"sha384"!==t&&"sha512"!==t)throw TypeError('digestAlgorithm must one of "sha256", "sha384", or "sha512"');switch(r.kty){case"EC":eD(r.crv,'"crv" (Curve) Parameter'),eD(r.x,'"x" (X Coordinate) Parameter'),eD(r.y,'"y" (Y Coordinate) Parameter'),n={crv:r.crv,kty:r.kty,x:r.x,y:r.y};break;case"OKP":eD(r.crv,'"crv" (Subtype of Key Pair) Parameter'),eD(r.x,'"x" (Public Key) Parameter'),n={crv:r.crv,kty:r.kty,x:r.x};break;case"RSA":eD(r.e,'"e" (Exponent) Parameter'),eD(r.n,'"n" (Modulus) Parameter'),n={e:r.e,kty:r.kty,n:r.n};break;case"oct":eD(r.k,'"k" (Key Value) Parameter'),n={k:r.k,kty:r.kty};break;default:throw new ew('"kty" (Key Type) Parameter missing or unsupported')}let a=ec.encode(JSON.stringify(n));return ey(await es(t,a))}let eK=Symbol();function eL(e){switch(e){case"A128GCM":case"A128GCMKW":case"A192GCM":case"A192GCMKW":case"A256GCM":case"A256GCMKW":return 96;case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return 128;default:throw new ew(`Unsupported JWE Algorithm: ${e}`)}}let eM=e=>crypto.getRandomValues(new Uint8Array(eL(e)>>3)),eN=(e,t)=>{if(t.length<<3!==eL(e))throw new ev("Invalid Initialization Vector length")},eJ=(e,t)=>{let r=e.byteLength<<3;if(r!==t)throw new ev(`Invalid Content Encryption Key length. Expected ${t} bits, got ${r} bits`)};function eB(e,t="algorithm.name"){return TypeError(`CryptoKey does not support this operation, its ${t} must be ${e}`)}function eq(e,t){return e.name===t}function ez(e,t,r){switch(t){case"A128GCM":case"A192GCM":case"A256GCM":{if(!eq(e.algorithm,"AES-GCM"))throw eB("AES-GCM");let r=parseInt(t.slice(1,4),10);if(e.algorithm.length!==r)throw eB(r,"algorithm.length");break}case"A128KW":case"A192KW":case"A256KW":{if(!eq(e.algorithm,"AES-KW"))throw eB("AES-KW");let r=parseInt(t.slice(1,4),10);if(e.algorithm.length!==r)throw eB(r,"algorithm.length");break}case"ECDH":switch(e.algorithm.name){case"ECDH":case"X25519":break;default:throw eB("ECDH or X25519")}break;case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":if(!eq(e.algorithm,"PBKDF2"))throw eB("PBKDF2");break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":{if(!eq(e.algorithm,"RSA-OAEP"))throw eB("RSA-OAEP");let r=parseInt(t.slice(9),10)||1;if(parseInt(e.algorithm.hash.name.slice(4),10)!==r)throw eB(`SHA-${r}`,"algorithm.hash");break}default:throw TypeError("CryptoKey does not support this operation")}(function(e,t){if(t&&!e.usages.includes(t))throw TypeError(`CryptoKey does not support this operation, its usages must include ${t}.`)})(e,r)}async function eF(e,t,r,n,a){if(!(r instanceof Uint8Array))throw TypeError(e$(r,"Uint8Array"));let i=parseInt(e.slice(1,4),10),o=await crypto.subtle.importKey("raw",r.subarray(i>>3),"AES-CBC",!1,["encrypt"]),s=await crypto.subtle.importKey("raw",r.subarray(0,i>>3),{hash:`SHA-${i<<1}`,name:"HMAC"},!1,["sign"]),c=new Uint8Array(await crypto.subtle.encrypt({iv:n,name:"AES-CBC"},o,t)),l=ed(a,n,c,ef(a.length<<3));return{ciphertext:c,tag:new Uint8Array((await crypto.subtle.sign("HMAC",s,l)).slice(0,i>>3)),iv:n}}async function eV(e,t,r,n,a){let i;r instanceof Uint8Array?i=await crypto.subtle.importKey("raw",r,"AES-GCM",!1,["encrypt"]):(ez(r,e,"encrypt"),i=r);let o=new Uint8Array(await crypto.subtle.encrypt({additionalData:a,iv:n,name:"AES-GCM",tagLength:128},i,t)),s=o.slice(-16);return{ciphertext:o.slice(0,-16),tag:s,iv:n}}let eG=async(e,t,r,n,a)=>{if(!eT(r)&&!(r instanceof Uint8Array))throw TypeError(e$(r,"CryptoKey","KeyObject","Uint8Array","JSON Web Key"));switch(n?eN(e,n):n=eM(e),e){case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return r instanceof Uint8Array&&eJ(r,parseInt(e.slice(-3),10)),eF(e,t,r,n,a);case"A128GCM":case"A192GCM":case"A256GCM":return r instanceof Uint8Array&&eJ(r,parseInt(e.slice(1,4),10)),eV(e,t,r,n,a);default:throw new ew("Unsupported JWE Content Encryption Algorithm")}};function eX(e,t){if(e.algorithm.length!==parseInt(t.slice(1,4),10))throw TypeError(`Invalid key size for alg: ${t}`)}function eY(e,t,r){return e instanceof Uint8Array?crypto.subtle.importKey("raw",e,"AES-KW",!0,[r]):(ez(e,t,r),e)}async function eZ(e,t,r){let n=await eY(t,e,"wrapKey");eX(n,e);let a=await crypto.subtle.importKey("raw",r,{hash:"SHA-256",name:"HMAC"},!0,["sign"]);return new Uint8Array(await crypto.subtle.wrapKey("raw",a,n,"AES-KW"))}async function eQ(e,t,r){let n=await eY(t,e,"unwrapKey");eX(n,e);let a=await crypto.subtle.unwrapKey("raw",r,n,"AES-KW",{hash:"SHA-256",name:"HMAC"},!0,["sign"]);return new Uint8Array(await crypto.subtle.exportKey("raw",a))}function e0(e){return ed(ep(e.length),e)}async function e1(e,t,r){let n=Math.ceil((t>>3)/32),a=new Uint8Array(32*n);for(let t=0;t<n;t++){let n=new Uint8Array(4+e.length+r.length);n.set(ep(t+1)),n.set(e,4),n.set(r,4+e.length),a.set(await es("sha256",n),32*t)}return a.slice(0,t>>3)}async function e2(e,t,r,n,a=new Uint8Array(0),i=new Uint8Array(0)){let o;ez(e,"ECDH"),ez(t,"ECDH","deriveBits");let s=ed(e0(ec.encode(r)),e0(a),e0(i),ep(n));return o="X25519"===e.algorithm.name?256:Math.ceil(parseInt(e.algorithm.namedCurve.slice(-3),10)/8)<<3,e1(new Uint8Array(await crypto.subtle.deriveBits({name:e.algorithm.name,public:e},t,o)),n,s)}function e5(e){switch(e.algorithm.namedCurve){case"P-256":case"P-384":case"P-521":return!0;default:return"X25519"===e.algorithm.name}}let e6=(e,t)=>ed(ec.encode(e),new Uint8Array([0]),t);async function e3(e,t,r,n){if(!(e instanceof Uint8Array)||e.length<8)throw new ev("PBES2 Salt Input must be 8 or more octets");let a=e6(t,e),i=parseInt(t.slice(13,16),10),o={hash:`SHA-${t.slice(8,11)}`,iterations:r,name:"PBKDF2",salt:a},s=await (n instanceof Uint8Array?crypto.subtle.importKey("raw",n,"PBKDF2",!1,["deriveBits"]):(ez(n,t,"deriveBits"),n));return new Uint8Array(await crypto.subtle.deriveBits(o,s,i))}async function e8(e,t,r,n=2048,a=crypto.getRandomValues(new Uint8Array(16))){let i=await e3(a,e,n,t);return{encryptedKey:await eZ(e.slice(-6),i,r),p2c:n,p2s:ey(a)}}async function e4(e,t,r,n,a){let i=await e3(a,e,n,t);return eQ(e.slice(-6),i,r)}let e9=(e,t)=>{if(e.startsWith("RS")||e.startsWith("PS")){let{modulusLength:r}=t.algorithm;if("number"!=typeof r||r<2048)throw TypeError(`${e} requires key modulusLength to be 2048 bits or larger`)}},e7=e=>{switch(e){case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":return"RSA-OAEP";default:throw new ew(`alg ${e} is not supported either by JOSE or your javascript runtime`)}};async function te(e,t,r){return ez(t,e,"encrypt"),e9(e,t),new Uint8Array(await crypto.subtle.encrypt(e7(e),t,r))}async function tt(e,t,r){return ez(t,e,"decrypt"),e9(e,t),new Uint8Array(await crypto.subtle.decrypt(e7(e),t,r))}let tr=async e=>{if(!e.alg)throw TypeError('"alg" argument is required when "jwk.alg" is not present');let{algorithm:t,keyUsages:r}=function(e){let t,r;switch(e.kty){case"RSA":switch(e.alg){case"PS256":case"PS384":case"PS512":t={name:"RSA-PSS",hash:`SHA-${e.alg.slice(-3)}`},r=e.d?["sign"]:["verify"];break;case"RS256":case"RS384":case"RS512":t={name:"RSASSA-PKCS1-v1_5",hash:`SHA-${e.alg.slice(-3)}`},r=e.d?["sign"]:["verify"];break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":t={name:"RSA-OAEP",hash:`SHA-${parseInt(e.alg.slice(-3),10)||1}`},r=e.d?["decrypt","unwrapKey"]:["encrypt","wrapKey"];break;default:throw new ew('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;case"EC":switch(e.alg){case"ES256":t={name:"ECDSA",namedCurve:"P-256"},r=e.d?["sign"]:["verify"];break;case"ES384":t={name:"ECDSA",namedCurve:"P-384"},r=e.d?["sign"]:["verify"];break;case"ES512":t={name:"ECDSA",namedCurve:"P-521"},r=e.d?["sign"]:["verify"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":t={name:"ECDH",namedCurve:e.crv},r=e.d?["deriveBits"]:[];break;default:throw new ew('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;case"OKP":switch(e.alg){case"Ed25519":case"EdDSA":t={name:"Ed25519"},r=e.d?["sign"]:["verify"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":t={name:e.crv},r=e.d?["deriveBits"]:[];break;default:throw new ew('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;default:throw new ew('Invalid or unsupported JWK "kty" (Key Type) Parameter value')}return{algorithm:t,keyUsages:r}}(e),n={...e};return delete n.alg,delete n.use,crypto.subtle.importKey("jwk",n,t,e.ext??!e.d,e.key_ops??r)},tn=async(e,t,r,n=!1)=>{let i=(a||=new WeakMap).get(e);if(i?.[r])return i[r];let o=await tr({...t,alg:r});return n&&Object.freeze(e),i?i[r]=o:a.set(e,{[r]:o}),o},ta=(e,t)=>{let r;let n=(a||=new WeakMap).get(e);if(n?.[t])return n[t];let i="public"===e.type,o=!!i;if("x25519"===e.asymmetricKeyType){switch(t){case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":break;default:throw TypeError("given KeyObject instance cannot be used for this algorithm")}r=e.toCryptoKey(e.asymmetricKeyType,o,i?[]:["deriveBits"])}if("ed25519"===e.asymmetricKeyType){if("EdDSA"!==t&&"Ed25519"!==t)throw TypeError("given KeyObject instance cannot be used for this algorithm");r=e.toCryptoKey(e.asymmetricKeyType,o,[i?"verify":"sign"])}if("rsa"===e.asymmetricKeyType){let n;switch(t){case"RSA-OAEP":n="SHA-1";break;case"RS256":case"PS256":case"RSA-OAEP-256":n="SHA-256";break;case"RS384":case"PS384":case"RSA-OAEP-384":n="SHA-384";break;case"RS512":case"PS512":case"RSA-OAEP-512":n="SHA-512";break;default:throw TypeError("given KeyObject instance cannot be used for this algorithm")}if(t.startsWith("RSA-OAEP"))return e.toCryptoKey({name:"RSA-OAEP",hash:n},o,i?["encrypt"]:["decrypt"]);r=e.toCryptoKey({name:t.startsWith("PS")?"RSA-PSS":"RSASSA-PKCS1-v1_5",hash:n},o,[i?"verify":"sign"])}if("ec"===e.asymmetricKeyType){let n=new Map([["prime256v1","P-256"],["secp384r1","P-384"],["secp521r1","P-521"]]).get(e.asymmetricKeyDetails?.namedCurve);if(!n)throw TypeError("given KeyObject instance cannot be used for this algorithm");"ES256"===t&&"P-256"===n&&(r=e.toCryptoKey({name:"ECDSA",namedCurve:n},o,[i?"verify":"sign"])),"ES384"===t&&"P-384"===n&&(r=e.toCryptoKey({name:"ECDSA",namedCurve:n},o,[i?"verify":"sign"])),"ES512"===t&&"P-521"===n&&(r=e.toCryptoKey({name:"ECDSA",namedCurve:n},o,[i?"verify":"sign"])),t.startsWith("ECDH-ES")&&(r=e.toCryptoKey({name:"ECDH",namedCurve:n},o,i?[]:["deriveBits"]))}if(!r)throw TypeError("given KeyObject instance cannot be used for this algorithm");return n?n[t]=r:a.set(e,{[t]:r}),r},ti=async(e,t)=>{if(e instanceof Uint8Array||eT(e))return e;if(eR(e)){if("secret"===e.type)return e.export();if("toCryptoKey"in e&&"function"==typeof e.toCryptoKey)try{return ta(e,t)}catch(e){if(e instanceof TypeError)throw e}let r=e.export({format:"jwk"});return tn(e,r,t)}if(eO(e))return e.k?eh(e.k):tn(e,e,t,!0);throw Error("unreachable")};function to(e){switch(e){case"A128GCM":return 128;case"A192GCM":return 192;case"A256GCM":case"A128CBC-HS256":return 256;case"A192CBC-HS384":return 384;case"A256CBC-HS512":return 512;default:throw new ew(`Unsupported JWE Algorithm: ${e}`)}}let ts=e=>crypto.getRandomValues(new Uint8Array(to(e)>>3));async function tc(e,t){if(!(e instanceof Uint8Array))throw TypeError("First argument must be a buffer");if(!(t instanceof Uint8Array))throw TypeError("Second argument must be a buffer");let r={name:"HMAC",hash:"SHA-256"},n=await crypto.subtle.generateKey(r,!1,["sign"]),a=new Uint8Array(await crypto.subtle.sign(r,n,e)),i=new Uint8Array(await crypto.subtle.sign(r,n,t)),o=0,s=-1;for(;++s<32;)o|=a[s]^i[s];return 0===o}async function tl(e,t,r,n,a,i){let o,s;if(!(t instanceof Uint8Array))throw TypeError(e$(t,"Uint8Array"));let c=parseInt(e.slice(1,4),10),l=await crypto.subtle.importKey("raw",t.subarray(c>>3),"AES-CBC",!1,["decrypt"]),d=await crypto.subtle.importKey("raw",t.subarray(0,c>>3),{hash:`SHA-${c<<1}`,name:"HMAC"},!1,["sign"]),u=ed(i,n,r,ef(i.length<<3)),f=new Uint8Array((await crypto.subtle.sign("HMAC",d,u)).slice(0,c>>3));try{o=await tc(a,f)}catch{}if(!o)throw new e_;try{s=new Uint8Array(await crypto.subtle.decrypt({iv:n,name:"AES-CBC"},l,r))}catch{}if(!s)throw new e_;return s}async function td(e,t,r,n,a,i){let o;t instanceof Uint8Array?o=await crypto.subtle.importKey("raw",t,"AES-GCM",!1,["decrypt"]):(ez(t,e,"decrypt"),o=t);try{return new Uint8Array(await crypto.subtle.decrypt({additionalData:i,iv:n,name:"AES-GCM",tagLength:128},o,ed(r,a)))}catch{throw new e_}}let tu=async(e,t,r,n,a,i)=>{if(!eT(t)&&!(t instanceof Uint8Array))throw TypeError(e$(t,"CryptoKey","KeyObject","Uint8Array","JSON Web Key"));if(!n)throw new ev("JWE Initialization Vector missing");if(!a)throw new ev("JWE Authentication Tag missing");switch(eN(e,n),e){case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return t instanceof Uint8Array&&eJ(t,parseInt(e.slice(-3),10)),tl(e,t,r,n,a,i);case"A128GCM":case"A192GCM":case"A256GCM":return t instanceof Uint8Array&&eJ(t,parseInt(e.slice(1,4),10)),td(e,t,r,n,a,i);default:throw new ew("Unsupported JWE Content Encryption Algorithm")}};async function tf(e,t,r,n){let a=e.slice(0,7),i=await eG(a,r,t,n,new Uint8Array(0));return{encryptedKey:i.ciphertext,iv:ey(i.iv),tag:ey(i.tag)}}async function tp(e,t,r,n,a){return tu(e.slice(0,7),t,r,n,a,new Uint8Array(0))}let th=async(e,t,r,n,a={})=>{let i,o,s;switch(e){case"dir":s=r;break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":{let c;if(eE(r),!e5(r))throw new ew("ECDH with the provided key is not allowed or not supported by your javascript runtime");let{apu:l,apv:d}=a;c=a.epk?await ti(a.epk,e):(await crypto.subtle.generateKey(r.algorithm,!0,["deriveBits"])).privateKey;let{x:u,y:f,crv:p,kty:h}=await eH(c),y=await e2(r,c,"ECDH-ES"===e?t:e,"ECDH-ES"===e?to(t):parseInt(e.slice(-5,-2),10),l,d);if(o={epk:{x:u,crv:p,kty:h}},"EC"===h&&(o.epk.y=f),l&&(o.apu=ey(l)),d&&(o.apv=ey(d)),"ECDH-ES"===e){s=y;break}s=n||ts(t);let b=e.slice(-6);i=await eZ(b,y,s);break}case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":s=n||ts(t),eE(r),i=await te(e,r,s);break;case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":{s=n||ts(t);let{p2c:c,p2s:l}=a;({encryptedKey:i,...o}=await e8(e,r,s,c,l));break}case"A128KW":case"A192KW":case"A256KW":s=n||ts(t),i=await eZ(e,r,s);break;case"A128GCMKW":case"A192GCMKW":case"A256GCMKW":{s=n||ts(t);let{iv:c}=a;({encryptedKey:i,...o}=await tf(e,r,s,c));break}default:throw new ew('Invalid or unsupported "alg" (JWE Algorithm) header value')}return{cek:s,encryptedKey:i,parameters:o}},ty=(...e)=>{let t;let r=e.filter(Boolean);if(0===r.length||1===r.length)return!0;for(let e of r){let r=Object.keys(e);if(!t||0===t.size){t=new Set(r);continue}for(let e of r){if(t.has(e))return!1;t.add(e)}}return!0},tb=(e,t,r,n,a)=>{let i;if(void 0!==a.crit&&n?.crit===void 0)throw new e('"crit" (Critical) Header Parameter MUST be integrity protected');if(!n||void 0===n.crit)return new Set;if(!Array.isArray(n.crit)||0===n.crit.length||n.crit.some(e=>"string"!=typeof e||0===e.length))throw new e('"crit" (Critical) Header Parameter MUST be an array of non-empty strings when present');for(let o of(i=void 0!==r?new Map([...Object.entries(r),...t.entries()]):t,n.crit)){if(!i.has(o))throw new ew(`Extension Header Parameter "${o}" is not recognized`);if(void 0===a[o])throw new e(`Extension Header Parameter "${o}" is missing`);if(i.get(o)&&void 0===n[o])throw new e(`Extension Header Parameter "${o}" MUST be integrity protected`)}return new Set(n.crit)},tm=e=>e?.[Symbol.toStringTag],tx=(e,t,r)=>{if(void 0!==t.use){let e;switch(r){case"sign":case"verify":e="sig";break;case"encrypt":case"decrypt":e="enc"}if(t.use!==e)throw TypeError(`Invalid key for this operation, its "use" must be "${e}" when present`)}if(void 0!==t.alg&&t.alg!==e)throw TypeError(`Invalid key for this operation, its "alg" must be "${e}" when present`);if(Array.isArray(t.key_ops)){let n;switch(!0){case"sign"===r||"verify"===r:case"dir"===e:case e.includes("CBC-HS"):n=r;break;case e.startsWith("PBES2"):n="deriveBits";break;case/^A\d{3}(?:GCM)?(?:KW)?$/.test(e):n=!e.includes("GCM")&&e.endsWith("KW")?"encrypt"===r?"wrapKey":"unwrapKey":r;break;case"encrypt"===r&&e.startsWith("RSA"):n="wrapKey";break;case"decrypt"===r:n=e.startsWith("RSA")?"unwrapKey":"deriveBits"}if(n&&t.key_ops?.includes?.(n)===!1)throw TypeError(`Invalid key for this operation, its "key_ops" must include "${n}" when present`)}return!0},tg=(e,t,r)=>{if(!(t instanceof Uint8Array)){if(eO(t)){if(function(e){return"oct"===e.kty&&"string"==typeof e.k}(t)&&tx(e,t,r))return;throw TypeError('JSON Web Key for symmetric algorithms must have JWK "kty" (Key Type) equal to "oct" and the JWK "k" (Key Value) present')}if(!eC(t))throw TypeError(eI(e,t,"CryptoKey","KeyObject","JSON Web Key","Uint8Array"));if("secret"!==t.type)throw TypeError(`${tm(t)} instances for symmetric algorithms must be of type "secret"`)}},tw=(e,t,r)=>{if(eO(t))switch(r){case"decrypt":case"sign":if(function(e){return"oct"!==e.kty&&"string"==typeof e.d}(t)&&tx(e,t,r))return;throw TypeError("JSON Web Key for this operation be a private JWK");case"encrypt":case"verify":if(function(e){return"oct"!==e.kty&&void 0===e.d}(t)&&tx(e,t,r))return;throw TypeError("JSON Web Key for this operation be a public JWK")}if(!eC(t))throw TypeError(eI(e,t,"CryptoKey","KeyObject","JSON Web Key"));if("secret"===t.type)throw TypeError(`${tm(t)} instances for asymmetric algorithms must not be of type "secret"`);if("public"===t.type)switch(r){case"sign":throw TypeError(`${tm(t)} instances for asymmetric algorithm signing must be of type "private"`);case"decrypt":throw TypeError(`${tm(t)} instances for asymmetric algorithm decryption must be of type "private"`)}if("private"===t.type)switch(r){case"verify":throw TypeError(`${tm(t)} instances for asymmetric algorithm verifying must be of type "public"`);case"encrypt":throw TypeError(`${tm(t)} instances for asymmetric algorithm encryption must be of type "public"`)}},t_=(e,t,r)=>{e.startsWith("HS")||"dir"===e||e.startsWith("PBES2")||/^A(?:128|192|256)(?:GCM)?(?:KW)?$/.test(e)||/^A(?:128|192|256)CBC-HS(?:256|384|512)$/.test(e)?tg(e,t,r):tw(e,t,r)};class tv{#e;#t;#r;#n;#a;#i;#o;#s;constructor(e){if(!(e instanceof Uint8Array))throw TypeError("plaintext must be an instance of Uint8Array");this.#e=e}setKeyManagementParameters(e){if(this.#s)throw TypeError("setKeyManagementParameters can only be called once");return this.#s=e,this}setProtectedHeader(e){if(this.#t)throw TypeError("setProtectedHeader can only be called once");return this.#t=e,this}setSharedUnprotectedHeader(e){if(this.#r)throw TypeError("setSharedUnprotectedHeader can only be called once");return this.#r=e,this}setUnprotectedHeader(e){if(this.#n)throw TypeError("setUnprotectedHeader can only be called once");return this.#n=e,this}setAdditionalAuthenticatedData(e){return this.#a=e,this}setContentEncryptionKey(e){if(this.#i)throw TypeError("setContentEncryptionKey can only be called once");return this.#i=e,this}setInitializationVector(e){if(this.#o)throw TypeError("setInitializationVector can only be called once");return this.#o=e,this}async encrypt(e,t){let r,n,a,i,o;if(!this.#t&&!this.#n&&!this.#r)throw new ev("either setProtectedHeader, setUnprotectedHeader, or sharedUnprotectedHeader must be called before #encrypt()");if(!ty(this.#t,this.#n,this.#r))throw new ev("JWE Protected, JWE Shared Unprotected and JWE Per-Recipient Header Parameter names must be disjoint");let s={...this.#t,...this.#n,...this.#r};if(tb(ev,new Map,t?.crit,this.#t,s),void 0!==s.zip)throw new ew('JWE "zip" (Compression Algorithm) Header Parameter is not supported.');let{alg:c,enc:l}=s;if("string"!=typeof c||!c)throw new ev('JWE "alg" (Algorithm) Header Parameter missing or invalid');if("string"!=typeof l||!l)throw new ev('JWE "enc" (Encryption Algorithm) Header Parameter missing or invalid');if(this.#i&&("dir"===c||"ECDH-ES"===c))throw TypeError(`setContentEncryptionKey cannot be called with JWE "alg" (Algorithm) Header ${c}`);t_("dir"===c?l:c,e,"encrypt");{let a;let i=await ti(e,c);({cek:n,encryptedKey:r,parameters:a}=await th(c,l,i,this.#i,this.#s)),a&&(t&&eK in t?this.#n?this.#n={...this.#n,...a}:this.setUnprotectedHeader(a):this.#t?this.#t={...this.#t,...a}:this.setProtectedHeader(a))}i=this.#t?ec.encode(ey(JSON.stringify(this.#t))):ec.encode(""),this.#a?(o=ey(this.#a),a=ed(i,ec.encode("."),ec.encode(o))):a=i;let{ciphertext:d,tag:u,iv:f}=await eG(l,this.#e,n,this.#o,a),p={ciphertext:ey(d)};return f&&(p.iv=ey(f)),u&&(p.tag=ey(u)),r&&(p.encrypted_key=ey(r)),o&&(p.aad=o),this.#t&&(p.protected=el.decode(i)),this.#r&&(p.unprotected=this.#r),this.#n&&(p.header=this.#n),p}}class tk{#c;constructor(e){this.#c=new tv(e)}setContentEncryptionKey(e){return this.#c.setContentEncryptionKey(e),this}setInitializationVector(e){return this.#c.setInitializationVector(e),this}setProtectedHeader(e){return this.#c.setProtectedHeader(e),this}setKeyManagementParameters(e){return this.#c.setKeyManagementParameters(e),this}async encrypt(e,t){let r=await this.#c.encrypt(e,t);return[r.protected,r.encrypted_key,r.iv,r.ciphertext,r.tag].join(".")}}let tA=e=>Math.floor(e.getTime()/1e3),tS=/^(\+|\-)? ?(\d+|\d+\.\d+) ?(seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)(?: (ago|from now))?$/i,tE=e=>{let t;let r=tS.exec(e);if(!r||r[4]&&r[1])throw TypeError("Invalid time period format");let n=parseFloat(r[2]);switch(r[3].toLowerCase()){case"sec":case"secs":case"second":case"seconds":case"s":t=Math.round(n);break;case"minute":case"minutes":case"min":case"mins":case"m":t=Math.round(60*n);break;case"hour":case"hours":case"hr":case"hrs":case"h":t=Math.round(3600*n);break;case"day":case"days":case"d":t=Math.round(86400*n);break;case"week":case"weeks":case"w":t=Math.round(604800*n);break;default:t=Math.round(0x1e187e0*n)}return"-"===r[1]||"ago"===r[4]?-t:t};function tT(e,t){if(!Number.isFinite(t))throw TypeError(`Invalid ${e} input`);return t}let tR=e=>e.includes("/")?e.toLowerCase():`application/${e.toLowerCase()}`,tC=(e,t)=>"string"==typeof e?t.includes(e):!!Array.isArray(e)&&t.some(Set.prototype.has.bind(new Set(e)));class tP{#l;constructor(e){if(!eP(e))throw TypeError("JWT Claims Set MUST be an object");this.#l=structuredClone(e)}data(){return ec.encode(JSON.stringify(this.#l))}get iss(){return this.#l.iss}set iss(e){this.#l.iss=e}get sub(){return this.#l.sub}set sub(e){this.#l.sub=e}get aud(){return this.#l.aud}set aud(e){this.#l.aud=e}set jti(e){this.#l.jti=e}set nbf(e){"number"==typeof e?this.#l.nbf=tT("setNotBefore",e):e instanceof Date?this.#l.nbf=tT("setNotBefore",tA(e)):this.#l.nbf=tA(new Date)+tE(e)}set exp(e){"number"==typeof e?this.#l.exp=tT("setExpirationTime",e):e instanceof Date?this.#l.exp=tT("setExpirationTime",tA(e)):this.#l.exp=tA(new Date)+tE(e)}set iat(e){void 0===e?this.#l.iat=tA(new Date):e instanceof Date?this.#l.iat=tT("setIssuedAt",tA(e)):"string"==typeof e?this.#l.iat=tT("setIssuedAt",tA(new Date)+tE(e)):this.#l.iat=tT("setIssuedAt",e)}}class tO{#i;#o;#s;#t;#d;#u;#f;#p;constructor(e={}){this.#p=new tP(e)}setIssuer(e){return this.#p.iss=e,this}setSubject(e){return this.#p.sub=e,this}setAudience(e){return this.#p.aud=e,this}setJti(e){return this.#p.jti=e,this}setNotBefore(e){return this.#p.nbf=e,this}setExpirationTime(e){return this.#p.exp=e,this}setIssuedAt(e){return this.#p.iat=e,this}setProtectedHeader(e){if(this.#t)throw TypeError("setProtectedHeader can only be called once");return this.#t=e,this}setKeyManagementParameters(e){if(this.#s)throw TypeError("setKeyManagementParameters can only be called once");return this.#s=e,this}setContentEncryptionKey(e){if(this.#i)throw TypeError("setContentEncryptionKey can only be called once");return this.#i=e,this}setInitializationVector(e){if(this.#o)throw TypeError("setInitializationVector can only be called once");return this.#o=e,this}replicateIssuerAsHeader(){return this.#d=!0,this}replicateSubjectAsHeader(){return this.#u=!0,this}replicateAudienceAsHeader(){return this.#f=!0,this}async encrypt(e,t){let r=new tk(this.#p.data());return this.#t&&(this.#d||this.#u||this.#f)&&(this.#t={...this.#t,iss:this.#d?this.#p.iss:void 0,sub:this.#u?this.#p.sub:void 0,aud:this.#f?this.#p.aud:void 0}),r.setProtectedHeader(this.#t),this.#o&&r.setInitializationVector(this.#o),this.#i&&r.setContentEncryptionKey(this.#i),this.#s&&r.setKeyManagementParameters(this.#s),r.encrypt(e,t)}}async function tU(e,t,r){let n;if(!eP(e))throw TypeError("JWK must be an object");switch(t??=e.alg,n??=r?.extractable??e.ext,e.kty){case"oct":if("string"!=typeof e.k||!e.k)throw TypeError('missing "k" (Key Value) Parameter value');return eh(e.k);case"RSA":if("oth"in e&&void 0!==e.oth)throw new ew('RSA JWK "oth" (Other Primes Info) Parameter value is not supported');case"EC":case"OKP":return tr({...e,alg:t,ext:n});default:throw new ew('Unsupported "kty" (Key Type) Parameter value')}}let t$=async(e,t,r,n,a)=>{switch(e){case"dir":if(void 0!==r)throw new ev("Encountered unexpected JWE Encrypted Key");return t;case"ECDH-ES":if(void 0!==r)throw new ev("Encountered unexpected JWE Encrypted Key");case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":{let a,i;if(!eP(n.epk))throw new ev('JOSE Header "epk" (Ephemeral Public Key) missing or invalid');if(eE(t),!e5(t))throw new ew("ECDH with the provided key is not allowed or not supported by your javascript runtime");let o=await tU(n.epk,e);if(eE(o),void 0!==n.apu){if("string"!=typeof n.apu)throw new ev('JOSE Header "apu" (Agreement PartyUInfo) invalid');try{a=eh(n.apu)}catch{throw new ev("Failed to base64url decode the apu")}}if(void 0!==n.apv){if("string"!=typeof n.apv)throw new ev('JOSE Header "apv" (Agreement PartyVInfo) invalid');try{i=eh(n.apv)}catch{throw new ev("Failed to base64url decode the apv")}}let s=await e2(o,t,"ECDH-ES"===e?n.enc:e,"ECDH-ES"===e?to(n.enc):parseInt(e.slice(-5,-2),10),a,i);if("ECDH-ES"===e)return s;if(void 0===r)throw new ev("JWE Encrypted Key missing");return eQ(e.slice(-6),s,r)}case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":if(void 0===r)throw new ev("JWE Encrypted Key missing");return eE(t),tt(e,t,r);case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":{let i;if(void 0===r)throw new ev("JWE Encrypted Key missing");if("number"!=typeof n.p2c)throw new ev('JOSE Header "p2c" (PBES2 Count) missing or invalid');let o=a?.maxPBES2Count||1e4;if(n.p2c>o)throw new ev('JOSE Header "p2c" (PBES2 Count) out is of acceptable bounds');if("string"!=typeof n.p2s)throw new ev('JOSE Header "p2s" (PBES2 Salt) missing or invalid');try{i=eh(n.p2s)}catch{throw new ev("Failed to base64url decode the p2s")}return e4(e,t,r,n.p2c,i)}case"A128KW":case"A192KW":case"A256KW":if(void 0===r)throw new ev("JWE Encrypted Key missing");return eQ(e,t,r);case"A128GCMKW":case"A192GCMKW":case"A256GCMKW":{let a,i;if(void 0===r)throw new ev("JWE Encrypted Key missing");if("string"!=typeof n.iv)throw new ev('JOSE Header "iv" (Initialization Vector) missing or invalid');if("string"!=typeof n.tag)throw new ev('JOSE Header "tag" (Authentication Tag) missing or invalid');try{a=eh(n.iv)}catch{throw new ev("Failed to base64url decode the iv")}try{i=eh(n.tag)}catch{throw new ev("Failed to base64url decode the tag")}return tp(e,t,r,a,i)}default:throw new ew('Invalid or unsupported "alg" (JWE Algorithm) header value')}},tI=(e,t)=>{if(void 0!==t&&(!Array.isArray(t)||t.some(e=>"string"!=typeof e)))throw TypeError(`"${e}" option must be an array of strings`);if(t)return new Set(t)};async function tj(e,t,r){let n,a,i,o,s,c,l;if(!eP(e))throw new ev("Flattened JWE must be an object");if(void 0===e.protected&&void 0===e.header&&void 0===e.unprotected)throw new ev("JOSE Header missing");if(void 0!==e.iv&&"string"!=typeof e.iv)throw new ev("JWE Initialization Vector incorrect type");if("string"!=typeof e.ciphertext)throw new ev("JWE Ciphertext missing or incorrect type");if(void 0!==e.tag&&"string"!=typeof e.tag)throw new ev("JWE Authentication Tag incorrect type");if(void 0!==e.protected&&"string"!=typeof e.protected)throw new ev("JWE Protected Header incorrect type");if(void 0!==e.encrypted_key&&"string"!=typeof e.encrypted_key)throw new ev("JWE Encrypted Key incorrect type");if(void 0!==e.aad&&"string"!=typeof e.aad)throw new ev("JWE AAD incorrect type");if(void 0!==e.header&&!eP(e.header))throw new ev("JWE Shared Unprotected Header incorrect type");if(void 0!==e.unprotected&&!eP(e.unprotected))throw new ev("JWE Per-Recipient Unprotected Header incorrect type");if(e.protected)try{let t=eh(e.protected);n=JSON.parse(el.decode(t))}catch{throw new ev("JWE Protected Header is invalid")}if(!ty(n,e.header,e.unprotected))throw new ev("JWE Protected, JWE Unprotected Header, and JWE Per-Recipient Unprotected Header Parameter names must be disjoint");let d={...n,...e.header,...e.unprotected};if(tb(ev,new Map,r?.crit,n,d),void 0!==d.zip)throw new ew('JWE "zip" (Compression Algorithm) Header Parameter is not supported.');let{alg:u,enc:f}=d;if("string"!=typeof u||!u)throw new ev("missing JWE Algorithm (alg) in JWE Header");if("string"!=typeof f||!f)throw new ev("missing JWE Encryption Algorithm (enc) in JWE Header");let p=r&&tI("keyManagementAlgorithms",r.keyManagementAlgorithms),h=r&&tI("contentEncryptionAlgorithms",r.contentEncryptionAlgorithms);if(p&&!p.has(u)||!p&&u.startsWith("PBES2"))throw new eg('"alg" (Algorithm) Header Parameter value not allowed');if(h&&!h.has(f))throw new eg('"enc" (Encryption Algorithm) Header Parameter value not allowed');if(void 0!==e.encrypted_key)try{a=eh(e.encrypted_key)}catch{throw new ev("Failed to base64url decode the encrypted_key")}let y=!1;"function"==typeof t&&(t=await t(n,e),y=!0),t_("dir"===u?f:u,t,"decrypt");let b=await ti(t,u);try{i=await t$(u,b,a,d,r)}catch(e){if(e instanceof TypeError||e instanceof ev||e instanceof ew)throw e;i=ts(f)}if(void 0!==e.iv)try{o=eh(e.iv)}catch{throw new ev("Failed to base64url decode the iv")}if(void 0!==e.tag)try{s=eh(e.tag)}catch{throw new ev("Failed to base64url decode the tag")}let m=ec.encode(e.protected??"");c=void 0!==e.aad?ed(m,ec.encode("."),ec.encode(e.aad)):m;try{l=eh(e.ciphertext)}catch{throw new ev("Failed to base64url decode the ciphertext")}let x={plaintext:await tu(f,i,l,o,s,c)};if(void 0!==e.protected&&(x.protectedHeader=n),void 0!==e.aad)try{x.additionalAuthenticatedData=eh(e.aad)}catch{throw new ev("Failed to base64url decode the aad")}return(void 0!==e.unprotected&&(x.sharedUnprotectedHeader=e.unprotected),void 0!==e.header&&(x.unprotectedHeader=e.header),y)?{...x,key:b}:x}async function tH(e,t,r){if(e instanceof Uint8Array&&(e=el.decode(e)),"string"!=typeof e)throw new ev("Compact JWE must be a string or Uint8Array");let{0:n,1:a,2:i,3:o,4:s,length:c}=e.split(".");if(5!==c)throw new ev("Invalid Compact JWE");let l=await tj({ciphertext:o,iv:i||void 0,protected:n,tag:s||void 0,encrypted_key:a||void 0},t,r),d={plaintext:l.plaintext,protectedHeader:l.protectedHeader};return"function"==typeof t?{...d,key:l.key}:d}async function tD(e,t,r){let n=await tH(e,t,r),a=function(e,t,r={}){let n,a;try{n=JSON.parse(el.decode(t))}catch{}if(!eP(n))throw new ek("JWT Claims Set must be a top-level JSON object");let{typ:i}=r;if(i&&("string"!=typeof e.typ||tR(e.typ)!==tR(i)))throw new em('unexpected "typ" JWT header value',n,"typ","check_failed");let{requiredClaims:o=[],issuer:s,subject:c,audience:l,maxTokenAge:d}=r,u=[...o];for(let e of(void 0!==d&&u.push("iat"),void 0!==l&&u.push("aud"),void 0!==c&&u.push("sub"),void 0!==s&&u.push("iss"),new Set(u.reverse())))if(!(e in n))throw new em(`missing required "${e}" claim`,n,e,"missing");if(s&&!(Array.isArray(s)?s:[s]).includes(n.iss))throw new em('unexpected "iss" claim value',n,"iss","check_failed");if(c&&n.sub!==c)throw new em('unexpected "sub" claim value',n,"sub","check_failed");if(l&&!tC(n.aud,"string"==typeof l?[l]:l))throw new em('unexpected "aud" claim value',n,"aud","check_failed");switch(typeof r.clockTolerance){case"string":a=tE(r.clockTolerance);break;case"number":a=r.clockTolerance;break;case"undefined":a=0;break;default:throw TypeError("Invalid clockTolerance option type")}let{currentDate:f}=r,p=tA(f||new Date);if((void 0!==n.iat||d)&&"number"!=typeof n.iat)throw new em('"iat" claim must be a number',n,"iat","invalid");if(void 0!==n.nbf){if("number"!=typeof n.nbf)throw new em('"nbf" claim must be a number',n,"nbf","invalid");if(n.nbf>p+a)throw new em('"nbf" claim timestamp check failed',n,"nbf","check_failed")}if(void 0!==n.exp){if("number"!=typeof n.exp)throw new em('"exp" claim must be a number',n,"exp","invalid");if(n.exp<=p-a)throw new ex('"exp" claim timestamp check failed',n,"exp","check_failed")}if(d){let e=p-n.iat;if(e-a>("number"==typeof d?d:tE(d)))throw new ex('"iat" claim timestamp check failed (too far in the past)',n,"iat","check_failed");if(e<0-a)throw new em('"iat" claim timestamp check failed (it should be in the past)',n,"iat","check_failed")}return n}(n.protectedHeader,n.plaintext,r),{protectedHeader:i}=n;if(void 0!==i.iss&&i.iss!==a.iss)throw new em('replicated "iss" claim header parameter mismatch',a,"iss","mismatch");if(void 0!==i.sub&&i.sub!==a.sub)throw new em('replicated "sub" claim header parameter mismatch',a,"sub","mismatch");if(void 0!==i.aud&&JSON.stringify(i.aud)!==JSON.stringify(a.aud))throw new em('replicated "aud" claim header parameter mismatch',a,"aud","mismatch");let o={payload:a,protectedHeader:i};return"function"==typeof t?{...o,key:n.key}:o}let tW=/^[!#$%&'*+\-.^_`|~0-9A-Za-z]+$/,tK=/^("?)[\u0021\u0023-\u002B\u002D-\u003A\u003C-\u005B\u005D-\u007E]*\1$/,tL=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,tM=/^[\u0020-\u003A\u003D-\u007E]*$/,tN=Object.prototype.toString,tJ=(()=>{let e=function(){};return e.prototype=Object.create(null),e})();function tB(e,t){let r=new tJ,n=e.length;if(n<2)return r;let a=t?.decode||tV,i=0;do{let t=e.indexOf("=",i);if(-1===t)break;let o=e.indexOf(";",i),s=-1===o?n:o;if(t>s){i=e.lastIndexOf(";",t-1)+1;continue}let c=tq(e,i,t),l=tz(e,t,c),d=e.slice(c,l);if(void 0===r[d]){let n=tq(e,t+1,s),i=tz(e,s,n),o=a(e.slice(n,i));r[d]=o}i=s+1}while(i<n);return r}function tq(e,t,r){do{let r=e.charCodeAt(t);if(32!==r&&9!==r)return t}while(++t<r);return r}function tz(e,t,r){for(;t>r;){let r=e.charCodeAt(--t);if(32!==r&&9!==r)return t+1}return r}function tF(e,t,r){let n=r?.encode||encodeURIComponent;if(!tW.test(e))throw TypeError(`argument name is invalid: ${e}`);let a=n(t);if(!tK.test(a))throw TypeError(`argument val is invalid: ${t}`);let i=e+"="+a;if(!r)return i;if(void 0!==r.maxAge){if(!Number.isInteger(r.maxAge))throw TypeError(`option maxAge is invalid: ${r.maxAge}`);i+="; Max-Age="+r.maxAge}if(r.domain){if(!tL.test(r.domain))throw TypeError(`option domain is invalid: ${r.domain}`);i+="; Domain="+r.domain}if(r.path){if(!tM.test(r.path))throw TypeError(`option path is invalid: ${r.path}`);i+="; Path="+r.path}if(r.expires){var o;if(o=r.expires,"[object Date]"!==tN.call(o)||!Number.isFinite(r.expires.valueOf()))throw TypeError(`option expires is invalid: ${r.expires}`);i+="; Expires="+r.expires.toUTCString()}if(r.httpOnly&&(i+="; HttpOnly"),r.secure&&(i+="; Secure"),r.partitioned&&(i+="; Partitioned"),r.priority)switch("string"==typeof r.priority?r.priority.toLowerCase():void 0){case"low":i+="; Priority=Low";break;case"medium":i+="; Priority=Medium";break;case"high":i+="; Priority=High";break;default:throw TypeError(`option priority is invalid: ${r.priority}`)}if(r.sameSite)switch("string"==typeof r.sameSite?r.sameSite.toLowerCase():r.sameSite){case!0:case"strict":i+="; SameSite=Strict";break;case"lax":i+="; SameSite=Lax";break;case"none":i+="; SameSite=None";break;default:throw TypeError(`option sameSite is invalid: ${r.sameSite}`)}return i}function tV(e){if(-1===e.indexOf("%"))return e;try{return decodeURIComponent(e)}catch(t){return e}}let{q:tG}=c,tX=()=>Date.now()/1e3|0,tY="A256CBC-HS512";async function tZ(e){let{token:t={},secret:r,maxAge:n=2592e3,salt:a}=e,i=Array.isArray(r)?r:[r],o=await t0(tY,i[0],a),s=await eW({kty:"oct",k:ey(o)},`sha${o.byteLength<<3}`);return await new tO(t).setProtectedHeader({alg:"dir",enc:tY,kid:s}).setIssuedAt().setExpirationTime(tX()+n).setJti(crypto.randomUUID()).encrypt(o)}async function tQ(e){let{token:t,secret:r,salt:n}=e,a=Array.isArray(r)?r:[r];if(!t)return null;let{payload:i}=await tD(t,async({kid:e,enc:t})=>{for(let r of a){let a=await t0(t,r,n);if(void 0===e||e===await eW({kty:"oct",k:ey(a)},`sha${a.byteLength<<3}`))return a}throw Error("no matching decryption secret")},{clockTolerance:15,keyManagementAlgorithms:["dir"],contentEncryptionAlgorithms:[tY,"A256GCM"]});return i}async function t0(e,t,r){let n;switch(e){case"A256CBC-HS512":n=64;break;case"A256GCM":n=32;break;default:throw Error("Unsupported JWT Content Encryption Algorithm")}return await eo("sha256",t,r,`Auth.js Generated Encryption Key (${r})`,n)}async function t1({options:e,paramValue:t,cookieValue:r}){let{url:n,callbacks:a}=e,i=n.origin;return t?i=await a.redirect({url:t,baseUrl:n.origin}):r&&(i=await a.redirect({url:r,baseUrl:n.origin})),{callbackUrl:i,callbackUrlCookie:i!==r?i:void 0}}let t2="\x1b[31m",t5="\x1b[0m",t6={error(e){let t=e instanceof p?e.type:e.name;if(console.error(`${t2}[auth][error]${t5} ${t}: ${e.message}`),e.cause&&"object"==typeof e.cause&&"err"in e.cause&&e.cause.err instanceof Error){let{err:t,...r}=e.cause;console.error(`${t2}[auth][cause]${t5}:`,t.stack),r&&console.error(`${t2}[auth][details]${t5}:`,JSON.stringify(r,null,2))}else e.stack&&console.error(e.stack.replace(/.*/,"").substring(1))},warn(e){console.warn(`\x1b[33m[auth][warn][${e}]${t5}`,"Read more: https://warnings.authjs.dev")},debug(e,t){console.log(`\x1b[90m[auth][debug]:${t5} ${e}`,JSON.stringify(t,null,2))}};function t3(e){let t={...t6};return e.debug||(t.debug=()=>{}),e.logger?.error&&(t.error=e.logger.error),e.logger?.warn&&(t.warn=e.logger.warn),e.logger?.debug&&(t.debug=e.logger.debug),e.logger??(e.logger=t),t}let t8=["providers","session","csrf","signin","signout","callback","verify-request","error","webauthn-options"],{q:t4,l:t9}=c;async function t7(e){if(!("body"in e)||!e.body||"POST"!==e.method)return;let t=e.headers.get("content-type");return t?.includes("application/json")?await e.json():t?.includes("application/x-www-form-urlencoded")?Object.fromEntries(new URLSearchParams(await e.text())):void 0}async function re(e,t){try{if("GET"!==e.method&&"POST"!==e.method)throw new H("Only GET and POST requests are supported");t.basePath??(t.basePath="/auth");let r=new URL(e.url),{action:n,providerId:a}=function(e,t){let r=e.match(RegExp(`^${t}(.+)`));if(null===r)throw new H(`Cannot parse action at ${e}`);let n=r.at(-1).replace(/^\//,"").split("/").filter(Boolean);if(1!==n.length&&2!==n.length)throw new H(`Cannot parse action at ${e}`);let[a,i]=n;if(!t8.includes(a)||i&&!["signin","callback","webauthn-options"].includes(a))throw new H(`Cannot parse action at ${e}`);return{action:a,providerId:i}}(r.pathname,t.basePath);return{url:r,action:n,providerId:a,method:e.method,headers:Object.fromEntries(e.headers),body:e.body?await t7(e):void 0,cookies:t4(e.headers.get("cookie")??"")??{},error:r.searchParams.get("error")??void 0,query:Object.fromEntries(r.searchParams)}}catch(n){let r=t3(t);r.error(n),r.debug("request",e)}}function rt(e){let t=new Headers(e.headers);e.cookies?.forEach(e=>{let{name:r,value:n,options:a}=e,i=t9(r,n,a);t.has("Set-Cookie")?t.append("Set-Cookie",i):t.set("Set-Cookie",i)});let r=e.body;"application/json"===t.get("content-type")?r=JSON.stringify(e.body):"application/x-www-form-urlencoded"===t.get("content-type")&&(r=new URLSearchParams(e.body).toString());let n=new Response(r,{headers:t,status:e.redirect?302:e.status??200});return e.redirect&&n.headers.set("Location",e.redirect),n}async function rr(e){let t=new TextEncoder().encode(e);return Array.from(new Uint8Array(await crypto.subtle.digest("SHA-256",t))).map(e=>e.toString(16).padStart(2,"0")).join("").toString()}function rn(e){let t=e=>("0"+e.toString(16)).slice(-2);return Array.from(crypto.getRandomValues(new Uint8Array(e))).reduce((e,r)=>e+t(r),"")}async function ra({options:e,cookieValue:t,isPost:r,bodyValue:n}){if(t){let[a,i]=t.split("|");if(i===await rr(`${a}${e.secret}`))return{csrfTokenVerified:r&&a===n,csrfToken:a}}let a=rn(32),i=await rr(`${a}${e.secret}`);return{cookie:`${a}|${i}`,csrfToken:a}}function ri(e,t){if(!t)throw new M(`CSRF token was missing during an action ${e}`)}function ro(e){return null!==e&&"object"==typeof e}function rs(e,...t){if(!t.length)return e;let r=t.shift();if(ro(e)&&ro(r))for(let t in r)ro(r[t])?(ro(e[t])||(e[t]=Array.isArray(r[t])?[]:{}),rs(e[t],r[t])):void 0!==r[t]&&(e[t]=r[t]);return rs(e,...t)}let rc=Symbol("skip-csrf-check"),rl=Symbol("return-type-raw"),rd=Symbol("custom-fetch"),ru=Symbol("conform-internal"),rf=e=>rh({id:e.sub??e.id??crypto.randomUUID(),name:e.name??e.nickname??e.preferred_username,email:e.email,image:e.picture}),rp=e=>rh({access_token:e.access_token,id_token:e.id_token,refresh_token:e.refresh_token,expires_at:e.expires_at,scope:e.scope,token_type:e.token_type,session_state:e.session_state});function rh(e){let t={};for(let[r,n]of Object.entries(e))void 0!==n&&(t[r]=n);return t}function ry(e,t){if(!e&&t)return;if("string"==typeof e)return{url:new URL(e)};let r=new URL(e?.url??"https://authjs.dev");if(e?.params!=null)for(let[t,n]of Object.entries(e.params))"claims"===t&&(n=JSON.stringify(n)),r.searchParams.set(t,String(n));return{url:r,request:e?.request,conform:e?.conform,...e?.clientPrivateKey?{clientPrivateKey:e?.clientPrivateKey}:null}}let rb={signIn:()=>!0,redirect:({url:e,baseUrl:t})=>e.startsWith("/")?`${t}${e}`:new URL(e).origin===t?e:t,session:({session:e})=>({user:{name:e.user?.name,email:e.user?.email,image:e.user?.image},expires:e.expires?.toISOString?.()??e.expires}),jwt:({token:e})=>e};async function rm({authOptions:e,providerId:t,action:r,url:n,cookies:a,callbackUrl:i,csrfToken:o,csrfDisabled:s,isPost:c}){var l;let d=t3(e),{providers:f,provider:p}=function(e){let{providerId:t,config:r}=e,n=new URL(r.basePath??"/auth",e.url.origin),a=r.providers.map(e=>{let t="function"==typeof e?e():e,{options:a,...i}=t,o=a?.id??i.id,s=rs(i,a,{signinUrl:`${n}/signin/${o}`,callbackUrl:`${n}/callback/${o}`});if("oauth"===t.type||"oidc"===t.type){s.redirectProxyUrl??(s.redirectProxyUrl=a?.redirectProxyUrl??r.redirectProxyUrl);let e=function(e){e.issuer&&(e.wellKnown??(e.wellKnown=`${e.issuer}/.well-known/openid-configuration`));let t=ry(e.authorization,e.issuer);t&&!t.url?.searchParams.has("scope")&&t.url.searchParams.set("scope","openid profile email");let r=ry(e.token,e.issuer),n=ry(e.userinfo,e.issuer),a=e.checks??["pkce"];return e.redirectProxyUrl&&(a.includes("state")||a.push("state"),e.redirectProxyUrl=`${e.redirectProxyUrl}/callback/${e.id}`),{...e,authorization:t,token:r,checks:a,userinfo:n,profile:e.profile??rf,account:e.account??rp}}(s);return e.authorization?.url.searchParams.get("response_mode")==="form_post"&&delete e.redirectProxyUrl,e[rd]??(e[rd]=a?.[rd]),e}return s}),i=a.find(({id:e})=>e===t);if(t&&!i){let e=a.map(e=>e.id).join(", ");throw Error(`Provider with id "${t}" not found. Available providers: [${e}].`)}return{providers:a,provider:i}}({url:n,providerId:t,config:e}),h=!1;if((p?.type==="oauth"||p?.type==="oidc")&&p.redirectProxyUrl)try{h=new URL(p.redirectProxyUrl).origin===n.origin}catch{throw TypeError(`redirectProxyUrl must be a valid URL. Received: ${p.redirectProxyUrl}`)}let b={debug:!1,pages:{},theme:{colorScheme:"auto",logo:"",brandColor:"",buttonText:""},...e,url:n,action:r,provider:p,cookies:rs(u(e.useSecureCookies??"https:"===n.protocol),e.cookies),providers:f,session:{strategy:e.adapter?"database":"jwt",maxAge:2592e3,updateAge:86400,generateSessionToken:()=>crypto.randomUUID(),...e.session},jwt:{secret:e.secret,maxAge:e.session?.maxAge??2592e3,encode:tZ,decode:tQ,...e.jwt},events:Object.keys(l=e.events??{}).reduce((e,t)=>(e[t]=async(...e)=>{try{let r=l[t];return await r(...e)}catch(e){d.error(new g(e))}},e),{}),adapter:function(e,t){if(e)return Object.keys(e).reduce((r,n)=>(r[n]=async(...r)=>{try{t.debug(`adapter_${n}`,{args:r});let a=e[n];return await a(...r)}catch(r){let e=new y(r);throw t.error(e),e}},r),{})}(e.adapter,d),callbacks:{...rb,...e.callbacks},logger:d,callbackUrl:n.origin,isOnRedirectProxy:h,experimental:{...e.experimental}},m=[];if(s)b.csrfTokenVerified=!0;else{let{csrfToken:e,cookie:t,csrfTokenVerified:r}=await ra({options:b,cookieValue:a?.[b.cookies.csrfToken.name],isPost:c,bodyValue:o});b.csrfToken=e,b.csrfTokenVerified=r,t&&m.push({name:b.cookies.csrfToken.name,value:t,options:b.cookies.csrfToken.options})}let{callbackUrl:x,callbackUrlCookie:w}=await t1({options:b,cookieValue:a?.[b.cookies.callbackUrl.name],paramValue:i});return b.callbackUrl=x,w&&m.push({name:b.cookies.callbackUrl.name,value:w,options:b.cookies.callbackUrl.options}),{options:b,cookies:m}}var rx,rg,rw,r_,rv,rk,rA,rS,rE,rT,rR,rC,rP,rO,rU,r$,rI={},rj=[],rH=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,rD=Array.isArray;function rW(e,t){for(var r in t)e[r]=t[r];return e}function rK(e){e&&e.parentNode&&e.parentNode.removeChild(e)}function rL(e,t,r){var n,a,i,o={};for(i in t)"key"==i?n=t[i]:"ref"==i?a=t[i]:o[i]=t[i];if(arguments.length>2&&(o.children=arguments.length>3?rA.call(arguments,2):r),"function"==typeof e&&null!=e.defaultProps)for(i in e.defaultProps)void 0===o[i]&&(o[i]=e.defaultProps[i]);return rM(e,o,n,a,null)}function rM(e,t,r,n,a){var i={type:e,props:t,key:r,ref:n,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,constructor:void 0,__v:null==a?++rE:a,__i:-1,__u:0};return null==a&&null!=rS.vnode&&rS.vnode(i),i}function rN(e){return e.children}function rJ(e,t){this.props=e,this.context=t}function rB(e,t){if(null==t)return e.__?rB(e.__,e.__i+1):null;for(var r;t<e.__k.length;t++)if(null!=(r=e.__k[t])&&null!=r.__e)return r.__e;return"function"==typeof e.type?rB(e):null}function rq(e){(!e.__d&&(e.__d=!0)&&rT.push(e)&&!rz.__r++||rR!==rS.debounceRendering)&&((rR=rS.debounceRendering)||rC)(rz)}function rz(){var e,t,r,n,a,i,o,s;for(rT.sort(rP);e=rT.shift();)e.__d&&(t=rT.length,n=void 0,i=(a=(r=e).__v).__e,o=[],s=[],r.__P&&((n=rW({},a)).__v=a.__v+1,rS.vnode&&rS.vnode(n),rY(r.__P,n,a,r.__n,r.__P.namespaceURI,32&a.__u?[i]:null,o,null==i?rB(a):i,!!(32&a.__u),s),n.__v=a.__v,n.__.__k[n.__i]=n,rZ(o,n,s),n.__e!=i&&function e(t){var r,n;if(null!=(t=t.__)&&null!=t.__c){for(t.__e=t.__c.base=null,r=0;r<t.__k.length;r++)if(null!=(n=t.__k[r])&&null!=n.__e){t.__e=t.__c.base=n.__e;break}return e(t)}}(n)),rT.length>t&&rT.sort(rP));rz.__r=0}function rF(e,t,r,n,a,i,o,s,c,l,d){var u,f,p,h,y,b=n&&n.__k||rj,m=t.length;for(r.__d=c,function(e,t,r){var n,a,i,o,s,c=t.length,l=r.length,d=l,u=0;for(e.__k=[],n=0;n<c;n++)null!=(a=t[n])&&"boolean"!=typeof a&&"function"!=typeof a?(o=n+u,(a=e.__k[n]="string"==typeof a||"number"==typeof a||"bigint"==typeof a||a.constructor==String?rM(null,a,null,null,null):rD(a)?rM(rN,{children:a},null,null,null):void 0===a.constructor&&a.__b>0?rM(a.type,a.props,a.key,a.ref?a.ref:null,a.__v):a).__=e,a.__b=e.__b+1,i=null,-1!==(s=a.__i=function(e,t,r,n){var a=e.key,i=e.type,o=r-1,s=r+1,c=t[r];if(null===c||c&&a==c.key&&i===c.type&&0==(131072&c.__u))return r;if(n>(null!=c&&0==(131072&c.__u)?1:0))for(;o>=0||s<t.length;){if(o>=0){if((c=t[o])&&0==(131072&c.__u)&&a==c.key&&i===c.type)return o;o--}if(s<t.length){if((c=t[s])&&0==(131072&c.__u)&&a==c.key&&i===c.type)return s;s++}}return -1}(a,r,o,d))&&(d--,(i=r[s])&&(i.__u|=131072)),null==i||null===i.__v?(-1==s&&u--,"function"!=typeof a.type&&(a.__u|=65536)):s!==o&&(s==o-1?u--:s==o+1?u++:(s>o?u--:u++,a.__u|=65536))):a=e.__k[n]=null;if(d)for(n=0;n<l;n++)null!=(i=r[n])&&0==(131072&i.__u)&&(i.__e==e.__d&&(e.__d=rB(i)),function e(t,r,n){var a,i;if(rS.unmount&&rS.unmount(t),(a=t.ref)&&(a.current&&a.current!==t.__e||rQ(a,null,r)),null!=(a=t.__c)){if(a.componentWillUnmount)try{a.componentWillUnmount()}catch(e){rS.__e(e,r)}a.base=a.__P=null}if(a=t.__k)for(i=0;i<a.length;i++)a[i]&&e(a[i],r,n||"function"!=typeof t.type);n||rK(t.__e),t.__c=t.__=t.__e=t.__d=void 0}(i,i))}(r,t,b),c=r.__d,u=0;u<m;u++)null!=(p=r.__k[u])&&(f=-1===p.__i?rI:b[p.__i]||rI,p.__i=u,rY(e,p,f,a,i,o,s,c,l,d),h=p.__e,p.ref&&f.ref!=p.ref&&(f.ref&&rQ(f.ref,null,p),d.push(p.ref,p.__c||h,p)),null==y&&null!=h&&(y=h),65536&p.__u||f.__k===p.__k?c=function e(t,r,n){var a,i;if("function"==typeof t.type){for(a=t.__k,i=0;a&&i<a.length;i++)a[i]&&(a[i].__=t,r=e(a[i],r,n));return r}t.__e!=r&&(r&&t.type&&!n.contains(r)&&(r=rB(t)),n.insertBefore(t.__e,r||null),r=t.__e);do r=r&&r.nextSibling;while(null!=r&&8===r.nodeType);return r}(p,c,e):"function"==typeof p.type&&void 0!==p.__d?c=p.__d:h&&(c=h.nextSibling),p.__d=void 0,p.__u&=-196609);r.__d=c,r.__e=y}function rV(e,t,r){"-"===t[0]?e.setProperty(t,null==r?"":r):e[t]=null==r?"":"number"!=typeof r||rH.test(t)?r:r+"px"}function rG(e,t,r,n,a){var i;e:if("style"===t){if("string"==typeof r)e.style.cssText=r;else{if("string"==typeof n&&(e.style.cssText=n=""),n)for(t in n)r&&t in r||rV(e.style,t,"");if(r)for(t in r)n&&r[t]===n[t]||rV(e.style,t,r[t])}}else if("o"===t[0]&&"n"===t[1])i=t!==(t=t.replace(/(PointerCapture)$|Capture$/i,"$1")),t=t.toLowerCase()in e||"onFocusOut"===t||"onFocusIn"===t?t.toLowerCase().slice(2):t.slice(2),e.l||(e.l={}),e.l[t+i]=r,r?n?r.u=n.u:(r.u=rO,e.addEventListener(t,i?r$:rU,i)):e.removeEventListener(t,i?r$:rU,i);else{if("http://www.w3.org/2000/svg"==a)t=t.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if("width"!=t&&"height"!=t&&"href"!=t&&"list"!=t&&"form"!=t&&"tabIndex"!=t&&"download"!=t&&"rowSpan"!=t&&"colSpan"!=t&&"role"!=t&&"popover"!=t&&t in e)try{e[t]=null==r?"":r;break e}catch(e){}"function"==typeof r||(null==r||!1===r&&"-"!==t[4]?e.removeAttribute(t):e.setAttribute(t,"popover"==t&&1==r?"":r))}}function rX(e){return function(t){if(this.l){var r=this.l[t.type+e];if(null==t.t)t.t=rO++;else if(t.t<r.u)return;return r(rS.event?rS.event(t):t)}}}function rY(e,t,r,n,a,i,o,s,c,l){var d,u,f,p,h,y,b,m,x,g,w,_,v,k,A,S,E=t.type;if(void 0!==t.constructor)return null;128&r.__u&&(c=!!(32&r.__u),i=[s=t.__e=r.__e]),(d=rS.__b)&&d(t);e:if("function"==typeof E)try{if(m=t.props,x="prototype"in E&&E.prototype.render,g=(d=E.contextType)&&n[d.__c],w=d?g?g.props.value:d.__:n,r.__c?b=(u=t.__c=r.__c).__=u.__E:(x?t.__c=u=new E(m,w):(t.__c=u=new rJ(m,w),u.constructor=E,u.render=r0),g&&g.sub(u),u.props=m,u.state||(u.state={}),u.context=w,u.__n=n,f=u.__d=!0,u.__h=[],u._sb=[]),x&&null==u.__s&&(u.__s=u.state),x&&null!=E.getDerivedStateFromProps&&(u.__s==u.state&&(u.__s=rW({},u.__s)),rW(u.__s,E.getDerivedStateFromProps(m,u.__s))),p=u.props,h=u.state,u.__v=t,f)x&&null==E.getDerivedStateFromProps&&null!=u.componentWillMount&&u.componentWillMount(),x&&null!=u.componentDidMount&&u.__h.push(u.componentDidMount);else{if(x&&null==E.getDerivedStateFromProps&&m!==p&&null!=u.componentWillReceiveProps&&u.componentWillReceiveProps(m,w),!u.__e&&(null!=u.shouldComponentUpdate&&!1===u.shouldComponentUpdate(m,u.__s,w)||t.__v===r.__v)){for(t.__v!==r.__v&&(u.props=m,u.state=u.__s,u.__d=!1),t.__e=r.__e,t.__k=r.__k,t.__k.some(function(e){e&&(e.__=t)}),_=0;_<u._sb.length;_++)u.__h.push(u._sb[_]);u._sb=[],u.__h.length&&o.push(u);break e}null!=u.componentWillUpdate&&u.componentWillUpdate(m,u.__s,w),x&&null!=u.componentDidUpdate&&u.__h.push(function(){u.componentDidUpdate(p,h,y)})}if(u.context=w,u.props=m,u.__P=e,u.__e=!1,v=rS.__r,k=0,x){for(u.state=u.__s,u.__d=!1,v&&v(t),d=u.render(u.props,u.state,u.context),A=0;A<u._sb.length;A++)u.__h.push(u._sb[A]);u._sb=[]}else do u.__d=!1,v&&v(t),d=u.render(u.props,u.state,u.context),u.state=u.__s;while(u.__d&&++k<25);u.state=u.__s,null!=u.getChildContext&&(n=rW(rW({},n),u.getChildContext())),x&&!f&&null!=u.getSnapshotBeforeUpdate&&(y=u.getSnapshotBeforeUpdate(p,h)),rF(e,rD(S=null!=d&&d.type===rN&&null==d.key?d.props.children:d)?S:[S],t,r,n,a,i,o,s,c,l),u.base=t.__e,t.__u&=-161,u.__h.length&&o.push(u),b&&(u.__E=u.__=null)}catch(e){if(t.__v=null,c||null!=i){for(t.__u|=c?160:128;s&&8===s.nodeType&&s.nextSibling;)s=s.nextSibling;i[i.indexOf(s)]=null,t.__e=s}else t.__e=r.__e,t.__k=r.__k;rS.__e(e,t,r)}else null==i&&t.__v===r.__v?(t.__k=r.__k,t.__e=r.__e):t.__e=function(e,t,r,n,a,i,o,s,c){var l,d,u,f,p,h,y,b=r.props,m=t.props,x=t.type;if("svg"===x?a="http://www.w3.org/2000/svg":"math"===x?a="http://www.w3.org/1998/Math/MathML":a||(a="http://www.w3.org/1999/xhtml"),null!=i){for(l=0;l<i.length;l++)if((p=i[l])&&"setAttribute"in p==!!x&&(x?p.localName===x:3===p.nodeType)){e=p,i[l]=null;break}}if(null==e){if(null===x)return document.createTextNode(m);e=document.createElementNS(a,x,m.is&&m),s&&(rS.__m&&rS.__m(t,i),s=!1),i=null}if(null===x)b===m||s&&e.data===m||(e.data=m);else{if(i=i&&rA.call(e.childNodes),b=r.props||rI,!s&&null!=i)for(b={},l=0;l<e.attributes.length;l++)b[(p=e.attributes[l]).name]=p.value;for(l in b)if(p=b[l],"children"==l);else if("dangerouslySetInnerHTML"==l)u=p;else if(!(l in m)){if("value"==l&&"defaultValue"in m||"checked"==l&&"defaultChecked"in m)continue;rG(e,l,null,p,a)}for(l in m)p=m[l],"children"==l?f=p:"dangerouslySetInnerHTML"==l?d=p:"value"==l?h=p:"checked"==l?y=p:s&&"function"!=typeof p||b[l]===p||rG(e,l,p,b[l],a);if(d)s||u&&(d.__html===u.__html||d.__html===e.innerHTML)||(e.innerHTML=d.__html),t.__k=[];else if(u&&(e.innerHTML=""),rF(e,rD(f)?f:[f],t,r,n,"foreignObject"===x?"http://www.w3.org/1999/xhtml":a,i,o,i?i[0]:r.__k&&rB(r,0),s,c),null!=i)for(l=i.length;l--;)rK(i[l]);s||(l="value","progress"===x&&null==h?e.removeAttribute("value"):void 0===h||h===e[l]&&("progress"!==x||h)&&("option"!==x||h===b[l])||rG(e,l,h,b[l],a),l="checked",void 0!==y&&y!==e[l]&&rG(e,l,y,b[l],a))}return e}(r.__e,t,r,n,a,i,o,c,l);(d=rS.diffed)&&d(t)}function rZ(e,t,r){t.__d=void 0;for(var n=0;n<r.length;n++)rQ(r[n],r[++n],r[++n]);rS.__c&&rS.__c(t,e),e.some(function(t){try{e=t.__h,t.__h=[],e.some(function(e){e.call(t)})}catch(e){rS.__e(e,t.__v)}})}function rQ(e,t,r){try{if("function"==typeof e){var n="function"==typeof e.__u;n&&e.__u(),n&&null==t||(e.__u=e(t))}else e.current=t}catch(e){rS.__e(e,r)}}function r0(e,t,r){return this.constructor(e,r)}function r1(e,t){var r,n,a,i,o;r=e,rS.__&&rS.__(r,t),a=(n="function"==typeof r1)?null:r1&&r1.__k||t.__k,i=[],o=[],rY(t,r=(!n&&r1||t).__k=rL(rN,null,[r]),a||rI,rI,t.namespaceURI,!n&&r1?[r1]:a?null:t.firstChild?rA.call(t.childNodes):null,i,!n&&r1?r1:a?a.__e:t.firstChild,n,o),rZ(i,r,o)}rA=rj.slice,rS={__e:function(e,t,r,n){for(var a,i,o;t=t.__;)if((a=t.__c)&&!a.__)try{if((i=a.constructor)&&null!=i.getDerivedStateFromError&&(a.setState(i.getDerivedStateFromError(e)),o=a.__d),null!=a.componentDidCatch&&(a.componentDidCatch(e,n||{}),o=a.__d),o)return a.__E=a}catch(t){e=t}throw e}},rE=0,rJ.prototype.setState=function(e,t){var r;r=null!=this.__s&&this.__s!==this.state?this.__s:this.__s=rW({},this.state),"function"==typeof e&&(e=e(rW({},r),this.props)),e&&rW(r,e),null!=e&&this.__v&&(t&&this._sb.push(t),rq(this))},rJ.prototype.forceUpdate=function(e){this.__v&&(this.__e=!0,e&&this.__h.push(e),rq(this))},rJ.prototype.render=rN,rT=[],rC="function"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,rP=function(e,t){return e.__v.__b-t.__v.__b},rz.__r=0,rO=0,rU=rX(!1),r$=rX(!0);var r2=/[\s\n\\/='"\0<>]/,r5=/^(xlink|xmlns|xml)([A-Z])/,r6=/^accessK|^auto[A-Z]|^cell|^ch|^col|cont|cross|dateT|encT|form[A-Z]|frame|hrefL|inputM|maxL|minL|noV|playsI|popoverT|readO|rowS|src[A-Z]|tabI|useM|item[A-Z]/,r3=/^ac|^ali|arabic|basel|cap|clipPath$|clipRule$|color|dominant|enable|fill|flood|font|glyph[^R]|horiz|image|letter|lighting|marker[^WUH]|overline|panose|pointe|paint|rendering|shape|stop|strikethrough|stroke|text[^L]|transform|underline|unicode|units|^v[^i]|^w|^xH/,r8=new Set(["draggable","spellcheck"]),r4=/["&<]/;function r9(e){if(0===e.length||!1===r4.test(e))return e;for(var t=0,r=0,n="",a="";r<e.length;r++){switch(e.charCodeAt(r)){case 34:a="&quot;";break;case 38:a="&amp;";break;case 60:a="&lt;";break;default:continue}r!==t&&(n+=e.slice(t,r)),n+=a,t=r+1}return r!==t&&(n+=e.slice(t,r)),n}var r7={},ne=new Set(["animation-iteration-count","border-image-outset","border-image-slice","border-image-width","box-flex","box-flex-group","box-ordinal-group","column-count","fill-opacity","flex","flex-grow","flex-negative","flex-order","flex-positive","flex-shrink","flood-opacity","font-weight","grid-column","grid-row","line-clamp","line-height","opacity","order","orphans","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-miterlimit","stroke-opacity","stroke-width","tab-size","widows","z-index","zoom"]),nt=/[A-Z]/g;function nr(){this.__d=!0}var nn,na,ni,no,ns={},nc=[],nl=Array.isArray,nd=Object.assign;function nu(e,t){var r,n=e.type,a=!0;return e.__c?(a=!1,(r=e.__c).state=r.__s):r=new n(e.props,t),e.__c=r,r.__v=e,r.props=e.props,r.context=t,r.__d=!0,null==r.state&&(r.state=ns),null==r.__s&&(r.__s=r.state),n.getDerivedStateFromProps?r.state=nd({},r.state,n.getDerivedStateFromProps(r.props,r.state)):a&&r.componentWillMount?(r.componentWillMount(),r.state=r.__s!==r.state?r.__s:r.state):!a&&r.componentWillUpdate&&r.componentWillUpdate(),ni&&ni(e),r.render(r.props,r.state,t)}var nf=new Set(["area","base","br","col","command","embed","hr","img","input","keygen","link","meta","param","source","track","wbr"]),np=0;function nh(e,t,r,n,a,i){t||(t={});var o,s,c=t;"ref"in t&&(o=t.ref,delete t.ref);var l={type:e,props:c,key:r,ref:o,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,constructor:void 0,__v:--np,__i:-1,__u:0,__source:a,__self:i};if("function"==typeof e&&(o=e.defaultProps))for(s in o)void 0===c[s]&&(c[s]=o[s]);return rS.vnode&&rS.vnode(l),l}async function ny(e,t){let r=window.SimpleWebAuthnBrowser;async function n(r){let n=new URL(`${e}/webauthn-options/${t}`);r&&n.searchParams.append("action",r),i().forEach(e=>{n.searchParams.append(e.name,e.value)});let a=await fetch(n);if(!a.ok){console.error("Failed to fetch options",a);return}return a.json()}function a(){let e=`#${t}-form`,r=document.querySelector(e);if(!r)throw Error(`Form '${e}' not found`);return r}function i(){return Array.from(a().querySelectorAll("input[data-form-field]"))}async function o(e,t){let r=a();if(e){let t=document.createElement("input");t.type="hidden",t.name="action",t.value=e,r.appendChild(t)}if(t){let e=document.createElement("input");e.type="hidden",e.name="data",e.value=JSON.stringify(t),r.appendChild(e)}return r.submit()}async function s(e,t){let n=await r.startAuthentication(e,t);return await o("authenticate",n)}async function c(e){i().forEach(e=>{if(e.required&&!e.value)throw Error(`Missing required field: ${e.name}`)});let t=await r.startRegistration(e);return await o("register",t)}async function l(){if(!r.browserSupportsWebAuthnAutofill())return;let e=await n("authenticate");if(!e){console.error("Failed to fetch option for autofill authentication");return}try{await s(e.options,!0)}catch(e){console.error(e)}}(async function(){let e=a();if(!r.browserSupportsWebAuthn()){e.style.display="none";return}e&&e.addEventListener("submit",async e=>{e.preventDefault();let t=await n(void 0);if(!t){console.error("Failed to fetch options for form submission");return}if("authenticate"===t.action)try{await s(t.options,!1)}catch(e){console.error(e)}else if("register"===t.action)try{await c(t.options)}catch(e){console.error(e)}})})(),l()}let nb={default:"Unable to sign in.",Signin:"Try signing in with a different account.",OAuthSignin:"Try signing in with a different account.",OAuthCallbackError:"Try signing in with a different account.",OAuthCreateAccount:"Try signing in with a different account.",EmailCreateAccount:"Try signing in with a different account.",Callback:"Try signing in with a different account.",OAuthAccountNotLinked:"To confirm your identity, sign in with the same account you used originally.",EmailSignin:"The e-mail could not be sent.",CredentialsSignin:"Sign in failed. Check the details you provided are correct.",SessionRequired:"Please sign in to access this page."},nm=`:root {
  --border-width: 1px;
  --border-radius: 0.5rem;
  --color-error: #c94b4b;
  --color-info: #157efb;
  --color-info-hover: #0f6ddb;
  --color-info-text: #fff;
}

.__next-auth-theme-auto,
.__next-auth-theme-light {
  --color-background: #ececec;
  --color-background-hover: rgba(236, 236, 236, 0.8);
  --color-background-card: #fff;
  --color-text: #000;
  --color-primary: #444;
  --color-control-border: #bbb;
  --color-button-active-background: #f9f9f9;
  --color-button-active-border: #aaa;
  --color-separator: #ccc;
  --provider-bg: #fff;
  --provider-bg-hover: color-mix(
    in srgb,
    var(--provider-brand-color) 30%,
    #fff
  );
}

.__next-auth-theme-dark {
  --color-background: #161b22;
  --color-background-hover: rgba(22, 27, 34, 0.8);
  --color-background-card: #0d1117;
  --color-text: #fff;
  --color-primary: #ccc;
  --color-control-border: #555;
  --color-button-active-background: #060606;
  --color-button-active-border: #666;
  --color-separator: #444;
  --provider-bg: #161b22;
  --provider-bg-hover: color-mix(
    in srgb,
    var(--provider-brand-color) 30%,
    #000
  );
}

.__next-auth-theme-dark img[src$="42-school.svg"],
  .__next-auth-theme-dark img[src$="apple.svg"],
  .__next-auth-theme-dark img[src$="boxyhq-saml.svg"],
  .__next-auth-theme-dark img[src$="eveonline.svg"],
  .__next-auth-theme-dark img[src$="github.svg"],
  .__next-auth-theme-dark img[src$="mailchimp.svg"],
  .__next-auth-theme-dark img[src$="medium.svg"],
  .__next-auth-theme-dark img[src$="okta.svg"],
  .__next-auth-theme-dark img[src$="patreon.svg"],
  .__next-auth-theme-dark img[src$="ping-id.svg"],
  .__next-auth-theme-dark img[src$="roblox.svg"],
  .__next-auth-theme-dark img[src$="threads.svg"],
  .__next-auth-theme-dark img[src$="wikimedia.svg"] {
    filter: invert(1);
  }

.__next-auth-theme-dark #submitButton {
    background-color: var(--provider-bg, var(--color-info));
  }

@media (prefers-color-scheme: dark) {
  .__next-auth-theme-auto {
    --color-background: #161b22;
    --color-background-hover: rgba(22, 27, 34, 0.8);
    --color-background-card: #0d1117;
    --color-text: #fff;
    --color-primary: #ccc;
    --color-control-border: #555;
    --color-button-active-background: #060606;
    --color-button-active-border: #666;
    --color-separator: #444;
    --provider-bg: #161b22;
    --provider-bg-hover: color-mix(
      in srgb,
      var(--provider-brand-color) 30%,
      #000
    );
  }
    .__next-auth-theme-auto img[src$="42-school.svg"],
    .__next-auth-theme-auto img[src$="apple.svg"],
    .__next-auth-theme-auto img[src$="boxyhq-saml.svg"],
    .__next-auth-theme-auto img[src$="eveonline.svg"],
    .__next-auth-theme-auto img[src$="github.svg"],
    .__next-auth-theme-auto img[src$="mailchimp.svg"],
    .__next-auth-theme-auto img[src$="medium.svg"],
    .__next-auth-theme-auto img[src$="okta.svg"],
    .__next-auth-theme-auto img[src$="patreon.svg"],
    .__next-auth-theme-auto img[src$="ping-id.svg"],
    .__next-auth-theme-auto img[src$="roblox.svg"],
    .__next-auth-theme-auto img[src$="threads.svg"],
    .__next-auth-theme-auto img[src$="wikimedia.svg"] {
      filter: invert(1);
    }
    .__next-auth-theme-auto #submitButton {
      background-color: var(--provider-bg, var(--color-info));
    }
}

html {
  box-sizing: border-box;
}

*,
*:before,
*:after {
  box-sizing: inherit;
  margin: 0;
  padding: 0;
}

body {
  background-color: var(--color-background);
  margin: 0;
  padding: 0;
  font-family:
    ui-sans-serif,
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    "Segoe UI",
    Roboto,
    "Helvetica Neue",
    Arial,
    "Noto Sans",
    sans-serif,
    "Apple Color Emoji",
    "Segoe UI Emoji",
    "Segoe UI Symbol",
    "Noto Color Emoji";
}

h1 {
  margin-bottom: 1.5rem;
  padding: 0 1rem;
  font-weight: 400;
  color: var(--color-text);
}

p {
  margin-bottom: 1.5rem;
  padding: 0 1rem;
  color: var(--color-text);
}

form {
  margin: 0;
  padding: 0;
}

label {
  font-weight: 500;
  text-align: left;
  margin-bottom: 0.25rem;
  display: block;
  color: var(--color-text);
}

input[type] {
  box-sizing: border-box;
  display: block;
  width: 100%;
  padding: 0.5rem 1rem;
  border: var(--border-width) solid var(--color-control-border);
  background: var(--color-background-card);
  font-size: 1rem;
  border-radius: var(--border-radius);
  color: var(--color-text);
}

p {
  font-size: 1.1rem;
  line-height: 2rem;
}

a.button {
  text-decoration: none;
  line-height: 1rem;
}

a.button:link,
  a.button:visited {
    background-color: var(--color-background);
    color: var(--color-primary);
  }

button,
a.button {
  padding: 0.75rem 1rem;
  color: var(--provider-color, var(--color-primary));
  background-color: var(--provider-bg, var(--color-background));
  border: 1px solid #00000031;
  font-size: 0.9rem;
  height: 50px;
  border-radius: var(--border-radius);
  transition: background-color 250ms ease-in-out;
  font-weight: 300;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

:is(button,a.button):hover {
    background-color: var(--provider-bg-hover, var(--color-background-hover));
    cursor: pointer;
  }

:is(button,a.button):active {
    cursor: pointer;
  }

:is(button,a.button) span {
    color: var(--provider-bg);
  }

#submitButton {
  color: var(--button-text-color, var(--color-info-text));
  background-color: var(--brand-color, var(--color-info));
  width: 100%;
}

#submitButton:hover {
    background-color: var(
      --button-hover-bg,
      var(--color-info-hover)
    ) !important;
  }

a.site {
  color: var(--color-primary);
  text-decoration: none;
  font-size: 1rem;
  line-height: 2rem;
}

a.site:hover {
    text-decoration: underline;
  }

.page {
  position: absolute;
  width: 100%;
  height: 100%;
  display: grid;
  place-items: center;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.page > div {
    text-align: center;
  }

.error a.button {
    padding-left: 2rem;
    padding-right: 2rem;
    margin-top: 0.5rem;
  }

.error .message {
    margin-bottom: 1.5rem;
  }

.signin input[type="text"] {
    margin-left: auto;
    margin-right: auto;
    display: block;
  }

.signin hr {
    display: block;
    border: 0;
    border-top: 1px solid var(--color-separator);
    margin: 2rem auto 1rem auto;
    overflow: visible;
  }

.signin hr::before {
      content: "or";
      background: var(--color-background-card);
      color: #888;
      padding: 0 0.4rem;
      position: relative;
      top: -0.7rem;
    }

.signin .error {
    background: #f5f5f5;
    font-weight: 500;
    border-radius: 0.3rem;
    background: var(--color-error);
  }

.signin .error p {
      text-align: left;
      padding: 0.5rem 1rem;
      font-size: 0.9rem;
      line-height: 1.2rem;
      color: var(--color-info-text);
    }

.signin > div,
  .signin form {
    display: block;
  }

.signin > div input[type], .signin form input[type] {
      margin-bottom: 0.5rem;
    }

.signin > div button, .signin form button {
      width: 100%;
    }

.signin .provider + .provider {
    margin-top: 1rem;
  }

.logo {
  display: inline-block;
  max-width: 150px;
  margin: 1.25rem 0;
  max-height: 70px;
}

.card {
  background-color: var(--color-background-card);
  border-radius: 1rem;
  padding: 1.25rem 2rem;
}

.card .header {
    color: var(--color-primary);
  }

.card input[type]::-moz-placeholder {
    color: color-mix(
      in srgb,
      var(--color-text) 20%,
      var(--color-button-active-background)
    );
  }

.card input[type]::placeholder {
    color: color-mix(
      in srgb,
      var(--color-text) 20%,
      var(--color-button-active-background)
    );
  }

.card input[type] {
    background: color-mix(in srgb, var(--color-background-card) 95%, black);
  }

.section-header {
  color: var(--color-text);
}

@media screen and (min-width: 450px) {
  .card {
    margin: 2rem 0;
    width: 368px;
  }
}

@media screen and (max-width: 450px) {
  .card {
    margin: 1rem 0;
    width: 343px;
  }
}
`;function nx({html:e,title:t,status:r,cookies:n,theme:a,headTags:i}){return{cookies:n,status:r,headers:{"Content-Type":"text/html"},body:`<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width, initial-scale=1.0"><style>${nm}</style><title>${t}</title>${i??""}</head><body class="__next-auth-theme-${a?.colorScheme??"auto"}"><div class="page">${function(e,t,r){var n=rS.__s;rS.__s=!0,nn=rS.__b,na=rS.diffed,ni=rS.__r,no=rS.unmount;var a=rL(rN,null);a.__k=[e];try{var i=function e(t,r,n,a,i,o,s){if(null==t||!0===t||!1===t||""===t)return"";var c=typeof t;if("object"!=c)return"function"==c?"":"string"==c?r9(t):t+"";if(nl(t)){var l,d="";i.__k=t;for(var u=0;u<t.length;u++){var f=t[u];if(null!=f&&"boolean"!=typeof f){var p,h=e(f,r,n,a,i,o,s);"string"==typeof h?d+=h:(l||(l=[]),d&&l.push(d),d="",nl(h)?(p=l).push.apply(p,h):l.push(h))}}return l?(d&&l.push(d),l):d}if(void 0!==t.constructor)return"";t.__=i,nn&&nn(t);var y=t.type,b=t.props;if("function"==typeof y){var m,x,g,w=r;if(y===rN){if("tpl"in b){for(var _="",v=0;v<b.tpl.length;v++)if(_+=b.tpl[v],b.exprs&&v<b.exprs.length){var k=b.exprs[v];if(null==k)continue;"object"==typeof k&&(void 0===k.constructor||nl(k))?_+=e(k,r,n,a,t,o,s):_+=k}return _}if("UNSTABLE_comment"in b)return"\x3c!--"+r9(b.UNSTABLE_comment)+"--\x3e";x=b.children}else{if(null!=(m=y.contextType)){var A=r[m.__c];w=A?A.props.value:m.__}var S=y.prototype&&"function"==typeof y.prototype.render;if(S)x=nu(t,w),g=t.__c;else{t.__c=g={__v:t,context:w,props:t.props,setState:nr,forceUpdate:nr,__d:!0,__h:[]};for(var E=0;g.__d&&E++<25;)g.__d=!1,ni&&ni(t),x=y.call(g,b,w);g.__d=!0}if(null!=g.getChildContext&&(r=nd({},r,g.getChildContext())),S&&rS.errorBoundaries&&(y.getDerivedStateFromError||g.componentDidCatch)){x=null!=x&&x.type===rN&&null==x.key&&null==x.props.tpl?x.props.children:x;try{return e(x,r,n,a,t,o,s)}catch(i){return y.getDerivedStateFromError&&(g.__s=y.getDerivedStateFromError(i)),g.componentDidCatch&&g.componentDidCatch(i,ns),g.__d?(x=nu(t,r),null!=(g=t.__c).getChildContext&&(r=nd({},r,g.getChildContext())),e(x=null!=x&&x.type===rN&&null==x.key&&null==x.props.tpl?x.props.children:x,r,n,a,t,o,s)):""}finally{na&&na(t),t.__=null,no&&no(t)}}}x=null!=x&&x.type===rN&&null==x.key&&null==x.props.tpl?x.props.children:x;try{var T=e(x,r,n,a,t,o,s);return na&&na(t),t.__=null,rS.unmount&&rS.unmount(t),T}catch(i){if(!o&&s&&s.onError){var R=s.onError(i,t,function(i){return e(i,r,n,a,t,o,s)});if(void 0!==R)return R;var C=rS.__e;return C&&C(i,t),""}if(!o||!i||"function"!=typeof i.then)throw i;return i.then(function i(){try{return e(x,r,n,a,t,o,s)}catch(c){if(!c||"function"!=typeof c.then)throw c;return c.then(function(){return e(x,r,n,a,t,o,s)},i)}})}}var P,O="<"+y,U="";for(var $ in b){var I=b[$];if("function"!=typeof I||"class"===$||"className"===$){switch($){case"children":P=I;continue;case"key":case"ref":case"__self":case"__source":continue;case"htmlFor":if("for"in b)continue;$="for";break;case"className":if("class"in b)continue;$="class";break;case"defaultChecked":$="checked";break;case"defaultSelected":$="selected";break;case"defaultValue":case"value":switch($="value",y){case"textarea":P=I;continue;case"select":a=I;continue;case"option":a!=I||"selected"in b||(O+=" selected")}break;case"dangerouslySetInnerHTML":U=I&&I.__html;continue;case"style":"object"==typeof I&&(I=function(e){var t="";for(var r in e){var n=e[r];if(null!=n&&""!==n){var a="-"==r[0]?r:r7[r]||(r7[r]=r.replace(nt,"-$&").toLowerCase()),i=";";"number"!=typeof n||a.startsWith("--")||ne.has(a)||(i="px;"),t=t+a+":"+n+i}}return t||void 0}(I));break;case"acceptCharset":$="accept-charset";break;case"httpEquiv":$="http-equiv";break;default:if(r5.test($))$=$.replace(r5,"$1:$2").toLowerCase();else{if(r2.test($))continue;("-"===$[4]||r8.has($))&&null!=I?I+="":n?r3.test($)&&($="panose1"===$?"panose-1":$.replace(/([A-Z])/g,"-$1").toLowerCase()):r6.test($)&&($=$.toLowerCase())}}null!=I&&!1!==I&&(O=!0===I||""===I?O+" "+$:O+" "+$+'="'+("string"==typeof I?r9(I):I+"")+'"')}}if(r2.test(y))throw Error(y+" is not a valid HTML tag name in "+O+">");if(U||("string"==typeof P?U=r9(P):null!=P&&!1!==P&&!0!==P&&(U=e(P,r,"svg"===y||"foreignObject"!==y&&n,a,t,o,s))),na&&na(t),t.__=null,no&&no(t),!U&&nf.has(y))return O+"/>";var j="</"+y+">",H=O+">";return nl(U)?[H].concat(U,[j]):"string"!=typeof U?[H,U,j]:H+U+j}(e,ns,!1,void 0,a,!1,void 0);return nl(i)?i.join(""):i}catch(e){if(e.then)throw Error('Use "renderToStringAsync" for suspenseful rendering.');throw e}finally{rS.__c&&rS.__c(e,nc),rS.__s=n,nc.length=0}}(e)}</div></body></html>`}}function ng(e){let{url:t,theme:r,query:n,cookies:a,pages:i,providers:o}=e;return{csrf:(e,t,r)=>e?(t.logger.warn("csrf-disabled"),r.push({name:t.cookies.csrfToken.name,value:"",options:{...t.cookies.csrfToken.options,maxAge:0}}),{status:404,cookies:r}):{headers:{"Content-Type":"application/json","Cache-Control":"private, no-cache, no-store",Expires:"0",Pragma:"no-cache"},body:{csrfToken:t.csrfToken},cookies:r},providers:e=>({headers:{"Content-Type":"application/json"},body:e.reduce((e,{id:t,name:r,type:n,signinUrl:a,callbackUrl:i})=>(e[t]={id:t,name:r,type:n,signinUrl:a,callbackUrl:i},e),{})}),signin(t,s){if(t)throw new H("Unsupported action");if(i?.signIn){let t=`${i.signIn}${i.signIn.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:e.callbackUrl??"/"})}`;return s&&(t=`${t}&${new URLSearchParams({error:s})}`),{redirect:t,cookies:a}}let c=o?.find(e=>"webauthn"===e.type&&e.enableConditionalUI&&!!e.simpleWebAuthnBrowserVersion),l="";if(c){let{simpleWebAuthnBrowserVersion:e}=c;l=`<script src="https://unpkg.com/@simplewebauthn/browser@${e}/dist/bundle/index.umd.min.js" crossorigin="anonymous"></script>`}return nx({cookies:a,theme:r,html:function(e){let{csrfToken:t,providers:r=[],callbackUrl:n,theme:a,email:i,error:o}=e;"undefined"!=typeof document&&a?.brandColor&&document.documentElement.style.setProperty("--brand-color",a.brandColor),"undefined"!=typeof document&&a?.buttonText&&document.documentElement.style.setProperty("--button-text-color",a.buttonText);let s=o&&(nb[o]??nb.default),c=r.find(e=>"webauthn"===e.type&&e.enableConditionalUI)?.id;return nh("div",{className:"signin",children:[a?.brandColor&&nh("style",{dangerouslySetInnerHTML:{__html:`:root {--brand-color: ${a.brandColor}}`}}),a?.buttonText&&nh("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --button-text-color: ${a.buttonText}
        }
      `}}),nh("div",{className:"card",children:[s&&nh("div",{className:"error",children:nh("p",{children:s})}),a?.logo&&nh("img",{src:a.logo,alt:"Logo",className:"logo"}),r.map((e,a)=>{let o,s,c;("oauth"===e.type||"oidc"===e.type)&&({bg:o="#fff",brandColor:s,logo:c=`https://authjs.dev/img/providers/${e.id}.svg`}=e.style??{});let l=s??o??"#fff";return nh("div",{className:"provider",children:["oauth"===e.type||"oidc"===e.type?nh("form",{action:e.signinUrl,method:"POST",children:[nh("input",{type:"hidden",name:"csrfToken",value:t}),n&&nh("input",{type:"hidden",name:"callbackUrl",value:n}),nh("button",{type:"submit",className:"button",style:{"--provider-brand-color":l},tabIndex:0,children:[nh("span",{style:{filter:"invert(1) grayscale(1) brightness(1.3) contrast(9000)","mix-blend-mode":"luminosity",opacity:.95},children:["Sign in with ",e.name]}),c&&nh("img",{loading:"lazy",height:24,src:c})]})]}):null,("email"===e.type||"credentials"===e.type||"webauthn"===e.type)&&a>0&&"email"!==r[a-1].type&&"credentials"!==r[a-1].type&&"webauthn"!==r[a-1].type&&nh("hr",{}),"email"===e.type&&nh("form",{action:e.signinUrl,method:"POST",children:[nh("input",{type:"hidden",name:"csrfToken",value:t}),nh("label",{className:"section-header",htmlFor:`input-email-for-${e.id}-provider`,children:"Email"}),nh("input",{id:`input-email-for-${e.id}-provider`,autoFocus:!0,type:"email",name:"email",value:i,placeholder:"<EMAIL>",required:!0}),nh("button",{id:"submitButton",type:"submit",tabIndex:0,children:["Sign in with ",e.name]})]}),"credentials"===e.type&&nh("form",{action:e.callbackUrl,method:"POST",children:[nh("input",{type:"hidden",name:"csrfToken",value:t}),Object.keys(e.credentials).map(t=>nh("div",{children:[nh("label",{className:"section-header",htmlFor:`input-${t}-for-${e.id}-provider`,children:e.credentials[t].label??t}),nh("input",{name:t,id:`input-${t}-for-${e.id}-provider`,type:e.credentials[t].type??"text",placeholder:e.credentials[t].placeholder??"",...e.credentials[t]})]},`input-group-${e.id}`)),nh("button",{id:"submitButton",type:"submit",tabIndex:0,children:["Sign in with ",e.name]})]}),"webauthn"===e.type&&nh("form",{action:e.callbackUrl,method:"POST",id:`${e.id}-form`,children:[nh("input",{type:"hidden",name:"csrfToken",value:t}),Object.keys(e.formFields).map(t=>nh("div",{children:[nh("label",{className:"section-header",htmlFor:`input-${t}-for-${e.id}-provider`,children:e.formFields[t].label??t}),nh("input",{name:t,"data-form-field":!0,id:`input-${t}-for-${e.id}-provider`,type:e.formFields[t].type??"text",placeholder:e.formFields[t].placeholder??"",...e.formFields[t]})]},`input-group-${e.id}`)),nh("button",{id:`submitButton-${e.id}`,type:"submit",tabIndex:0,children:["Sign in with ",e.name]})]}),("email"===e.type||"credentials"===e.type||"webauthn"===e.type)&&a+1<r.length&&nh("hr",{})]},e.id)})]}),c&&nh(rN,{children:nh("script",{dangerouslySetInnerHTML:{__html:`
const currentURL = window.location.href;
const authURL = currentURL.substring(0, currentURL.lastIndexOf('/'));
(${ny})(authURL, "${c}");
`}})})]})}({csrfToken:e.csrfToken,providers:e.providers?.filter(e=>["email","oauth","oidc"].includes(e.type)||"credentials"===e.type&&e.credentials||"webauthn"===e.type&&e.formFields||!1),callbackUrl:e.callbackUrl,theme:e.theme,error:s,...n}),title:"Sign In",headTags:l})},signout:()=>i?.signOut?{redirect:i.signOut,cookies:a}:nx({cookies:a,theme:r,html:function(e){let{url:t,csrfToken:r,theme:n}=e;return nh("div",{className:"signout",children:[n?.brandColor&&nh("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${n.brandColor}
        }
      `}}),n?.buttonText&&nh("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --button-text-color: ${n.buttonText}
        }
      `}}),nh("div",{className:"card",children:[n?.logo&&nh("img",{src:n.logo,alt:"Logo",className:"logo"}),nh("h1",{children:"Signout"}),nh("p",{children:"Are you sure you want to sign out?"}),nh("form",{action:t?.toString(),method:"POST",children:[nh("input",{type:"hidden",name:"csrfToken",value:r}),nh("button",{id:"submitButton",type:"submit",children:"Sign out"})]})]})]})}({csrfToken:e.csrfToken,url:t,theme:r}),title:"Sign Out"}),verifyRequest:e=>i?.verifyRequest?{redirect:`${i.verifyRequest}${t?.search??""}`,cookies:a}:nx({cookies:a,theme:r,html:function(e){let{url:t,theme:r}=e;return nh("div",{className:"verify-request",children:[r.brandColor&&nh("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${r.brandColor}
        }
      `}}),nh("div",{className:"card",children:[r.logo&&nh("img",{src:r.logo,alt:"Logo",className:"logo"}),nh("h1",{children:"Check your email"}),nh("p",{children:"A sign in link has been sent to your email address."}),nh("p",{children:nh("a",{className:"site",href:t.origin,children:t.host})})]})]})}({url:t,theme:r,...e}),title:"Verify Request"}),error:e=>i?.error?{redirect:`${i.error}${i.error.includes("?")?"&":"?"}error=${e}`,cookies:a}:nx({cookies:a,theme:r,...function(e){let{url:t,error:r="default",theme:n}=e,a=`${t}/signin`,i={default:{status:200,heading:"Error",message:nh("p",{children:nh("a",{className:"site",href:t?.origin,children:t?.host})})},Configuration:{status:500,heading:"Server error",message:nh("div",{children:[nh("p",{children:"There is a problem with the server configuration."}),nh("p",{children:"Check the server logs for more information."})]})},AccessDenied:{status:403,heading:"Access Denied",message:nh("div",{children:[nh("p",{children:"You do not have permission to sign in."}),nh("p",{children:nh("a",{className:"button",href:a,children:"Sign in"})})]})},Verification:{status:403,heading:"Unable to sign in",message:nh("div",{children:[nh("p",{children:"The sign in link is no longer valid."}),nh("p",{children:"It may have been used already or it may have expired."})]}),signin:nh("a",{className:"button",href:a,children:"Sign in"})}},{status:o,heading:s,message:c,signin:l}=i[r]??i.default;return{status:o,html:nh("div",{className:"error",children:[n?.brandColor&&nh("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${n?.brandColor}
        }
      `}}),nh("div",{className:"card",children:[n?.logo&&nh("img",{src:n?.logo,alt:"Logo",className:"logo"}),nh("h1",{children:s}),nh("div",{className:"message",children:c}),l]})]})}}({url:t,theme:r,error:e}),title:"Error"})}}function nw(e,t=Date.now()){return new Date(t+1e3*e)}async function n_(e,t,r,n){if(!r?.providerAccountId||!r.type)throw Error("Missing or invalid provider account");if(!["email","oauth","oidc","webauthn"].includes(r.type))throw Error("Provider not supported");let{adapter:a,jwt:i,events:o,session:{strategy:s,generateSessionToken:c}}=n;if(!a)return{user:t,account:r};let l=r,{createUser:d,updateUser:u,getUser:f,getUserByAccount:p,getUserByEmail:h,linkAccount:y,createSession:b,getSessionAndUser:m,deleteSession:x}=a,g=null,w=null,_=!1,v="jwt"===s;if(e){if(v)try{let t=n.cookies.sessionToken.name;(g=await i.decode({...i,token:e,salt:t}))&&"sub"in g&&g.sub&&(w=await f(g.sub))}catch{}else{let t=await m(e);t&&(g=t.session,w=t.user)}}if("email"===l.type){let r=await h(t.email);return r?(w?.id!==r.id&&!v&&e&&await x(e),w=await u({id:r.id,emailVerified:new Date}),await o.updateUser?.({user:w})):(w=await d({...t,emailVerified:new Date}),await o.createUser?.({user:w}),_=!0),{session:g=v?{}:await b({sessionToken:c(),userId:w.id,expires:nw(n.session.maxAge)}),user:w,isNewUser:_}}if("webauthn"===l.type){let e=await p({providerAccountId:l.providerAccountId,provider:l.provider});if(e){if(w){if(e.id===w.id){let e={...l,userId:w.id};return{session:g,user:w,isNewUser:_,account:e}}throw new z("The account is already associated with another user",{provider:l.provider})}g=v?{}:await b({sessionToken:c(),userId:e.id,expires:nw(n.session.maxAge)});let t={...l,userId:e.id};return{session:g,user:e,isNewUser:_,account:t}}{if(w){await y({...l,userId:w.id}),await o.linkAccount?.({user:w,account:l,profile:t});let e={...l,userId:w.id};return{session:g,user:w,isNewUser:_,account:e}}if(t.email?await h(t.email):null)throw new z("Another account already exists with the same e-mail address",{provider:l.provider});w=await d({...t}),await o.createUser?.({user:w}),await y({...l,userId:w.id}),await o.linkAccount?.({user:w,account:l,profile:t}),g=v?{}:await b({sessionToken:c(),userId:w.id,expires:nw(n.session.maxAge)});let e={...l,userId:w.id};return{session:g,user:w,isNewUser:!0,account:e}}}let k=await p({providerAccountId:l.providerAccountId,provider:l.provider});if(k){if(w){if(k.id===w.id)return{session:g,user:w,isNewUser:_};throw new C("The account is already associated with another user",{provider:l.provider})}return{session:g=v?{}:await b({sessionToken:c(),userId:k.id,expires:nw(n.session.maxAge)}),user:k,isNewUser:_}}{let{provider:e}=n,{type:r,provider:a,providerAccountId:i,userId:s,...u}=l;if(l=Object.assign(e.account(u)??{},{providerAccountId:i,provider:a,type:r,userId:s}),w)return await y({...l,userId:w.id}),await o.linkAccount?.({user:w,account:l,profile:t}),{session:g,user:w,isNewUser:_};let f=t.email?await h(t.email):null;if(f){let e=n.provider;if(e?.allowDangerousEmailAccountLinking)w=f,_=!1;else throw new C("Another account already exists with the same e-mail address",{provider:l.provider})}else w=await d({...t,emailVerified:null}),_=!0;return await o.createUser?.({user:w}),await y({...l,userId:w.id}),await o.linkAccount?.({user:w,account:l,profile:t}),{session:g=v?{}:await b({sessionToken:c(),userId:w.id,expires:nw(n.session.maxAge)}),user:w,isNewUser:_}}}function nv(e,t){if(null==e)return!1;try{return e instanceof t||Object.getPrototypeOf(e)[Symbol.toStringTag]===t.prototype[Symbol.toStringTag]}catch{return!1}}"undefined"!=typeof navigator&&navigator.userAgent?.startsWith?.("Mozilla/5.0 ")||(i="oauth4webapi/v3.5.1");let nk="ERR_INVALID_ARG_VALUE",nA="ERR_INVALID_ARG_TYPE";function nS(e,t,r){let n=TypeError(e,{cause:r});return Object.assign(n,{code:t}),n}let nE=Symbol(),nT=Symbol(),nR=Symbol(),nC=Symbol(),nP=Symbol(),nO=Symbol(),nU=Symbol(),n$=new TextEncoder,nI=new TextDecoder;function nj(e){return"string"==typeof e?n$.encode(e):nI.decode(e)}function nH(e){return"string"==typeof e?function(e){try{let t=atob(e.replace(/-/g,"+").replace(/_/g,"/").replace(/\s/g,"")),r=new Uint8Array(t.length);for(let e=0;e<t.length;e++)r[e]=t.charCodeAt(e);return r}catch(e){throw nS("The input to be decoded is not correctly encoded.",nk,e)}}(e):function(e){e instanceof ArrayBuffer&&(e=new Uint8Array(e));let t=[];for(let r=0;r<e.byteLength;r+=32768)t.push(String.fromCharCode.apply(null,e.subarray(r,r+32768)));return btoa(t.join("")).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}(e)}class nD extends Error{code;constructor(e,t){super(e,t),this.name=this.constructor.name,this.code=aM,Error.captureStackTrace?.(this,this.constructor)}}class nW extends Error{code;constructor(e,t){super(e,t),this.name=this.constructor.name,t?.code&&(this.code=t?.code),Error.captureStackTrace?.(this,this.constructor)}}function nK(e,t,r){return new nW(e,{code:t,cause:r})}function nL(e){return!(null===e||"object"!=typeof e||Array.isArray(e))}function nM(e){nv(e,Headers)&&(e=Object.fromEntries(e.entries()));let t=new Headers(e);if(i&&!t.has("user-agent")&&t.set("user-agent",i),t.has("authorization"))throw nS('"options.headers" must not include the "authorization" header name',nk);return t}function nN(e){if("function"==typeof e&&(e=e()),!(e instanceof AbortSignal))throw nS('"options.signal" must return or be an instance of AbortSignal',nA);return e}function nJ(e){return e.includes("//")?e.replace("//","/"):e}async function nB(e,t,r,n){if(!(e instanceof URL))throw nS(`"${t}" must be an instance of URL`,nA);n9(e,n?.[nE]!==!0);let a=r(new URL(e.href)),i=nM(n?.headers);return i.set("accept","application/json"),(n?.[nC]||fetch)(a.href,{body:void 0,headers:Object.fromEntries(i.entries()),method:"GET",redirect:"manual",signal:n?.signal?nN(n.signal):void 0})}async function nq(e,t){return nB(e,"issuerIdentifier",e=>{switch(t?.algorithm){case void 0:case"oidc":e.pathname=nJ(`${e.pathname}/.well-known/openid-configuration`);break;case"oauth2":var r;r=".well-known/oauth-authorization-server","/"===e.pathname?e.pathname=r:e.pathname=nJ(`${r}/${e.pathname}`);break;default:throw nS('"options.algorithm" must be "oidc" (default), or "oauth2"',nk)}return e},t)}function nz(e,t,r,n,a){try{if("number"!=typeof e||!Number.isFinite(e))throw nS(`${r} must be a number`,nA,a);if(e>0)return;if(t){if(0!==e)throw nS(`${r} must be a non-negative number`,nk,a);return}throw nS(`${r} must be a positive number`,nk,a)}catch(e){if(n)throw nK(e.message,n,a);throw e}}function nF(e,t,r,n){try{if("string"!=typeof e)throw nS(`${t} must be a string`,nA,n);if(0===e.length)throw nS(`${t} must not be empty`,nk,n)}catch(e){if(r)throw nK(e.message,r,n);throw e}}async function nV(e,t){if(!(e instanceof URL)&&e!==ic)throw nS('"expectedIssuerIdentifier" must be an instance of URL',nA);if(!nv(t,Response))throw nS('"response" must be an instance of Response',nA);if(200!==t.status)throw nK('"response" is not a conform Authorization Server Metadata response (unexpected HTTP status code)',aF,t);a2(t);let r=await io(t);if(nF(r.issuer,'"response" body "issuer" property',aq,{body:r}),e!==ic&&new URL(r.issuer).href!==e.href)throw nK('"response" body "issuer" property does not match the expected value',aZ,{expected:e.href,body:r,attribute:"issuer"});return r}function nG(e){(function(e,t){if(am(e)!==t)throw nX(e,t)})(e,"application/json")}function nX(e,...t){let r='"response" content-type must be ';if(t.length>2){let e=t.pop();r+=`${t.join(", ")}, or ${e}`}else 2===t.length?r+=`${t[0]} or ${t[1]}`:r+=t[0];return nK(r,az,e)}function nY(){return nH(crypto.getRandomValues(new Uint8Array(32)))}async function nZ(e){return nF(e,"codeVerifier"),nH(await crypto.subtle.digest("SHA-256",nj(e)))}function nQ(e){let t=e?.[nT];return"number"==typeof t&&Number.isFinite(t)?t:0}function n0(e){let t=e?.[nR];return"number"==typeof t&&Number.isFinite(t)&&-1!==Math.sign(t)?t:30}function n1(){return Math.floor(Date.now()/1e3)}function n2(e){if("object"!=typeof e||null===e)throw nS('"as" must be an object',nA);nF(e.issuer,'"as.issuer"')}function n5(e){if("object"!=typeof e||null===e)throw nS('"client" must be an object',nA);nF(e.client_id,'"client.client_id"')}function n6(e,t){let r=n1()+nQ(t);return{jti:nY(),aud:e.issuer,exp:r+60,iat:r,nbf:r,iss:t.client_id,sub:t.client_id}}async function n3(e,t,r){if(!r.usages.includes("sign"))throw nS('CryptoKey instances used for signing assertions must include "sign" in their "usages"',nk);let n=`${nH(nj(JSON.stringify(e)))}.${nH(nj(JSON.stringify(t)))}`,a=nH(await crypto.subtle.sign(a8(r),r,nj(n)));return`${n}.${a}`}async function n8(e){let{kty:t,e:r,n,x:a,y:i,crv:s}=await crypto.subtle.exportKey("jwk",e),c={kty:t,e:r,n,x:a,y:i,crv:s};return o.set(e,c),c}let n4=URL.parse?(e,t)=>URL.parse(e,t):(e,t)=>{try{return new URL(e,t)}catch{return null}};function n9(e,t){if(t&&"https:"!==e.protocol)throw nK("only requests to HTTPS are allowed",aV,e);if("https:"!==e.protocol&&"http:"!==e.protocol)throw nK("only HTTP and HTTPS requests are allowed",aG,e)}function n7(e,t,r,n){let a;if("string"!=typeof e||!(a=n4(e)))throw nK(`authorization server metadata does not contain a valid ${r?`"as.mtls_endpoint_aliases.${t}"`:`"as.${t}"`}`,void 0===e?a0:a1,{attribute:r?`mtls_endpoint_aliases.${t}`:t});return n9(a,n),a}function ae(e,t,r,n){return r&&e.mtls_endpoint_aliases&&t in e.mtls_endpoint_aliases?n7(e.mtls_endpoint_aliases[t],t,r,n):n7(e[t],t,r,n)}class at extends Error{cause;code;error;status;error_description;response;constructor(e,t){super(e,t),this.name=this.constructor.name,this.code=aL,this.cause=t.cause,this.error=t.cause.error,this.status=t.response.status,this.error_description=t.cause.error_description,Object.defineProperty(this,"response",{enumerable:!1,value:t.response}),Error.captureStackTrace?.(this,this.constructor)}}class ar extends Error{cause;code;error;error_description;constructor(e,t){super(e,t),this.name=this.constructor.name,this.code=aN,this.cause=t.cause,this.error=t.cause.get("error"),this.error_description=t.cause.get("error_description")??void 0,Error.captureStackTrace?.(this,this.constructor)}}class an extends Error{cause;code;response;status;constructor(e,t){super(e,t),this.name=this.constructor.name,this.code=aK,this.cause=t.cause,this.status=t.response.status,this.response=t.response,Object.defineProperty(this,"response",{enumerable:!1}),Error.captureStackTrace?.(this,this.constructor)}}let aa="[a-zA-Z0-9!#$%&\\'\\*\\+\\-\\.\\^_`\\|~]+",ai=RegExp("^[,\\s]*("+aa+")\\s(.*)"),ao=RegExp("^[,\\s]*("+aa+')\\s*=\\s*"((?:[^"\\\\]|\\\\.)*)"[,\\s]*(.*)'),as=RegExp("^[,\\s]*("+aa+")\\s*=\\s*("+aa+")[,\\s]*(.*)"),ac=RegExp("^([a-zA-Z0-9\\-\\._\\~\\+\\/]+[=]{0,2})(?:$|[,\\s])(.*)");async function al(e){if(e.status>399&&e.status<500){a2(e),nG(e);try{let t=await e.clone().json();if(nL(t)&&"string"==typeof t.error&&t.error.length)return t}catch{}}}async function ad(e,t,r){if(e.status!==t){let t;if(t=await al(e))throw await e.body?.cancel(),new at("server responded with an error in the response body",{cause:t,response:e});throw nK(`"response" is not a conform ${r} response (unexpected HTTP status code)`,aF,e)}}function au(e){if(!aP.has(e))throw nS('"options.DPoP" is not a valid DPoPHandle',nk)}async function af(e,t,r,n,a,i){if(nF(e,'"accessToken"'),!(r instanceof URL))throw nS('"url" must be an instance of URL',nA);n9(r,i?.[nE]!==!0),n=nM(n),i?.DPoP&&(au(i.DPoP),await i.DPoP.addProof(r,n,t.toUpperCase(),e)),n.set("authorization",`${n.has("dpop")?"DPoP":"Bearer"} ${e}`);let o=await (i?.[nC]||fetch)(r.href,{body:a,headers:Object.fromEntries(n.entries()),method:t,redirect:"manual",signal:i?.signal?nN(i.signal):void 0});return i?.DPoP?.cacheNonce(o),o}async function ap(e,t,r,n){n2(e),n5(t);let a=ae(e,"userinfo_endpoint",t.use_mtls_endpoint_aliases,n?.[nE]!==!0),i=nM(n?.headers);return t.userinfo_signed_response_alg?i.set("accept","application/jwt"):(i.set("accept","application/json"),i.append("accept","application/jwt")),af(r,"GET",a,i,null,{...n,[nT]:nQ(t)})}function ah(e,t,r,n){(s||=new WeakMap).set(e,{jwks:t,uat:r,get age(){return n1()-this.uat}}),n&&Object.assign(n,{jwks:structuredClone(t),uat:r})}function ay(e,t){s?.delete(e),delete t?.jwks,delete t?.uat}let ab=Symbol();function am(e){return e.headers.get("content-type")?.split(";")[0]}async function ax(e,t,r,n,a){let i;if(n2(e),n5(t),!nv(n,Response))throw nS('"response" must be an instance of Response',nA);if(aS(n),200!==n.status)throw nK('"response" is not a conform UserInfo Endpoint response (unexpected HTTP status code)',aF,n);if(a2(n),"application/jwt"===am(n)){let{claims:r,jwt:o}=await a4(await n.text(),ie.bind(void 0,t.userinfo_signed_response_alg,e.userinfo_signing_alg_values_supported,void 0),nQ(t),n0(t),a?.[nO]).then(aE.bind(void 0,t.client_id)).then(aR.bind(void 0,e));av.set(n,o),i=r}else{if(t.userinfo_signed_response_alg)throw nK("JWT UserInfo Response expected",aJ,n);i=await io(n)}if(nF(i.sub,'"response" body "sub" property',aq,{body:i}),r===ab);else if(nF(r,'"expectedSubject"'),i.sub!==r)throw nK('unexpected "response" body "sub" property value',aZ,{expected:r,body:i,attribute:"sub"});return i}async function ag(e,t,r,n,a,i,o){return await r(e,t,a,i),i.set("content-type","application/x-www-form-urlencoded;charset=UTF-8"),(o?.[nC]||fetch)(n.href,{body:a,headers:Object.fromEntries(i.entries()),method:"POST",redirect:"manual",signal:o?.signal?nN(o.signal):void 0})}async function aw(e,t,r,n,a,i){let o=ae(e,"token_endpoint",t.use_mtls_endpoint_aliases,i?.[nE]!==!0);a.set("grant_type",n);let s=nM(i?.headers);s.set("accept","application/json"),i?.DPoP!==void 0&&(au(i.DPoP),await i.DPoP.addProof(o,s,"POST"));let c=await ag(e,t,r,o,a,s,i);return i?.DPoP?.cacheNonce(c),c}let a_=new WeakMap,av=new WeakMap;function ak(e){if(!e.id_token)return;let t=a_.get(e);if(!t)throw nS('"ref" was already garbage collected or did not resolve from the proper sources',nk);return t}async function aA(e,t,r,n,a){if(n2(e),n5(t),!nv(r,Response))throw nS('"response" must be an instance of Response',nA);aS(r),await ad(r,200,"Token Endpoint"),a2(r);let i=await io(r);if(nF(i.access_token,'"response" body "access_token" property',aq,{body:i}),nF(i.token_type,'"response" body "token_type" property',aq,{body:i}),i.token_type=i.token_type.toLowerCase(),"dpop"!==i.token_type&&"bearer"!==i.token_type)throw new nD("unsupported `token_type` value",{cause:{body:i}});if(void 0!==i.expires_in){let e="number"!=typeof i.expires_in?parseFloat(i.expires_in):i.expires_in;nz(e,!1,'"response" body "expires_in" property',aq,{body:i}),i.expires_in=e}if(void 0!==i.refresh_token&&nF(i.refresh_token,'"response" body "refresh_token" property',aq,{body:i}),void 0!==i.scope&&"string"!=typeof i.scope)throw nK('"response" body "scope" property must be a string',aq,{body:i});if(void 0!==i.id_token){nF(i.id_token,'"response" body "id_token" property',aq,{body:i});let o=["aud","exp","iat","iss","sub"];!0===t.require_auth_time&&o.push("auth_time"),void 0!==t.default_max_age&&(nz(t.default_max_age,!1,'"client.default_max_age"'),o.push("auth_time")),n?.length&&o.push(...n);let{claims:s,jwt:c}=await a4(i.id_token,ie.bind(void 0,t.id_token_signed_response_alg,e.id_token_signing_alg_values_supported,"RS256"),nQ(t),n0(t),a?.[nO]).then(a$.bind(void 0,o)).then(aC.bind(void 0,e)).then(aT.bind(void 0,t.client_id));if(Array.isArray(s.aud)&&1!==s.aud.length){if(void 0===s.azp)throw nK('ID Token "aud" (audience) claim includes additional untrusted audiences',aY,{claims:s,claim:"aud"});if(s.azp!==t.client_id)throw nK('unexpected ID Token "azp" (authorized party) claim value',aY,{expected:t.client_id,claims:s,claim:"azp"})}void 0!==s.auth_time&&nz(s.auth_time,!1,'ID Token "auth_time" (authentication time)',aq,{claims:s}),av.set(r,c),a_.set(i,s)}return i}function aS(e){let t;if(t=function(e){if(!nv(e,Response))throw nS('"response" must be an instance of Response',nA);let t=e.headers.get("www-authenticate");if(null===t)return;let r=[],n=t;for(;n;){let e,t=n.match(ai),a=t?.["1"].toLowerCase();if(n=t?.["2"],!a)return;let i={};for(;n;){let r,a;if(t=n.match(ao)){if([,r,a,n]=t,a.includes("\\"))try{a=JSON.parse(`"${a}"`)}catch{}i[r.toLowerCase()]=a;continue}if(t=n.match(as)){[,r,a,n]=t,i[r.toLowerCase()]=a;continue}if(t=n.match(ac)){if(Object.keys(i).length)break;[,e,n]=t;break}return}let o={scheme:a,parameters:i};e&&(o.token68=e),r.push(o)}if(r.length)return r}(e))throw new an("server responded with a challenge in the WWW-Authenticate HTTP Header",{cause:t,response:e})}function aE(e,t){return void 0!==t.claims.aud?aT(e,t):t}function aT(e,t){if(Array.isArray(t.claims.aud)){if(!t.claims.aud.includes(e))throw nK('unexpected JWT "aud" (audience) claim value',aY,{expected:e,claims:t.claims,claim:"aud"})}else if(t.claims.aud!==e)throw nK('unexpected JWT "aud" (audience) claim value',aY,{expected:e,claims:t.claims,claim:"aud"});return t}function aR(e,t){return void 0!==t.claims.iss?aC(e,t):t}function aC(e,t){let r=e[il]?.(t)??e.issuer;if(t.claims.iss!==r)throw nK('unexpected JWT "iss" (issuer) claim value',aY,{expected:r,claims:t.claims,claim:"iss"});return t}let aP=new WeakSet;async function aO(e,t,r,n,a,i,o){if(n2(e),n5(t),!aP.has(n))throw nS('"callbackParameters" must be an instance of URLSearchParams obtained from "validateAuthResponse()", or "validateJwtAuthResponse()',nk);nF(a,'"redirectUri"');let s=it(n,"code");if(!s)throw nK('no authorization code in "callbackParameters"',aq);let c=new URLSearchParams(o?.additionalParameters);return c.set("redirect_uri",a),c.set("code",s),i!==is&&(nF(i,'"codeVerifier"'),c.set("code_verifier",i)),aw(e,t,r,"authorization_code",c,o)}let aU={aud:"audience",c_hash:"code hash",client_id:"client id",exp:"expiration time",iat:"issued at",iss:"issuer",jti:"jwt id",nonce:"nonce",s_hash:"state hash",sub:"subject",ath:"access token hash",htm:"http method",htu:"http uri",cnf:"confirmation",auth_time:"authentication time"};function a$(e,t){for(let r of e)if(void 0===t.claims[r])throw nK(`JWT "${r}" (${aU[r]}) claim missing`,aq,{claims:t.claims});return t}let aI=Symbol(),aj=Symbol();async function aH(e,t,r,n){return"string"==typeof n?.expectedNonce||"number"==typeof n?.maxAge||n?.requireIdToken?aD(e,t,r,n.expectedNonce,n.maxAge,{[nO]:n[nO]}):aW(e,t,r,n)}async function aD(e,t,r,n,a,i){let o=[];switch(n){case void 0:n=aI;break;case aI:break;default:nF(n,'"expectedNonce" argument'),o.push("nonce")}switch(a??=t.default_max_age){case void 0:a=aj;break;case aj:break;default:nz(a,!1,'"maxAge" argument'),o.push("auth_time")}let s=await aA(e,t,r,o,i);nF(s.id_token,'"response" body "id_token" property',aq,{body:s});let c=ak(s);if(a!==aj){let e=n1()+nQ(t),r=n0(t);if(c.auth_time+a<e-r)throw nK("too much time has elapsed since the last End-User authentication",aX,{claims:c,now:e,tolerance:r,claim:"auth_time"})}if(n===aI){if(void 0!==c.nonce)throw nK('unexpected ID Token "nonce" claim value',aY,{expected:void 0,claims:c,claim:"nonce"})}else if(c.nonce!==n)throw nK('unexpected ID Token "nonce" claim value',aY,{expected:n,claims:c,claim:"nonce"});return s}async function aW(e,t,r,n){let a=await aA(e,t,r,void 0,n),i=ak(a);if(i){if(void 0!==t.default_max_age){nz(t.default_max_age,!1,'"client.default_max_age"');let e=n1()+nQ(t),r=n0(t);if(i.auth_time+t.default_max_age<e-r)throw nK("too much time has elapsed since the last End-User authentication",aX,{claims:i,now:e,tolerance:r,claim:"auth_time"})}if(void 0!==i.nonce)throw nK('unexpected ID Token "nonce" claim value',aY,{expected:void 0,claims:i,claim:"nonce"})}return a}let aK="OAUTH_WWW_AUTHENTICATE_CHALLENGE",aL="OAUTH_RESPONSE_BODY_ERROR",aM="OAUTH_UNSUPPORTED_OPERATION",aN="OAUTH_AUTHORIZATION_RESPONSE_ERROR",aJ="OAUTH_JWT_USERINFO_EXPECTED",aB="OAUTH_PARSE_ERROR",aq="OAUTH_INVALID_RESPONSE",az="OAUTH_RESPONSE_IS_NOT_JSON",aF="OAUTH_RESPONSE_IS_NOT_CONFORM",aV="OAUTH_HTTP_REQUEST_FORBIDDEN",aG="OAUTH_REQUEST_PROTOCOL_FORBIDDEN",aX="OAUTH_JWT_TIMESTAMP_CHECK_FAILED",aY="OAUTH_JWT_CLAIM_COMPARISON_FAILED",aZ="OAUTH_JSON_ATTRIBUTE_COMPARISON_FAILED",aQ="OAUTH_KEY_SELECTION_FAILED",a0="OAUTH_MISSING_SERVER_METADATA",a1="OAUTH_INVALID_SERVER_METADATA";function a2(e){if(e.bodyUsed)throw nS('"response" body has been used already',nk)}async function a5(e,t){n2(e);let r=ae(e,"jwks_uri",!1,t?.[nE]!==!0),n=nM(t?.headers);return n.set("accept","application/json"),n.append("accept","application/jwk-set+json"),(t?.[nC]||fetch)(r.href,{body:void 0,headers:Object.fromEntries(n.entries()),method:"GET",redirect:"manual",signal:t?.signal?nN(t.signal):void 0})}async function a6(e){if(!nv(e,Response))throw nS('"response" must be an instance of Response',nA);if(200!==e.status)throw nK('"response" is not a conform JSON Web Key Set response (unexpected HTTP status code)',aF,e);a2(e);let t=await io(e,e=>(function(e,...t){if(!t.includes(am(e)))throw nX(e,...t)})(e,"application/json","application/jwk-set+json"));if(!Array.isArray(t.keys))throw nK('"response" body "keys" property must be an array',aq,{body:t});if(!Array.prototype.every.call(t.keys,nL))throw nK('"response" body "keys" property members must be JWK formatted objects',aq,{body:t});return t}function a3(e){let{algorithm:t}=e;if("number"!=typeof t.modulusLength||t.modulusLength<2048)throw new nD(`unsupported ${t.name} modulusLength`,{cause:e})}function a8(e){switch(e.algorithm.name){case"ECDSA":return{name:e.algorithm.name,hash:function(e){let{algorithm:t}=e;switch(t.namedCurve){case"P-256":return"SHA-256";case"P-384":return"SHA-384";case"P-521":return"SHA-512";default:throw new nD("unsupported ECDSA namedCurve",{cause:e})}}(e)};case"RSA-PSS":switch(a3(e),e.algorithm.hash.name){case"SHA-256":case"SHA-384":case"SHA-512":return{name:e.algorithm.name,saltLength:parseInt(e.algorithm.hash.name.slice(-3),10)>>3};default:throw new nD("unsupported RSA-PSS hash name",{cause:e})}case"RSASSA-PKCS1-v1_5":return a3(e),e.algorithm.name;case"Ed25519":return e.algorithm.name}throw new nD("unsupported CryptoKey algorithm name",{cause:e})}async function a4(e,t,r,n,a){let i,o,{0:s,1:c,length:l}=e.split(".");if(5===l){if(void 0!==a)e=await a(e),{0:s,1:c,length:l}=e.split(".");else throw new nD("JWE decryption is not configured",{cause:e})}if(3!==l)throw nK("Invalid JWT",aq,e);try{i=JSON.parse(nj(nH(s)))}catch(e){throw nK("failed to parse JWT Header body as base64url encoded JSON",aB,e)}if(!nL(i))throw nK("JWT Header must be a top level object",aq,e);if(t(i),void 0!==i.crit)throw new nD('no JWT "crit" header parameter extensions are supported',{cause:{header:i}});try{o=JSON.parse(nj(nH(c)))}catch(e){throw nK("failed to parse JWT Payload body as base64url encoded JSON",aB,e)}if(!nL(o))throw nK("JWT Payload must be a top level object",aq,e);let d=n1()+r;if(void 0!==o.exp){if("number"!=typeof o.exp)throw nK('unexpected JWT "exp" (expiration time) claim type',aq,{claims:o});if(o.exp<=d-n)throw nK('unexpected JWT "exp" (expiration time) claim value, expiration is past current timestamp',aX,{claims:o,now:d,tolerance:n,claim:"exp"})}if(void 0!==o.iat&&"number"!=typeof o.iat)throw nK('unexpected JWT "iat" (issued at) claim type',aq,{claims:o});if(void 0!==o.iss&&"string"!=typeof o.iss)throw nK('unexpected JWT "iss" (issuer) claim type',aq,{claims:o});if(void 0!==o.nbf){if("number"!=typeof o.nbf)throw nK('unexpected JWT "nbf" (not before) claim type',aq,{claims:o});if(o.nbf>d+n)throw nK('unexpected JWT "nbf" (not before) claim value',aX,{claims:o,now:d,tolerance:n,claim:"nbf"})}if(void 0!==o.aud&&"string"!=typeof o.aud&&!Array.isArray(o.aud))throw nK('unexpected JWT "aud" (audience) claim type',aq,{claims:o});return{header:i,claims:o,jwt:e}}async function a9(e,t,r){let n;switch(t.alg){case"RS256":case"PS256":case"ES256":n="SHA-256";break;case"RS384":case"PS384":case"ES384":n="SHA-384";break;case"RS512":case"PS512":case"ES512":case"Ed25519":case"EdDSA":n="SHA-512";break;default:throw new nD(`unsupported JWS algorithm for ${r} calculation`,{cause:{alg:t.alg}})}let a=await crypto.subtle.digest(n,nj(e));return nH(a.slice(0,a.byteLength/2))}async function a7(e){if(e.bodyUsed)throw nS("form_post Request instances must contain a readable body",nk,{cause:e});return e.text()}function ie(e,t,r,n){if(void 0!==e){if("string"==typeof e?n.alg!==e:!e.includes(n.alg))throw nK('unexpected JWT "alg" header parameter',aq,{header:n,expected:e,reason:"client configuration"});return}if(Array.isArray(t)){if(!t.includes(n.alg))throw nK('unexpected JWT "alg" header parameter',aq,{header:n,expected:t,reason:"authorization server metadata"});return}if(void 0!==r){if("string"==typeof r?n.alg!==r:"function"==typeof r?!r(n.alg):!r.includes(n.alg))throw nK('unexpected JWT "alg" header parameter',aq,{header:n,expected:r,reason:"default value"});return}throw nK('missing client or server configuration to verify used JWT "alg" header parameter',void 0,{client:e,issuer:t,fallback:r})}function it(e,t){let{0:r,length:n}=e.getAll(t);if(n>1)throw nK(`"${t}" parameter must be provided only once`,aq);return r}let ir=Symbol(),ia=Symbol();async function ii(e,t){let{ext:r,key_ops:n,use:a,...i}=t;return crypto.subtle.importKey("jwk",i,function(e){switch(e){case"PS256":case"PS384":case"PS512":return{name:"RSA-PSS",hash:`SHA-${e.slice(-3)}`};case"RS256":case"RS384":case"RS512":return{name:"RSASSA-PKCS1-v1_5",hash:`SHA-${e.slice(-3)}`};case"ES256":case"ES384":return{name:"ECDSA",namedCurve:`P-${e.slice(-3)}`};case"ES512":return{name:"ECDSA",namedCurve:"P-521"};case"Ed25519":case"EdDSA":return"Ed25519";default:throw new nD("unsupported JWS algorithm",{cause:{alg:e}})}}(e),!0,["verify"])}async function io(e,t=nG){let r;try{r=await e.json()}catch(r){throw t(e),nK('failed to parse "response" body as JSON',aB,r)}if(!nL(r))throw nK('"response" body must be a top level object',aq,{body:r});return r}let is=Symbol(),ic=Symbol(),il=Symbol();async function id(e,t,r){let{cookies:n,logger:a}=r,i=n[e],o=new Date;o.setTime(o.getTime()+9e5),a.debug(`CREATE_${e.toUpperCase()}`,{name:i.name,payload:t,COOKIE_TTL:900,expires:o});let s=await tZ({...r.jwt,maxAge:900,token:{value:t},salt:i.name}),c={...i.options,expires:o};return{name:i.name,value:s,options:c}}async function iu(e,t,r){try{let{logger:n,cookies:a,jwt:i}=r;if(n.debug(`PARSE_${e.toUpperCase()}`,{cookie:t}),!t)throw new k(`${e} cookie was missing`);let o=await tQ({...i,token:t,salt:a[e].name});if(o?.value)return o.value;throw Error("Invalid cookie")}catch(t){throw new k(`${e} value could not be parsed`,{cause:t})}}function ip(e,t,r){let{logger:n,cookies:a}=t,i=a[e];n.debug(`CLEAR_${e.toUpperCase()}`,{cookie:i}),r.push({name:i.name,value:"",options:{...a[e].options,maxAge:0}})}function ih(e,t){return async function(r,n,a){let{provider:i,logger:o}=a;if(!i?.checks?.includes(e))return;let s=r?.[a.cookies[t].name];o.debug(`USE_${t.toUpperCase()}`,{value:s});let c=await iu(t,s,a);return ip(t,a,n),c}}let iy={async create(e){let t=nY(),r=await nZ(t);return{cookie:await id("pkceCodeVerifier",t,e),value:r}},use:ih("pkce","pkceCodeVerifier")},ib="encodedState",im={async create(e,t){let{provider:r}=e;if(!r.checks.includes("state")){if(t)throw new k("State data was provided but the provider is not configured to use state");return}let n={origin:t,random:nY()},a=await tZ({secret:e.jwt.secret,token:n,salt:ib,maxAge:900});return{cookie:await id("state",a,e),value:a}},use:ih("state","state"),async decode(e,t){try{t.logger.debug("DECODE_STATE",{state:e});let r=await tQ({secret:t.jwt.secret,token:e,salt:ib});if(r)return r;throw Error("Invalid state")}catch(e){throw new k("State could not be decoded",{cause:e})}}},ix={async create(e){if(!e.provider.checks.includes("nonce"))return;let t=nY();return{cookie:await id("nonce",t,e),value:t}},use:ih("nonce","nonce")},ig="encodedWebauthnChallenge",iw={create:async(e,t,r)=>({cookie:await id("webauthnChallenge",await tZ({secret:e.jwt.secret,token:{challenge:t,registerData:r},salt:ig,maxAge:900}),e)}),async use(e,t,r){let n=t?.[e.cookies.webauthnChallenge.name],a=await iu("webauthnChallenge",n,e),i=await tQ({secret:e.jwt.secret,token:a,salt:ig});if(ip("webauthnChallenge",e,r),!i)throw new k("WebAuthn challenge was missing");return i}};function i_(e){return encodeURIComponent(e).replace(/%20/g,"+")}async function iv(e,t,r){let n,a,i;let{logger:o,provider:s}=r,{token:c,userinfo:l}=s;if(c?.url&&"authjs.dev"!==c.url.host||l?.url&&"authjs.dev"!==l.url.host)n={issuer:s.issuer??"https://authjs.dev",token_endpoint:c?.url.toString(),userinfo_endpoint:l?.url.toString()};else{let e=new URL(s.issuer),t=await nq(e,{[nE]:!0,[nC]:s[rd]});if(!(n=await nV(e,t)).token_endpoint)throw TypeError("TODO: Authorization server did not provide a token endpoint.");if(!n.userinfo_endpoint)throw TypeError("TODO: Authorization server did not provide a userinfo endpoint.")}let d={client_id:s.clientId,...s.client};switch(d.token_endpoint_auth_method){case void 0:case"client_secret_basic":a=(e,t,r,n)=>{n.set("authorization",function(e,t){let r=i_(e),n=i_(t),a=btoa(`${r}:${n}`);return`Basic ${a}`}(s.clientId,s.clientSecret))};break;case"client_secret_post":var u;nF(u=s.clientSecret,'"clientSecret"'),a=(e,t,r,n)=>{r.set("client_id",t.client_id),r.set("client_secret",u)};break;case"client_secret_jwt":a=function(e,t){let r;nF(e,'"clientSecret"');let n=void 0;return async(t,a,i,o)=>{r||=await crypto.subtle.importKey("raw",nj(e),{hash:"SHA-256",name:"HMAC"},!1,["sign"]);let s={alg:"HS256"},c=n6(t,a);n?.(s,c);let l=`${nH(nj(JSON.stringify(s)))}.${nH(nj(JSON.stringify(c)))}`,d=await crypto.subtle.sign(r.algorithm,r,nj(l));i.set("client_id",a.client_id),i.set("client_assertion_type","urn:ietf:params:oauth:client-assertion-type:jwt-bearer"),i.set("client_assertion",`${l}.${nH(new Uint8Array(d))}`)}}(s.clientSecret);break;case"private_key_jwt":a=function(e,t){let{key:r,kid:n}=e instanceof CryptoKey?{key:e}:e?.key instanceof CryptoKey?(void 0!==e.kid&&nF(e.kid,'"kid"'),{key:e.key,kid:e.kid}):{};return function(e,t){if(function(e,t){if(!(e instanceof CryptoKey))throw nS(`${t} must be a CryptoKey`,nA)}(e,t),"private"!==e.type)throw nS(`${t} must be a private CryptoKey`,nk)}(r,'"clientPrivateKey.key"'),async(e,a,i,o)=>{let s={alg:function(e){switch(e.algorithm.name){case"RSA-PSS":return function(e){switch(e.algorithm.hash.name){case"SHA-256":return"PS256";case"SHA-384":return"PS384";case"SHA-512":return"PS512";default:throw new nD("unsupported RsaHashedKeyAlgorithm hash name",{cause:e})}}(e);case"RSASSA-PKCS1-v1_5":return function(e){switch(e.algorithm.hash.name){case"SHA-256":return"RS256";case"SHA-384":return"RS384";case"SHA-512":return"RS512";default:throw new nD("unsupported RsaHashedKeyAlgorithm hash name",{cause:e})}}(e);case"ECDSA":return function(e){switch(e.algorithm.namedCurve){case"P-256":return"ES256";case"P-384":return"ES384";case"P-521":return"ES512";default:throw new nD("unsupported EcKeyAlgorithm namedCurve",{cause:e})}}(e);case"Ed25519":case"EdDSA":return"Ed25519";default:throw new nD("unsupported CryptoKey algorithm name",{cause:e})}}(r),kid:n},c=n6(e,a);t?.[nP]?.(s,c),i.set("client_id",a.client_id),i.set("client_assertion_type","urn:ietf:params:oauth:client-assertion-type:jwt-bearer"),i.set("client_assertion",await n3(s,c,r))}}(s.token.clientPrivateKey,{[nP](e,t){t.aud=[n.issuer,n.token_endpoint]}});break;case"none":a=(e,t,r,n)=>{r.set("client_id",t.client_id)};break;default:throw Error("unsupported client authentication method")}let f=[],p=await im.use(t,f,r);try{i=function(e,t,r,n){var a;if(n2(e),n5(t),r instanceof URL&&(r=r.searchParams),!(r instanceof URLSearchParams))throw nS('"parameters" must be an instance of URLSearchParams, or URL',nA);if(it(r,"response"))throw nK('"parameters" contains a JARM response, use validateJwtAuthResponse() instead of validateAuthResponse()',aq,{parameters:r});let i=it(r,"iss"),o=it(r,"state");if(!i&&e.authorization_response_iss_parameter_supported)throw nK('response parameter "iss" (issuer) missing',aq,{parameters:r});if(i&&i!==e.issuer)throw nK('unexpected "iss" (issuer) response parameter value',aq,{expected:e.issuer,parameters:r});switch(n){case void 0:case ia:if(void 0!==o)throw nK('unexpected "state" response parameter encountered',aq,{expected:void 0,parameters:r});break;case ir:break;default:if(nF(n,'"expectedState" argument'),o!==n)throw nK(void 0===o?'response parameter "state" missing':'unexpected "state" response parameter value',aq,{expected:n,parameters:r})}if(it(r,"error"))throw new ar("authorization response from the server is an error",{cause:r});let s=it(r,"id_token"),c=it(r,"token");if(void 0!==s||void 0!==c)throw new nD("implicit and hybrid flows are not supported");return a=new URLSearchParams(r),aP.add(a),a}(n,d,new URLSearchParams(e),s.checks.includes("state")?p:ir)}catch(e){if(e instanceof ar){let t={providerId:s.id,...Object.fromEntries(e.cause.entries())};throw o.debug("OAuthCallbackError",t),new P("OAuth Provider returned an error",t)}throw e}let h=await iy.use(t,f,r),y=s.callbackUrl;!r.isOnRedirectProxy&&s.redirectProxyUrl&&(y=s.redirectProxyUrl);let b=await aO(n,d,a,i,y,h??"decoy",{[nE]:!0,[nC]:(...e)=>(s.checks.includes("pkce")||e[1].body.delete("code_verifier"),(s[rd]??fetch)(...e))});s.token?.conform&&(b=await s.token.conform(b.clone())??b);let m={},x="oidc"===s.type;if(s[ru])switch(s.id){case"microsoft-entra-id":case"azure-ad":{let e=await b.clone().json();if(e.error){let t={providerId:s.id,...e};throw new P(`OAuth Provider returned an error: ${e.error}`,t)}let{tid:t}=function(e){let t,r;if("string"!=typeof e)throw new ek("JWTs must use Compact JWS serialization, JWT must be a string");let{1:n,length:a}=e.split(".");if(5===a)throw new ek("Only JWTs using Compact JWS serialization can be decoded");if(3!==a)throw new ek("Invalid JWT");if(!n)throw new ek("JWTs must contain a payload");try{t=eh(n)}catch{throw new ek("Failed to base64url decode the payload")}try{r=JSON.parse(el.decode(t))}catch{throw new ek("Failed to parse the decoded payload as JSON")}if(!eP(r))throw new ek("Invalid JWT Claims Set");return r}(e.id_token);if("string"==typeof t){let e=n.issuer?.match(/microsoftonline\.com\/(\w+)\/v2\.0/)?.[1]??"common",r=new URL(n.issuer.replace(e,t)),a=await nq(r,{[nC]:s[rd]});n=await nV(r,a)}}}let g=await aH(n,d,b,{expectedNonce:await ix.use(t,f,r),requireIdToken:x});if(x){let t=ak(g);if(m=t,s[ru]&&"apple"===s.id)try{m.user=JSON.parse(e?.user)}catch{}if(!1===s.idToken){let e=await ap(n,d,g.access_token,{[nC]:s[rd],[nE]:!0});m=await ax(n,d,t.sub,e)}}else if(l?.request){let e=await l.request({tokens:g,provider:s});e instanceof Object&&(m=e)}else if(l?.url){let e=await ap(n,d,g.access_token,{[nC]:s[rd],[nE]:!0});m=await e.json()}else throw TypeError("No userinfo endpoint configured");return g.expires_in&&(g.expires_at=Math.floor(Date.now()/1e3)+Number(g.expires_in)),{...await ik(m,s,g,o),profile:m,cookies:f}}async function ik(e,t,r,n){try{let n=await t.profile(e,r);return{user:{...n,id:crypto.randomUUID(),email:n.email?.toLowerCase()},account:{...r,provider:t.id,type:t.type,providerAccountId:n.id??crypto.randomUUID()}}}catch(r){n.debug("getProfile error details",e),n.error(new O(r,{provider:t.id}))}}async function iA(e,t,r,n){let a=await iC(e,t,r),{cookie:i}=await iw.create(e,a.challenge,r);return{status:200,cookies:[...n??[],i],body:{action:"register",options:a},headers:{"Content-Type":"application/json"}}}async function iS(e,t,r,n){let a=await iR(e,t,r),{cookie:i}=await iw.create(e,a.challenge);return{status:200,cookies:[...n??[],i],body:{action:"authenticate",options:a},headers:{"Content-Type":"application/json"}}}async function iE(e,t,r){let n;let{adapter:a,provider:i}=e,o=t.body&&"string"==typeof t.body.data?JSON.parse(t.body.data):void 0;if(!o||"object"!=typeof o||!("id"in o)||"string"!=typeof o.id)throw new p("Invalid WebAuthn Authentication response");let s=iU(iO(o.id)),c=await a.getAuthenticator(s);if(!c)throw new p(`WebAuthn authenticator not found in database: ${JSON.stringify({credentialID:s})}`);let{challenge:l}=await iw.use(e,t.cookies,r);try{let r=i.getRelayingParty(e,t);n=await i.simpleWebAuthn.verifyAuthenticationResponse({...i.verifyAuthenticationOptions,expectedChallenge:l,response:o,authenticator:{...c,credentialDeviceType:c.credentialDeviceType,transports:i$(c.transports),credentialID:iO(c.credentialID),credentialPublicKey:iO(c.credentialPublicKey)},expectedOrigin:r.origin,expectedRPID:r.id})}catch(e){throw new q(e)}let{verified:d,authenticationInfo:u}=n;if(!d)throw new q("WebAuthn authentication response could not be verified");try{let{newCounter:e}=u;await a.updateAuthenticatorCounter(c.credentialID,e)}catch(e){throw new y(`Failed to update authenticator counter. This may cause future authentication attempts to fail. ${JSON.stringify({credentialID:s,oldCounter:c.counter,newCounter:u.newCounter})}`,e)}let f=await a.getAccount(c.providerAccountId,i.id);if(!f)throw new p(`WebAuthn account not found in database: ${JSON.stringify({credentialID:s,providerAccountId:c.providerAccountId})}`);let h=await a.getUser(f.userId);if(!h)throw new p(`WebAuthn user not found in database: ${JSON.stringify({credentialID:s,providerAccountId:c.providerAccountId,userID:f.userId})}`);return{account:f,user:h}}async function iT(e,t,r){var n;let a;let{provider:i}=e,o=t.body&&"string"==typeof t.body.data?JSON.parse(t.body.data):void 0;if(!o||"object"!=typeof o||!("id"in o)||"string"!=typeof o.id)throw new p("Invalid WebAuthn Registration response");let{challenge:s,registerData:c}=await iw.use(e,t.cookies,r);if(!c)throw new p("Missing user registration data in WebAuthn challenge cookie");try{let r=i.getRelayingParty(e,t);a=await i.simpleWebAuthn.verifyRegistrationResponse({...i.verifyRegistrationOptions,expectedChallenge:s,response:o,expectedOrigin:r.origin,expectedRPID:r.id})}catch(e){throw new q(e)}if(!a.verified||!a.registrationInfo)throw new q("WebAuthn registration response could not be verified");let l={providerAccountId:iU(a.registrationInfo.credentialID),provider:e.provider.id,type:i.type},d={providerAccountId:l.providerAccountId,counter:a.registrationInfo.counter,credentialID:iU(a.registrationInfo.credentialID),credentialPublicKey:iU(a.registrationInfo.credentialPublicKey),credentialBackedUp:a.registrationInfo.credentialBackedUp,credentialDeviceType:a.registrationInfo.credentialDeviceType,transports:(n=o.response.transports,n?.join(","))};return{user:c,account:l,authenticator:d}}async function iR(e,t,r){let{provider:n,adapter:a}=e,i=r&&r.id?await a.listAuthenticatorsByUserId(r.id):null,o=n.getRelayingParty(e,t);return await n.simpleWebAuthn.generateAuthenticationOptions({...n.authenticationOptions,rpID:o.id,allowCredentials:i?.map(e=>({id:iO(e.credentialID),type:"public-key",transports:i$(e.transports)}))})}async function iC(e,t,r){let{provider:n,adapter:a}=e,i=r.id?await a.listAuthenticatorsByUserId(r.id):null,o=rn(32),s=n.getRelayingParty(e,t);return await n.simpleWebAuthn.generateRegistrationOptions({...n.registrationOptions,userID:o,userName:r.email,userDisplayName:r.name??void 0,rpID:s.id,rpName:s.name,excludeCredentials:i?.map(e=>({id:iO(e.credentialID),type:"public-key",transports:i$(e.transports)}))})}function iP(e){let{provider:t,adapter:r}=e;if(!r)throw new S("An adapter is required for the WebAuthn provider");if(!t||"webauthn"!==t.type)throw new W("Provider must be WebAuthn");return{...e,provider:t,adapter:r}}function iO(e){return new Uint8Array(Buffer.from(e,"base64"))}function iU(e){return Buffer.from(e).toString("base64")}function i$(e){return e?e.split(","):void 0}async function iI(e,t,r,n){if(!t.provider)throw new W("Callback route called without provider");let{query:a,body:i,method:o,headers:s}=e,{provider:c,adapter:l,url:d,callbackUrl:u,pages:f,jwt:h,events:y,callbacks:b,session:{strategy:x,maxAge:g},logger:w}=t,v="jwt"===x;try{if("oauth"===c.type||"oidc"===c.type){let o;let s=c.authorization?.url.searchParams.get("response_mode")==="form_post"?i:a;if(t.isOnRedirectProxy&&s?.state){let e=await im.decode(s.state,t);if(e?.origin&&new URL(e.origin).origin!==t.url.origin){let t=`${e.origin}?${new URLSearchParams(s)}`;return w.debug("Proxy redirecting to",t),{redirect:t,cookies:n}}}let p=await iv(s,e.cookies,t);p.cookies.length&&n.push(...p.cookies),w.debug("authorization result",p);let{user:m,account:x,profile:_}=p;if(!m||!x||!_)return{redirect:`${d}/signin`,cookies:n};if(l){let{getUserByAccount:e}=l;o=await e({providerAccountId:x.providerAccountId,provider:c.id})}let k=await ij({user:o??m,account:x,profile:_},t);if(k)return{redirect:k,cookies:n};let{user:A,session:S,isNewUser:E}=await n_(r.value,m,x,t);if(v){let e={name:A.name,email:A.email,picture:A.image,sub:A.id?.toString()},a=await b.jwt({token:e,user:A,account:x,profile:_,isNewUser:E,trigger:E?"signUp":"signIn"});if(null===a)n.push(...r.clean());else{let e=t.cookies.sessionToken.name,i=await h.encode({...h,token:a,salt:e}),o=new Date;o.setTime(o.getTime()+1e3*g);let s=r.chunk(i,{expires:o});n.push(...s)}}else n.push({name:t.cookies.sessionToken.name,value:S.sessionToken,options:{...t.cookies.sessionToken.options,expires:S.expires}});if(await y.signIn?.({user:A,account:x,profile:_,isNewUser:E}),E&&f.newUser)return{redirect:`${f.newUser}${f.newUser.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:u})}`,cookies:n};return{redirect:u,cookies:n}}if("email"===c.type){let e=a?.token,i=a?.email;if(!e){let t=TypeError("Missing token. The sign-in URL was manually opened without token or the link was not sent correctly in the email.",{cause:{hasToken:!!e}});throw t.name="Configuration",t}let o=c.secret??t.secret,s=await l.useVerificationToken({identifier:i,token:await rr(`${e}${o}`)}),d=!!s,p=d&&s.expires.valueOf()<Date.now();if(!d||p||i&&s.identifier!==i)throw new L({hasInvite:d,expired:p});let{identifier:m}=s,x=await l.getUserByEmail(m)??{id:crypto.randomUUID(),email:m,emailVerified:null},w={providerAccountId:x.email,userId:x.id,type:"email",provider:c.id},_=await ij({user:x,account:w},t);if(_)return{redirect:_,cookies:n};let{user:k,session:A,isNewUser:S}=await n_(r.value,x,w,t);if(v){let e={name:k.name,email:k.email,picture:k.image,sub:k.id?.toString()},a=await b.jwt({token:e,user:k,account:w,isNewUser:S,trigger:S?"signUp":"signIn"});if(null===a)n.push(...r.clean());else{let e=t.cookies.sessionToken.name,i=await h.encode({...h,token:a,salt:e}),o=new Date;o.setTime(o.getTime()+1e3*g);let s=r.chunk(i,{expires:o});n.push(...s)}}else n.push({name:t.cookies.sessionToken.name,value:A.sessionToken,options:{...t.cookies.sessionToken.options,expires:A.expires}});if(await y.signIn?.({user:k,account:w,isNewUser:S}),S&&f.newUser)return{redirect:`${f.newUser}${f.newUser.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:u})}`,cookies:n};return{redirect:u,cookies:n}}if("credentials"===c.type&&"POST"===o){let e=i??{};Object.entries(a??{}).forEach(([e,t])=>d.searchParams.set(e,t));let l=await c.authorize(e,new Request(d,{headers:s,method:o,body:JSON.stringify(i)}));if(l)l.id=l.id?.toString()??crypto.randomUUID();else throw new _;let f={providerAccountId:l.id,type:"credentials",provider:c.id},p=await ij({user:l,account:f,credentials:e},t);if(p)return{redirect:p,cookies:n};let m={name:l.name,email:l.email,picture:l.image,sub:l.id},x=await b.jwt({token:m,user:l,account:f,isNewUser:!1,trigger:"signIn"});if(null===x)n.push(...r.clean());else{let e=t.cookies.sessionToken.name,a=await h.encode({...h,token:x,salt:e}),i=new Date;i.setTime(i.getTime()+1e3*g);let o=r.chunk(a,{expires:i});n.push(...o)}return await y.signIn?.({user:l,account:f}),{redirect:u,cookies:n}}if("webauthn"===c.type&&"POST"===o){let a,i,o;let s=e.body?.action;if("string"!=typeof s||"authenticate"!==s&&"register"!==s)throw new p("Invalid action parameter");let c=iP(t);switch(s){case"authenticate":{let t=await iE(c,e,n);a=t.user,i=t.account;break}case"register":{let r=await iT(t,e,n);a=r.user,i=r.account,o=r.authenticator}}await ij({user:a,account:i},t);let{user:l,isNewUser:d,session:m,account:x}=await n_(r.value,a,i,t);if(!x)throw new p("Error creating or finding account");if(o&&l.id&&await c.adapter.createAuthenticator({...o,userId:l.id}),v){let e={name:l.name,email:l.email,picture:l.image,sub:l.id?.toString()},a=await b.jwt({token:e,user:l,account:x,isNewUser:d,trigger:d?"signUp":"signIn"});if(null===a)n.push(...r.clean());else{let e=t.cookies.sessionToken.name,i=await h.encode({...h,token:a,salt:e}),o=new Date;o.setTime(o.getTime()+1e3*g);let s=r.chunk(i,{expires:o});n.push(...s)}}else n.push({name:t.cookies.sessionToken.name,value:m.sessionToken,options:{...t.cookies.sessionToken.options,expires:m.expires}});if(await y.signIn?.({user:l,account:x,isNewUser:d}),d&&f.newUser)return{redirect:`${f.newUser}${f.newUser.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:u})}`,cookies:n};return{redirect:u,cookies:n}}throw new W(`Callback for provider type (${c.type}) is not supported`)}catch(t){if(t instanceof p)throw t;let e=new m(t,{provider:c.id});throw w.debug("callback route error details",{method:o,query:a,body:i}),e}}async function ij(e,t){let r;let{signIn:n,redirect:a}=t.callbacks;try{r=await n(e)}catch(e){if(e instanceof p)throw e;throw new b(e)}if(!r)throw new b("AccessDenied");if("string"==typeof r)return await a({url:r,baseUrl:t.url.origin})}async function iH(e,t,r,n,a){let{adapter:i,jwt:o,events:s,callbacks:c,logger:l,session:{strategy:d,maxAge:u}}=e,f={body:null,headers:{"Content-Type":"application/json",...!n&&{"Cache-Control":"private, no-cache, no-store",Expires:"0",Pragma:"no-cache"}},cookies:r},p=t.value;if(!p)return f;if("jwt"===d){try{let r=e.cookies.sessionToken.name,i=await o.decode({...o,token:p,salt:r});if(!i)throw Error("Invalid JWT");let l=await c.jwt({token:i,...n&&{trigger:"update"},session:a}),d=nw(u);if(null!==l){let e={user:{name:l.name,email:l.email,image:l.picture},expires:d.toISOString()},n=await c.session({session:e,token:l});f.body=n;let a=await o.encode({...o,token:l,salt:r}),i=t.chunk(a,{expires:d});f.cookies?.push(...i),await s.session?.({session:n,token:l})}else f.cookies?.push(...t.clean())}catch(e){l.error(new A(e)),f.cookies?.push(...t.clean())}return f}try{let{getSessionAndUser:r,deleteSession:o,updateSession:l}=i,d=await r(p);if(d&&d.session.expires.valueOf()<Date.now()&&(await o(p),d=null),d){let{user:t,session:r}=d,i=e.session.updateAge,o=r.expires.valueOf()-1e3*u+1e3*i,h=nw(u);o<=Date.now()&&await l({sessionToken:p,expires:h});let y=await c.session({session:{...r,user:t},user:t,newSession:a,...n?{trigger:"update"}:{}});f.body=y,f.cookies?.push({name:e.cookies.sessionToken.name,value:p,options:{...e.cookies.sessionToken.options,expires:h}}),await s.session?.({session:y})}else p&&f.cookies?.push(...t.clean())}catch(e){l.error(new U(e))}return f}async function iD(e,t){let r,n;let{logger:a,provider:i}=t,o=i.authorization?.url;if(!o||"authjs.dev"===o.host){let e=new URL(i.issuer),t=await nq(e,{[nC]:i[rd],[nE]:!0}),r=await nV(e,t).catch(t=>{if(!(t instanceof TypeError)||"Invalid URL"!==t.message)throw t;throw TypeError(`Discovery request responded with an invalid issuer. expected: ${e}`)});if(!r.authorization_endpoint)throw TypeError("Authorization server did not provide an authorization endpoint.");o=new URL(r.authorization_endpoint)}let s=o.searchParams,c=i.callbackUrl;!t.isOnRedirectProxy&&i.redirectProxyUrl&&(c=i.redirectProxyUrl,n=i.callbackUrl,a.debug("using redirect proxy",{redirect_uri:c,data:n}));let l=Object.assign({response_type:"code",client_id:i.clientId,redirect_uri:c,...i.authorization?.params},Object.fromEntries(i.authorization?.url.searchParams??[]),e);for(let e in l)s.set(e,l[e]);let d=[];i.authorization?.url.searchParams.get("response_mode")==="form_post"&&(t.cookies.state.options.sameSite="none",t.cookies.state.options.secure=!0,t.cookies.nonce.options.sameSite="none",t.cookies.nonce.options.secure=!0);let u=await im.create(t,n);if(u&&(s.set("state",u.value),d.push(u.cookie)),i.checks?.includes("pkce")){if(r&&!r.code_challenge_methods_supported?.includes("S256"))"oidc"===i.type&&(i.checks=["nonce"]);else{let{value:e,cookie:r}=await iy.create(t);s.set("code_challenge",e),s.set("code_challenge_method","S256"),d.push(r)}}let f=await ix.create(t);return f&&(s.set("nonce",f.value),d.push(f.cookie)),"oidc"!==i.type||o.searchParams.has("scope")||o.searchParams.set("scope","openid profile email"),a.debug("authorization url is ready",{url:o,cookies:d,provider:i}),{redirect:o.toString(),cookies:d}}async function iW(e,t){let r;let{body:n}=e,{provider:a,callbacks:i,adapter:o}=t,s=(a.normalizeIdentifier??function(e){if(!e)throw Error("Missing email from request body.");let[t,r]=e.toLowerCase().trim().split("@");return r=r.split(",")[0],`${t}@${r}`})(n?.email),c={id:crypto.randomUUID(),email:s,emailVerified:null},l=await o.getUserByEmail(s)??c,d={providerAccountId:s,userId:l.id,type:"email",provider:a.id};try{r=await i.signIn({user:l,account:d,email:{verificationRequest:!0}})}catch(e){throw new b(e)}if(!r)throw new b("AccessDenied");if("string"==typeof r)return{redirect:await i.redirect({url:r,baseUrl:t.url.origin})};let{callbackUrl:u,theme:f}=t,p=await a.generateVerificationToken?.()??rn(32),h=new Date(Date.now()+(a.maxAge??86400)*1e3),y=a.secret??t.secret,m=new URL(t.basePath,t.url.origin),x=a.sendVerificationRequest({identifier:s,token:p,expires:h,url:`${m}/callback/${a.id}?${new URLSearchParams({callbackUrl:u,token:p,email:s})}`,provider:a,theme:f,request:new Request(e.url,{headers:e.headers,method:e.method,body:"POST"===e.method?JSON.stringify(e.body??{}):void 0})}),g=o.createVerificationToken?.({identifier:s,token:await rr(`${p}${y}`),expires:h});return await Promise.all([x,g]),{redirect:`${m}/verify-request?${new URLSearchParams({provider:a.id,type:a.type})}`}}async function iK(e,t,r){let n=`${r.url.origin}${r.basePath}/signin`;if(!r.provider)return{redirect:n,cookies:t};switch(r.provider.type){case"oauth":case"oidc":{let{redirect:n,cookies:a}=await iD(e.query,r);return a&&t.push(...a),{redirect:n,cookies:t}}case"email":return{...await iW(e,r),cookies:t};default:return{redirect:n,cookies:t}}}async function iL(e,t,r){let{jwt:n,events:a,callbackUrl:i,logger:o,session:s}=r,c=t.value;if(!c)return{redirect:i,cookies:e};try{if("jwt"===s.strategy){let e=r.cookies.sessionToken.name,t=await n.decode({...n,token:c,salt:e});await a.signOut?.({token:t})}else{let e=await r.adapter?.deleteSession(c);await a.signOut?.({session:e})}}catch(e){o.error(new j(e))}return e.push(...t.clean()),{redirect:i,cookies:e}}async function iM(e,t){let{adapter:r,jwt:n,session:{strategy:a}}=e,i=t.value;if(!i)return null;if("jwt"===a){let t=e.cookies.sessionToken.name,r=await n.decode({...n,token:i,salt:t});if(r&&r.sub)return{id:r.sub,name:r.name,email:r.email,image:r.picture}}else{let e=await r?.getSessionAndUser(i);if(e)return e.user}return null}async function iN(e,t,r,n){let a=iP(t),{provider:i}=a,{action:o}=e.query??{};if("register"!==o&&"authenticate"!==o&&void 0!==o)return{status:400,body:{error:"Invalid action"},cookies:n,headers:{"Content-Type":"application/json"}};let s=await iM(t,r),c=s?{user:s,exists:!0}:await i.getUserInfo(t,e),l=c?.user;switch(function(e,t,r){let{user:n,exists:a=!1}=r??{};switch(e){case"authenticate":return"authenticate";case"register":if(n&&t===a)return"register";break;case void 0:if(!t){if(!n||a)return"authenticate";return"register"}}return null}(o,!!s,c)){case"authenticate":return iS(a,e,l,n);case"register":if("string"==typeof l?.email)return iA(a,e,l,n);break;default:return{status:400,body:{error:"Invalid request"},cookies:n,headers:{"Content-Type":"application/json"}}}}async function iJ(e,t){let{action:r,providerId:n,error:a,method:i}=e,o=t.skipCSRFCheck===rc,{options:s,cookies:c}=await rm({authOptions:t,action:r,providerId:n,url:e.url,callbackUrl:e.body?.callbackUrl??e.query?.callbackUrl,csrfToken:e.body?.csrfToken,cookies:e.cookies,isPost:"POST"===i,csrfDisabled:o}),l=new f(s.cookies.sessionToken,e.cookies,s.logger);if("GET"===i){let t=ng({...s,query:e.query,cookies:c});switch(r){case"callback":return await iI(e,s,l,c);case"csrf":return t.csrf(o,s,c);case"error":return t.error(a);case"providers":return t.providers(s.providers);case"session":return await iH(s,l,c);case"signin":return t.signin(n,a);case"signout":return t.signout();case"verify-request":return t.verifyRequest();case"webauthn-options":return await iN(e,s,l,c)}}else{let{csrfTokenVerified:t}=s;switch(r){case"callback":return"credentials"===s.provider.type&&ri(r,t),await iI(e,s,l,c);case"session":return ri(r,t),await iH(s,l,c,!0,e.body?.data);case"signin":return ri(r,t),await iK(e,c,s);case"signout":return ri(r,t),await iL(c,l,s)}}throw new H(`Cannot handle action: ${r}`)}function iB(e,t,r,n,a){let i;let o=a?.basePath,s=n.AUTH_URL??n.NEXTAUTH_URL;if(s)i=new URL(s),o&&"/"!==o&&"/"!==i.pathname&&(i.pathname!==o&&t3(a).warn("env-url-basepath-mismatch"),i.pathname="/");else{let e=r.get("x-forwarded-host")??r.get("host"),n=r.get("x-forwarded-proto")??t??"https",a=n.endsWith(":")?n:n+":";i=new URL(`${a}//${e}`)}let c=i.toString().replace(/\/$/,"");if(o){let t=o?.replace(/(^\/|\/$)/g,"")??"";return new URL(`${c}/${t}/${e}`)}return new URL(`${c}/${e}`)}async function iq(e,t){let r=t3(t),n=await re(e,t);if(!n)return Response.json("Bad request.",{status:400});let a=function(e,t){let{url:r}=e,n=[];if(!V&&t.debug&&n.push("debug-enabled"),!t.trustHost)return new K(`Host must be trusted. URL was: ${e.url}`);if(!t.secret?.length)return new R("Please define a `secret`");let a=e.query?.callbackUrl;if(a&&!G(a,r.origin))return new w(`Invalid callback URL. Received: ${a}`);let{callbackUrl:i}=u(t.useSecureCookies??"https:"===r.protocol),o=e.cookies?.[t.cookies?.callbackUrl?.name??i.name];if(o&&!G(o,r.origin))return new w(`Invalid callback URL. Received: ${o}`);let s=!1;for(let e of t.providers){let t="function"==typeof e?e():e;if(("oauth"===t.type||"oidc"===t.type)&&!(t.issuer??t.options?.issuer)){let e;let{authorization:r,token:n,userinfo:a}=t;if("string"==typeof r||r?.url?"string"==typeof n||n?.url?"string"==typeof a||a?.url||(e="userinfo"):e="token":e="authorization",e)return new v(`Provider "${t.id}" is missing both \`issuer\` and \`${e}\` endpoint config. At least one of them is required`)}if("credentials"===t.type)X=!0;else if("email"===t.type)Y=!0;else if("webauthn"===t.type){var c;if(Z=!0,t.simpleWebAuthnBrowserVersion&&(c=t.simpleWebAuthnBrowserVersion,!/^v\d+(?:\.\d+){0,2}$/.test(c)))return new p(`Invalid provider config for "${t.id}": simpleWebAuthnBrowserVersion "${t.simpleWebAuthnBrowserVersion}" must be a valid semver string.`);if(t.enableConditionalUI){if(s)return new J("Multiple webauthn providers have 'enableConditionalUI' set to True. Only one provider can have this option enabled at a time");if(s=!0,!Object.values(t.formFields).some(e=>e.autocomplete&&e.autocomplete.toString().indexOf("webauthn")>-1))return new B(`Provider "${t.id}" has 'enableConditionalUI' set to True, but none of its formFields have 'webauthn' in their autocomplete param`)}}}if(X){let e=t.session?.strategy==="database",r=!t.providers.some(e=>"credentials"!==("function"==typeof e?e():e).type);if(e&&r)return new D("Signing in with credentials only supported if JWT strategy is enabled");if(t.providers.some(e=>{let t="function"==typeof e?e():e;return"credentials"===t.type&&!t.authorize}))return new T("Must define an authorize() handler to use credentials authentication provider")}let{adapter:l,session:d}=t,f=[];if(Y||d?.strategy==="database"||!d?.strategy&&l){if(Y){if(!l)return new S("Email login requires an adapter");f.push(...Q)}else{if(!l)return new S("Database session requires an adapter");f.push(...ee)}}if(Z){if(!t.experimental?.enableWebAuthn)return new F("WebAuthn is an experimental feature. To enable it, set `experimental.enableWebAuthn` to `true` in your config");if(n.push("experimental-webauthn"),!l)return new S("WebAuthn requires an adapter");f.push(...et)}if(l){let e=f.filter(e=>!(e in l));if(e.length)return new E(`Required adapter methods were missing: ${e.join(", ")}`)}return V||(V=!0),n}(n,t);if(Array.isArray(a))a.forEach(r.warn);else if(a){if(r.error(a),!new Set(["signin","signout","error","verify-request"]).has(n.action)||"GET"!==n.method)return Response.json({message:"There was a problem with the server configuration. Check the server logs for more information."},{status:500});let{pages:e,theme:i}=t,o=e?.error&&n.url.searchParams.get("callbackUrl")?.startsWith(e.error);if(!e?.error||o)return o&&r.error(new x(`The error page ${e?.error} should not require authentication`)),rt(ng({theme:i}).error("Configuration"));let s=`${n.url.origin}${e.error}?error=Configuration`;return Response.redirect(s)}let i=e.headers?.has("X-Auth-Return-Redirect"),o=t.raw===rl;try{let e=await iJ(n,t);if(o)return e;let r=rt(e),a=r.headers.get("Location");if(!i||!a)return r;return Response.json({url:a},{headers:r.headers})}catch(u){r.error(u);let a=u instanceof p;if(a&&o&&!i)throw u;if("POST"===e.method&&"session"===n.action)return Response.json(null,{status:400});let s=new URLSearchParams({error:u instanceof p&&N.has(u.type)?u.type:"Configuration"});u instanceof _&&s.set("code",u.code);let c=a&&u.kind||"error",l=t.pages?.[c]??`${t.basePath}/${c.toLowerCase()}`,d=`${n.url.origin}${l}?${s}`;if(i)return Response.json({url:d});return Response.redirect(d)}}var iz=r(39187);function iF(e){let t=process.env.AUTH_URL??process.env.NEXTAUTH_URL;if(!t)return e;let{origin:r}=new URL(t),{href:n,origin:a}=e.nextUrl;return new iz.NextRequest(n.replace(a,r),e)}function iV(e){try{e.secret??(e.secret=process.env.AUTH_SECRET??process.env.NEXTAUTH_SECRET);let t=process.env.AUTH_URL??process.env.NEXTAUTH_URL;if(!t)return;let{pathname:r}=new URL(t);if("/"===r)return;e.basePath||(e.basePath=r)}catch{}finally{e.basePath||(e.basePath="/api/auth"),function(e,t,r=!1){try{let n=e.AUTH_URL;n&&(t.basePath?r||t3(t).warn("env-url-basepath-redundant"):t.basePath=new URL(n).pathname)}catch{}finally{t.basePath??(t.basePath="/auth")}if(!t.secret?.length){t.secret=[];let r=e.AUTH_SECRET;for(let n of(r&&t.secret.push(r),[1,2,3])){let r=e[`AUTH_SECRET_${n}`];r&&t.secret.unshift(r)}}t.redirectProxyUrl??(t.redirectProxyUrl=e.AUTH_REDIRECT_PROXY_URL),t.trustHost??(t.trustHost=!!(e.AUTH_URL??e.AUTH_TRUST_HOST??e.VERCEL??e.CF_PAGES??"production"!==e.NODE_ENV)),t.providers=t.providers.map(t=>{let{id:r}="function"==typeof t?t({}):t,n=r.toUpperCase().replace(/-/g,"_"),a=e[`AUTH_${n}_ID`],i=e[`AUTH_${n}_SECRET`],o=e[`AUTH_${n}_ISSUER`],s=e[`AUTH_${n}_KEY`],c="function"==typeof t?t({clientId:a,clientSecret:i,issuer:o,apiKey:s}):t;return"oauth"===c.type||"oidc"===c.type?(c.clientId??(c.clientId=a),c.clientSecret??(c.clientSecret=i),c.issuer??(c.issuer=o)):"email"===c.type&&(c.apiKey??(c.apiKey=s)),c})}(process.env,e,!0)}}var iG=r(97200),iX=r(83009);async function iY(e,t){return iq(new Request(iB("session",e.get("x-forwarded-proto"),e,process.env,t),{headers:{cookie:e.get("cookie")??""}}),{...t,callbacks:{...t.callbacks,async session(...e){let r=await t.callbacks?.session?.(...e)??{...e[0].session,expires:e[0].session.expires?.toISOString?.()??e[0].session.expires};return{user:e[0].user??e[0].token,...r}}}})}function iZ(e){return"function"==typeof e}function iQ(e,t){return"function"==typeof e?async(...r)=>{if(!r.length){let r=await (0,iX.b)(),n=await e(void 0);return t?.(n),iY(r,n).then(e=>e.json())}if(r[0]instanceof Request){let n=r[0],a=r[1],i=await e(n);return t?.(i),i0([n,a],i)}if(iZ(r[0])){let n=r[0];return async(...r)=>{let a=await e(r[0]);return t?.(a),i0(r,a,n)}}let n="req"in r[0]?r[0].req:r[0],a="res"in r[0]?r[0].res:r[1],i=await e(n);return t?.(i),iY(new Headers(n.headers),i).then(async e=>{let t=await e.json();for(let t of e.headers.getSetCookie())"headers"in a?a.headers.append("set-cookie",t):a.appendHeader("set-cookie",t);return t})}:(...t)=>{if(!t.length)return Promise.resolve((0,iX.b)()).then(t=>iY(t,e).then(e=>e.json()));if(t[0]instanceof Request)return i0([t[0],t[1]],e);if(iZ(t[0])){let r=t[0];return async(...t)=>i0(t,e,r).then(e=>e)}let r="req"in t[0]?t[0].req:t[0],n="res"in t[0]?t[0].res:t[1];return iY(new Headers(r.headers),e).then(async e=>{let t=await e.json();for(let t of e.headers.getSetCookie())"headers"in n?n.headers.append("set-cookie",t):n.appendHeader("set-cookie",t);return t})}}async function i0(e,t,r){let n=iF(e[0]),a=await iY(n.headers,t),i=await a.json(),o=!0;t.callbacks?.authorized&&(o=await t.callbacks.authorized({request:n,auth:i}));let s=iz.NextResponse.next?.();if(o instanceof Response){s=o;let e=o.headers.get("Location"),{pathname:r}=n.nextUrl;e&&function(e,t,r){let n=t.replace(`${e}/`,""),a=Object.values(r.pages??{});return(i1.has(n)||a.includes(t))&&t===e}(r,new URL(e).pathname,t)&&(o=!0)}else if(r)n.auth=i,s=await r(n,e[1])??iz.NextResponse.next();else if(!o){let e=t.pages?.signIn??`${t.basePath}/signin`;if(n.nextUrl.pathname!==e){let t=n.nextUrl.clone();t.pathname=e,t.searchParams.set("callbackUrl",n.nextUrl.href),s=iz.NextResponse.redirect(t)}}let c=new Response(s?.body,s);for(let e of a.headers.getSetCookie())c.headers.append("set-cookie",e);return c}r(46250);let i1=new Set(["providers","session","csrf","signin","signout","callback","verify-request","error"]);var i2=r(67359);async function i5(e,t={},r,n){let a=new Headers(await (0,iX.b)()),{redirect:i=!0,redirectTo:o,...s}=t instanceof FormData?Object.fromEntries(t):t,c=o?.toString()??a.get("Referer")??"/",l=iB("signin",a.get("x-forwarded-proto"),a,process.env,n);if(!e)return l.searchParams.append("callbackUrl",c),i&&(0,i2.redirect)(l.toString()),l.toString();let d=`${l}/${e}?${new URLSearchParams(r)}`,u={};for(let t of n.providers){let{options:r,...n}="function"==typeof t?t():t,a=r?.id??n.id;if(a===e){u={id:a,type:r?.type??n.type};break}}if(!u.id){let e=`${l}?${new URLSearchParams({callbackUrl:c})}`;return i&&(0,i2.redirect)(e),e}"credentials"===u.type&&(d=d.replace("signin","callback")),a.set("Content-Type","application/x-www-form-urlencoded");let f=new Request(d,{method:"POST",headers:a,body:new URLSearchParams({...s,callbackUrl:c})}),p=await iq(f,{...n,raw:rl,skipCSRFCheck:rc}),h=await (0,iG.U)();for(let e of p?.cookies??[])h.set(e.name,e.value,e.options);let y=(p instanceof Response?p.headers.get("Location"):p.redirect)??d;return i?(0,i2.redirect)(y):y}async function i6(e,t){let r=new Headers(await (0,iX.b)());r.set("Content-Type","application/x-www-form-urlencoded");let n=iB("signout",r.get("x-forwarded-proto"),r,process.env,t),a=new URLSearchParams({callbackUrl:e?.redirectTo??r.get("Referer")??"/"}),i=new Request(n,{method:"POST",headers:r,body:a}),o=await iq(i,{...t,raw:rl,skipCSRFCheck:rc}),s=await (0,iG.U)();for(let e of o?.cookies??[])s.set(e.name,e.value,e.options);return e?.redirect??!0?(0,i2.redirect)(o.redirect):o}async function i3(e,t){let r=new Headers(await (0,iX.b)());r.set("Content-Type","application/json");let n=new Request(iB("session",r.get("x-forwarded-proto"),r,process.env,t),{method:"POST",headers:r,body:JSON.stringify({data:e})}),a=await iq(n,{...t,raw:rl,skipCSRFCheck:rc}),i=await (0,iG.U)();for(let e of a?.cookies??[])i.set(e.name,e.value,e.options);return a.body}function i8(e){if("function"==typeof e){let t=async t=>{let r=await e(t);return iV(r),iq(iF(t),r)};return{handlers:{GET:t,POST:t},auth:iQ(e,e=>iV(e)),signIn:async(t,r,n)=>{let a=await e(void 0);return iV(a),i5(t,r,n,a)},signOut:async t=>{let r=await e(void 0);return iV(r),i6(t,r)},unstable_update:async t=>{let r=await e(void 0);return iV(r),i3(t,r)}}}iV(e);let t=t=>iq(iF(t),e);return{handlers:{GET:t,POST:t},auth:iQ(e),signIn:(t,r,n)=>i5(t,r,n,e),signOut:t=>i6(t,e),unstable_update:t=>i3(t,e)}}}};