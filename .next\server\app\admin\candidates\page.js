(()=>{var e={};e.id=8997,e.ids=[8997],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},42076:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>d.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>c});var a=s(70260),r=s(28203),i=s(25155),d=s.n(i),n=s(67292),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);s.d(t,l);let c=["",{children:["admin",{children:["candidates",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,12702)),"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\admin\\candidates\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,96038)),"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\admin\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,71354)),"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],o=["C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\admin\\candidates\\page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/admin/candidates/page",pathname:"/admin/candidates",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},29162:(e,t,s)=>{Promise.resolve().then(s.bind(s,12702))},92210:(e,t,s)=>{Promise.resolve().then(s.bind(s,67611))},8866:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(41680).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},67611:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>f});var a=s(45512),r=s(58009),i=s(28531),d=s.n(i),n=s(45103),l=s(69855),c=s(16873),o=s(94889),x=s(41680);let m=(0,x.A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);var h=s(19473),p=s(8866);let u=(0,x.A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]]);var y=s(45723),b=s(21956),g=s(19904);function f(){let[e,t]=(0,r.useState)([]),[s,i]=(0,r.useState)(!0),[x,f]=(0,r.useState)(""),[j,v]=(0,r.useState)(1),[N,w]=(0,r.useState)(1),[k,C]=(0,r.useState)([]),S=(0,r.useCallback)(async()=>{i(!0);try{let e=new URLSearchParams({page:j.toString(),limit:"20",search:x}),s=await fetch(`/api/admin/candidates?${e}`);if(s.ok){let e=await s.json();t(e.candidates),w(Math.ceil(e.total/20))}}catch(e){console.error("Error fetching candidates:",e)}finally{i(!1)}},[j,x,20]),A=e=>{C(t=>t.includes(e)?t.filter(t=>t!==e):[...t,e])},P=async()=>{if(confirm(`Are you sure you want to delete ${k.length} candidate(s)?`))try{(await fetch("/api/admin/candidates/bulk-delete",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({candidateIds:k})})).ok&&(C([]),S())}catch(e){console.error("Error deleting candidates:",e)}};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Candidates"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Manage test candidates and their information"})]}),(0,a.jsxs)(d(),{href:"/admin/candidates/new",className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700",children:[(0,a.jsx)(l.A,{className:"h-4 w-4 mr-2"}),"Add Candidate"]})]}),(0,a.jsx)("div",{className:"bg-white shadow rounded-lg p-6",children:(0,a.jsxs)("form",{onSubmit:e=>{e.preventDefault(),v(1),S()},className:"flex gap-4",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(c.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,a.jsx)("input",{type:"text",placeholder:"Search by name, email, or passport number...",value:x,onChange:e=>f(e.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]})}),(0,a.jsx)("button",{type:"submit",className:"px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700",children:"Search"}),(0,a.jsxs)("button",{type:"button",className:"px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50",children:[(0,a.jsx)(o.A,{className:"h-4 w-4 mr-2 inline"}),"Filters"]})]})}),k.length>0&&(0,a.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("span",{className:"text-sm text-blue-700",children:[k.length," candidate(s) selected"]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)("button",{onClick:P,className:"px-3 py-1 text-sm font-medium text-red-700 bg-red-100 rounded hover:bg-red-200",children:[(0,a.jsx)(m,{className:"h-4 w-4 mr-1 inline"}),"Delete"]}),(0,a.jsxs)("button",{className:"px-3 py-1 text-sm font-medium text-blue-700 bg-blue-100 rounded hover:bg-blue-200",children:[(0,a.jsx)(h.A,{className:"h-4 w-4 mr-1 inline"}),"Export"]})]})]})}),(0,a.jsx)("div",{className:"bg-white shadow rounded-lg overflow-hidden",children:s?(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left",children:(0,a.jsx)("input",{type:"checkbox",checked:k.length===e.length&&e.length>0,onChange:()=>{k.length===e.length?C([]):C(e.map(e=>e.id))},className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"})}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Candidate"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Contact"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Test Details"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Registration"}),(0,a.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:e.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsx)("input",{type:"checkbox",checked:k.includes(e.id),onChange:()=>A(e.id),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"})}),(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[e.photoUrl&&(0,a.jsx)(n.default,{className:"h-10 w-10 rounded-full mr-4",src:e.photoUrl,alt:e.fullName,width:40,height:40}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.fullName}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.passportNumber})]})]})}),(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsxs)("div",{className:"text-sm text-gray-900",children:[(0,a.jsxs)("div",{className:"flex items-center mb-1",children:[(0,a.jsx)(p.A,{className:"h-4 w-4 mr-1 text-gray-400"}),e.email]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(u,{className:"h-4 w-4 mr-1 text-gray-400"}),e.phoneNumber]})]})}),(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsxs)("div",{className:"text-sm text-gray-900",children:[(0,a.jsxs)("div",{className:"flex items-center mb-1",children:[(0,a.jsx)(y.A,{className:"h-4 w-4 mr-1 text-gray-400"}),new Date(e.testDate).toLocaleDateString()]}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.testCenter})]})}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-500",children:new Date(e.createdAt).toLocaleDateString()}),(0,a.jsx)("td",{className:"px-6 py-4 text-right text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex justify-end gap-2",children:[(0,a.jsx)(d(),{href:`/admin/candidates/${e.id}`,className:"text-blue-600 hover:text-blue-900",children:(0,a.jsx)(b.A,{className:"h-4 w-4"})}),(0,a.jsx)(d(),{href:`/admin/candidates/${e.id}/edit`,className:"text-green-600 hover:text-green-900",children:(0,a.jsx)(g.A,{className:"h-4 w-4"})}),(0,a.jsx)("button",{className:"text-red-600 hover:text-red-900",children:(0,a.jsx)(m,{className:"h-4 w-4"})})]})})]},e.id))})]})}),N>1&&(0,a.jsx)("div",{className:"bg-white px-4 py-3 border-t border-gray-200 sm:px-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"text-sm text-gray-700",children:["Page ",j," of ",N]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)("button",{onClick:()=>v(e=>Math.max(1,e-1)),disabled:1===j,className:"px-3 py-1 text-sm border border-gray-300 rounded disabled:opacity-50",children:"Previous"}),(0,a.jsx)("button",{onClick:()=>v(e=>Math.min(N,e+1)),disabled:j===N,className:"px-3 py-1 text-sm border border-gray-300 rounded disabled:opacity-50",children:"Next"})]})]})})]})})]})}},12702:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\candidates\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\admin\\candidates\\page.tsx","default")}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[638,8338,2367,8324,9807],()=>s(42076));module.exports=a})();