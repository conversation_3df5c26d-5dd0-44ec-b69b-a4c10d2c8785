(()=>{var e={};e.id=5105,e.ids=[5105],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},43158:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>h,pages:()=>c,routeModule:()=>m,tree:()=>o});var r=t(70260),a=t(28203),i=t(25155),l=t.n(i),n=t(67292),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);t.d(s,d);let o=["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,57154)),"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\dashboard\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,33405)),"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,71354)),"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\dashboard\\page.tsx"],h={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},97032:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,13219,23)),Promise.resolve().then(t.t.bind(t,34863,23)),Promise.resolve().then(t.t.bind(t,25155,23)),Promise.resolve().then(t.t.bind(t,40802,23)),Promise.resolve().then(t.t.bind(t,9350,23)),Promise.resolve().then(t.t.bind(t,48530,23)),Promise.resolve().then(t.t.bind(t,88921,23))},60584:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,66959,23)),Promise.resolve().then(t.t.bind(t,33875,23)),Promise.resolve().then(t.t.bind(t,88903,23)),Promise.resolve().then(t.t.bind(t,57174,23)),Promise.resolve().then(t.t.bind(t,84178,23)),Promise.resolve().then(t.t.bind(t,87190,23)),Promise.resolve().then(t.t.bind(t,61365,23))},71315:(e,s,t)=>{Promise.resolve().then(t.bind(t,70452))},8267:(e,s,t)=>{Promise.resolve().then(t.bind(t,98805))},63508:(e,s,t)=>{Promise.resolve().then(t.bind(t,33405))},16244:(e,s,t)=>{Promise.resolve().then(t.bind(t,12361))},33587:(e,s,t)=>{Promise.resolve().then(t.bind(t,57154))},70539:(e,s,t)=>{Promise.resolve().then(t.bind(t,40134))},41680:(e,s,t)=>{"use strict";t.d(s,{A:()=>h});var r=t(58009);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,s,t)=>t?t.toUpperCase():s.toLowerCase()),l=e=>{let s=i(e);return s.charAt(0).toUpperCase()+s.slice(1)},n=(...e)=>e.filter((e,s,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===s).join(" ").trim(),d=e=>{for(let s in e)if(s.startsWith("aria-")||"role"===s||"title"===s)return!0};var o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,r.forwardRef)(({color:e="currentColor",size:s=24,strokeWidth:t=2,absoluteStrokeWidth:a,className:i="",children:l,iconNode:c,...h},m)=>(0,r.createElement)("svg",{ref:m,...o,width:s,height:s,stroke:e,strokeWidth:a?24*Number(t)/Number(s):t,className:n("lucide",i),...!l&&!d(h)&&{"aria-hidden":"true"},...h},[...c.map(([e,s])=>(0,r.createElement)(e,s)),...Array.isArray(l)?l:[l]])),h=(e,s)=>{let t=(0,r.forwardRef)(({className:t,...i},d)=>(0,r.createElement)(c,{ref:d,iconNode:s,className:n(`lucide-${a(l(e))}`,`lucide-${e}`,t),...i}));return t.displayName=l(e),t}},78397:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(41680).A)("brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]])},79660:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(41680).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},45037:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(41680).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},46583:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(41680).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},94172:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(41680).A)("clipboard-list",[["rect",{width:"8",height:"4",x:"8",y:"2",rx:"1",ry:"1",key:"tgr4d6"}],["path",{d:"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2",key:"116196"}],["path",{d:"M12 11h4",key:"1jrz19"}],["path",{d:"M12 16h4",key:"n85exb"}],["path",{d:"M8 11h.01",key:"1dfujw"}],["path",{d:"M8 16h.01",key:"18s6g9"}]])},4643:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(41680).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},61075:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(41680).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},87137:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(41680).A)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},30722:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(41680).A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},16873:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(41680).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},79334:(e,s,t)=>{"use strict";var r=t(58686);t.o(r,"useParams")&&t.d(s,{useParams:function(){return r.useParams}}),t.o(r,"useRouter")&&t.d(s,{useRouter:function(){return r.useRouter}}),t.o(r,"useSearchParams")&&t.d(s,{useSearchParams:function(){return r.useSearchParams}})},12361:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>p});var r=t(45512),a=t(98805),i=t(79334);t(58009);var l=t(28531),n=t.n(l),d=t(87137),o=t(16873),c=t(94172),h=t(79660),m=t(78397),u=t(61075),x=t(30722);function p({children:e}){let{data:s,status:t}=(0,a.wV)();if((0,i.useRouter)(),"loading"===t)return(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})});if(!s)return null;let l=[{name:"Dashboard",href:"/dashboard",icon:d.A},{name:"Search Candidates",href:"/dashboard/search",icon:o.A},{name:"Enter Results",href:"/dashboard/results",icon:c.A},{name:"Test Results",href:"/dashboard/results/list",icon:h.A},{name:"AI Feedback",href:"/dashboard/feedback",icon:m.A}];return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)("div",{className:"fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg",children:(0,r.jsxs)("div",{className:"flex flex-col h-full",children:[(0,r.jsxs)("div",{className:"flex items-center px-6 py-4 border-b border-gray-200",children:[(0,r.jsx)(u.A,{className:"h-8 w-8 text-blue-600 mr-3"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-lg font-semibold text-gray-900",children:"IELTS Checker"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Test Results Portal"})]})]}),(0,r.jsx)("nav",{className:"flex-1 px-4 py-6 space-y-2",children:l.map(e=>{let s=e.icon;return(0,r.jsxs)(n(),{href:e.href,className:"flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-100 hover:text-gray-900 group",children:[(0,r.jsx)(s,{className:"h-5 w-5 mr-3 text-gray-400 group-hover:text-gray-500"}),e.name]},e.name)})}),(0,r.jsxs)("div",{className:"border-t border-gray-200 p-4",children:[(0,r.jsx)("div",{className:"flex items-center mb-3",children:(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900",children:s.user?.name}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:s.user?.email}),(0,r.jsx)("p",{className:"text-xs text-green-600 font-medium",children:"Test Checker"})]})}),(0,r.jsxs)("button",{onClick:()=>(0,a.CI)({callbackUrl:"/"}),className:"flex items-center w-full px-3 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-100 hover:text-gray-900",children:[(0,r.jsx)(x.A,{className:"h-4 w-4 mr-3"}),"Sign out"]})]})]})}),(0,r.jsx)("div",{className:"pl-64",children:(0,r.jsx)("main",{className:"py-6",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:e})})})]})}},40134:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>p});var r=t(45512),a=t(58009),i=t(28531),l=t.n(i),n=t(94172),d=t(4643),o=t(46583),c=t(16873),h=t(79660),m=t(78397),u=t(45037),x=t(61075);function p(){let[e,s]=(0,a.useState)({totalResultsEntered:0,pendingResults:0,completedResults:0,recentResults:[]}),[t,i]=(0,a.useState)(!0),p=[{name:"Results Entered",value:e.totalResultsEntered,icon:n.A,color:"bg-blue-500",href:"/dashboard/results/list"},{name:"Pending Review",value:e.pendingResults,icon:d.A,color:"bg-yellow-500",href:"/dashboard/results/list?status=pending"},{name:"Completed",value:e.completedResults,icon:o.A,color:"bg-green-500",href:"/dashboard/results/list?status=completed"}],b=[{name:"Search Candidates",description:"Find candidates to enter results",href:"/dashboard/search",icon:c.A,color:"bg-blue-600 hover:bg-blue-700"},{name:"Enter Test Results",description:"Add new test scores",href:"/dashboard/results",icon:n.A,color:"bg-green-600 hover:bg-green-700"},{name:"View All Results",description:"Browse entered results",href:"/dashboard/results/list",icon:h.A,color:"bg-purple-600 hover:bg-purple-700"},{name:"AI Feedback",description:"Generate AI feedback",href:"/dashboard/feedback",icon:m.A,color:"bg-indigo-600 hover:bg-indigo-700"}];return t?(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Test Checker Dashboard"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Manage IELTS test results and candidate information"})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:p.map(e=>{let s=e.icon;return(0,r.jsx)(l(),{href:e.href,className:"bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow",children:(0,r.jsx)("div",{className:"p-5",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:`${e.color} p-3 rounded-md`,children:(0,r.jsx)(s,{className:"h-6 w-6 text-white"})})}),(0,r.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,r.jsxs)("dl",{children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:e.name}),(0,r.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:e.value.toLocaleString()})]})})]})})},e.name)})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-lg font-medium text-gray-900 mb-4",children:"Quick Actions"}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:b.map(e=>{let s=e.icon;return(0,r.jsx)(l(),{href:e.href,className:`${e.color} text-white p-6 rounded-lg shadow hover:shadow-md transition-all`,children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(s,{className:"h-8 w-8 mr-4"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-medium",children:e.name}),(0,r.jsx)("p",{className:"text-sm opacity-90",children:e.description})]})]})},e.name)})})]}),(0,r.jsxs)("div",{className:"bg-white shadow rounded-lg",children:[(0,r.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Recent Test Results"})}),(0,r.jsxs)("div",{className:"p-6",children:[e.recentResults.length>0?(0,r.jsx)("div",{className:"space-y-4",children:e.recentResults.slice(0,5).map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border border-gray-200 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:"completed"===e.status?(0,r.jsx)(o.A,{className:"h-8 w-8 text-green-500"}):"pending"===e.status?(0,r.jsx)(d.A,{className:"h-8 w-8 text-yellow-500"}):(0,r.jsx)(u.A,{className:"h-8 w-8 text-red-500"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"font-medium text-gray-900",children:e.candidate?.fullName}),(0,r.jsxs)("p",{className:"text-sm text-gray-500",children:["Overall Band: ",e.overallBandScore||"Pending"]})]})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)("p",{className:"text-sm text-gray-500",children:new Date(e.createdAt).toLocaleDateString()}),(0,r.jsx)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${"completed"===e.status?"bg-green-100 text-green-800":"pending"===e.status?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"}`,children:e.status})]})]},e.id))}):(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)(x.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-500",children:"No recent results"}),(0,r.jsx)(l(),{href:"/dashboard/results",className:"mt-2 inline-flex items-center text-blue-600 hover:text-blue-700",children:"Enter your first result →"})]}),e.recentResults.length>0&&(0,r.jsx)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:(0,r.jsx)(l(),{href:"/dashboard/results/list",className:"text-blue-600 hover:text-blue-700 text-sm font-medium",children:"View all results →"})})]})]}),(0,r.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-blue-900 mb-4",children:"Test Checker Guidelines"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-800",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium mb-2",children:"Scoring Guidelines:"}),(0,r.jsxs)("ul",{className:"space-y-1",children:[(0,r.jsx)("li",{children:"• Listening: 0-40 raw score → 1-9 band score"}),(0,r.jsx)("li",{children:"• Reading: 0-40 raw score → 1-9 band score"}),(0,r.jsx)("li",{children:"• Writing: Direct band scores (1-9)"}),(0,r.jsx)("li",{children:"• Speaking: Direct band scores (1-9)"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium mb-2",children:"Quality Assurance:"}),(0,r.jsxs)("ul",{className:"space-y-1",children:[(0,r.jsx)("li",{children:"• Double-check all scores before submission"}),(0,r.jsx)("li",{children:"• Ensure candidate details match test papers"}),(0,r.jsx)("li",{children:"• Generate AI feedback for improvement areas"}),(0,r.jsx)("li",{children:"• Mark results as completed when verified"})]})]})]})]})]})}},33405:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\dashboard\\layout.tsx","default")},57154:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\dashboard\\page.tsx","default")},71354:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>d,metadata:()=>n});var r=t(62740),a=t(85041),i=t.n(a),l=t(70452);t(61135);let n={title:"IELTS Certification System",description:"Professional IELTS test result management and certification system"};function d({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsx)("body",{className:i().className,children:(0,r.jsx)(l.SessionProvider,{children:e})})})}},70440:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});var r=t(88077);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},61135:()=>{}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[638,8338,2367],()=>t(43158));module.exports=r})();