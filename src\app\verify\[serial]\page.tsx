'use client';

import { useState, useEffect, useCallback } from 'react';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import {
  ArrowLeft,
  Shield,
  CheckCircle,
  XCircle,
  AlertCircle,
  Calendar,
  MapPin,
  User,
  FileText,
  Award
} from 'lucide-react';

interface VerificationResult {
  valid: boolean;
  certificate?: {
    serial: string;
    resultId: string;
    candidateName: string;
    nationality: string;
    testDate: string;
    testCenter: string;
    overallBandScore: number | null;
    status: string;
    issueDate: string;
  };
  verification?: {
    verified: boolean;
    verifiedAt: string;
    message: string;
  };
  error?: string;
  message?: string;
}

export default function CertificateVerificationPage() {
  const params = useParams();
  const serial = params.serial as string;

  const [verificationResult, setVerificationResult] = useState<VerificationResult | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');

  const verifySerial = useCallback(async () => {
    try {
      const response = await fetch(`/api/certificate/verify/${serial}`);
      const data = await response.json();
      
      if (response.ok) {
        setVerificationResult(data);
      } else {
        setVerificationResult(data);
        setError(data.message || 'Verification failed');
      }
    } catch (error) {
      console.error('Error verifying certificate:', error);
      setError('Failed to verify certificate');
      setVerificationResult({
        valid: false,
        error: 'Network error',
        message: 'Unable to connect to verification service'
      });
    } finally {
      setIsLoading(false);
    }
  }, [serial]);

  useEffect(() => {
    if (serial) {
      verifySerial();
    }
  }, [verifySerial, serial]);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Verifying certificate...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <Link href="/search" className="flex items-center text-blue-600 hover:text-blue-700 mr-4">
                <ArrowLeft className="h-5 w-5 mr-1" />
                Back to Search
              </Link>
              <Shield className="h-8 w-8 text-blue-600 mr-3" />
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Certificate Verification</h1>
                <p className="text-gray-600">Verify IELTS Certificate Authenticity</p>
              </div>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Verification Status */}
        <div className="bg-white rounded-lg shadow-lg p-8 mb-8">
          <div className="text-center">
            <div className="mb-6">
              {verificationResult?.valid ? (
                <CheckCircle className="h-16 w-16 text-green-500 mx-auto" />
              ) : (
                <XCircle className="h-16 w-16 text-red-500 mx-auto" />
              )}
            </div>
            
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              {verificationResult?.valid ? 'Certificate Verified' : 'Verification Failed'}
            </h2>
            
            <div className={`inline-flex items-center px-4 py-2 rounded-full text-sm font-medium ${
              verificationResult?.valid 
                ? 'bg-green-100 text-green-800' 
                : 'bg-red-100 text-red-800'
            }`}>
              {verificationResult?.valid ? (
                <>
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Valid Certificate
                </>
              ) : (
                <>
                  <XCircle className="h-4 w-4 mr-2" />
                  Invalid Certificate
                </>
              )}
            </div>
            
            <p className="mt-4 text-gray-600">
              Serial Number: <span className="font-mono font-semibold">{serial}</span>
            </p>
            
            <p className="mt-2 text-sm text-gray-500">
              {verificationResult?.verification?.message || verificationResult?.message}
            </p>
          </div>
        </div>

        {/* Certificate Details */}
        {verificationResult?.valid && verificationResult.certificate && (
          <div className="bg-white rounded-lg shadow-lg p-8">
            <div className="flex items-center mb-6">
              <Award className="h-6 w-6 text-blue-600 mr-2" />
              <h3 className="text-xl font-semibold text-gray-900">Certificate Details</h3>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Candidate Information */}
              <div className="space-y-4">
                <h4 className="text-lg font-medium text-gray-900 border-b pb-2">Candidate Information</h4>
                
                <div className="flex items-center">
                  <User className="h-5 w-5 text-gray-400 mr-3" />
                  <div>
                    <p className="text-sm text-gray-600">Full Name</p>
                    <p className="font-semibold">{verificationResult.certificate.candidateName}</p>
                  </div>
                </div>
                
                <div className="flex items-center">
                  <MapPin className="h-5 w-5 text-gray-400 mr-3" />
                  <div>
                    <p className="text-sm text-gray-600">Nationality</p>
                    <p className="font-semibold">{verificationResult.certificate.nationality}</p>
                  </div>
                </div>
              </div>

              {/* Test Information */}
              <div className="space-y-4">
                <h4 className="text-lg font-medium text-gray-900 border-b pb-2">Test Information</h4>
                
                <div className="flex items-center">
                  <Calendar className="h-5 w-5 text-gray-400 mr-3" />
                  <div>
                    <p className="text-sm text-gray-600">Test Date</p>
                    <p className="font-semibold">
                      {new Date(verificationResult.certificate.testDate).toLocaleDateString()}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center">
                  <MapPin className="h-5 w-5 text-gray-400 mr-3" />
                  <div>
                    <p className="text-sm text-gray-600">Test Center</p>
                    <p className="font-semibold">{verificationResult.certificate.testCenter}</p>
                  </div>
                </div>
                
                <div className="flex items-center">
                  <FileText className="h-5 w-5 text-gray-400 mr-3" />
                  <div>
                    <p className="text-sm text-gray-600">Overall Band Score</p>
                    <p className="text-2xl font-bold text-indigo-600">
                      {verificationResult.certificate.overallBandScore || 'N/A'}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Certificate Metadata */}
            <div className="mt-8 pt-6 border-t border-gray-200">
              <h4 className="text-lg font-medium text-gray-900 mb-4">Certificate Metadata</h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div>
                  <p className="text-gray-600">Certificate Serial</p>
                  <p className="font-mono font-semibold">{verificationResult.certificate.serial}</p>
                </div>
                <div>
                  <p className="text-gray-600">Result ID</p>
                  <p className="font-mono font-semibold">{verificationResult.certificate.resultId}</p>
                </div>
                <div>
                  <p className="text-gray-600">Issue Date</p>
                  <p className="font-semibold">
                    {new Date(verificationResult.certificate.issueDate).toLocaleDateString()}
                  </p>
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="mt-8 flex justify-center space-x-4">
              <Link
                href={`/results/${verificationResult.certificate.resultId}`}
                className="inline-flex items-center px-4 py-2 border border-blue-300 text-sm font-medium rounded-md text-blue-700 bg-blue-50 hover:bg-blue-100"
              >
                <FileText className="h-4 w-4 mr-2" />
                View Full Results
              </Link>
            </div>
          </div>
        )}

        {/* Error Details */}
        {!verificationResult?.valid && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-6">
            <div className="flex items-center mb-4">
              <AlertCircle className="h-6 w-6 text-red-600 mr-2" />
              <h3 className="text-lg font-semibold text-red-800">Verification Failed</h3>
            </div>
            <p className="text-red-700 mb-4">
              {verificationResult?.message || 'The certificate could not be verified.'}
            </p>
            <div className="text-sm text-red-600">
              <p>Possible reasons:</p>
              <ul className="list-disc list-inside mt-2 space-y-1">
                <li>Invalid or incorrect serial number</li>
                <li>Certificate has not been generated</li>
                <li>Test result is still pending</li>
                <li>Certificate has been revoked</li>
              </ul>
            </div>
          </div>
        )}

        {/* Footer */}
        <div className="mt-12 text-center">
          <p className="text-gray-600 text-sm">
            For additional verification or support, please contact the IELTS administration office.
          </p>
          <div className="mt-4">
            <Link
              href="/search"
              className="text-blue-600 hover:text-blue-700 text-sm font-medium"
            >
              Search for Results
            </Link>
          </div>
        </div>
      </main>
    </div>
  );
}
