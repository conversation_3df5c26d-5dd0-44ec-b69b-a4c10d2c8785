(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4824],{7761:(e,r,a)=>{Promise.resolve().then(a.bind(a,7047))},7401:(e,r,a)=>{"use strict";a.d(r,{A:()=>m});var s=a(2115);let t=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),n=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,r,a)=>a?a.toUpperCase():r.toLowerCase()),l=e=>{let r=n(e);return r.charAt(0).toUpperCase()+r.slice(1)},d=function(){for(var e=arguments.length,r=Array(e),a=0;a<e;a++)r[a]=arguments[a];return r.filter((e,r,a)=>!!e&&""!==e.trim()&&a.indexOf(e)===r).join(" ").trim()},i=e=>{for(let r in e)if(r.startsWith("aria-")||"role"===r||"title"===r)return!0};var o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,s.forwardRef)((e,r)=>{let{color:a="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:l,className:c="",children:m,iconNode:u,...x}=e;return(0,s.createElement)("svg",{ref:r,...o,width:t,height:t,stroke:a,strokeWidth:l?24*Number(n)/Number(t):n,className:d("lucide",c),...!m&&!i(x)&&{"aria-hidden":"true"},...x},[...u.map(e=>{let[r,a]=e;return(0,s.createElement)(r,a)}),...Array.isArray(m)?m:[m]])}),m=(e,r)=>{let a=(0,s.forwardRef)((a,n)=>{let{className:i,...o}=a;return(0,s.createElement)(c,{ref:n,iconNode:r,className:d("lucide-".concat(t(l(e))),"lucide-".concat(e),i),...o})});return a.displayName=l(e),a}},7364:(e,r,a)=>{"use strict";a.d(r,{A:()=>s});let s=(0,a(7401).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},3467:(e,r,a)=>{"use strict";a.d(r,{A:()=>s});let s=(0,a(7401).A)("book-open",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},8453:(e,r,a)=>{"use strict";a.d(r,{A:()=>s});let s=(0,a(7401).A)("calculator",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",key:"1nb95v"}],["line",{x1:"8",x2:"16",y1:"6",y2:"6",key:"x4nwl0"}],["line",{x1:"16",x2:"16",y1:"14",y2:"18",key:"wjye3r"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M8 14h.01",key:"6423bh"}],["path",{d:"M12 18h.01",key:"mhygvu"}],["path",{d:"M8 18h.01",key:"lrp35t"}]])},8178:(e,r,a)=>{"use strict";a.d(r,{A:()=>s});let s=(0,a(7401).A)("headphones",[["path",{d:"M3 14h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-7a9 9 0 0 1 18 0v7a2 2 0 0 1-2 2h-1a2 2 0 0 1-2-2v-3a2 2 0 0 1 2-2h3",key:"1xhozi"}]])},6164:(e,r,a)=>{"use strict";a.d(r,{A:()=>s});let s=(0,a(7401).A)("mic",[["path",{d:"M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z",key:"131961"}],["path",{d:"M19 10v2a7 7 0 0 1-14 0v-2",key:"1vc78b"}],["line",{x1:"12",x2:"12",y1:"19",y2:"22",key:"x3vr5v"}]])},6005:(e,r,a)=>{"use strict";a.d(r,{A:()=>s});let s=(0,a(7401).A)("pen-tool",[["path",{d:"M15.707 21.293a1 1 0 0 1-1.414 0l-1.586-1.586a1 1 0 0 1 0-1.414l5.586-5.586a1 1 0 0 1 1.414 0l1.586 1.586a1 1 0 0 1 0 1.414z",key:"nt11vn"}],["path",{d:"m18 13-1.375-6.874a1 1 0 0 0-.746-.776L3.235 2.028a1 1 0 0 0-1.207 1.207L5.35 15.879a1 1 0 0 0 .776.746L13 18",key:"15qc1e"}],["path",{d:"m2.3 2.3 7.286 7.286",key:"1wuzzi"}],["circle",{cx:"11",cy:"11",r:"2",key:"xmgehs"}]])},7780:(e,r,a)=>{"use strict";a.d(r,{A:()=>s});let s=(0,a(7401).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},853:(e,r,a)=>{"use strict";a.d(r,{A:()=>s});let s=(0,a(7401).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},1466:(e,r,a)=>{"use strict";a.d(r,{A:()=>s});let s=(0,a(7401).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},6046:(e,r,a)=>{"use strict";var s=a(6658);a.o(s,"useParams")&&a.d(r,{useParams:function(){return s.useParams}}),a.o(s,"useRouter")&&a.d(r,{useRouter:function(){return s.useRouter}}),a.o(s,"useSearchParams")&&a.d(r,{useSearchParams:function(){return s.useSearchParams}})},7047:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>p});var s=a(5155),t=a(2115),n=a(6046),l=a(8173),d=a.n(l),i=a(7364),o=a(1466),c=a(853),m=a(8178),u=a(3467),x=a(6005),h=a(6164),g=a(8453),b=a(7780);function p(){let e=(0,n.useRouter)(),r=(0,n.useSearchParams)().get("candidateId"),[a,l]=(0,t.useState)(null),[p,y]=(0,t.useState)(!1),[f,j]=(0,t.useState)(""),[v,k]=(0,t.useState)({candidateId:r||"",listeningScore:"",listeningBandScore:"",readingScore:"",readingBandScore:"",writingTask1Score:"",writingTask2Score:"",writingBandScore:"",speakingFluencyScore:"",speakingLexicalScore:"",speakingGrammarScore:"",speakingPronunciationScore:"",speakingBandScore:"",overallBandScore:""});(0,t.useEffect)(()=>{r&&w(r)},[r]);let w=async e=>{try{let r=await fetch("/api/checker/candidates/".concat(e));if(r.ok){let a=await r.json();l(a),k(r=>({...r,candidateId:e}))}}catch(e){console.error("Error fetching candidate:",e)}},S=e=>{let{name:r,value:a}=e.target;k(e=>({...e,[r]:a}))},N=(0,t.useCallback)(()=>{let{listeningBandScore:e,readingBandScore:r,writingBandScore:a,speakingBandScore:s}=v;if(e&&r&&a&&s){let t=Math.round(2*((parseFloat(e)+parseFloat(r)+parseFloat(a)+parseFloat(s))/4))/2;k(e=>({...e,overallBandScore:t.toString()}))}},[v]);(0,t.useEffect)(()=>{N()},[N]);let A=async r=>{r.preventDefault(),y(!0),j("");try{let r=await fetch("/api/checker/results",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(v)});if(r.ok){let a=await r.json();e.push("/dashboard/results/".concat(a.id))}else{let e=await r.json();j(e.error||"Failed to save results")}}catch(e){j("An error occurred. Please try again.")}finally{y(!1)}};return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)("div",{className:"flex items-center justify-between",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(d(),{href:"/dashboard/search",className:"mr-4 p-2 text-gray-400 hover:text-gray-600",children:(0,s.jsx)(i.A,{className:"h-5 w-5"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Enter Test Results"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Record IELTS test scores for a candidate"})]})]})}),a?(0,s.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(o.A,{className:"h-5 w-5 text-blue-600 mr-2"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-medium text-blue-900",children:a.fullName}),(0,s.jsxs)("p",{className:"text-sm text-blue-700",children:[a.passportNumber," • Test Date: ",new Date(a.testDate).toLocaleDateString()]})]})]})}):r?(0,s.jsx)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4",children:(0,s.jsx)("div",{className:"animate-pulse",children:"Loading candidate information..."})}):(0,s.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(c.A,{className:"h-5 w-5 text-yellow-600 mr-2"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-medium text-yellow-900",children:"No candidate selected"}),(0,s.jsx)("p",{className:"text-sm text-yellow-700",children:"Please search for a candidate first or enter their ID below."})]})]})}),(0,s.jsx)("div",{className:"bg-white shadow rounded-lg",children:(0,s.jsxs)("form",{onSubmit:A,className:"p-6 space-y-8",children:[f&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm",children:f}),!r&&(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"candidateId",className:"block text-sm font-medium text-gray-700 mb-2",children:"Candidate ID *"}),(0,s.jsx)("input",{type:"text",id:"candidateId",name:"candidateId",value:v.candidateId,onChange:S,required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter candidate ID"})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("h3",{className:"text-lg font-medium text-gray-900 mb-4 flex items-center",children:[(0,s.jsx)(m.A,{className:"h-5 w-5 mr-2 text-blue-600"}),"Listening"]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"listeningScore",className:"block text-sm font-medium text-gray-700 mb-2",children:"Raw Score (0-40)"}),(0,s.jsx)("input",{type:"number",id:"listeningScore",name:"listeningScore",value:v.listeningScore,onChange:S,min:"0",max:"40",step:"1",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"listeningBandScore",className:"block text-sm font-medium text-gray-700 mb-2",children:"Band Score (1-9)"}),(0,s.jsx)("input",{type:"number",id:"listeningBandScore",name:"listeningBandScore",value:v.listeningBandScore,onChange:S,min:"1",max:"9",step:"0.5",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("h3",{className:"text-lg font-medium text-gray-900 mb-4 flex items-center",children:[(0,s.jsx)(u.A,{className:"h-5 w-5 mr-2 text-green-600"}),"Reading"]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"readingScore",className:"block text-sm font-medium text-gray-700 mb-2",children:"Raw Score (0-40)"}),(0,s.jsx)("input",{type:"number",id:"readingScore",name:"readingScore",value:v.readingScore,onChange:S,min:"0",max:"40",step:"1",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"readingBandScore",className:"block text-sm font-medium text-gray-700 mb-2",children:"Band Score (1-9)"}),(0,s.jsx)("input",{type:"number",id:"readingBandScore",name:"readingBandScore",value:v.readingBandScore,onChange:S,min:"1",max:"9",step:"0.5",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("h3",{className:"text-lg font-medium text-gray-900 mb-4 flex items-center",children:[(0,s.jsx)(x.A,{className:"h-5 w-5 mr-2 text-purple-600"}),"Writing"]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"writingTask1Score",className:"block text-sm font-medium text-gray-700 mb-2",children:"Task 1 Score (1-9)"}),(0,s.jsx)("input",{type:"number",id:"writingTask1Score",name:"writingTask1Score",value:v.writingTask1Score,onChange:S,min:"1",max:"9",step:"0.5",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"writingTask2Score",className:"block text-sm font-medium text-gray-700 mb-2",children:"Task 2 Score (1-9)"}),(0,s.jsx)("input",{type:"number",id:"writingTask2Score",name:"writingTask2Score",value:v.writingTask2Score,onChange:S,min:"1",max:"9",step:"0.5",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"writingBandScore",className:"block text-sm font-medium text-gray-700 mb-2",children:"Overall Band Score (1-9)"}),(0,s.jsx)("input",{type:"number",id:"writingBandScore",name:"writingBandScore",value:v.writingBandScore,onChange:S,min:"1",max:"9",step:"0.5",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("h3",{className:"text-lg font-medium text-gray-900 mb-4 flex items-center",children:[(0,s.jsx)(h.A,{className:"h-5 w-5 mr-2 text-red-600"}),"Speaking"]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"speakingFluencyScore",className:"block text-sm font-medium text-gray-700 mb-2",children:"Fluency & Coherence"}),(0,s.jsx)("input",{type:"number",id:"speakingFluencyScore",name:"speakingFluencyScore",value:v.speakingFluencyScore,onChange:S,min:"1",max:"9",step:"0.5",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"speakingLexicalScore",className:"block text-sm font-medium text-gray-700 mb-2",children:"Lexical Resource"}),(0,s.jsx)("input",{type:"number",id:"speakingLexicalScore",name:"speakingLexicalScore",value:v.speakingLexicalScore,onChange:S,min:"1",max:"9",step:"0.5",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"speakingGrammarScore",className:"block text-sm font-medium text-gray-700 mb-2",children:"Grammar & Accuracy"}),(0,s.jsx)("input",{type:"number",id:"speakingGrammarScore",name:"speakingGrammarScore",value:v.speakingGrammarScore,onChange:S,min:"1",max:"9",step:"0.5",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"speakingPronunciationScore",className:"block text-sm font-medium text-gray-700 mb-2",children:"Pronunciation"}),(0,s.jsx)("input",{type:"number",id:"speakingPronunciationScore",name:"speakingPronunciationScore",value:v.speakingPronunciationScore,onChange:S,min:"1",max:"9",step:"0.5",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"speakingBandScore",className:"block text-sm font-medium text-gray-700 mb-2",children:"Overall Band Score"}),(0,s.jsx)("input",{type:"number",id:"speakingBandScore",name:"speakingBandScore",value:v.speakingBandScore,onChange:S,min:"1",max:"9",step:"0.5",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]})]})]}),(0,s.jsxs)("div",{className:"bg-gray-50 p-6 rounded-lg",children:[(0,s.jsxs)("h3",{className:"text-lg font-medium text-gray-900 mb-4 flex items-center",children:[(0,s.jsx)(g.A,{className:"h-5 w-5 mr-2 text-indigo-600"}),"Overall Band Score"]}),(0,s.jsxs)("div",{className:"max-w-xs",children:[(0,s.jsx)("label",{htmlFor:"overallBandScore",className:"block text-sm font-medium text-gray-700 mb-2",children:"Calculated Overall Score"}),(0,s.jsx)("input",{type:"number",id:"overallBandScore",name:"overallBandScore",value:v.overallBandScore,onChange:S,min:"1",max:"9",step:"0.5",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white",readOnly:!0}),(0,s.jsx)("p",{className:"mt-1 text-xs text-gray-500",children:"Automatically calculated from individual band scores"})]})]}),(0,s.jsxs)("div",{className:"flex justify-end space-x-4 pt-6 border-t border-gray-200",children:[(0,s.jsx)(d(),{href:"/dashboard/search",className:"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50",children:"Cancel"}),(0,s.jsx)("button",{type:"submit",disabled:p||!v.candidateId,className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50",children:p?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Saving..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(b.A,{className:"h-4 w-4 mr-2"}),"Save Results"]})})]})]})})]})}}},e=>{var r=r=>e(e.s=r);e.O(0,[8173,8441,1517,7358],()=>r(7761)),_N_E=e.O()}]);