exports.id=9757,exports.ids=[9757],exports.modules={69842:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.toBig=t.shrSL=t.shrSH=t.rotrSL=t.rotrSH=t.rotrBL=t.rotrBH=t.rotr32L=t.rotr32H=t.rotlSL=t.rotlSH=t.rotlBL=t.rotlBH=t.add5L=t.add5H=t.add4L=t.add4H=t.add3L=t.add3H=void 0,t.add=w,t.fromBig=r,t.split=n;let i=BigInt(0x100000000-1),s=BigInt(32);function r(e,t=!1){return t?{h:Number(e&i),l:Number(e>>s&i)}:{h:0|Number(e>>s&i),l:0|Number(e&i)}}function n(e,t=!1){let i=e.length,s=new Uint32Array(i),l=new Uint32Array(i);for(let n=0;n<i;n++){let{h:i,l:o}=r(e[n],t);[s[n],l[n]]=[i,o]}return[s,l]}let l=(e,t)=>BigInt(e>>>0)<<s|BigInt(t>>>0);t.toBig=l;let o=(e,t,i)=>e>>>i;t.shrSH=o;let a=(e,t,i)=>e<<32-i|t>>>i;t.shrSL=a;let u=(e,t,i)=>e>>>i|t<<32-i;t.rotrSH=u;let c=(e,t,i)=>e<<32-i|t>>>i;t.rotrSL=c;let h=(e,t,i)=>e<<64-i|t>>>i-32;t.rotrBH=h;let d=(e,t,i)=>e>>>i-32|t<<64-i;t.rotrBL=d;let f=(e,t)=>t;t.rotr32H=f;let g=(e,t)=>e;t.rotr32L=g;let p=(e,t,i)=>e<<i|t>>>32-i;t.rotlSH=p;let m=(e,t,i)=>t<<i|e>>>32-i;t.rotlSL=m;let y=(e,t,i)=>t<<i-32|e>>>64-i;t.rotlBH=y;let b=(e,t,i)=>e<<i-32|t>>>64-i;function w(e,t,i,s){let r=(t>>>0)+(s>>>0);return{h:e+i+(r/0x100000000|0)|0,l:0|r}}t.rotlBL=b;let S=(e,t,i)=>(e>>>0)+(t>>>0)+(i>>>0);t.add3L=S;let v=(e,t,i,s)=>t+i+s+(e/0x100000000|0)|0;t.add3H=v;let T=(e,t,i,s)=>(e>>>0)+(t>>>0)+(i>>>0)+(s>>>0);t.add4L=T;let $=(e,t,i,s,r)=>t+i+s+r+(e/0x100000000|0)|0;t.add4H=$;let x=(e,t,i,s,r)=>(e>>>0)+(t>>>0)+(i>>>0)+(s>>>0)+(r>>>0);t.add5L=x;let P=(e,t,i,s,r,n)=>t+i+s+r+n+(e/0x100000000|0)|0;t.add5H=P,t.default={fromBig:r,split:n,toBig:l,shrSH:o,shrSL:a,rotrSH:u,rotrSL:c,rotrBH:h,rotrBL:d,rotr32H:f,rotr32L:g,rotlSH:p,rotlSL:m,rotlBH:y,rotlBL:b,add:w,add3L:S,add3H:v,add4L:T,add4H:$,add5H:P,add5L:x}},91013:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.crypto=void 0;let s=i(77598);t.crypto=s&&"object"==typeof s&&"webcrypto"in s?s.webcrypto:s&&"object"==typeof s&&"randomBytes"in s?s:void 0},94879:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.shake256=t.shake128=t.keccak_512=t.keccak_384=t.keccak_256=t.keccak_224=t.sha3_512=t.sha3_384=t.sha3_256=t.sha3_224=t.Keccak=void 0,t.keccakP=w;let s=i(69842),r=i(11731),n=BigInt(0),l=BigInt(1),o=BigInt(2),a=BigInt(7),u=BigInt(256),c=BigInt(113),h=[],d=[],f=[];for(let e=0,t=l,i=1,s=0;e<24;e++){[i,s]=[s,(2*i+3*s)%5],h.push(2*(5*s+i)),d.push((e+1)*(e+2)/2%64);let r=n;for(let e=0;e<7;e++)(t=(t<<l^(t>>a)*c)%u)&o&&(r^=l<<(l<<BigInt(e))-l);f.push(r)}let g=(0,s.split)(f,!0),p=g[0],m=g[1],y=(e,t,i)=>i>32?(0,s.rotlBH)(e,t,i):(0,s.rotlSH)(e,t,i),b=(e,t,i)=>i>32?(0,s.rotlBL)(e,t,i):(0,s.rotlSL)(e,t,i);function w(e,t=24){let i=new Uint32Array(10);for(let s=24-t;s<24;s++){for(let t=0;t<10;t++)i[t]=e[t]^e[t+10]^e[t+20]^e[t+30]^e[t+40];for(let t=0;t<10;t+=2){let s=(t+8)%10,r=(t+2)%10,n=i[r],l=i[r+1],o=y(n,l,1)^i[s],a=b(n,l,1)^i[s+1];for(let i=0;i<50;i+=10)e[t+i]^=o,e[t+i+1]^=a}let t=e[2],r=e[3];for(let i=0;i<24;i++){let s=d[i],n=y(t,r,s),l=b(t,r,s),o=h[i];t=e[o],r=e[o+1],e[o]=n,e[o+1]=l}for(let t=0;t<50;t+=10){for(let s=0;s<10;s++)i[s]=e[t+s];for(let s=0;s<10;s++)e[t+s]^=~i[(s+2)%10]&i[(s+4)%10]}e[0]^=p[s],e[1]^=m[s]}(0,r.clean)(i)}class S extends r.Hash{constructor(e,t,i,s=!1,n=24){if(super(),this.pos=0,this.posOut=0,this.finished=!1,this.destroyed=!1,this.enableXOF=!1,this.blockLen=e,this.suffix=t,this.outputLen=i,this.enableXOF=s,this.rounds=n,(0,r.anumber)(i),!(0<e&&e<200))throw Error("only keccak-f1600 function is supported");this.state=new Uint8Array(200),this.state32=(0,r.u32)(this.state)}clone(){return this._cloneInto()}keccak(){(0,r.swap32IfBE)(this.state32),w(this.state32,this.rounds),(0,r.swap32IfBE)(this.state32),this.posOut=0,this.pos=0}update(e){(0,r.aexists)(this),e=(0,r.toBytes)(e),(0,r.abytes)(e);let{blockLen:t,state:i}=this,s=e.length;for(let r=0;r<s;){let n=Math.min(t-this.pos,s-r);for(let t=0;t<n;t++)i[this.pos++]^=e[r++];this.pos===t&&this.keccak()}return this}finish(){if(this.finished)return;this.finished=!0;let{state:e,suffix:t,pos:i,blockLen:s}=this;e[i]^=t,(128&t)!=0&&i===s-1&&this.keccak(),e[s-1]^=128,this.keccak()}writeInto(e){(0,r.aexists)(this,!1),(0,r.abytes)(e),this.finish();let t=this.state,{blockLen:i}=this;for(let s=0,r=e.length;s<r;){this.posOut>=i&&this.keccak();let n=Math.min(i-this.posOut,r-s);e.set(t.subarray(this.posOut,this.posOut+n),s),this.posOut+=n,s+=n}return e}xofInto(e){if(!this.enableXOF)throw Error("XOF is not possible for this instance");return this.writeInto(e)}xof(e){return(0,r.anumber)(e),this.xofInto(new Uint8Array(e))}digestInto(e){if((0,r.aoutput)(e,this),this.finished)throw Error("digest() was already called");return this.writeInto(e),this.destroy(),e}digest(){return this.digestInto(new Uint8Array(this.outputLen))}destroy(){this.destroyed=!0,(0,r.clean)(this.state)}_cloneInto(e){let{blockLen:t,suffix:i,outputLen:s,rounds:r,enableXOF:n}=this;return e||(e=new S(t,i,s,n,r)),e.state32.set(this.state32),e.pos=this.pos,e.posOut=this.posOut,e.finished=this.finished,e.rounds=r,e.suffix=i,e.outputLen=s,e.enableXOF=n,e.destroyed=this.destroyed,e}}t.Keccak=S;let v=(e,t,i)=>(0,r.createHasher)(()=>new S(t,e,i));t.sha3_224=v(6,144,28),t.sha3_256=v(6,136,32),t.sha3_384=v(6,104,48),t.sha3_512=v(6,72,64),t.keccak_224=v(1,144,28),t.keccak_256=v(1,136,32),t.keccak_384=v(1,104,48),t.keccak_512=v(1,72,64);let T=(e,t,i)=>(0,r.createXOFer)((s={})=>new S(t,e,void 0===s.dkLen?i:s.dkLen,!0));t.shake128=T(31,168,16),t.shake256=T(31,136,32)},11731:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.wrapXOFConstructorWithOpts=t.wrapConstructorWithOpts=t.wrapConstructor=t.Hash=t.nextTick=t.swap32IfBE=t.byteSwapIfBE=t.swap8IfBE=t.isLE=void 0,t.isBytes=r,t.anumber=n,t.abytes=l,t.ahash=function(e){if("function"!=typeof e||"function"!=typeof e.create)throw Error("Hash should be wrapped by utils.createHasher");n(e.outputLen),n(e.blockLen)},t.aexists=function(e,t=!0){if(e.destroyed)throw Error("Hash instance has been destroyed");if(t&&e.finished)throw Error("Hash#digest() has already been called")},t.aoutput=function(e,t){l(e);let i=t.outputLen;if(e.length<i)throw Error("digestInto() expects output buffer of length at least "+i)},t.u8=function(e){return new Uint8Array(e.buffer,e.byteOffset,e.byteLength)},t.u32=function(e){return new Uint32Array(e.buffer,e.byteOffset,Math.floor(e.byteLength/4))},t.clean=function(...e){for(let t=0;t<e.length;t++)e[t].fill(0)},t.createView=function(e){return new DataView(e.buffer,e.byteOffset,e.byteLength)},t.rotr=function(e,t){return e<<32-t|e>>>t},t.rotl=function(e,t){return e<<t|e>>>32-t>>>0},t.byteSwap=o,t.byteSwap32=a,t.bytesToHex=function(e){if(l(e),u)return e.toHex();let t="";for(let i=0;i<e.length;i++)t+=c[e[i]];return t},t.hexToBytes=function(e){if("string"!=typeof e)throw Error("hex string expected, got "+typeof e);if(u)return Uint8Array.fromHex(e);let t=e.length,i=t/2;if(t%2)throw Error("hex string expected, got unpadded hex of length "+t);let s=new Uint8Array(i);for(let t=0,r=0;t<i;t++,r+=2){let i=d(e.charCodeAt(r)),n=d(e.charCodeAt(r+1));if(void 0===i||void 0===n)throw Error('hex string expected, got non-hex character "'+(e[r]+e[r+1])+'" at index '+r);s[t]=16*i+n}return s},t.asyncLoop=g,t.utf8ToBytes=p,t.bytesToUtf8=function(e){return new TextDecoder().decode(e)},t.toBytes=m,t.kdfInputToBytes=function(e){return"string"==typeof e&&(e=p(e)),l(e),e},t.concatBytes=function(...e){let t=0;for(let i=0;i<e.length;i++){let s=e[i];l(s),t+=s.length}let i=new Uint8Array(t);for(let t=0,s=0;t<e.length;t++){let r=e[t];i.set(r,s),s+=r.length}return i},t.checkOpts=function(e,t){if(void 0!==t&&"[object Object]"!==({}).toString.call(t))throw Error("options should be object or undefined");return Object.assign(e,t)},t.createHasher=b,t.createOptHasher=w,t.createXOFer=S,t.randomBytes=function(e=32){if(s.crypto&&"function"==typeof s.crypto.getRandomValues)return s.crypto.getRandomValues(new Uint8Array(e));if(s.crypto&&"function"==typeof s.crypto.randomBytes)return Uint8Array.from(s.crypto.randomBytes(e));throw Error("crypto.getRandomValues must be defined")};let s=i(91013);function r(e){return e instanceof Uint8Array||ArrayBuffer.isView(e)&&"Uint8Array"===e.constructor.name}function n(e){if(!Number.isSafeInteger(e)||e<0)throw Error("positive integer expected, got "+e)}function l(e,...t){if(!r(e))throw Error("Uint8Array expected");if(t.length>0&&!t.includes(e.length))throw Error("Uint8Array expected of length "+t+", got length="+e.length)}function o(e){return e<<24&0xff000000|e<<8&0xff0000|e>>>8&65280|e>>>24&255}function a(e){for(let t=0;t<e.length;t++)e[t]=o(e[t]);return e}t.isLE=68===new Uint8Array(new Uint32Array([0x11223344]).buffer)[0],t.swap8IfBE=t.isLE?e=>e:e=>o(e),t.byteSwapIfBE=t.swap8IfBE,t.swap32IfBE=t.isLE?e=>e:a;let u="function"==typeof Uint8Array.from([]).toHex&&"function"==typeof Uint8Array.fromHex,c=Array.from({length:256},(e,t)=>t.toString(16).padStart(2,"0")),h={_0:48,_9:57,A:65,F:70,a:97,f:102};function d(e){return e>=h._0&&e<=h._9?e-h._0:e>=h.A&&e<=h.F?e-(h.A-10):e>=h.a&&e<=h.f?e-(h.a-10):void 0}let f=async()=>{};async function g(e,i,s){let r=Date.now();for(let n=0;n<e;n++){s(n);let e=Date.now()-r;e>=0&&e<i||(await (0,t.nextTick)(),r+=e)}}function p(e){if("string"!=typeof e)throw Error("string expected");return new Uint8Array(new TextEncoder().encode(e))}function m(e){return"string"==typeof e&&(e=p(e)),l(e),e}t.nextTick=f;class y{}function b(e){let t=t=>e().update(m(t)).digest(),i=e();return t.outputLen=i.outputLen,t.blockLen=i.blockLen,t.create=()=>e(),t}function w(e){let t=(t,i)=>e(i).update(m(t)).digest(),i=e({});return t.outputLen=i.outputLen,t.blockLen=i.blockLen,t.create=t=>e(t),t}function S(e){let t=(t,i)=>e(i).update(m(t)).digest(),i=e({});return t.outputLen=i.outputLen,t.blockLen=i.blockLen,t.create=t=>e(t),t}t.Hash=y,t.wrapConstructor=b,t.wrapConstructorWithOpts=w,t.wrapXOFConstructorWithOpts=S},4502:(e,t,i)=>{let{createId:s,init:r,getConstants:n,isCuid:l}=i(67511);e.exports.sX=s},67511:(e,t,i)=>{let{sha3_512:s}=i(94879),r=24,n=32,l=(e=4,t=Math.random)=>{let i="";for(;i.length<e;)i+=Math.floor(36*t()).toString(36);return i};function o(e){let t=0n;for(let i of e.values())t=(t<<8n)+BigInt(i);return t}let a=(e="")=>o(s(e)).toString(36).slice(1),u=Array.from({length:26},(e,t)=>String.fromCharCode(t+97)),c=e=>u[Math.floor(e()*u.length)],h=({globalObj:e="undefined"!=typeof global?global:"undefined"!=typeof window?window:{},random:t=Math.random}={})=>{let i=Object.keys(e).toString();return a(i.length?i+l(n,t):l(n,t)).substring(0,n)},d=e=>()=>e++,f=0x1c6b1f1f,g=({random:e=Math.random,counter:t=d(Math.floor(e()*f)),length:i=r,fingerprint:s=h({random:e})}={})=>function(){let r=c(e),n=Date.now().toString(36),o=t().toString(36),u=l(i,e),h=`${n+u+o+s}`;return`${r+a(h).substring(1,i)}`},p=g();e.exports.getConstants=()=>({defaultLength:r,bigLength:n}),e.exports.init=g,e.exports.createId=p,e.exports.bufToBigInt=o,e.exports.createCounter=d,e.exports.createFingerprint=h,e.exports.isCuid=(e,{minLength:t=2,maxLength:i=n}={})=>{let s=e.length;return!!("string"==typeof e&&s>=t&&s<=i&&/^[0-9a-z]+$/.test(e))}},89490:(e,t,i)=>{"use strict";i.d(t,{Hs:()=>f,Ht:()=>a,h_:()=>u,oG:()=>h,ug:()=>d,yY:()=>g});var s=i(7078),r=i(77799),n=i(91990),l=i(65258),o=i(18611);class a{constructor(e){this.table=e}static [r.i]="ColumnAliasProxyHandler";get(e,t){return"table"===t?this.table:e[t]}}class u{constructor(e,t){this.alias=e,this.replaceOriginalName=t}static [r.i]="TableAliasProxyHandler";get(e,t){if(t===l.XI.Symbol.IsAlias)return!0;if(t===l.XI.Symbol.Name||this.replaceOriginalName&&t===l.XI.Symbol.OriginalName)return this.alias;if(t===o.n)return{...e[o.n],name:this.alias,isAlias:!0};if(t===l.XI.Symbol.Columns){let t=e[l.XI.Symbol.Columns];if(!t)return t;let i={};return Object.keys(t).map(s=>{i[s]=new Proxy(t[s],new a(new Proxy(e,this)))}),i}let i=e[t];return(0,r.is)(i,s.V)?new Proxy(i,new a(new Proxy(e,this))):i}}class c{constructor(e){this.alias=e}static [r.i]=null;get(e,t){return"sourceTable"===t?h(e.sourceTable,this.alias):e[t]}}function h(e,t){return new Proxy(e,new u(t,!1))}function d(e,t){return new Proxy(e,new a(new Proxy(e.table,new u(t,!1))))}function f(e,t){return new n.Xs.Aliased(g(e.sql,t),e.fieldAlias)}function g(e,t){return n.ll.join(e.queryChunks.map(e=>(0,r.is)(e,s.V)?d(e,t):(0,r.is)(e,n.Xs)?g(e,t):(0,r.is)(e,n.Xs.Aliased)?f(e,t):e))}},7078:(e,t,i)=>{"use strict";i.d(t,{V:()=>r});var s=i(77799);class r{constructor(e,t){this.table=e,this.config=t,this.name=t.name,this.keyAsName=t.keyAsName,this.notNull=t.notNull,this.default=t.default,this.defaultFn=t.defaultFn,this.onUpdateFn=t.onUpdateFn,this.hasDefault=t.hasDefault,this.primary=t.primaryKey,this.isUnique=t.isUnique,this.uniqueName=t.uniqueName,this.uniqueType=t.uniqueType,this.dataType=t.dataType,this.columnType=t.columnType,this.generated=t.generated,this.generatedIdentity=t.generatedIdentity}static [s.i]="Column";name;keyAsName;primary;notNull;default;defaultFn;onUpdateFn;hasDefault;isUnique;uniqueName;uniqueType;dataType;columnType;enumValues=void 0;generated=void 0;generatedIdentity=void 0;config;mapFromDriverValue(e){return e}mapToDriverValue(e){return e}shouldDisableInsert(){return void 0!==this.config.generated&&"byDefault"!==this.config.generated.type}}},77799:(e,t,i)=>{"use strict";i.d(t,{i:()=>s,is:()=>r});let s=Symbol.for("drizzle:entityKind");function r(e,t){if(!e||"object"!=typeof e)return!1;if(e instanceof t)return!0;if(!Object.prototype.hasOwnProperty.call(t,s))throw Error(`Class "${t.name??"<unknown>"}" doesn't look like a Drizzle entity. If this is incorrect and the class is provided by Drizzle, please report this as a bug.`);let i=Object.getPrototypeOf(e).constructor;if(i)for(;i;){if(s in i&&i[s]===t[s])return!0;i=Object.getPrototypeOf(i)}return!1}Symbol.for("drizzle:hasOwnEntityKind")},28611:(e,t,i)=>{"use strict";i.d(t,{j:()=>n,n:()=>r});var s=i(77799);class r extends Error{static [s.i]="DrizzleError";constructor({message:e,cause:t}){super(e),this.name="DrizzleError",this.cause=t}}class n extends r{static [s.i]="TransactionRollbackError";constructor(){super({message:"Rollback"})}}},18992:(e,t,i)=>{"use strict";i.d(t,{Pv:()=>l,w:()=>n});var s=i(77799);class r{static [s.i]="ConsoleLogWriter";write(e){console.log(e)}}class n{static [s.i]="DefaultLogger";writer;constructor(e){this.writer=e?.writer??new r}logQuery(e,t){let i=t.map(e=>{try{return JSON.stringify(e)}catch{return String(e)}}),s=i.length?` -- params: [${i.join(", ")}]`:"";this.writer.write(`Query: ${e}${s}`)}}class l{static [s.i]="NoopLogger";logQuery(){}}},27390:(e,t,i)=>{"use strict";i.d(t,{zM:()=>o});var s=i(77799),r=i(98199);class n extends r.pe{static [s.i]="PgBooleanBuilder";constructor(e){super(e,"boolean","PgBoolean")}build(e){return new l(e,this.config)}}class l extends r.Kl{static [s.i]="PgBoolean";getSQLType(){return"boolean"}}function o(e){return new n(e??"")}},98199:(e,t,i)=>{"use strict";i.d(t,{Kl:()=>m,pe:()=>p});var s=i(77799);class r{static [s.i]="ColumnBuilder";config;constructor(e,t,i){this.config={name:e,keyAsName:""===e,notNull:!1,default:void 0,hasDefault:!1,primaryKey:!1,isUnique:!1,uniqueName:void 0,uniqueType:void 0,dataType:t,columnType:i,generated:void 0}}$type(){return this}notNull(){return this.config.notNull=!0,this}default(e){return this.config.default=e,this.config.hasDefault=!0,this}$defaultFn(e){return this.config.defaultFn=e,this.config.hasDefault=!0,this}$default=this.$defaultFn;$onUpdateFn(e){return this.config.onUpdateFn=e,this.config.hasDefault=!0,this}$onUpdate=this.$onUpdateFn;primaryKey(){return this.config.primaryKey=!0,this.config.notNull=!0,this}setName(e){""===this.config.name&&(this.config.name=e)}}var n=i(7078),l=i(25957);class o{static [s.i]="PgForeignKeyBuilder";reference;_onUpdate="no action";_onDelete="no action";constructor(e,t){this.reference=()=>{let{name:t,columns:i,foreignColumns:s}=e();return{name:t,columns:i,foreignTable:s[0].table,foreignColumns:s}},t&&(this._onUpdate=t.onUpdate,this._onDelete=t.onDelete)}onUpdate(e){return this._onUpdate=void 0===e?"no action":e,this}onDelete(e){return this._onDelete=void 0===e?"no action":e,this}build(e){return new a(e,this)}}class a{constructor(e,t){this.table=e,this.reference=t.reference,this.onUpdate=t._onUpdate,this.onDelete=t._onDelete}static [s.i]="PgForeignKey";reference;onUpdate;onDelete;getName(){let{name:e,columns:t,foreignColumns:i}=this.reference(),s=t.map(e=>e.name),r=i.map(e=>e.name),n=[this.table[l.E],...s,i[0].table[l.E],...r];return e??`${n.join("_")}_fk`}}var u=i(53908);function c(e,t){return`${e[l.E]}_${t.join("_")}_unique`}class h{constructor(e,t){this.name=t,this.columns=e}static [s.i]=null;columns;nullsNotDistinctConfig=!1;nullsNotDistinct(){return this.nullsNotDistinctConfig=!0,this}build(e){return new f(e,this.columns,this.nullsNotDistinctConfig,this.name)}}class d{static [s.i]=null;name;constructor(e){this.name=e}on(...e){return new h(e,this.name)}}class f{constructor(e,t,i,s){this.table=e,this.columns=t,this.name=s??c(this.table,this.columns.map(e=>e.name)),this.nullsNotDistinct=i}static [s.i]=null;columns;name;nullsNotDistinct=!1;getName(){return this.name}}function g(e,t,i){for(let s=t;s<e.length;s++){let r=e[s];if("\\"===r){s++;continue}if('"'===r)return[e.slice(t,s).replace(/\\/g,""),s+1];if(!i&&(","===r||"}"===r))return[e.slice(t,s).replace(/\\/g,""),s]}return[e.slice(t).replace(/\\/g,""),e.length]}class p extends r{foreignKeyConfigs=[];static [s.i]="PgColumnBuilder";array(e){return new w(this.config.name,this,e)}references(e,t={}){return this.foreignKeyConfigs.push({ref:e,actions:t}),this}unique(e,t){return this.config.isUnique=!0,this.config.uniqueName=e,this.config.uniqueType=t?.nulls,this}generatedAlwaysAs(e){return this.config.generated={as:e,type:"always",mode:"stored"},this}buildForeignKeys(e,t){return this.foreignKeyConfigs.map(({ref:i,actions:s})=>(0,u.i)((i,s)=>{let r=new o(()=>({columns:[e],foreignColumns:[i()]}));return s.onUpdate&&r.onUpdate(s.onUpdate),s.onDelete&&r.onDelete(s.onDelete),r.build(t)},i,s))}buildExtraConfigColumn(e){return new y(e,this.config)}}class m extends n.V{constructor(e,t){t.uniqueName||(t.uniqueName=c(e,[t.name])),super(e,t),this.table=e}static [s.i]="PgColumn"}class y extends m{static [s.i]="ExtraConfigColumn";getSQLType(){return this.getSQLType()}indexConfig={order:this.config.order??"asc",nulls:this.config.nulls??"last",opClass:this.config.opClass};defaultConfig={order:"asc",nulls:"last",opClass:void 0};asc(){return this.indexConfig.order="asc",this}desc(){return this.indexConfig.order="desc",this}nullsFirst(){return this.indexConfig.nulls="first",this}nullsLast(){return this.indexConfig.nulls="last",this}op(e){return this.indexConfig.opClass=e,this}}class b{static [s.i]=null;constructor(e,t,i,s){this.name=e,this.keyAsName=t,this.type=i,this.indexConfig=s}name;keyAsName;type;indexConfig}class w extends p{static [s.i]="PgArrayBuilder";constructor(e,t,i){super(e,"array","PgArray"),this.config.baseBuilder=t,this.config.size=i}build(e){let t=this.config.baseBuilder.build(e);return new S(e,this.config,t)}}class S extends m{constructor(e,t,i,s){super(e,t),this.baseColumn=i,this.range=s,this.size=t.size}size;static [s.i]="PgArray";getSQLType(){return`${this.baseColumn.getSQLType()}[${"number"==typeof this.size?this.size:""}]`}mapFromDriverValue(e){return"string"==typeof e&&(e=function(e){let[t]=function e(t,i=0){let s=[],r=i,n=!1;for(;r<t.length;){let l=t[r];if(","===l){(n||r===i)&&s.push(""),n=!0,r++;continue}if(n=!1,"\\"===l){r+=2;continue}if('"'===l){let[e,i]=g(t,r+1,!0);s.push(e),r=i;continue}if("}"===l)return[s,r+1];if("{"===l){let[i,n]=e(t,r+1);s.push(i),r=n;continue}let[o,a]=g(t,r,!1);s.push(o),r=a}return[s,r]}(e,1);return t}(e)),e.map(e=>this.baseColumn.mapFromDriverValue(e))}mapToDriverValue(e,t=!1){let i=e.map(e=>null===e?null:(0,s.is)(this.baseColumn,S)?this.baseColumn.mapToDriverValue(e,!0):this.baseColumn.mapToDriverValue(e));return t?i:function e(t){return`{${t.map(t=>Array.isArray(t)?e(t):"string"==typeof t?`"${t.replace(/\\/g,"\\\\").replace(/"/g,'\\"')}"`:`${t}`).join(",")}}`}(i)}}},37837:(e,t,i)=>{"use strict";i.d(t,{u:()=>l});var s=i(77799),r=i(91990),n=i(98199);class l extends n.pe{static [s.i]="PgDateColumnBaseBuilder";defaultNow(){return this.default((0,r.ll)`now()`)}}},46964:(e,t,i)=>{"use strict";i.d(t,{dw:()=>c,p6:()=>h,qw:()=>a});var s=i(77799),r=i(78199),n=i(98199),l=i(37837);class o extends l.u{static [s.i]="PgDateBuilder";constructor(e){super(e,"date","PgDate")}build(e){return new a(e,this.config)}}class a extends n.Kl{static [s.i]="PgDate";getSQLType(){return"date"}mapFromDriverValue(e){return new Date(e)}mapToDriverValue(e){return e.toISOString()}}class u extends l.u{static [s.i]="PgDateStringBuilder";constructor(e){super(e,"string","PgDateString")}build(e){return new c(e,this.config)}}class c extends n.Kl{static [s.i]="PgDateString";getSQLType(){return"date"}}function h(e,t){let{name:i,config:s}=(0,r.Ll)(e,t);return s?.mode==="date"?new o(i):new u(i)}},56620:(e,t,i)=>{"use strict";i.d(t,{p:()=>n});var s=i(77799),r=i(98199);class n extends r.pe{static [s.i]="PgIntColumnBaseBuilder";generatedAlwaysAsIdentity(e){if(e){let{name:t,...i}=e;this.config.generatedIdentity={type:"always",sequenceName:t,sequenceOptions:i}}else this.config.generatedIdentity={type:"always"};return this.config.hasDefault=!0,this.config.notNull=!0,this}generatedByDefaultAsIdentity(e){if(e){let{name:t,...i}=e;this.config.generatedIdentity={type:"byDefault",sequenceName:t,sequenceOptions:i}}else this.config.generatedIdentity={type:"byDefault"};return this.config.hasDefault=!0,this.config.notNull=!0,this}}},9848:(e,t,i)=>{"use strict";i.d(t,{nd:()=>a});var s=i(77799),r=i(98199),n=i(56620);class l extends n.p{static [s.i]="PgIntegerBuilder";constructor(e){super(e,"number","PgInteger")}build(e){return new o(e,this.config)}}class o extends r.Kl{static [s.i]="PgInteger";getSQLType(){return"integer"}mapFromDriverValue(e){return"string"==typeof e?Number.parseInt(e):e}}function a(e){return new l(e??"")}},32190:(e,t,i)=>{"use strict";i.d(t,{Pq:()=>o,iX:()=>l});var s=i(77799),r=i(98199);class n extends r.pe{static [s.i]="PgJsonBuilder";constructor(e){super(e,"json","PgJson")}build(e){return new l(e,this.config)}}class l extends r.Kl{static [s.i]="PgJson";constructor(e,t){super(e,t)}getSQLType(){return"json"}mapToDriverValue(e){return JSON.stringify(e)}mapFromDriverValue(e){if("string"==typeof e)try{return JSON.parse(e)}catch{}return e}}function o(e){return new n(e??"")}},57102:(e,t,i)=>{"use strict";i.d(t,{Fx:()=>o,kn:()=>l});var s=i(77799),r=i(98199);class n extends r.pe{static [s.i]="PgJsonbBuilder";constructor(e){super(e,"json","PgJsonb")}build(e){return new l(e,this.config)}}class l extends r.Kl{static [s.i]="PgJsonb";constructor(e,t){super(e,t)}getSQLType(){return"jsonb"}mapToDriverValue(e){return JSON.stringify(e)}mapFromDriverValue(e){if("string"==typeof e)try{return JSON.parse(e)}catch{}return e}}function o(e){return new n(e??"")}},70009:(e,t,i)=>{"use strict";i.d(t,{Z5:()=>o,_:()=>f,sH:()=>d});var s=i(77799),r=i(78199),n=i(98199);class l extends n.pe{static [s.i]="PgNumericBuilder";constructor(e,t,i){super(e,"string","PgNumeric"),this.config.precision=t,this.config.scale=i}build(e){return new o(e,this.config)}}class o extends n.Kl{static [s.i]="PgNumeric";precision;scale;constructor(e,t){super(e,t),this.precision=t.precision,this.scale=t.scale}mapFromDriverValue(e){return"string"==typeof e?e:String(e)}getSQLType(){return void 0!==this.precision&&void 0!==this.scale?`numeric(${this.precision}, ${this.scale})`:void 0===this.precision?"numeric":`numeric(${this.precision})`}}class a extends n.pe{static [s.i]="PgNumericNumberBuilder";constructor(e,t,i){super(e,"number","PgNumericNumber"),this.config.precision=t,this.config.scale=i}build(e){return new u(e,this.config)}}class u extends n.Kl{static [s.i]="PgNumericNumber";precision;scale;constructor(e,t){super(e,t),this.precision=t.precision,this.scale=t.scale}mapFromDriverValue(e){return"number"==typeof e?e:Number(e)}mapToDriverValue=String;getSQLType(){return void 0!==this.precision&&void 0!==this.scale?`numeric(${this.precision}, ${this.scale})`:void 0===this.precision?"numeric":`numeric(${this.precision})`}}class c extends n.pe{static [s.i]="PgNumericBigIntBuilder";constructor(e,t,i){super(e,"bigint","PgNumericBigInt"),this.config.precision=t,this.config.scale=i}build(e){return new h(e,this.config)}}class h extends n.Kl{static [s.i]="PgNumericBigInt";precision;scale;constructor(e,t){super(e,t),this.precision=t.precision,this.scale=t.scale}mapFromDriverValue=BigInt;mapToDriverValue=String;getSQLType(){return void 0!==this.precision&&void 0!==this.scale?`numeric(${this.precision}, ${this.scale})`:void 0===this.precision?"numeric":`numeric(${this.precision})`}}function d(e,t){let{name:i,config:s}=(0,r.Ll)(e,t),n=s?.mode;return"number"===n?new a(i,s?.precision,s?.scale):"bigint"===n?new c(i,s?.precision,s?.scale):new l(i,s?.precision,s?.scale)}let f=d},44799:(e,t,i)=>{"use strict";i.d(t,{Qq:()=>a});var s=i(77799),r=i(78199),n=i(98199);class l extends n.pe{static [s.i]="PgTextBuilder";constructor(e,t){super(e,"string","PgText"),this.config.enumValues=t.enum}build(e){return new o(e,this.config)}}class o extends n.Kl{static [s.i]="PgText";enumValues=this.config.enumValues;getSQLType(){return"text"}}function a(e,t={}){let{name:i,config:s}=(0,r.Ll)(e,t);return new l(i,s)}},90293:(e,t,i)=>{"use strict";i.d(t,{Xd:()=>a,kB:()=>u});var s=i(77799),r=i(78199),n=i(98199),l=i(37837);class o extends l.u{constructor(e,t,i){super(e,"string","PgTime"),this.withTimezone=t,this.precision=i,this.config.withTimezone=t,this.config.precision=i}static [s.i]="PgTimeBuilder";build(e){return new a(e,this.config)}}class a extends n.Kl{static [s.i]="PgTime";withTimezone;precision;constructor(e,t){super(e,t),this.withTimezone=t.withTimezone,this.precision=t.precision}getSQLType(){let e=void 0===this.precision?"":`(${this.precision})`;return`time${e}${this.withTimezone?" with time zone":""}`}}function u(e,t={}){let{name:i,config:s}=(0,r.Ll)(e,t);return new o(i,s.withTimezone??!1,s.precision)}},32590:(e,t,i)=>{"use strict";i.d(t,{KM:()=>a,vE:()=>h,xQ:()=>c});var s=i(77799),r=i(78199),n=i(98199),l=i(37837);class o extends l.u{static [s.i]="PgTimestampBuilder";constructor(e,t,i){super(e,"date","PgTimestamp"),this.config.withTimezone=t,this.config.precision=i}build(e){return new a(e,this.config)}}class a extends n.Kl{static [s.i]="PgTimestamp";withTimezone;precision;constructor(e,t){super(e,t),this.withTimezone=t.withTimezone,this.precision=t.precision}getSQLType(){let e=void 0===this.precision?"":` (${this.precision})`;return`timestamp${e}${this.withTimezone?" with time zone":""}`}mapFromDriverValue=e=>new Date(this.withTimezone?e:e+"+0000");mapToDriverValue=e=>e.toISOString()}class u extends l.u{static [s.i]="PgTimestampStringBuilder";constructor(e,t,i){super(e,"string","PgTimestampString"),this.config.withTimezone=t,this.config.precision=i}build(e){return new c(e,this.config)}}class c extends n.Kl{static [s.i]="PgTimestampString";withTimezone;precision;constructor(e,t){super(e,t),this.withTimezone=t.withTimezone,this.precision=t.precision}getSQLType(){let e=void 0===this.precision?"":`(${this.precision})`;return`timestamp${e}${this.withTimezone?" with time zone":""}`}}function h(e,t={}){let{name:i,config:s}=(0,r.Ll)(e,t);return s?.mode==="string"?new u(i,s.withTimezone??!1,s.precision):new o(i,s?.withTimezone??!1,s?.precision)}},60011:(e,t,i)=>{"use strict";i.d(t,{dL:()=>o,uR:()=>a});var s=i(77799),r=i(91990),n=i(98199);class l extends n.pe{static [s.i]="PgUUIDBuilder";constructor(e){super(e,"string","PgUUID")}defaultRandom(){return this.default((0,r.ll)`gen_random_uuid()`)}build(e){return new o(e,this.config)}}class o extends n.Kl{static [s.i]="PgUUID";getSQLType(){return"uuid"}}function a(e){return new l(e??"")}},90358:(e,t,i)=>{"use strict";i.d(t,{p:()=>_});var s=i(77799),r=i(19719),n=i(89490),l=i(7078),o=i(91990),a=i(73322),u=i(18611);class c{static [s.i]="SelectionProxyHandler";config;constructor(e){this.config={...e}}get(e,t){if("_"===t)return{...e._,selectedFields:new Proxy(e._.selectedFields,this)};if(t===u.n)return{...e[u.n],selectedFields:new Proxy(e[u.n].selectedFields,this)};if("symbol"==typeof t)return e[t];let i=((0,s.is)(e,a.n)?e._.selectedFields:(0,s.is)(e,o.Ss)?e[u.n].selectedFields:e)[t];if((0,s.is)(i,o.Xs.Aliased)){if("sql"===this.config.sqlAliasedBehavior&&!i.isSelectionField)return i.sql;let e=i.clone();return e.isSelectionField=!0,e}if((0,s.is)(i,o.Xs)){if("sql"===this.config.sqlBehavior)return i;throw Error(`You tried to reference "${t}" field from a subquery, which is a raw SQL field, but it doesn't have an alias declared. Please add an alias to the field using ".as('alias')" method.`)}return(0,s.is)(i,l.V)?this.config.alias?new Proxy(i,new n.Ht(new Proxy(i.table,new n.h_(this.config.alias,this.config.replaceOriginalName??!1)))):i:"object"!=typeof i||null===i?i:new Proxy(i,new c(this.config))}}var h=i(53375);class d{static [s.i]="TypedQueryBuilder";getSelectedFields(){return this._.selectedFields}}class f{static [s.i]="QueryPromise";[Symbol.toStringTag]="QueryPromise";catch(e){return this.then(void 0,e)}finally(e){return this.then(t=>(e?.(),t),t=>{throw e?.(),t})}then(e,t){return this.execute().then(e,t)}}var g=i(65258),p=i(55731),m=i(78199);class y{static [s.i]="PgSelectBuilder";fields;session;dialect;withList=[];distinct;constructor(e){this.fields=e.fields,this.session=e.session,this.dialect=e.dialect,e.withList&&(this.withList=e.withList),this.distinct=e.distinct}authToken;setToken(e){return this.authToken=e,this}from(e){let t;let i=!!this.fields;return t=this.fields?this.fields:(0,s.is)(e,a.n)?Object.fromEntries(Object.keys(e._.selectedFields).map(t=>[t,e[t]])):(0,s.is)(e,h.w)?e[u.n].selectedFields:(0,s.is)(e,o.Xs)?{}:(0,m.YD)(e),new w({table:e,fields:t,isPartialSelect:i,session:this.session,dialect:this.dialect,withList:this.withList,distinct:this.distinct}).setToken(this.authToken)}}class b extends d{static [s.i]="PgSelectQueryBuilder";_;config;joinsNotNullableMap;tableName;isPartialSelect;session;dialect;constructor({table:e,fields:t,isPartialSelect:i,session:s,dialect:r,withList:n,distinct:l}){super(),this.config={withList:n,table:e,fields:{...t},distinct:l,setOperators:[]},this.isPartialSelect=i,this.session=s,this.dialect=r,this._={selectedFields:t},this.tableName=(0,m.zN)(e),this.joinsNotNullableMap="string"==typeof this.tableName?{[this.tableName]:!0}:{}}createJoin(e,t){return(i,r)=>{let n=this.tableName,l=(0,m.zN)(i);if("string"==typeof l&&this.config.joins?.some(e=>e.alias===l))throw Error(`Alias "${l}" is already used in this query`);if(!this.isPartialSelect&&(1===Object.keys(this.joinsNotNullableMap).length&&"string"==typeof n&&(this.config.fields={[n]:this.config.fields}),"string"==typeof l&&!(0,s.is)(i,o.Xs))){let e=(0,s.is)(i,a.n)?i._.selectedFields:(0,s.is)(i,o.Ss)?i[u.n].selectedFields:i[g.XI.Symbol.Columns];this.config.fields[l]=e}if("function"==typeof r&&(r=r(new Proxy(this.config.fields,new c({sqlAliasedBehavior:"sql",sqlBehavior:"sql"})))),this.config.joins||(this.config.joins=[]),this.config.joins.push({on:r,table:i,joinType:e,alias:l,lateral:t}),"string"==typeof l)switch(e){case"left":this.joinsNotNullableMap[l]=!1;break;case"right":this.joinsNotNullableMap=Object.fromEntries(Object.entries(this.joinsNotNullableMap).map(([e])=>[e,!1])),this.joinsNotNullableMap[l]=!0;break;case"cross":case"inner":this.joinsNotNullableMap[l]=!0;break;case"full":this.joinsNotNullableMap=Object.fromEntries(Object.entries(this.joinsNotNullableMap).map(([e])=>[e,!1])),this.joinsNotNullableMap[l]=!1}return this}}leftJoin=this.createJoin("left",!1);leftJoinLateral=this.createJoin("left",!0);rightJoin=this.createJoin("right",!1);innerJoin=this.createJoin("inner",!1);innerJoinLateral=this.createJoin("inner",!0);fullJoin=this.createJoin("full",!1);crossJoin=this.createJoin("cross",!1);crossJoinLateral=this.createJoin("cross",!0);createSetOperator(e,t){return i=>{let s="function"==typeof i?i(v()):i;if(!(0,m.DV)(this.getSelectedFields(),s.getSelectedFields()))throw Error("Set operator error (union / intersect / except): selected fields are not the same or are in a different order");return this.config.setOperators.push({type:e,isAll:t,rightSelect:s}),this}}union=this.createSetOperator("union",!1);unionAll=this.createSetOperator("union",!0);intersect=this.createSetOperator("intersect",!1);intersectAll=this.createSetOperator("intersect",!0);except=this.createSetOperator("except",!1);exceptAll=this.createSetOperator("except",!0);addSetOperators(e){return this.config.setOperators.push(...e),this}where(e){return"function"==typeof e&&(e=e(new Proxy(this.config.fields,new c({sqlAliasedBehavior:"sql",sqlBehavior:"sql"})))),this.config.where=e,this}having(e){return"function"==typeof e&&(e=e(new Proxy(this.config.fields,new c({sqlAliasedBehavior:"sql",sqlBehavior:"sql"})))),this.config.having=e,this}groupBy(...e){if("function"==typeof e[0]){let t=e[0](new Proxy(this.config.fields,new c({sqlAliasedBehavior:"alias",sqlBehavior:"sql"})));this.config.groupBy=Array.isArray(t)?t:[t]}else this.config.groupBy=e;return this}orderBy(...e){if("function"==typeof e[0]){let t=e[0](new Proxy(this.config.fields,new c({sqlAliasedBehavior:"alias",sqlBehavior:"sql"}))),i=Array.isArray(t)?t:[t];this.config.setOperators.length>0?this.config.setOperators.at(-1).orderBy=i:this.config.orderBy=i}else this.config.setOperators.length>0?this.config.setOperators.at(-1).orderBy=e:this.config.orderBy=e;return this}limit(e){return this.config.setOperators.length>0?this.config.setOperators.at(-1).limit=e:this.config.limit=e,this}offset(e){return this.config.setOperators.length>0?this.config.setOperators.at(-1).offset=e:this.config.offset=e,this}for(e,t={}){return this.config.lockingClause={strength:e,config:t},this}getSQL(){return this.dialect.buildSelectQuery(this.config)}toSQL(){let{typings:e,...t}=this.dialect.sqlToQuery(this.getSQL());return t}as(e){return new Proxy(new a.n(this.getSQL(),this.config.fields,e),new c({alias:e,sqlAliasedBehavior:"alias",sqlBehavior:"error"}))}getSelectedFields(){return new Proxy(this.config.fields,new c({alias:this.tableName,sqlAliasedBehavior:"alias",sqlBehavior:"error"}))}$dynamic(){return this}}class w extends b{static [s.i]="PgSelect";_prepare(e){let{session:t,config:i,dialect:s,joinsNotNullableMap:r,authToken:n}=this;if(!t)throw Error("Cannot execute a query on a query builder. Please use a database instance instead.");return p.k.startActiveSpan("drizzle.prepareQuery",()=>{let l=(0,m.He)(i.fields),o=t.prepareQuery(s.sqlToQuery(this.getSQL()),l,e,!0);return o.joinsNotNullableMap=r,o.setToken(n)})}prepare(e){return this._prepare(e)}authToken;setToken(e){return this.authToken=e,this}execute=e=>p.k.startActiveSpan("drizzle.operation",()=>this._prepare().execute(e,this.authToken))}function S(e,t){return(i,s,...r)=>{let n=[s,...r].map(i=>({type:e,isAll:t,rightSelect:i}));for(let e of n)if(!(0,m.DV)(i.getSelectedFields(),e.rightSelect.getSelectedFields()))throw Error("Set operator error (union / intersect / except): selected fields are not the same or are in a different order");return i.addSetOperators(n)}}(0,m.XJ)(w,[f]);let v=()=>({union:T,unionAll:$,intersect:x,intersectAll:P,except:N,exceptAll:L}),T=S("union",!1),$=S("union",!0),x=S("intersect",!1),P=S("intersect",!0),N=S("except",!1),L=S("except",!0);class q{static [s.i]="PgQueryBuilder";dialect;dialectConfig;constructor(e){this.dialect=(0,s.is)(e,r.s)?e:void 0,this.dialectConfig=(0,s.is)(e,r.s)?void 0:e}$with=(e,t)=>{let i=this;return{as:s=>("function"==typeof s&&(s=s(i)),new Proxy(new a.J(s.getSQL(),t??("getSelectedFields"in s?s.getSelectedFields()??{}:{}),e,!0),new c({alias:e,sqlAliasedBehavior:"alias",sqlBehavior:"error"})))}};with(...e){let t=this;return{select:function(i){return new y({fields:i??void 0,session:void 0,dialect:t.getDialect(),withList:e})},selectDistinct:function(e){return new y({fields:e??void 0,session:void 0,dialect:t.getDialect(),distinct:!0})},selectDistinctOn:function(e,i){return new y({fields:i??void 0,session:void 0,dialect:t.getDialect(),distinct:{on:e}})}}}select(e){return new y({fields:e??void 0,session:void 0,dialect:this.getDialect()})}selectDistinct(e){return new y({fields:e??void 0,session:void 0,dialect:this.getDialect(),distinct:!0})}selectDistinctOn(e,t){return new y({fields:t??void 0,session:void 0,dialect:this.getDialect(),distinct:{on:e}})}getDialect(){return this.dialect||(this.dialect=new r.s(this.dialectConfig)),this.dialect}}var B=i(87858);class C{constructor(e,t,i,s){this.table=e,this.session=t,this.dialect=i,this.withList=s}static [s.i]="PgUpdateBuilder";authToken;setToken(e){return this.authToken=e,this}set(e){return new k(this.table,(0,m.q)(this.table,e),this.session,this.dialect,this.withList).setToken(this.authToken)}}class k extends f{constructor(e,t,i,s,r){super(),this.session=i,this.dialect=s,this.config={set:t,table:e,withList:r,joins:[]},this.tableName=(0,m.zN)(e),this.joinsNotNullableMap="string"==typeof this.tableName?{[this.tableName]:!0}:{}}static [s.i]="PgUpdate";config;tableName;joinsNotNullableMap;from(e){let t=(0,m.zN)(e);return"string"==typeof t&&(this.joinsNotNullableMap[t]=!0),this.config.from=e,this}getTableLikeFields(e){return(0,s.is)(e,B.mu)?e[g.XI.Symbol.Columns]:(0,s.is)(e,a.n)?e._.selectedFields:e[u.n].selectedFields}createJoin(e){return(t,i)=>{let r=(0,m.zN)(t);if("string"==typeof r&&this.config.joins.some(e=>e.alias===r))throw Error(`Alias "${r}" is already used in this query`);if("function"==typeof i){let e=this.config.from&&!(0,s.is)(this.config.from,o.Xs)?this.getTableLikeFields(this.config.from):void 0;i=i(new Proxy(this.config.table[g.XI.Symbol.Columns],new c({sqlAliasedBehavior:"sql",sqlBehavior:"sql"})),e&&new Proxy(e,new c({sqlAliasedBehavior:"sql",sqlBehavior:"sql"})))}if(this.config.joins.push({on:i,table:t,joinType:e,alias:r}),"string"==typeof r)switch(e){case"left":this.joinsNotNullableMap[r]=!1;break;case"right":this.joinsNotNullableMap=Object.fromEntries(Object.entries(this.joinsNotNullableMap).map(([e])=>[e,!1])),this.joinsNotNullableMap[r]=!0;break;case"inner":this.joinsNotNullableMap[r]=!0;break;case"full":this.joinsNotNullableMap=Object.fromEntries(Object.entries(this.joinsNotNullableMap).map(([e])=>[e,!1])),this.joinsNotNullableMap[r]=!1}return this}}leftJoin=this.createJoin("left");rightJoin=this.createJoin("right");innerJoin=this.createJoin("inner");fullJoin=this.createJoin("full");where(e){return this.config.where=e,this}returning(e){if(!e&&(e=Object.assign({},this.config.table[g.XI.Symbol.Columns]),this.config.from)){let t=(0,m.zN)(this.config.from);if("string"==typeof t&&this.config.from&&!(0,s.is)(this.config.from,o.Xs)){let i=this.getTableLikeFields(this.config.from);e[t]=i}for(let t of this.config.joins){let i=(0,m.zN)(t.table);if("string"==typeof i&&!(0,s.is)(t.table,o.Xs)){let s=this.getTableLikeFields(t.table);e[i]=s}}}return this.config.returningFields=e,this.config.returning=(0,m.He)(e),this}getSQL(){return this.dialect.buildUpdateQuery(this.config)}toSQL(){let{typings:e,...t}=this.dialect.sqlToQuery(this.getSQL());return t}_prepare(e){let t=this.session.prepareQuery(this.dialect.sqlToQuery(this.getSQL()),this.config.returning,e,!0);return t.joinsNotNullableMap=this.joinsNotNullableMap,t}prepare(e){return this._prepare(e)}authToken;setToken(e){return this.authToken=e,this}execute=e=>this._prepare().execute(e,this.authToken);getSelectedFields(){return this.config.returningFields?new Proxy(this.config.returningFields,new c({alias:(0,g.Io)(this.config.table),sqlAliasedBehavior:"alias",sqlBehavior:"error"})):void 0}$dynamic(){return this}}class I{constructor(e,t,i,s,r){this.table=e,this.session=t,this.dialect=i,this.withList=s,this.overridingSystemValue_=r}static [s.i]="PgInsertBuilder";authToken;setToken(e){return this.authToken=e,this}overridingSystemValue(){return this.overridingSystemValue_=!0,this}values(e){if(0===(e=Array.isArray(e)?e:[e]).length)throw Error("values() must be called with at least one value");let t=e.map(e=>{let t={},i=this.table[g.XI.Symbol.Columns];for(let r of Object.keys(e)){let n=e[r];t[r]=(0,s.is)(n,o.Xs)?n:new o.Iw(n,i[r])}return t});return new Q(this.table,t,this.session,this.dialect,this.withList,!1,this.overridingSystemValue_).setToken(this.authToken)}select(e){let t="function"==typeof e?e(new q):e;if(!(0,s.is)(t,o.Xs)&&!(0,m.DV)(this.table[g.e],t._.selectedFields))throw Error("Insert select error: selected fields are not the same or are in a different order compared to the table definition");return new Q(this.table,t,this.session,this.dialect,this.withList,!0)}}class Q extends f{constructor(e,t,i,s,r,n,l){super(),this.session=i,this.dialect=s,this.config={table:e,values:t,withList:r,select:n,overridingSystemValue_:l}}static [s.i]="PgInsert";config;returning(e=this.config.table[g.XI.Symbol.Columns]){return this.config.returningFields=e,this.config.returning=(0,m.He)(e),this}onConflictDoNothing(e={}){if(void 0===e.target)this.config.onConflict=(0,o.ll)`do nothing`;else{let t="";t=Array.isArray(e.target)?e.target.map(e=>this.dialect.escapeName(this.dialect.casing.getColumnCasing(e))).join(","):this.dialect.escapeName(this.dialect.casing.getColumnCasing(e.target));let i=e.where?(0,o.ll)` where ${e.where}`:void 0;this.config.onConflict=(0,o.ll)`(${o.ll.raw(t)})${i} do nothing`}return this}onConflictDoUpdate(e){if(e.where&&(e.targetWhere||e.setWhere))throw Error('You cannot use both "where" and "targetWhere"/"setWhere" at the same time - "where" is deprecated, use "targetWhere" or "setWhere" instead.');let t=e.where?(0,o.ll)` where ${e.where}`:void 0,i=e.targetWhere?(0,o.ll)` where ${e.targetWhere}`:void 0,s=e.setWhere?(0,o.ll)` where ${e.setWhere}`:void 0,r=this.dialect.buildUpdateSet(this.config.table,(0,m.q)(this.config.table,e.set)),n="";return n=Array.isArray(e.target)?e.target.map(e=>this.dialect.escapeName(this.dialect.casing.getColumnCasing(e))).join(","):this.dialect.escapeName(this.dialect.casing.getColumnCasing(e.target)),this.config.onConflict=(0,o.ll)`(${o.ll.raw(n)})${i} do update set ${r}${t}${s}`,this}getSQL(){return this.dialect.buildInsertQuery(this.config)}toSQL(){let{typings:e,...t}=this.dialect.sqlToQuery(this.getSQL());return t}_prepare(e){return p.k.startActiveSpan("drizzle.prepareQuery",()=>this.session.prepareQuery(this.dialect.sqlToQuery(this.getSQL()),this.config.returning,e,!0))}prepare(e){return this._prepare(e)}authToken;setToken(e){return this.authToken=e,this}execute=e=>p.k.startActiveSpan("drizzle.operation",()=>this._prepare().execute(e,this.authToken));getSelectedFields(){return this.config.returningFields?new Proxy(this.config.returningFields,new c({alias:(0,g.Io)(this.config.table),sqlAliasedBehavior:"alias",sqlBehavior:"error"})):void 0}$dynamic(){return this}}class j extends f{constructor(e,t,i,s){super(),this.session=t,this.dialect=i,this.config={table:e,withList:s}}static [s.i]="PgDelete";config;where(e){return this.config.where=e,this}returning(e=this.config.table[g.XI.Symbol.Columns]){return this.config.returningFields=e,this.config.returning=(0,m.He)(e),this}getSQL(){return this.dialect.buildDeleteQuery(this.config)}toSQL(){let{typings:e,...t}=this.dialect.sqlToQuery(this.getSQL());return t}_prepare(e){return p.k.startActiveSpan("drizzle.prepareQuery",()=>this.session.prepareQuery(this.dialect.sqlToQuery(this.getSQL()),this.config.returning,e,!0))}prepare(e){return this._prepare(e)}authToken;setToken(e){return this.authToken=e,this}execute=e=>p.k.startActiveSpan("drizzle.operation",()=>this._prepare().execute(e,this.authToken));getSelectedFields(){return this.config.returningFields?new Proxy(this.config.returningFields,new c({alias:(0,g.Io)(this.config.table),sqlAliasedBehavior:"alias",sqlBehavior:"error"})):void 0}$dynamic(){return this}}class O extends o.Xs{constructor(e){super(O.buildEmbeddedCount(e.source,e.filters).queryChunks),this.params=e,this.mapWith(Number),this.session=e.session,this.sql=O.buildCount(e.source,e.filters)}sql;token;static [s.i]="PgCountBuilder";[Symbol.toStringTag]="PgCountBuilder";session;static buildEmbeddedCount(e,t){return(0,o.ll)`(select count(*) from ${e}${o.ll.raw(" where ").if(t)}${t})`}static buildCount(e,t){return(0,o.ll)`select count(*) as count from ${e}${o.ll.raw(" where ").if(t)}${t};`}setToken(e){return this.token=e,this}then(e,t){return Promise.resolve(this.session.count(this.sql,this.token)).then(e,t)}catch(e){return this.then(void 0,e)}finally(e){return this.then(t=>(e?.(),t),t=>{throw e?.(),t})}}var A=i(95937);class D{constructor(e,t,i,s,r,n,l){this.fullSchema=e,this.schema=t,this.tableNamesMap=i,this.table=s,this.tableConfig=r,this.dialect=n,this.session=l}static [s.i]="PgRelationalQueryBuilder";findMany(e){return new z(this.fullSchema,this.schema,this.tableNamesMap,this.table,this.tableConfig,this.dialect,this.session,e||{},"many")}findFirst(e){return new z(this.fullSchema,this.schema,this.tableNamesMap,this.table,this.tableConfig,this.dialect,this.session,e?{...e,limit:1}:{limit:1},"first")}}class z extends f{constructor(e,t,i,s,r,n,l,o,a){super(),this.fullSchema=e,this.schema=t,this.tableNamesMap=i,this.table=s,this.tableConfig=r,this.dialect=n,this.session=l,this.config=o,this.mode=a}static [s.i]="PgRelationalQuery";_prepare(e){return p.k.startActiveSpan("drizzle.prepareQuery",()=>{let{query:t,builtQuery:i}=this._toSQL();return this.session.prepareQuery(i,void 0,e,!0,(e,i)=>{let s=e.map(e=>(0,A.I$)(this.schema,this.tableConfig,e,t.selection,i));return"first"===this.mode?s[0]:s})})}prepare(e){return this._prepare(e)}_getQuery(){return this.dialect.buildRelationalQueryWithoutPK({fullSchema:this.fullSchema,schema:this.schema,tableNamesMap:this.tableNamesMap,table:this.table,tableConfig:this.tableConfig,queryConfig:this.config,tableAlias:this.tableConfig.tsName})}getSQL(){return this._getQuery().sql}_toSQL(){let e=this._getQuery(),t=this.dialect.sqlToQuery(e.sql);return{query:e,builtQuery:t}}toSQL(){return this._toSQL().builtQuery}authToken;setToken(e){return this.authToken=e,this}execute(){return p.k.startActiveSpan("drizzle.operation",()=>this._prepare().execute(void 0,this.authToken))}}class F extends f{constructor(e,t,i,s){super(),this.execute=e,this.sql=t,this.query=i,this.mapBatchResult=s}static [s.i]="PgRaw";getSQL(){return this.sql}getQuery(){return this.query}mapResult(e,t){return t?this.mapBatchResult(e):e}_prepare(){return this}isResponseInArrayMode(){return!1}}class V extends f{constructor(e,t,i){super(),this.session=t,this.dialect=i,this.config={view:e}}static [s.i]="PgRefreshMaterializedView";config;concurrently(){if(void 0!==this.config.withNoData)throw Error("Cannot use concurrently and withNoData together");return this.config.concurrently=!0,this}withNoData(){if(void 0!==this.config.concurrently)throw Error("Cannot use concurrently and withNoData together");return this.config.withNoData=!0,this}getSQL(){return this.dialect.buildRefreshMaterializedViewQuery(this.config)}toSQL(){let{typings:e,...t}=this.dialect.sqlToQuery(this.getSQL());return t}_prepare(e){return p.k.startActiveSpan("drizzle.prepareQuery",()=>this.session.prepareQuery(this.dialect.sqlToQuery(this.getSQL()),void 0,e,!0))}prepare(e){return this._prepare(e)}authToken;setToken(e){return this.authToken=e,this}execute=e=>p.k.startActiveSpan("drizzle.operation",()=>this._prepare().execute(e,this.authToken))}class _{constructor(e,t,i){if(this.dialect=e,this.session=t,this._=i?{schema:i.schema,fullSchema:i.fullSchema,tableNamesMap:i.tableNamesMap,session:t}:{schema:void 0,fullSchema:{},tableNamesMap:{},session:t},this.query={},this._.schema)for(let[s,r]of Object.entries(this._.schema))this.query[s]=new D(i.fullSchema,this._.schema,this._.tableNamesMap,i.fullSchema[s],r,e,t)}static [s.i]="PgDatabase";query;$with=(e,t)=>{let i=this;return{as:s=>("function"==typeof s&&(s=s(new q(i.dialect))),new Proxy(new a.J(s.getSQL(),t??("getSelectedFields"in s?s.getSelectedFields()??{}:{}),e,!0),new c({alias:e,sqlAliasedBehavior:"alias",sqlBehavior:"error"})))}};$count(e,t){return new O({source:e,filters:t,session:this.session})}with(...e){let t=this;return{select:function(i){return new y({fields:i??void 0,session:t.session,dialect:t.dialect,withList:e})},selectDistinct:function(i){return new y({fields:i??void 0,session:t.session,dialect:t.dialect,withList:e,distinct:!0})},selectDistinctOn:function(i,s){return new y({fields:s??void 0,session:t.session,dialect:t.dialect,withList:e,distinct:{on:i}})},update:function(i){return new C(i,t.session,t.dialect,e)},insert:function(i){return new I(i,t.session,t.dialect,e)},delete:function(i){return new j(i,t.session,t.dialect,e)}}}select(e){return new y({fields:e??void 0,session:this.session,dialect:this.dialect})}selectDistinct(e){return new y({fields:e??void 0,session:this.session,dialect:this.dialect,distinct:!0})}selectDistinctOn(e,t){return new y({fields:t??void 0,session:this.session,dialect:this.dialect,distinct:{on:e}})}update(e){return new C(e,this.session,this.dialect)}insert(e){return new I(e,this.session,this.dialect)}delete(e){return new j(e,this.session,this.dialect)}refreshMaterializedView(e){return new V(e,this.session,this.dialect)}authToken;execute(e){let t="string"==typeof e?o.ll.raw(e):e.getSQL(),i=this.dialect.sqlToQuery(t),s=this.session.prepareQuery(i,void 0,void 0,!1);return new F(()=>s.execute(void 0,this.authToken),t,i,e=>s.mapResult(e,!0))}transaction(e,t){return this.session.transaction(e,t)}}},19719:(e,t,i)=>{"use strict";i.d(t,{s:()=>q});var s=i(89490),r=i(77799),n=i(65258);function l(e){return(e.replace(/['\u2019]/g,"").match(/[\da-z]+|[A-Z]+(?![a-z])|[A-Z][\da-z]+/g)??[]).map(e=>e.toLowerCase()).join("_")}function o(e){return(e.replace(/['\u2019]/g,"").match(/[\da-z]+|[A-Z]+(?![a-z])|[A-Z][\da-z]+/g)??[]).reduce((e,t,i)=>e+(0===i?t.toLowerCase():`${t[0].toUpperCase()}${t.slice(1)}`),"")}function a(e){return e}class u{static [r.i]="CasingCache";cache={};cachedTables={};convert;constructor(e){this.convert="snake_case"===e?l:"camelCase"===e?o:a}getColumnCasing(e){if(!e.keyAsName)return e.name;let t=e.table[n.XI.Symbol.Schema]??"public",i=e.table[n.XI.Symbol.OriginalName],s=`${t}.${i}.${e.name}`;return this.cache[s]||this.cacheTable(e.table),this.cache[s]}cacheTable(e){let t=e[n.XI.Symbol.Schema]??"public",i=e[n.XI.Symbol.OriginalName],s=`${t}.${i}`;if(!this.cachedTables[s]){for(let t of Object.values(e[n.XI.Symbol.Columns])){let e=`${s}.${t.name}`;this.cache[e]=this.convert(t.name)}this.cachedTables[s]=!0}}clearCache(){this.cache={},this.cachedTables={}}}var c=i(7078),h=i(28611),d=i(98199),f=i(57102),g=i(32190),p=i(70009),m=i(90293),y=i(32590),b=i(46964),w=i(60011),S=i(87858),v=i(95937),T=i(91990),$=i(47579),x=i(73322),P=i(78199),N=i(18611),L=i(53375);class q{static [r.i]="PgDialect";casing;constructor(e){this.casing=new u(e?.casing)}async migrate(e,t,i){let s="string"==typeof i?"__drizzle_migrations":i.migrationsTable??"__drizzle_migrations",r="string"==typeof i?"drizzle":i.migrationsSchema??"drizzle",n=(0,T.ll)`
			CREATE TABLE IF NOT EXISTS ${T.ll.identifier(r)}.${T.ll.identifier(s)} (
				id SERIAL PRIMARY KEY,
				hash text NOT NULL,
				created_at bigint
			)
		`;await t.execute((0,T.ll)`CREATE SCHEMA IF NOT EXISTS ${T.ll.identifier(r)}`),await t.execute(n);let l=(await t.all((0,T.ll)`select id, hash, created_at from ${T.ll.identifier(r)}.${T.ll.identifier(s)} order by created_at desc limit 1`))[0];await t.transaction(async t=>{for await(let i of e)if(!l||Number(l.created_at)<i.folderMillis){for(let e of i.sql)await t.execute(T.ll.raw(e));await t.execute((0,T.ll)`insert into ${T.ll.identifier(r)}.${T.ll.identifier(s)} ("hash", "created_at") values(${i.hash}, ${i.folderMillis})`)}})}escapeName(e){return`"${e}"`}escapeParam(e){return`$${e+1}`}escapeString(e){return`'${e.replace(/'/g,"''")}'`}buildWithCTE(e){if(!e?.length)return;let t=[(0,T.ll)`with `];for(let[i,s]of e.entries())t.push((0,T.ll)`${T.ll.identifier(s._.alias)} as (${s._.sql})`),i<e.length-1&&t.push((0,T.ll)`, `);return t.push((0,T.ll)` `),T.ll.join(t)}buildDeleteQuery({table:e,where:t,returning:i,withList:s}){let r=this.buildWithCTE(s),n=i?(0,T.ll)` returning ${this.buildSelection(i,{isSingleTable:!0})}`:void 0,l=t?(0,T.ll)` where ${t}`:void 0;return(0,T.ll)`${r}delete from ${e}${l}${n}`}buildUpdateSet(e,t){let i=e[n.XI.Symbol.Columns],s=Object.keys(i).filter(e=>void 0!==t[e]||i[e]?.onUpdateFn!==void 0),r=s.length;return T.ll.join(s.flatMap((e,s)=>{let n=i[e],l=t[e]??T.ll.param(n.onUpdateFn(),n),o=(0,T.ll)`${T.ll.identifier(this.casing.getColumnCasing(n))} = ${l}`;return s<r-1?[o,T.ll.raw(", ")]:[o]}))}buildUpdateQuery({table:e,set:t,where:i,returning:s,withList:r,from:n,joins:l}){let o=this.buildWithCTE(r),a=e[S.mu.Symbol.Name],u=e[S.mu.Symbol.Schema],c=e[S.mu.Symbol.OriginalName],h=a===c?void 0:a,d=(0,T.ll)`${u?(0,T.ll)`${T.ll.identifier(u)}.`:void 0}${T.ll.identifier(c)}${h&&(0,T.ll)` ${T.ll.identifier(h)}`}`,f=this.buildUpdateSet(e,t),g=n&&T.ll.join([T.ll.raw(" from "),this.buildFromTable(n)]),p=this.buildJoins(l),m=s?(0,T.ll)` returning ${this.buildSelection(s,{isSingleTable:!n})}`:void 0,y=i?(0,T.ll)` where ${i}`:void 0;return(0,T.ll)`${o}update ${d} set ${f}${g}${p}${y}${m}`}buildSelection(e,{isSingleTable:t=!1}={}){let i=e.length,s=e.flatMap(({field:e},s)=>{let n=[];if((0,r.is)(e,T.Xs.Aliased)&&e.isSelectionField)n.push(T.ll.identifier(e.fieldAlias));else if((0,r.is)(e,T.Xs.Aliased)||(0,r.is)(e,T.Xs)){let i=(0,r.is)(e,T.Xs.Aliased)?e.sql:e;t?n.push(new T.Xs(i.queryChunks.map(e=>(0,r.is)(e,d.Kl)?T.ll.identifier(this.casing.getColumnCasing(e)):e))):n.push(i),(0,r.is)(e,T.Xs.Aliased)&&n.push((0,T.ll)` as ${T.ll.identifier(e.fieldAlias)}`)}else(0,r.is)(e,c.V)&&(t?n.push(T.ll.identifier(this.casing.getColumnCasing(e))):n.push(e));return s<i-1&&n.push((0,T.ll)`, `),n});return T.ll.join(s)}buildJoins(e){if(!e||0===e.length)return;let t=[];for(let[i,s]of e.entries()){0===i&&t.push((0,T.ll)` `);let n=s.table,l=s.lateral?(0,T.ll)` lateral`:void 0,o=s.on?(0,T.ll)` on ${s.on}`:void 0;if((0,r.is)(n,S.mu)){let e=n[S.mu.Symbol.Name],i=n[S.mu.Symbol.Schema],r=n[S.mu.Symbol.OriginalName],a=e===r?void 0:s.alias;t.push((0,T.ll)`${T.ll.raw(s.joinType)} join${l} ${i?(0,T.ll)`${T.ll.identifier(i)}.`:void 0}${T.ll.identifier(r)}${a&&(0,T.ll)` ${T.ll.identifier(a)}`}${o}`)}else if((0,r.is)(n,T.Ss)){let e=n[N.n].name,i=n[N.n].schema,r=n[N.n].originalName,a=e===r?void 0:s.alias;t.push((0,T.ll)`${T.ll.raw(s.joinType)} join${l} ${i?(0,T.ll)`${T.ll.identifier(i)}.`:void 0}${T.ll.identifier(r)}${a&&(0,T.ll)` ${T.ll.identifier(a)}`}${o}`)}else t.push((0,T.ll)`${T.ll.raw(s.joinType)} join${l} ${n}${o}`);i<e.length-1&&t.push((0,T.ll)` `)}return T.ll.join(t)}buildFromTable(e){if((0,r.is)(e,n.XI)&&e[n.XI.Symbol.IsAlias]){let t=(0,T.ll)`${T.ll.identifier(e[n.XI.Symbol.OriginalName])}`;return e[n.XI.Symbol.Schema]&&(t=(0,T.ll)`${T.ll.identifier(e[n.XI.Symbol.Schema])}.${t}`),(0,T.ll)`${t} ${T.ll.identifier(e[n.XI.Symbol.Name])}`}return e}buildSelectQuery({withList:e,fields:t,fieldsFlat:i,where:s,having:l,table:o,joins:a,orderBy:u,groupBy:h,limit:d,offset:f,lockingClause:g,distinct:p,setOperators:m}){let y,b,w;let S=i??(0,P.He)(t);for(let e of S){let t;if((0,r.is)(e.field,c.V)&&(0,n.Io)(e.field.table)!==((0,r.is)(o,x.n)?o._.alias:(0,r.is)(o,L.w)?o[N.n].name:(0,r.is)(o,T.Xs)?void 0:(0,n.Io)(o))&&(t=e.field.table,!a?.some(({alias:e})=>e===(t[n.XI.Symbol.IsAlias]?n.Io(t):t[n.XI.Symbol.BaseName])))){let t=(0,n.Io)(e.field.table);throw Error(`Your "${e.path.join("->")}" field references a column "${t}"."${e.field.name}", but the table "${t}" is not part of the query! Did you forget to join it?`)}}let v=!a||0===a.length,$=this.buildWithCTE(e);p&&(y=!0===p?(0,T.ll)` distinct`:(0,T.ll)` distinct on (${T.ll.join(p.on,(0,T.ll)`, `)})`);let q=this.buildSelection(S,{isSingleTable:v}),B=this.buildFromTable(o),C=this.buildJoins(a),k=s?(0,T.ll)` where ${s}`:void 0,I=l?(0,T.ll)` having ${l}`:void 0;u&&u.length>0&&(b=(0,T.ll)` order by ${T.ll.join(u,(0,T.ll)`, `)}`),h&&h.length>0&&(w=(0,T.ll)` group by ${T.ll.join(h,(0,T.ll)`, `)}`);let Q="object"==typeof d||"number"==typeof d&&d>=0?(0,T.ll)` limit ${d}`:void 0,j=f?(0,T.ll)` offset ${f}`:void 0,O=T.ll.empty();if(g){let e=(0,T.ll)` for ${T.ll.raw(g.strength)}`;g.config.of&&e.append((0,T.ll)` of ${T.ll.join(Array.isArray(g.config.of)?g.config.of:[g.config.of],(0,T.ll)`, `)}`),g.config.noWait?e.append((0,T.ll)` nowait`):g.config.skipLocked&&e.append((0,T.ll)` skip locked`),O.append(e)}let A=(0,T.ll)`${$}select${y} ${q} from ${B}${C}${k}${w}${I}${b}${Q}${j}${O}`;return m.length>0?this.buildSetOperations(A,m):A}buildSetOperations(e,t){let[i,...s]=t;if(!i)throw Error("Cannot pass undefined values to any set operator");return 0===s.length?this.buildSetOperationQuery({leftSelect:e,setOperator:i}):this.buildSetOperations(this.buildSetOperationQuery({leftSelect:e,setOperator:i}),s)}buildSetOperationQuery({leftSelect:e,setOperator:{type:t,isAll:i,rightSelect:s,limit:n,orderBy:l,offset:o}}){let a;let u=(0,T.ll)`(${e.getSQL()}) `,c=(0,T.ll)`(${s.getSQL()})`;if(l&&l.length>0){let e=[];for(let t of l)if((0,r.is)(t,d.Kl))e.push(T.ll.identifier(t.name));else if((0,r.is)(t,T.Xs)){for(let e=0;e<t.queryChunks.length;e++){let i=t.queryChunks[e];(0,r.is)(i,d.Kl)&&(t.queryChunks[e]=T.ll.identifier(i.name))}e.push((0,T.ll)`${t}`)}else e.push((0,T.ll)`${t}`);a=(0,T.ll)` order by ${T.ll.join(e,(0,T.ll)`, `)} `}let h="object"==typeof n||"number"==typeof n&&n>=0?(0,T.ll)` limit ${n}`:void 0,f=T.ll.raw(`${t} ${i?"all ":""}`),g=o?(0,T.ll)` offset ${o}`:void 0;return(0,T.ll)`${u}${f}${c}${a}${h}${g}`}buildInsertQuery({table:e,values:t,onConflict:i,returning:s,withList:l,select:o,overridingSystemValue_:a}){let u=[],c=Object.entries(e[n.XI.Symbol.Columns]).filter(([e,t])=>!t.shouldDisableInsert()),h=c.map(([,e])=>T.ll.identifier(this.casing.getColumnCasing(e)));if(o)(0,r.is)(t,T.Xs)?u.push(t):u.push(t.getSQL());else for(let[e,i]of(u.push(T.ll.raw("values ")),t.entries())){let s=[];for(let[e,t]of c){let n=i[e];if(void 0===n||(0,r.is)(n,T.Iw)&&void 0===n.value){if(void 0!==t.defaultFn){let e=t.defaultFn(),i=(0,r.is)(e,T.Xs)?e:T.ll.param(e,t);s.push(i)}else if(t.default||void 0===t.onUpdateFn)s.push((0,T.ll)`default`);else{let e=t.onUpdateFn(),i=(0,r.is)(e,T.Xs)?e:T.ll.param(e,t);s.push(i)}}else s.push(n)}u.push(s),e<t.length-1&&u.push((0,T.ll)`, `)}let d=this.buildWithCTE(l),f=T.ll.join(u),g=s?(0,T.ll)` returning ${this.buildSelection(s,{isSingleTable:!0})}`:void 0,p=i?(0,T.ll)` on conflict ${i}`:void 0,m=!0===a?(0,T.ll)`overriding system value `:void 0;return(0,T.ll)`${d}insert into ${e} ${h} ${m}${f}${p}${g}`}buildRefreshMaterializedViewQuery({view:e,concurrently:t,withNoData:i}){let s=t?(0,T.ll)` concurrently`:void 0,r=i?(0,T.ll)` with no data`:void 0;return(0,T.ll)`refresh materialized view${s} ${e}${r}`}prepareTyping(e){return(0,r.is)(e,f.kn)||(0,r.is)(e,g.iX)?"json":(0,r.is)(e,p.Z5)?"decimal":(0,r.is)(e,m.Xd)?"time":(0,r.is)(e,y.KM)||(0,r.is)(e,y.xQ)?"timestamp":(0,r.is)(e,b.qw)||(0,r.is)(e,b.dw)?"date":(0,r.is)(e,w.dL)?"uuid":"none"}sqlToQuery(e,t){return e.toQuery({casing:this.casing,escapeName:this.escapeName,escapeParam:this.escapeParam,escapeString:this.escapeString,prepareTyping:this.prepareTyping,invokeSource:t})}buildRelationalQueryWithoutPK({fullSchema:e,schema:t,tableNamesMap:i,table:l,tableConfig:o,queryConfig:a,tableAlias:u,nestedQueryRelation:d,joinOn:f}){let g,p=[],m,y,b=[],w,P=[];if(!0===a)p=Object.entries(o.columns).map(([e,t])=>({dbKey:t.name,tsKey:e,field:(0,s.ug)(t,u),relationTableTsKey:void 0,isJson:!1,selection:[]}));else{let l=Object.fromEntries(Object.entries(o.columns).map(([e,t])=>[e,(0,s.ug)(t,u)]));if(a.where){let e="function"==typeof a.where?a.where(l,(0,v.mm)()):a.where;w=e&&(0,s.yY)(e,u)}let h=[],d=[];if(a.columns){let e=!1;for(let[t,i]of Object.entries(a.columns))void 0!==i&&t in o.columns&&(e||!0!==i||(e=!0),d.push(t));d.length>0&&(d=e?d.filter(e=>a.columns?.[e]===!0):Object.keys(o.columns).filter(e=>!d.includes(e)))}else d=Object.keys(o.columns);for(let e of d){let t=o.columns[e];h.push({tsKey:e,value:t})}let f=[];if(a.with&&(f=Object.entries(a.with).filter(e=>!!e[1]).map(([e,t])=>({tsKey:e,queryConfig:t,relation:o.relations[e]}))),a.extras)for(let[e,t]of Object.entries("function"==typeof a.extras?a.extras(l,{sql:T.ll}):a.extras))h.push({tsKey:e,value:(0,s.Hs)(t,u)});for(let{tsKey:e,value:t}of h)p.push({dbKey:(0,r.is)(t,T.Xs.Aliased)?t.fieldAlias:o.columns[e].name,tsKey:e,field:(0,r.is)(t,c.V)?(0,s.ug)(t,u):t,relationTableTsKey:void 0,isJson:!1,selection:[]});let g="function"==typeof a.orderBy?a.orderBy(l,(0,v.rl)()):a.orderBy??[];for(let{tsKey:l,queryConfig:o,relation:h}of(Array.isArray(g)||(g=[g]),b=g.map(e=>(0,r.is)(e,c.V)?(0,s.ug)(e,u):(0,s.yY)(e,u)),m=a.limit,y=a.offset,f)){let a=(0,v.W0)(t,i,h),c=i[(0,n.Lf)(h.referencedTable)],d=`${u}_${l}`,f=(0,$.Uo)(...a.fields.map((e,t)=>(0,$.eq)((0,s.ug)(a.references[t],d),(0,s.ug)(e,u)))),g=this.buildRelationalQueryWithoutPK({fullSchema:e,schema:t,tableNamesMap:i,table:e[c],tableConfig:t[c],queryConfig:(0,r.is)(h,v.pD)?!0===o?{limit:1}:{...o,limit:1}:o,tableAlias:d,joinOn:f,nestedQueryRelation:h}),m=(0,T.ll)`${T.ll.identifier(d)}.${T.ll.identifier("data")}`.as(l);P.push({on:(0,T.ll)`true`,table:new x.n(g.sql,{},d),alias:d,joinType:"left",lateral:!0}),p.push({dbKey:l,tsKey:l,field:m,relationTableTsKey:c,isJson:!0,selection:g.selection})}}if(0===p.length)throw new h.n({message:`No fields selected for table "${o.tsName}" ("${u}")`});if(w=(0,$.Uo)(f,w),d){let e=(0,T.ll)`json_build_array(${T.ll.join(p.map(({field:e,tsKey:t,isJson:i})=>i?(0,T.ll)`${T.ll.identifier(`${u}_${t}`)}.${T.ll.identifier("data")}`:(0,r.is)(e,T.Xs.Aliased)?e.sql:e),(0,T.ll)`, `)})`;(0,r.is)(d,v.iv)&&(e=(0,T.ll)`coalesce(json_agg(${e}${b.length>0?(0,T.ll)` order by ${T.ll.join(b,(0,T.ll)`, `)}`:void 0}), '[]'::json)`);let t=[{dbKey:"data",tsKey:"data",field:e.as("data"),isJson:!0,relationTableTsKey:o.tsName,selection:p}];void 0!==m||void 0!==y||b.length>0?(g=this.buildSelectQuery({table:(0,s.oG)(l,u),fields:{},fieldsFlat:[{path:[],field:T.ll.raw("*")}],where:w,limit:m,offset:y,orderBy:b,setOperators:[]}),w=void 0,m=void 0,y=void 0,b=[]):g=(0,s.oG)(l,u),g=this.buildSelectQuery({table:(0,r.is)(g,S.mu)?g:new x.n(g,{},u),fields:{},fieldsFlat:t.map(({field:e})=>({path:[],field:(0,r.is)(e,c.V)?(0,s.ug)(e,u):e})),joins:P,where:w,limit:m,offset:y,orderBy:b,setOperators:[]})}else g=this.buildSelectQuery({table:(0,s.oG)(l,u),fields:{},fieldsFlat:p.map(({field:e})=>({path:[],field:(0,r.is)(e,c.V)?(0,s.ug)(e,u):e})),joins:P,where:w,limit:m,offset:y,orderBy:b,setOperators:[]});return{tableTsKey:o.tsName,sql:g,selection:p}}}},87858:(e,t,i)=>{"use strict";i.d(t,{mu:()=>eK,cJ:()=>eM});var s=i(77799),r=i(65258),n=i(78199),l=i(98199),o=i(56620);class a extends o.p{static [s.i]="PgBigInt53Builder";constructor(e){super(e,"number","PgBigInt53")}build(e){return new u(e,this.config)}}class u extends l.Kl{static [s.i]="PgBigInt53";getSQLType(){return"bigint"}mapFromDriverValue(e){return"number"==typeof e?e:Number(e)}}class c extends o.p{static [s.i]="PgBigInt64Builder";constructor(e){super(e,"bigint","PgBigInt64")}build(e){return new h(e,this.config)}}class h extends l.Kl{static [s.i]="PgBigInt64";getSQLType(){return"bigint"}mapFromDriverValue(e){return BigInt(e)}}function d(e,t){let{name:i,config:s}=(0,n.Ll)(e,t);return"number"===s.mode?new a(i):new c(i)}class f extends l.pe{static [s.i]="PgBigSerial53Builder";constructor(e){super(e,"number","PgBigSerial53"),this.config.hasDefault=!0,this.config.notNull=!0}build(e){return new g(e,this.config)}}class g extends l.Kl{static [s.i]="PgBigSerial53";getSQLType(){return"bigserial"}mapFromDriverValue(e){return"number"==typeof e?e:Number(e)}}class p extends l.pe{static [s.i]="PgBigSerial64Builder";constructor(e){super(e,"bigint","PgBigSerial64"),this.config.hasDefault=!0}build(e){return new m(e,this.config)}}class m extends l.Kl{static [s.i]="PgBigSerial64";getSQLType(){return"bigserial"}mapFromDriverValue(e){return BigInt(e)}}function y(e,t){let{name:i,config:s}=(0,n.Ll)(e,t);return"number"===s.mode?new f(i):new p(i)}var b=i(27390);class w extends l.pe{static [s.i]="PgCharBuilder";constructor(e,t){super(e,"string","PgChar"),this.config.length=t.length,this.config.enumValues=t.enum}build(e){return new S(e,this.config)}}class S extends l.Kl{static [s.i]="PgChar";length=this.config.length;enumValues=this.config.enumValues;getSQLType(){return void 0===this.length?"char":`char(${this.length})`}}function v(e,t={}){let{name:i,config:s}=(0,n.Ll)(e,t);return new w(i,s)}class T extends l.pe{static [s.i]="PgCidrBuilder";constructor(e){super(e,"string","PgCidr")}build(e){return new $(e,this.config)}}class $ extends l.Kl{static [s.i]="PgCidr";getSQLType(){return"cidr"}}function x(e){return new T(e??"")}class P extends l.pe{static [s.i]="PgCustomColumnBuilder";constructor(e,t,i){super(e,"custom","PgCustomColumn"),this.config.fieldConfig=t,this.config.customTypeParams=i}build(e){return new N(e,this.config)}}class N extends l.Kl{static [s.i]="PgCustomColumn";sqlName;mapTo;mapFrom;constructor(e,t){super(e,t),this.sqlName=t.customTypeParams.dataType(t.fieldConfig),this.mapTo=t.customTypeParams.toDriver,this.mapFrom=t.customTypeParams.fromDriver}getSQLType(){return this.sqlName}mapFromDriverValue(e){return"function"==typeof this.mapFrom?this.mapFrom(e):e}mapToDriverValue(e){return"function"==typeof this.mapTo?this.mapTo(e):e}}function L(e){return(t,i)=>{let{name:s,config:r}=(0,n.Ll)(t,i);return new P(s,r,e)}}var q=i(46964);class B extends l.pe{static [s.i]="PgDoublePrecisionBuilder";constructor(e){super(e,"number","PgDoublePrecision")}build(e){return new C(e,this.config)}}class C extends l.Kl{static [s.i]="PgDoublePrecision";getSQLType(){return"double precision"}mapFromDriverValue(e){return"string"==typeof e?Number.parseFloat(e):e}}function k(e){return new B(e??"")}class I extends l.pe{static [s.i]="PgInetBuilder";constructor(e){super(e,"string","PgInet")}build(e){return new Q(e,this.config)}}class Q extends l.Kl{static [s.i]="PgInet";getSQLType(){return"inet"}}function j(e){return new I(e??"")}var O=i(9848);class A extends l.pe{static [s.i]="PgIntervalBuilder";constructor(e,t){super(e,"string","PgInterval"),this.config.intervalConfig=t}build(e){return new D(e,this.config)}}class D extends l.Kl{static [s.i]="PgInterval";fields=this.config.intervalConfig.fields;precision=this.config.intervalConfig.precision;getSQLType(){let e=this.fields?` ${this.fields}`:"",t=this.precision?`(${this.precision})`:"";return`interval${e}${t}`}}function z(e,t={}){let{name:i,config:s}=(0,n.Ll)(e,t);return new A(i,s)}var F=i(32190),V=i(57102);class _ extends l.pe{static [s.i]="PgLineBuilder";constructor(e){super(e,"array","PgLine")}build(e){return new E(e,this.config)}}class E extends l.Kl{static [s.i]="PgLine";getSQLType(){return"line"}mapFromDriverValue(e){let[t,i,s]=e.slice(1,-1).split(",");return[Number.parseFloat(t),Number.parseFloat(i),Number.parseFloat(s)]}mapToDriverValue(e){return`{${e[0]},${e[1]},${e[2]}}`}}class X extends l.pe{static [s.i]="PgLineABCBuilder";constructor(e){super(e,"json","PgLineABC")}build(e){return new K(e,this.config)}}class K extends l.Kl{static [s.i]="PgLineABC";getSQLType(){return"line"}mapFromDriverValue(e){let[t,i,s]=e.slice(1,-1).split(",");return{a:Number.parseFloat(t),b:Number.parseFloat(i),c:Number.parseFloat(s)}}mapToDriverValue(e){return`{${e.a},${e.b},${e.c}}`}}function M(e,t){let{name:i,config:s}=(0,n.Ll)(e,t);return s?.mode&&"tuple"!==s.mode?new X(i):new _(i)}class U extends l.pe{static [s.i]="PgMacaddrBuilder";constructor(e){super(e,"string","PgMacaddr")}build(e){return new J(e,this.config)}}class J extends l.Kl{static [s.i]="PgMacaddr";getSQLType(){return"macaddr"}}function H(e){return new U(e??"")}class R extends l.pe{static [s.i]="PgMacaddr8Builder";constructor(e){super(e,"string","PgMacaddr8")}build(e){return new W(e,this.config)}}class W extends l.Kl{static [s.i]="PgMacaddr8";getSQLType(){return"macaddr8"}}function G(e){return new R(e??"")}var Y=i(70009);class Z extends l.pe{static [s.i]="PgPointTupleBuilder";constructor(e){super(e,"array","PgPointTuple")}build(e){return new ee(e,this.config)}}class ee extends l.Kl{static [s.i]="PgPointTuple";getSQLType(){return"point"}mapFromDriverValue(e){if("string"==typeof e){let[t,i]=e.slice(1,-1).split(",");return[Number.parseFloat(t),Number.parseFloat(i)]}return[e.x,e.y]}mapToDriverValue(e){return`(${e[0]},${e[1]})`}}class et extends l.pe{static [s.i]="PgPointObjectBuilder";constructor(e){super(e,"json","PgPointObject")}build(e){return new ei(e,this.config)}}class ei extends l.Kl{static [s.i]="PgPointObject";getSQLType(){return"point"}mapFromDriverValue(e){if("string"==typeof e){let[t,i]=e.slice(1,-1).split(",");return{x:Number.parseFloat(t),y:Number.parseFloat(i)}}return e}mapToDriverValue(e){return`(${e.x},${e.y})`}}function es(e,t){let{name:i,config:s}=(0,n.Ll)(e,t);return s?.mode&&"tuple"!==s.mode?new et(i):new Z(i)}function er(e,t){let i=new DataView(new ArrayBuffer(8));for(let s=0;s<8;s++)i.setUint8(s,e[t+s]);return i.getFloat64(0,!0)}function en(e){let t=function(e){let t=[];for(let i=0;i<e.length;i+=2)t.push(Number.parseInt(e.slice(i,i+2),16));return new Uint8Array(t)}(e),i=0,s=t[0];i+=1;let r=new DataView(t.buffer),n=r.getUint32(i,1===s);if(i+=4,0x20000000&n&&(r.getUint32(i,1===s),i+=4),(65535&n)==1){let e=er(t,i),s=er(t,i+=8);return i+=8,[e,s]}throw Error("Unsupported geometry type")}class el extends l.pe{static [s.i]="PgGeometryBuilder";constructor(e){super(e,"array","PgGeometry")}build(e){return new eo(e,this.config)}}class eo extends l.Kl{static [s.i]="PgGeometry";getSQLType(){return"geometry(point)"}mapFromDriverValue(e){return en(e)}mapToDriverValue(e){return`point(${e[0]} ${e[1]})`}}class ea extends l.pe{static [s.i]="PgGeometryObjectBuilder";constructor(e){super(e,"json","PgGeometryObject")}build(e){return new eu(e,this.config)}}class eu extends l.Kl{static [s.i]="PgGeometryObject";getSQLType(){return"geometry(point)"}mapFromDriverValue(e){let t=en(e);return{x:t[0],y:t[1]}}mapToDriverValue(e){return`point(${e.x} ${e.y})`}}function ec(e,t){let{name:i,config:s}=(0,n.Ll)(e,t);return s?.mode&&"tuple"!==s.mode?new ea(i):new el(i)}class eh extends l.pe{static [s.i]="PgRealBuilder";constructor(e,t){super(e,"number","PgReal"),this.config.length=t}build(e){return new ed(e,this.config)}}class ed extends l.Kl{static [s.i]="PgReal";constructor(e,t){super(e,t)}getSQLType(){return"real"}mapFromDriverValue=e=>"string"==typeof e?Number.parseFloat(e):e}function ef(e){return new eh(e??"")}class eg extends l.pe{static [s.i]="PgSerialBuilder";constructor(e){super(e,"number","PgSerial"),this.config.hasDefault=!0,this.config.notNull=!0}build(e){return new ep(e,this.config)}}class ep extends l.Kl{static [s.i]="PgSerial";getSQLType(){return"serial"}}function em(e){return new eg(e??"")}class ey extends o.p{static [s.i]="PgSmallIntBuilder";constructor(e){super(e,"number","PgSmallInt")}build(e){return new eb(e,this.config)}}class eb extends l.Kl{static [s.i]="PgSmallInt";getSQLType(){return"smallint"}mapFromDriverValue=e=>"string"==typeof e?Number(e):e}function ew(e){return new ey(e??"")}class eS extends l.pe{static [s.i]="PgSmallSerialBuilder";constructor(e){super(e,"number","PgSmallSerial"),this.config.hasDefault=!0,this.config.notNull=!0}build(e){return new ev(e,this.config)}}class ev extends l.Kl{static [s.i]="PgSmallSerial";getSQLType(){return"smallserial"}}function eT(e){return new eS(e??"")}var e$=i(44799),ex=i(90293),eP=i(32590),eN=i(60011);class eL extends l.pe{static [s.i]="PgVarcharBuilder";constructor(e,t){super(e,"string","PgVarchar"),this.config.length=t.length,this.config.enumValues=t.enum}build(e){return new eq(e,this.config)}}class eq extends l.Kl{static [s.i]="PgVarchar";length=this.config.length;enumValues=this.config.enumValues;getSQLType(){return void 0===this.length?"varchar":`varchar(${this.length})`}}function eB(e,t={}){let{name:i,config:s}=(0,n.Ll)(e,t);return new eL(i,s)}class eC extends l.pe{static [s.i]="PgBinaryVectorBuilder";constructor(e,t){super(e,"string","PgBinaryVector"),this.config.dimensions=t.dimensions}build(e){return new ek(e,this.config)}}class ek extends l.Kl{static [s.i]="PgBinaryVector";dimensions=this.config.dimensions;getSQLType(){return`bit(${this.dimensions})`}}function eI(e,t){let{name:i,config:s}=(0,n.Ll)(e,t);return new eC(i,s)}class eQ extends l.pe{static [s.i]="PgHalfVectorBuilder";constructor(e,t){super(e,"array","PgHalfVector"),this.config.dimensions=t.dimensions}build(e){return new ej(e,this.config)}}class ej extends l.Kl{static [s.i]="PgHalfVector";dimensions=this.config.dimensions;getSQLType(){return`halfvec(${this.dimensions})`}mapToDriverValue(e){return JSON.stringify(e)}mapFromDriverValue(e){return e.slice(1,-1).split(",").map(e=>Number.parseFloat(e))}}function eO(e,t){let{name:i,config:s}=(0,n.Ll)(e,t);return new eQ(i,s)}class eA extends l.pe{static [s.i]="PgSparseVectorBuilder";constructor(e,t){super(e,"string","PgSparseVector"),this.config.dimensions=t.dimensions}build(e){return new eD(e,this.config)}}class eD extends l.Kl{static [s.i]="PgSparseVector";dimensions=this.config.dimensions;getSQLType(){return`sparsevec(${this.dimensions})`}}function ez(e,t){let{name:i,config:s}=(0,n.Ll)(e,t);return new eA(i,s)}class eF extends l.pe{static [s.i]="PgVectorBuilder";constructor(e,t){super(e,"array","PgVector"),this.config.dimensions=t.dimensions}build(e){return new eV(e,this.config)}}class eV extends l.Kl{static [s.i]="PgVector";dimensions=this.config.dimensions;getSQLType(){return`vector(${this.dimensions})`}mapToDriverValue(e){return JSON.stringify(e)}mapFromDriverValue(e){return e.slice(1,-1).split(",").map(e=>Number.parseFloat(e))}}function e_(e,t){let{name:i,config:s}=(0,n.Ll)(e,t);return new eF(i,s)}let eE=Symbol.for("drizzle:PgInlineForeignKeys"),eX=Symbol.for("drizzle:EnableRLS");class eK extends r.XI{static [s.i]="PgTable";static Symbol=Object.assign({},r.XI.Symbol,{InlineForeignKeys:eE,EnableRLS:eX});[eE]=[];[eX]=!1;[r.XI.Symbol.ExtraConfigBuilder]=void 0;[r.XI.Symbol.ExtraConfigColumns]={}}let eM=(e,t,i)=>(function(e,t,i,s,n=e){let l=new eK(e,s,n),o="function"==typeof t?t({bigint:d,bigserial:y,boolean:b.zM,char:v,cidr:x,customType:L,date:q.p6,doublePrecision:k,inet:j,integer:O.nd,interval:z,json:F.Pq,jsonb:V.Fx,line:M,macaddr:H,macaddr8:G,numeric:Y.sH,point:es,geometry:ec,real:ef,serial:em,smallint:ew,smallserial:eT,text:e$.Qq,time:ex.kB,timestamp:eP.vE,uuid:eN.uR,varchar:eB,bit:eI,halfvec:eO,sparsevec:ez,vector:e_}):t,a=Object.fromEntries(Object.entries(o).map(([e,t])=>{t.setName(e);let i=t.build(l);return l[eE].push(...t.buildForeignKeys(i,l)),[e,i]})),u=Object.fromEntries(Object.entries(o).map(([e,t])=>(t.setName(e),[e,t.buildExtraConfigColumn(l)]))),c=Object.assign(l,a);return c[r.XI.Symbol.Columns]=a,c[r.XI.Symbol.ExtraConfigColumns]=u,i&&(c[eK.Symbol.ExtraConfigBuilder]=i),Object.assign(c,{enableRLS:()=>(c[eK.Symbol.EnableRLS]=!0,c)})})(e,t,i,void 0)},53375:(e,t,i)=>{"use strict";i.d(t,{w:()=>n});var s=i(77799),r=i(91990);class n extends r.Ss{static [s.i]="PgViewBase"}},10072:(e,t,i)=>{"use strict";i.a(e,async(e,s)=>{try{i.d(t,{f:()=>g});var r=i(2113),n=i(77799),l=i(18992),o=i(90358),a=i(19719),u=i(95937),c=i(78199),h=i(49789),d=e([r]);r=(d.then?(await d)():d)[0];class p extends o.p{static [n.i]="PostgresJsDatabase"}function f(e,t={}){let i,s;let r=e=>e;for(let t of["1184","1082","1083","1114","1182","1185","1115","1231"])e.options.parsers[t]=r,e.options.serializers[t]=r;e.options.serializers["114"]=r,e.options.serializers["3802"]=r;let n=new a.s({casing:t.casing});if(!0===t.logger?i=new l.w:!1!==t.logger&&(i=t.logger),t.schema){let e=(0,u._k)(t.schema,u.DZ);s={fullSchema:t.schema,schema:e.tables,tableNamesMap:e.tableNamesMap}}let o=new h.L0(e,n,s,{logger:i}),c=new p(n,o,s);return c.$client=e,c}function g(...e){if("string"==typeof e[0]){let t=(0,r.default)(e[0]);return f(t,e[1])}if((0,c.Lq)(e[0])){let{connection:t,client:i,...s}=e[0];if(i)return f(i,s);if("object"==typeof t&&void 0!==t.url){let{url:e,...i}=t,n=(0,r.default)(e,i);return f(n,s)}let n=(0,r.default)(t);return f(n,s)}return f(e[0],e[1])}(g||(g={})).mock=function(e){return f({options:{parsers:{},serializers:{}}},e)},s()}catch(e){s(e)}})},49789:(e,t,i)=>{"use strict";i.d(t,{L0:()=>g});var s=i(77799),r=i(18992),n=i(28611),l=i(91990),o=i(55731),a=i(90358);class u{constructor(e){this.query=e}authToken;getQuery(){return this.query}mapResult(e,t){return e}setToken(e){return this.authToken=e,this}static [s.i]="PgPreparedQuery";joinsNotNullableMap}class c{constructor(e){this.dialect=e}static [s.i]="PgSession";execute(e,t){return o.k.startActiveSpan("drizzle.operation",()=>o.k.startActiveSpan("drizzle.prepareQuery",()=>this.prepareQuery(this.dialect.sqlToQuery(e),void 0,void 0,!1)).setToken(t).execute(void 0,t))}all(e){return this.prepareQuery(this.dialect.sqlToQuery(e),void 0,void 0,!1).all()}async count(e,t){return Number((await this.execute(e,t))[0].count)}}class h extends a.p{constructor(e,t,i,s=0){super(e,t,i),this.schema=i,this.nestedIndex=s}static [s.i]="PgTransaction";rollback(){throw new n.j}getTransactionConfigSQL(e){let t=[];return e.isolationLevel&&t.push(`isolation level ${e.isolationLevel}`),e.accessMode&&t.push(e.accessMode),"boolean"==typeof e.deferrable&&t.push(e.deferrable?"deferrable":"not deferrable"),l.ll.raw(t.join(" "))}setTransaction(e){return this.session.execute((0,l.ll)`set transaction ${this.getTransactionConfigSQL(e)}`)}}var d=i(78199);class f extends u{constructor(e,t,i,s,r,n,l){super({sql:t,params:i}),this.client=e,this.queryString=t,this.params=i,this.logger=s,this.fields=r,this._isResponseInArrayMode=n,this.customResultMapper=l}static [s.i]="PostgresJsPreparedQuery";async execute(e={}){return o.k.startActiveSpan("drizzle.execute",async t=>{let i=(0,l.Ct)(this.params,e);t?.setAttributes({"drizzle.query.text":this.queryString,"drizzle.query.params":JSON.stringify(i)}),this.logger.logQuery(this.queryString,i);let{fields:s,queryString:r,client:n,joinsNotNullableMap:a,customResultMapper:u}=this;if(!s&&!u)return o.k.startActiveSpan("drizzle.driver.execute",()=>n.unsafe(r,i));let c=await o.k.startActiveSpan("drizzle.driver.execute",()=>(t?.setAttributes({"drizzle.query.text":r,"drizzle.query.params":JSON.stringify(i)}),n.unsafe(r,i).values()));return o.k.startActiveSpan("drizzle.mapResponse",()=>u?u(c):c.map(e=>(0,d.a6)(s,e,a)))})}all(e={}){return o.k.startActiveSpan("drizzle.execute",async t=>{let i=(0,l.Ct)(this.params,e);return t?.setAttributes({"drizzle.query.text":this.queryString,"drizzle.query.params":JSON.stringify(i)}),this.logger.logQuery(this.queryString,i),o.k.startActiveSpan("drizzle.driver.execute",()=>(t?.setAttributes({"drizzle.query.text":this.queryString,"drizzle.query.params":JSON.stringify(i)}),this.client.unsafe(this.queryString,i)))})}isResponseInArrayMode(){return this._isResponseInArrayMode}}class g extends c{constructor(e,t,i,s={}){super(t),this.client=e,this.schema=i,this.options=s,this.logger=s.logger??new r.Pv}static [s.i]="PostgresJsSession";logger;prepareQuery(e,t,i,s,r){return new f(this.client,e.sql,e.params,this.logger,t,s,r)}query(e,t){return this.logger.logQuery(e,t),this.client.unsafe(e,t).values()}queryObjects(e,t){return this.client.unsafe(e,t)}transaction(e,t){return this.client.begin(async i=>{let s=new g(i,this.dialect,this.schema,this.options),r=new p(this.dialect,s,this.schema);return t&&await r.setTransaction(t),e(r)})}}class p extends h{constructor(e,t,i,s=0){super(e,t,i,s),this.session=t}static [s.i]="PostgresJsTransaction";transaction(e){return this.session.client.savepoint(t=>{let i=new g(t,this.dialect,this.schema,this.session.options);return e(new p(this.dialect,i,this.schema))})}}},95937:(e,t,i)=>{"use strict";i.d(t,{iv:()=>p,pD:()=>g,DZ:()=>S,_k:()=>b,mm:()=>m,rl:()=>y,I$:()=>function e(t,i,s,l,o=e=>e){let a={};for(let[u,c]of l.entries())if(c.isJson){let r=i.relations[c.tsKey],l=s[u],h="string"==typeof l?JSON.parse(l):l;a[c.tsKey]=(0,n.is)(r,g)?h&&e(t,t[c.relationTableTsKey],h,c.selection,o):h.map(i=>e(t,t[c.relationTableTsKey],i,c.selection,o))}else{let e;let t=o(s[u]),i=c.field;e=(0,n.is)(i,r.V)?i:(0,n.is)(i,h.Xs)?i.decoder:i.sql.decoder,a[c.tsKey]=null===t?null:e.mapFromDriverValue(t)}return a},W0:()=>w});var s=i(65258),r=i(7078),n=i(77799),l=i(87858);class o{static [n.i]="PgPrimaryKeyBuilder";columns;name;constructor(e,t){this.columns=e,this.name=t}build(e){return new a(e,this.columns,this.name)}}class a{constructor(e,t,i){this.table=e,this.columns=t,this.name=i}static [n.i]="PgPrimaryKey";columns;name;getName(){return this.name??`${this.table[l.mu.Symbol.Name]}_${this.columns.map(e=>e.name).join("_")}_pk`}}var u=i(47579),c=i(92489),h=i(91990);class d{constructor(e,t,i){this.sourceTable=e,this.referencedTable=t,this.relationName=i,this.referencedTableName=t[s.XI.Symbol.Name]}static [n.i]="Relation";referencedTableName;fieldName}class f{constructor(e,t){this.table=e,this.config=t}static [n.i]="Relations"}class g extends d{constructor(e,t,i,s){super(e,t,i?.relationName),this.config=i,this.isNullable=s}static [n.i]="One";withFieldName(e){let t=new g(this.sourceTable,this.referencedTable,this.config,this.isNullable);return t.fieldName=e,t}}class p extends d{constructor(e,t,i){super(e,t,i?.relationName),this.config=i}static [n.i]="Many";withFieldName(e){let t=new p(this.sourceTable,this.referencedTable,this.config);return t.fieldName=e,t}}function m(){return{and:u.Uo,between:u.Tq,eq:u.eq,exists:u.t2,gt:u.gt,gte:u.RO,ilike:u.B3,inArray:u.RV,isNull:u.kZ,isNotNull:u.Pe,like:u.mj,lt:u.lt,lte:u.wJ,ne:u.ne,not:u.AU,notBetween:u.o8,notExists:u.KJ,notLike:u.RK,notIlike:u.q1,notInArray:u.KL,or:u.or,sql:h.ll}}function y(){return{sql:h.ll,asc:c.Y,desc:c.i}}function b(e,t){1===Object.keys(e).length&&"default"in e&&!(0,n.is)(e.default,s.XI)&&(e=e.default);let i={},r={},l={};for(let[a,u]of Object.entries(e))if((0,n.is)(u,s.XI)){let e=(0,s.Lf)(u),t=r[e];for(let r of(i[e]=a,l[a]={tsName:a,dbName:u[s.XI.Symbol.Name],schema:u[s.XI.Symbol.Schema],columns:u[s.XI.Symbol.Columns],relations:t?.relations??{},primaryKey:t?.primaryKey??[]},Object.values(u[s.XI.Symbol.Columns])))r.primary&&l[a].primaryKey.push(r);let c=u[s.XI.Symbol.ExtraConfigBuilder]?.(u[s.XI.Symbol.ExtraConfigColumns]);if(c)for(let e of Object.values(c))(0,n.is)(e,o)&&l[a].primaryKey.push(...e.columns)}else if((0,n.is)(u,f)){let e;let n=(0,s.Lf)(u.table),o=i[n];for(let[i,s]of Object.entries(u.config(t(u.table))))if(o){let t=l[o];t.relations[i]=s,e&&t.primaryKey.push(...e)}else n in r||(r[n]={relations:{},primaryKey:e}),r[n].relations[i]=s}return{tables:l,tableNamesMap:i}}function w(e,t,i){if((0,n.is)(i,g)&&i.config)return{fields:i.config.fields,references:i.config.references};let r=t[(0,s.Lf)(i.referencedTable)];if(!r)throw Error(`Table "${i.referencedTable[s.XI.Symbol.Name]}" not found in schema`);let l=e[r];if(!l)throw Error(`Table "${r}" not found in schema`);let o=i.sourceTable,a=t[(0,s.Lf)(o)];if(!a)throw Error(`Table "${o[s.XI.Symbol.Name]}" not found in schema`);let u=[];for(let e of Object.values(l.relations))(i.relationName&&i!==e&&e.relationName===i.relationName||!i.relationName&&e.referencedTable===i.sourceTable)&&u.push(e);if(u.length>1)throw i.relationName?Error(`There are multiple relations with name "${i.relationName}" in table "${r}"`):Error(`There are multiple relations between "${r}" and "${i.sourceTable[s.XI.Symbol.Name]}". Please specify relation name`);if(u[0]&&(0,n.is)(u[0],g)&&u[0].config)return{fields:u[0].config.references,references:u[0].config.fields};throw Error(`There is not enough information to infer relation "${a}.${i.fieldName}"`)}function S(e){return{one:function(t,i){return new g(e,t,i,i?.fields.reduce((e,t)=>e&&t.notNull,!0)??!1)},many:function(t,i){return new p(e,t,i)}}}},47579:(e,t,i)=>{"use strict";i.d(t,{AU:()=>d,B3:()=>L,KJ:()=>T,KL:()=>b,Pe:()=>S,RK:()=>N,RO:()=>g,RV:()=>y,Tq:()=>$,Uo:()=>c,eq:()=>a,gt:()=>f,kZ:()=>w,lt:()=>p,mj:()=>P,ne:()=>u,o8:()=>x,or:()=>h,q1:()=>q,t2:()=>v,wJ:()=>m});var s=i(7078),r=i(77799),n=i(65258),l=i(91990);function o(e,t){return!(0,l.eG)(t)||(0,l.qt)(e)||(0,r.is)(e,l.Iw)||(0,r.is)(e,l.Or)||(0,r.is)(e,s.V)||(0,r.is)(e,n.XI)||(0,r.is)(e,l.Ss)?e:new l.Iw(e,t)}let a=(e,t)=>(0,l.ll)`${e} = ${o(t,e)}`,u=(e,t)=>(0,l.ll)`${e} <> ${o(t,e)}`;function c(...e){let t=e.filter(e=>void 0!==e);return 0===t.length?void 0:new l.Xs(1===t.length?t:[new l.DJ("("),l.ll.join(t,new l.DJ(" and ")),new l.DJ(")")])}function h(...e){let t=e.filter(e=>void 0!==e);return 0===t.length?void 0:new l.Xs(1===t.length?t:[new l.DJ("("),l.ll.join(t,new l.DJ(" or ")),new l.DJ(")")])}function d(e){return(0,l.ll)`not ${e}`}let f=(e,t)=>(0,l.ll)`${e} > ${o(t,e)}`,g=(e,t)=>(0,l.ll)`${e} >= ${o(t,e)}`,p=(e,t)=>(0,l.ll)`${e} < ${o(t,e)}`,m=(e,t)=>(0,l.ll)`${e} <= ${o(t,e)}`;function y(e,t){return Array.isArray(t)?0===t.length?(0,l.ll)`false`:(0,l.ll)`${e} in ${t.map(t=>o(t,e))}`:(0,l.ll)`${e} in ${o(t,e)}`}function b(e,t){return Array.isArray(t)?0===t.length?(0,l.ll)`true`:(0,l.ll)`${e} not in ${t.map(t=>o(t,e))}`:(0,l.ll)`${e} not in ${o(t,e)}`}function w(e){return(0,l.ll)`${e} is null`}function S(e){return(0,l.ll)`${e} is not null`}function v(e){return(0,l.ll)`exists ${e}`}function T(e){return(0,l.ll)`not exists ${e}`}function $(e,t,i){return(0,l.ll)`${e} between ${o(t,e)} and ${o(i,e)}`}function x(e,t,i){return(0,l.ll)`${e} not between ${o(t,e)} and ${o(i,e)}`}function P(e,t){return(0,l.ll)`${e} like ${t}`}function N(e,t){return(0,l.ll)`${e} not like ${t}`}function L(e,t){return(0,l.ll)`${e} ilike ${t}`}function q(e,t){return(0,l.ll)`${e} not ilike ${t}`}},92489:(e,t,i)=>{"use strict";i.d(t,{Y:()=>r,i:()=>n});var s=i(91990);function r(e){return(0,s.ll)`${e} asc`}function n(e){return(0,s.ll)`${e} desc`}},91990:(e,t,i)=>{"use strict";i.d(t,{Iw:()=>$,Or:()=>P,Xs:()=>b,DJ:()=>y,Ss:()=>q,Ct:()=>N,eG:()=>S,qt:()=>m,ll:()=>x});var s=i(77799),r=i(98199);class n extends r.pe{static [s.i]="PgEnumObjectColumnBuilder";constructor(e,t){super(e,"string","PgEnumObjectColumn"),this.config.enum=t}build(e){return new l(e,this.config)}}class l extends r.Kl{static [s.i]="PgEnumObjectColumn";enum;enumValues=this.config.enum.enumValues;constructor(e,t){super(e,t),this.enum=t.enum}getSQLType(){return this.enum.enumName}}let o=Symbol.for("drizzle:isPgEnum");class a extends r.pe{static [s.i]="PgEnumColumnBuilder";constructor(e,t){super(e,"string","PgEnumColumn"),this.config.enum=t}build(e){return new u(e,this.config)}}class u extends r.Kl{static [s.i]="PgEnumColumn";enum=this.config.enum;enumValues=this.config.enum.enumValues;constructor(e,t){super(e,t),this.enum=t.enum}getSQLType(){return this.enum.enumName}}var c=i(73322),h=i(55731),d=i(18611),f=i(7078),g=i(65258);class p{static [s.i]=null}function m(e){return null!=e&&"function"==typeof e.getSQL}class y{static [s.i]="StringChunk";value;constructor(e){this.value=Array.isArray(e)?e:[e]}getSQL(){return new b([this])}}class b{constructor(e){this.queryChunks=e}static [s.i]="SQL";decoder=v;shouldInlineParams=!1;append(e){return this.queryChunks.push(...e.queryChunks),this}toQuery(e){return h.k.startActiveSpan("drizzle.buildSQL",t=>{let i=this.buildQueryFromSourceParams(this.queryChunks,e);return t?.setAttributes({"drizzle.query.text":i.sql,"drizzle.query.params":JSON.stringify(i.params)}),i})}buildQueryFromSourceParams(e,t){let i=Object.assign({},t,{inlineParams:t.inlineParams||this.shouldInlineParams,paramStartIndex:t.paramStartIndex||{value:0}}),{casing:r,escapeName:n,escapeParam:l,prepareTyping:a,inlineParams:u,paramStartIndex:h}=i;return function(e){let t={sql:"",params:[]};for(let i of e)t.sql+=i.sql,t.params.push(...i.params),i.typings?.length&&(t.typings||(t.typings=[]),t.typings.push(...i.typings));return t}(e.map(e=>{if((0,s.is)(e,y))return{sql:e.value.join(""),params:[]};if((0,s.is)(e,w))return{sql:n(e.value),params:[]};if(void 0===e)return{sql:"",params:[]};if(Array.isArray(e)){let t=[new y("(")];for(let[i,s]of e.entries())t.push(s),i<e.length-1&&t.push(new y(", "));return t.push(new y(")")),this.buildQueryFromSourceParams(t,i)}if((0,s.is)(e,b))return this.buildQueryFromSourceParams(e.queryChunks,{...i,inlineParams:u||e.shouldInlineParams});if((0,s.is)(e,g.XI)){let t=e[g.XI.Symbol.Schema],i=e[g.XI.Symbol.Name];return{sql:void 0===t||e[g.HE]?n(i):n(t)+"."+n(i),params:[]}}if((0,s.is)(e,f.V)){let i=r.getColumnCasing(e);if("indexes"===t.invokeSource)return{sql:n(i),params:[]};let s=e.table[g.XI.Symbol.Schema];return{sql:e.table[g.HE]||void 0===s?n(e.table[g.XI.Symbol.Name])+"."+n(i):n(s)+"."+n(e.table[g.XI.Symbol.Name])+"."+n(i),params:[]}}if((0,s.is)(e,q)){let t=e[d.n].schema,i=e[d.n].name;return{sql:void 0===t||e[d.n].isAlias?n(i):n(t)+"."+n(i),params:[]}}if((0,s.is)(e,$)){if((0,s.is)(e.value,P))return{sql:l(h.value++,e),params:[e],typings:["none"]};let t=null===e.value?null:e.encoder.mapToDriverValue(e.value);if((0,s.is)(t,b))return this.buildQueryFromSourceParams([t],i);if(u)return{sql:this.mapInlineParam(t,i),params:[]};let r=["none"];return a&&(r=[a(e.encoder)]),{sql:l(h.value++,t),params:[t],typings:r}}return(0,s.is)(e,P)?{sql:l(h.value++,e),params:[e],typings:["none"]}:(0,s.is)(e,b.Aliased)&&void 0!==e.fieldAlias?{sql:n(e.fieldAlias),params:[]}:(0,s.is)(e,c.n)?e._.isWith?{sql:n(e._.alias),params:[]}:this.buildQueryFromSourceParams([new y("("),e._.sql,new y(") "),new w(e._.alias)],i):e&&"function"==typeof e&&o in e&&!0===e[o]?e.schema?{sql:n(e.schema)+"."+n(e.enumName),params:[]}:{sql:n(e.enumName),params:[]}:m(e)?e.shouldOmitSQLParens?.()?this.buildQueryFromSourceParams([e.getSQL()],i):this.buildQueryFromSourceParams([new y("("),e.getSQL(),new y(")")],i):u?{sql:this.mapInlineParam(e,i),params:[]}:{sql:l(h.value++,e),params:[e],typings:["none"]}}))}mapInlineParam(e,{escapeString:t}){if(null===e)return"null";if("number"==typeof e||"boolean"==typeof e)return e.toString();if("string"==typeof e)return t(e);if("object"==typeof e){let i=e.toString();return"[object Object]"===i?t(JSON.stringify(e)):t(i)}throw Error("Unexpected param value: "+e)}getSQL(){return this}as(e){return void 0===e?this:new b.Aliased(this,e)}mapWith(e){return this.decoder="function"==typeof e?{mapFromDriverValue:e}:e,this}inlineParams(){return this.shouldInlineParams=!0,this}if(e){return e?this:void 0}}class w{constructor(e){this.value=e}static [s.i]="Name";brand;getSQL(){return new b([this])}}function S(e){return"object"==typeof e&&null!==e&&"mapToDriverValue"in e&&"function"==typeof e.mapToDriverValue}let v={mapFromDriverValue:e=>e},T={mapToDriverValue:e=>e};({...v,...T});class ${constructor(e,t=T){this.value=e,this.encoder=t}static [s.i]="Param";brand;getSQL(){return new b([this])}}function x(e,...t){let i=[];for(let[s,r]of((t.length>0||e.length>0&&""!==e[0])&&i.push(new y(e[0])),t.entries()))i.push(r,new y(e[s+1]));return new b(i)}(e=>{e.empty=function(){return new b([])},e.fromList=function(e){return new b(e)},e.raw=function(e){return new b([new y(e)])},e.join=function(e,t){let i=[];for(let[s,r]of e.entries())s>0&&void 0!==t&&i.push(t),i.push(r);return new b(i)},e.identifier=function(e){return new w(e)},e.placeholder=function(e){return new P(e)},e.param=function(e,t){return new $(e,t)}})(x||(x={})),(e=>{class t{constructor(e,t){this.sql=e,this.fieldAlias=t}static [s.i]="SQL.Aliased";isSelectionField=!1;getSQL(){return this.sql}clone(){return new t(this.sql,this.fieldAlias)}}e.Aliased=t})(b||(b={}));class P{constructor(e){this.name=e}static [s.i]="Placeholder";getSQL(){return new b([this])}}function N(e,t){return e.map(e=>{if((0,s.is)(e,P)){if(!(e.name in t))throw Error(`No value for placeholder "${e.name}" was provided`);return t[e.name]}if((0,s.is)(e,$)&&(0,s.is)(e.value,P)){if(!(e.value.name in t))throw Error(`No value for placeholder "${e.value.name}" was provided`);return e.encoder.mapToDriverValue(t[e.value.name])}return e})}let L=Symbol.for("drizzle:IsDrizzleView");class q{static [s.i]="View";[d.n];[L]=!0;constructor({name:e,schema:t,selectedFields:i,query:s}){this[d.n]={name:e,originalName:e,schema:t,selectedFields:i,query:s,isExisting:!s,isAlias:!1}}getSQL(){return new b([this])}}f.V.prototype.getSQL=function(){return new b([this])},g.XI.prototype.getSQL=function(){return new b([this])},c.n.prototype.getSQL=function(){return new b([this])}},73322:(e,t,i)=>{"use strict";i.d(t,{J:()=>n,n:()=>r});var s=i(77799);class r{static [s.i]="Subquery";constructor(e,t,i,s=!1){this._={brand:"Subquery",sql:e,selectedFields:t,alias:i,isWith:s}}}class n extends r{static [s.i]="WithSubquery"}},65258:(e,t,i)=>{"use strict";i.d(t,{HE:()=>c,Io:()=>g,Lf:()=>p,XI:()=>f,e:()=>l});var s=i(77799),r=i(25957);let n=Symbol.for("drizzle:Schema"),l=Symbol.for("drizzle:Columns"),o=Symbol.for("drizzle:ExtraConfigColumns"),a=Symbol.for("drizzle:OriginalName"),u=Symbol.for("drizzle:BaseName"),c=Symbol.for("drizzle:IsAlias"),h=Symbol.for("drizzle:ExtraConfigBuilder"),d=Symbol.for("drizzle:IsDrizzleTable");class f{static [s.i]="Table";static Symbol={Name:r.E,Schema:n,OriginalName:a,Columns:l,ExtraConfigColumns:o,BaseName:u,IsAlias:c,ExtraConfigBuilder:h};[r.E];[a];[n];[l];[o];[u];[c]=!1;[d]=!0;[h]=void 0;constructor(e,t,i){this[r.E]=this[a]=e,this[n]=t,this[u]=i}}function g(e){return e[r.E]}function p(e){return`${e[n]??"public"}.${e[r.E]}`}},25957:(e,t,i)=>{"use strict";i.d(t,{E:()=>s});let s=Symbol.for("drizzle:Name")},53908:(e,t,i)=>{"use strict";function s(e,...t){return e(...t)}i.d(t,{i:()=>s})},55731:(e,t,i)=>{"use strict";let s,r;i.d(t,{k:()=>l});var n=i(53908);let l={startActiveSpan:(e,t)=>s?(r||(r=s.trace.getTracer("drizzle-orm","0.43.1")),(0,n.i)((i,s)=>s.startActiveSpan(e,e=>{try{return t(e)}catch(t){throw e.setStatus({code:i.SpanStatusCode.ERROR,message:t instanceof Error?t.message:"Unknown error"}),t}finally{e.end()}}),s,r)):t()}},78199:(e,t,i)=>{"use strict";i.d(t,{DV:()=>c,He:()=>function e(t,i){return Object.entries(t).reduce((t,[l,a])=>{if("string"!=typeof l)return t;let u=i?[...i,l]:[l];return(0,r.is)(a,s.V)||(0,r.is)(a,n.Xs)||(0,r.is)(a,n.Xs.Aliased)?t.push({path:u,field:a}):(0,r.is)(a,o.XI)?t.push(...e(a[o.XI.Symbol.Columns],u)):t.push(...e(a,u)),t},[])},Ll:()=>p,Lq:()=>m,XJ:()=>d,YD:()=>f,a6:()=>u,q:()=>h,zN:()=>g});var s=i(7078),r=i(77799),n=i(91990),l=i(73322),o=i(65258),a=i(18611);function u(e,t,i){let l={},a=e.reduce((e,{path:a,field:u},c)=>{let h;h=(0,r.is)(u,s.V)?u:(0,r.is)(u,n.Xs)?u.decoder:u.sql.decoder;let d=e;for(let[e,n]of a.entries())if(e<a.length-1)n in d||(d[n]={}),d=d[n];else{let e=t[c],f=d[n]=null===e?null:h.mapFromDriverValue(e);if(i&&(0,r.is)(u,s.V)&&2===a.length){let e=a[0];e in l?"string"==typeof l[e]&&l[e]!==(0,o.Io)(u.table)&&(l[e]=!1):l[e]=null===f&&(0,o.Io)(u.table)}}return e},{});if(i&&Object.keys(l).length>0)for(let[e,t]of Object.entries(l))"string"!=typeof t||i[t]||(a[e]=null);return a}function c(e,t){let i=Object.keys(e),s=Object.keys(t);if(i.length!==s.length)return!1;for(let[e,t]of i.entries())if(t!==s[e])return!1;return!0}function h(e,t){let i=Object.entries(t).filter(([,e])=>void 0!==e).map(([t,i])=>(0,r.is)(i,n.Xs)||(0,r.is)(i,s.V)?[t,i]:[t,new n.Iw(i,e[o.XI.Symbol.Columns][t])]);if(0===i.length)throw Error("No values to set");return Object.fromEntries(i)}function d(e,t){for(let i of t)for(let t of Object.getOwnPropertyNames(i.prototype))"constructor"!==t&&Object.defineProperty(e.prototype,t,Object.getOwnPropertyDescriptor(i.prototype,t)||Object.create(null))}function f(e){return e[o.XI.Symbol.Columns]}function g(e){return(0,r.is)(e,l.n)?e._.alias:(0,r.is)(e,n.Ss)?e[a.n].name:(0,r.is)(e,n.Xs)?void 0:e[o.XI.Symbol.IsAlias]?e[o.XI.Symbol.Name]:e[o.XI.Symbol.BaseName]}function p(e,t){return{name:"string"==typeof e&&e.length>0?e:"",config:"object"==typeof e?e:t}}function m(e){if("object"!=typeof e||null===e||"Object"!==e.constructor.name)return!1;if("logger"in e){let t=typeof e.logger;return"boolean"===t||"object"===t&&"function"==typeof e.logger.logQuery||"undefined"===t}if("schema"in e){let t=typeof e.schema;return"object"===t||"undefined"===t}if("casing"in e){let t=typeof e.casing;return"string"===t||"undefined"===t}if("mode"in e)return"default"===e.mode&&"planetscale"===e.mode&&void 0===e.mode;if("connection"in e){let t=typeof e.connection;return"string"===t||"object"===t||"undefined"===t}if("client"in e){let t=typeof e.client;return"object"===t||"function"===t||"undefined"===t}return 0===Object.keys(e).length}},18611:(e,t,i)=>{"use strict";i.d(t,{n:()=>s});let s=Symbol.for("drizzle:ViewBaseConfig")}};