(()=>{var e={};e.id=4896,e.ids=[4896],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},2113:e=>{"use strict";e.exports=import("postgres")},77598:e=>{"use strict";e.exports=require("node:crypto")},80656:(e,t,a)=>{"use strict";a.a(e,async(e,r)=>{try{a.r(t),a.d(t,{patchFetch:()=>o,routeModule:()=>l,serverHooks:()=>f,workAsyncStorage:()=>u,workUnitAsyncStorage:()=>p});var s=a(42706),i=a(28203),n=a(45994),c=a(44465),d=e([c]);c=(d.then?(await d)():d)[0];let l=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/certificate/verify/[serial]/route",pathname:"/api/certificate/verify/[serial]",filename:"route",bundlePath:"app/api/certificate/verify/[serial]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\api\\certificate\\verify\\[serial]\\route.ts",nextConfigOutput:"",userland:c}),{workAsyncStorage:u,workUnitAsyncStorage:p,serverHooks:f}=l;function o(){return(0,n.patchFetch)({workAsyncStorage:u,workUnitAsyncStorage:p})}r()}catch(e){r(e)}})},96487:()=>{},78335:()=>{},44465:(e,t,a)=>{"use strict";a.a(e,async(e,r)=>{try{a.r(t),a.d(t,{GET:()=>o});var s=a(39187),i=a(62693),n=a(48590),c=a(47579),d=e([i]);async function o(e,{params:t}){try{let{serial:e}=await t;if(!e||e.length<5)return s.NextResponse.json({error:"Invalid certificate serial format"},{status:400});let a=await i.db.select({id:n.testResults.id,certificateSerial:n.testResults.certificateSerial,certificateGenerated:n.testResults.certificateGenerated,testDate:n.testResults.testDate,overallBandScore:n.testResults.overallBandScore,status:n.testResults.status,createdAt:n.testResults.createdAt,candidate:{fullName:n.candidates.fullName,nationality:n.candidates.nationality,testDate:n.candidates.testDate,testCenter:n.candidates.testCenter}}).from(n.testResults).innerJoin(n.candidates,(0,c.eq)(n.testResults.candidateId,n.candidates.id)).where((0,c.eq)(n.testResults.certificateSerial,e)).limit(1);if(!a.length)return s.NextResponse.json({valid:!1,error:"Certificate not found",message:"No certificate found with this serial number"},{status:404});let r=a[0];if(!r.certificateGenerated)return s.NextResponse.json({valid:!1,error:"Certificate not generated",message:"Certificate has not been generated for this result"},{status:404});if("pending"===r.status)return s.NextResponse.json({valid:!1,error:"Result pending",message:"Test result is still pending verification"},{status:403});return s.NextResponse.json({valid:!0,certificate:{serial:r.certificateSerial,resultId:r.id,candidateName:r.candidate.fullName,nationality:r.candidate.nationality,testDate:r.candidate.testDate,testCenter:r.candidate.testCenter,overallBandScore:r.overallBandScore,status:r.status,issueDate:r.createdAt},verification:{verified:!0,verifiedAt:new Date().toISOString(),message:"Certificate is valid and authentic"}})}catch(e){return console.error("Error verifying certificate:",e),s.NextResponse.json({valid:!1,error:"Verification failed",message:"An error occurred during certificate verification"},{status:500})}}i=(d.then?(await d)():d)[0],r()}catch(e){r(e)}})},62693:(e,t,a)=>{"use strict";a.a(e,async(e,r)=>{try{a.d(t,{db:()=>l});var s=a(10072),i=a(2113),n=a(48590),c=e([i,s]);[i,s]=c.then?(await c)():c;let d=process.env.DATABASE_URL,o=(0,i.default)(d,{prepare:!1}),l=(0,s.f)(o,{schema:n});r()}catch(e){r(e)}})},48590:(e,t,a)=>{"use strict";a.r(t),a.d(t,{accounts:()=>p,aiFeedback:()=>v,candidates:()=>g,sessions:()=>f,testResults:()=>m,users:()=>u,verificationTokens:()=>_});var r=a(87858),s=a(44799),i=a(32590),n=a(9848),c=a(70009),d=a(27390),o=a(32190),l=a(4502);let u=(0,r.cJ)("users",{id:(0,s.Qq)("id").primaryKey().$defaultFn(()=>(0,l.sX)()),name:(0,s.Qq)("name"),email:(0,s.Qq)("email").notNull().unique(),emailVerified:(0,i.vE)("emailVerified",{mode:"date"}),image:(0,s.Qq)("image"),password:(0,s.Qq)("password"),role:(0,s.Qq)("role",{enum:["admin","test_checker"]}).notNull().default("test_checker"),createdAt:(0,i.vE)("created_at").defaultNow().notNull(),updatedAt:(0,i.vE)("updated_at").defaultNow().notNull()}),p=(0,r.cJ)("accounts",{userId:(0,s.Qq)("userId").notNull().references(()=>u.id,{onDelete:"cascade"}),type:(0,s.Qq)("type").notNull(),provider:(0,s.Qq)("provider").notNull(),providerAccountId:(0,s.Qq)("providerAccountId").notNull(),refresh_token:(0,s.Qq)("refresh_token"),access_token:(0,s.Qq)("access_token"),expires_at:(0,n.nd)("expires_at"),token_type:(0,s.Qq)("token_type"),scope:(0,s.Qq)("scope"),id_token:(0,s.Qq)("id_token"),session_state:(0,s.Qq)("session_state")}),f=(0,r.cJ)("sessions",{sessionToken:(0,s.Qq)("sessionToken").primaryKey(),userId:(0,s.Qq)("userId").notNull().references(()=>u.id,{onDelete:"cascade"}),expires:(0,i.vE)("expires",{mode:"date"}).notNull()}),_=(0,r.cJ)("verificationTokens",{identifier:(0,s.Qq)("identifier").notNull(),token:(0,s.Qq)("token").notNull(),expires:(0,i.vE)("expires",{mode:"date"}).notNull()}),g=(0,r.cJ)("candidates",{id:(0,s.Qq)("id").primaryKey().$defaultFn(()=>(0,l.sX)()),fullName:(0,s.Qq)("full_name").notNull(),email:(0,s.Qq)("email").notNull().unique(),phoneNumber:(0,s.Qq)("phone_number").notNull(),dateOfBirth:(0,i.vE)("date_of_birth",{mode:"date"}).notNull(),nationality:(0,s.Qq)("nationality").notNull(),passportNumber:(0,s.Qq)("passport_number").notNull().unique(),testDate:(0,i.vE)("test_date",{mode:"date"}).notNull(),testCenter:(0,s.Qq)("test_center").notNull(),photoUrl:(0,s.Qq)("photo_url"),createdAt:(0,i.vE)("created_at").defaultNow().notNull(),updatedAt:(0,i.vE)("updated_at").defaultNow().notNull()}),m=(0,r.cJ)("test_results",{id:(0,s.Qq)("id").primaryKey().$defaultFn(()=>(0,l.sX)()),candidateId:(0,s.Qq)("candidate_id").notNull().references(()=>g.id,{onDelete:"cascade"}),listeningScore:(0,c._)("listening_score",{precision:3,scale:1}),listeningBandScore:(0,c._)("listening_band_score",{precision:2,scale:1}),readingScore:(0,c._)("reading_score",{precision:3,scale:1}),readingBandScore:(0,c._)("reading_band_score",{precision:2,scale:1}),writingTask1Score:(0,c._)("writing_task1_score",{precision:2,scale:1}),writingTask2Score:(0,c._)("writing_task2_score",{precision:2,scale:1}),writingBandScore:(0,c._)("writing_band_score",{precision:2,scale:1}),speakingFluencyScore:(0,c._)("speaking_fluency_score",{precision:2,scale:1}),speakingLexicalScore:(0,c._)("speaking_lexical_score",{precision:2,scale:1}),speakingGrammarScore:(0,c._)("speaking_grammar_score",{precision:2,scale:1}),speakingPronunciationScore:(0,c._)("speaking_pronunciation_score",{precision:2,scale:1}),speakingBandScore:(0,c._)("speaking_band_score",{precision:2,scale:1}),overallBandScore:(0,c._)("overall_band_score",{precision:2,scale:1}),status:(0,s.Qq)("status",{enum:["pending","completed","verified"]}).notNull().default("pending"),enteredBy:(0,s.Qq)("entered_by").references(()=>u.id),verifiedBy:(0,s.Qq)("verified_by").references(()=>u.id),certificateGenerated:(0,d.zM)("certificate_generated").default(!1),certificateSerial:(0,s.Qq)("certificate_serial").unique(),certificateUrl:(0,s.Qq)("certificate_url"),aiFeedbackGenerated:(0,d.zM)("ai_feedback_generated").default(!1),testDate:(0,i.vE)("test_date",{mode:"date"}),createdAt:(0,i.vE)("created_at").defaultNow().notNull(),updatedAt:(0,i.vE)("updated_at").defaultNow().notNull()}),v=(0,r.cJ)("ai_feedback",{id:(0,s.Qq)("id").primaryKey().$defaultFn(()=>(0,l.sX)()),testResultId:(0,s.Qq)("test_result_id").notNull().references(()=>m.id,{onDelete:"cascade"}),listeningFeedback:(0,s.Qq)("listening_feedback"),readingFeedback:(0,s.Qq)("reading_feedback"),writingFeedback:(0,s.Qq)("writing_feedback"),speakingFeedback:(0,s.Qq)("speaking_feedback"),overallFeedback:(0,s.Qq)("overall_feedback"),studyRecommendations:(0,s.Qq)("study_recommendations"),strengths:(0,o.Pq)("strengths").$type(),weaknesses:(0,o.Pq)("weaknesses").$type(),studyPlan:(0,o.Pq)("study_plan").$type(),generatedAt:(0,i.vE)("generated_at").defaultNow().notNull()})}};var t=require("../../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[638,5452,9757],()=>a(80656));module.exports=r})();