(()=>{var e={};e.id=635,e.ids=[635],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},65688:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>o,routeModule:()=>x,tree:()=>c});var a=t(70260),r=t(28203),l=t(25155),i=t.n(l),n=t(67292),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);t.d(s,d);let c=["",{children:["results",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,18832)),"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\results\\[id]\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,71354)),"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],o=["C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\results\\[id]\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/results/[id]/page",pathname:"/results/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},97032:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,13219,23)),Promise.resolve().then(t.t.bind(t,34863,23)),Promise.resolve().then(t.t.bind(t,25155,23)),Promise.resolve().then(t.t.bind(t,40802,23)),Promise.resolve().then(t.t.bind(t,9350,23)),Promise.resolve().then(t.t.bind(t,48530,23)),Promise.resolve().then(t.t.bind(t,88921,23))},60584:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,66959,23)),Promise.resolve().then(t.t.bind(t,33875,23)),Promise.resolve().then(t.t.bind(t,88903,23)),Promise.resolve().then(t.t.bind(t,57174,23)),Promise.resolve().then(t.t.bind(t,84178,23)),Promise.resolve().then(t.t.bind(t,87190,23)),Promise.resolve().then(t.t.bind(t,61365,23))},71315:(e,s,t)=>{Promise.resolve().then(t.bind(t,70452))},8267:(e,s,t)=>{Promise.resolve().then(t.bind(t,98805))},39684:(e,s,t)=>{Promise.resolve().then(t.bind(t,18832))},63652:(e,s,t)=>{Promise.resolve().then(t.bind(t,42945))},35668:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(41680).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},43464:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(41680).A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},78397:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(41680).A)("brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]])},45037:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(41680).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},46583:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(41680).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},4643:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(41680).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},19473:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(41680).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},48857:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(41680).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},80832:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(41680).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},87798:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(41680).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},42945:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>A});var a=t(45512),r=t(58009),l=t(79334),i=t(28531),n=t.n(i),d=t(45103),c=t(4643),o=t(46583),m=t(43464),x=t(45037),h=t(35668),g=t(61075),p=t(19473),u=t(87798),j=t(48857),b=t(45723),f=t(78397);function N({scores:e,className:s=""}){let t=[{name:"Listening",score:e.listening,color:"bg-blue-500"},{name:"Reading",score:e.reading,color:"bg-green-500"},{name:"Writing",score:e.writing,color:"bg-yellow-500"},{name:"Speaking",score:e.speaking,color:"bg-purple-500"}];return(0,a.jsxs)("div",{className:`bg-white rounded-lg shadow-lg p-6 ${s}`,children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-6",children:"Score Breakdown"}),(0,a.jsxs)("div",{className:"space-y-4",children:[t.map(e=>(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-20 text-sm font-medium text-gray-700",children:e.name}),(0,a.jsx)("div",{className:"flex-1 mx-4",children:(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-3",children:(0,a.jsx)("div",{className:`h-3 rounded-full ${e.color} transition-all duration-500 ease-out`,style:{width:e.score?`${e.score/9*100}%`:"0%"}})})}),(0,a.jsx)("div",{className:"w-12 text-right",children:(0,a.jsx)("span",{className:"text-lg font-bold text-gray-900",children:e.score||"N/A"})})]},e.name)),e.overall&&(0,a.jsx)("div",{className:"mt-6 pt-4 border-t border-gray-200",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-20 text-sm font-medium text-gray-700",children:"Overall"}),(0,a.jsx)("div",{className:"flex-1 mx-4",children:(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-4",children:(0,a.jsx)("div",{className:"h-4 rounded-full bg-indigo-600 transition-all duration-500 ease-out",style:{width:`${e.overall/9*100}%`}})})}),(0,a.jsx)("div",{className:"w-12 text-right",children:(0,a.jsx)("span",{className:"text-xl font-bold text-indigo-600",children:e.overall})})]})})]})]})}var v=t(41680);let y=(0,v.A)("minus",[["path",{d:"M5 12h14",key:"1ays0h"}]]);var w=t(80832);let k=(0,v.A)("trending-down",[["path",{d:"M16 17h6v-6",key:"t6n2it"}],["path",{d:"m22 17-8.5-8.5-5 5L2 7",key:"x473p"}]]);function S({metrics:e,overallScore:s,className:t=""}){let r=(e,s)=>e?e>s?{icon:w.A,color:"text-green-500",text:"Above Average"}:e<s?{icon:k,color:"text-red-500",text:"Below Average"}:{icon:y,color:"text-yellow-500",text:"Average"}:{icon:y,color:"text-gray-400",text:"N/A"},l=r(s,6.5);return(0,a.jsxs)("div",{className:`bg-white rounded-lg shadow-lg p-6 ${t}`,children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-6",children:"Performance Analysis"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"bg-gradient-to-br from-blue-50 to-indigo-100 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-700",children:"Overall Performance"}),(0,a.jsx)(l.icon,{className:`h-5 w-5 ${l.color}`})]}),(0,a.jsx)("div",{className:"text-2xl font-bold text-indigo-600 mb-1",children:s||"N/A"}),(0,a.jsxs)("div",{className:"text-xs text-gray-600",children:["Global Average: ",6.5]}),(0,a.jsx)("div",{className:`text-xs font-medium ${l.color}`,children:l.text})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-br from-green-50 to-emerald-100 rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:"Score Range"}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-lg font-bold text-green-600",children:e.highestScore||"N/A"}),(0,a.jsx)("div",{className:"text-xs text-gray-600",children:"Highest"})]}),(0,a.jsx)("div",{className:"text-gray-400",children:"-"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-lg font-bold text-red-600",children:e.lowestScore||"N/A"}),(0,a.jsx)("div",{className:"text-xs text-gray-600",children:"Lowest"})]})]}),(0,a.jsx)("div",{className:"mt-2",children:(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:["Average: ",(0,a.jsx)("span",{className:"font-semibold",children:e.averageScore?e.averageScore.toFixed(1):"N/A"})]})})]})]}),(0,a.jsxs)("div",{className:"mt-6",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-4",children:"Module Performance vs Global Average"}),(0,a.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-3",children:Object.entries(e.scoreDistribution).map(([e,s])=>{let t=r(s,6.5);return(0,a.jsxs)("div",{className:"text-center p-3 bg-gray-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-xs font-medium text-gray-600 capitalize mb-1",children:e}),(0,a.jsx)("div",{className:"text-lg font-bold text-gray-900 mb-1",children:s||"N/A"}),(0,a.jsx)(t.icon,{className:`h-4 w-4 mx-auto ${t.color}`})]},e)})})]}),(0,a.jsxs)("div",{className:"mt-6 p-4 bg-yellow-50 rounded-lg",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-yellow-800 mb-2",children:"Performance Insights"}),(0,a.jsxs)("div",{className:"text-sm text-yellow-700",children:[s&&s>=7&&(0,a.jsx)("p",{children:"• Excellent performance! You're well above the global average."}),s&&s>=6&&s<7&&(0,a.jsx)("p",{children:"• Good performance! You're close to or at the global average."}),s&&s<6&&(0,a.jsx)("p",{children:"• There's room for improvement. Focus on your weaker modules."}),e.highestScore&&e.lowestScore&&(0,a.jsxs)("p",{children:["• Score consistency: ",(e.highestScore-e.lowestScore).toFixed(1)," band difference between highest and lowest modules."]})]})]})]})}function A(){let e=(0,l.useParams)().id,[s,t]=(0,r.useState)(null),[i,v]=(0,r.useState)(null),[y,w]=(0,r.useState)(!0),[k,A]=(0,r.useState)(""),[P,C]=(0,r.useState)(!1);(0,r.useCallback)(async()=>{try{let s=await fetch(`/api/results/${e}`);if(s.ok){let e=await s.json();t(e)}else{let e=await s.json();A(e.error||"Result not found")}}catch(e){console.error("Error fetching result:",e),A("Failed to load result")}finally{w(!1)}},[e]),(0,r.useCallback)(async()=>{if(s?.aiFeedbackGenerated){C(!0);try{let s=await fetch(`/api/feedback/${e}`);if(s.ok){let e=await s.json();v(e)}}catch(e){console.error("Error fetching feedback:",e)}finally{C(!1)}}},[e,s?.aiFeedbackGenerated]);let L=async()=>{try{let t=await fetch(`/api/certificate/${e}`);if(t.ok){let a=await t.blob(),r=window.URL.createObjectURL(a),l=document.createElement("a");l.href=r,l.download=`IELTS_Certificate_${s?.candidate.fullName.replace(/\s+/g,"_")}_${e}.pdf`,document.body.appendChild(l),l.click(),window.URL.revokeObjectURL(r),document.body.removeChild(l)}}catch(e){console.error("Certificate download error:",e),alert("An error occurred while downloading the certificate")}};return y?(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Loading your results..."})]})}):k||!s?(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center max-w-md mx-auto p-8",children:[(0,a.jsx)(x.A,{className:"h-16 w-16 text-red-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-xl font-medium text-gray-900 mb-2",children:"Unable to Load Results"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:k||"Result not found"}),(0,a.jsxs)(n(),{href:"/search",className:"inline-flex items-center text-blue-600 hover:text-blue-700 font-medium",children:[(0,a.jsx)(h.A,{className:"h-4 w-4 mr-1"}),"Back to Search"]})]})}):(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100",children:[(0,a.jsx)("header",{className:"bg-white shadow-sm",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex justify-between items-center py-6",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsxs)(n(),{href:"/search",className:"flex items-center text-blue-600 hover:text-blue-700 mr-4",children:[(0,a.jsx)(h.A,{className:"h-5 w-5 mr-1"}),"Back to Search"]}),(0,a.jsx)(g.A,{className:"h-8 w-8 text-blue-600 mr-3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"IELTS Test Results"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Official Test Report"})]})]}),s.certificateGenerated&&(0,a.jsxs)("button",{onClick:L,className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700",children:[(0,a.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Download Certificate"]})]})})}),(0,a.jsxs)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,a.jsxs)("div",{className:"lg:col-span-1 space-y-6",children:[(0,a.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,a.jsx)("h2",{className:"text-lg font-medium text-gray-900 mb-4",children:"Candidate Information"}),(0,a.jsxs)("div",{className:"flex items-start space-x-4",children:[s.candidate.photoUrl?(0,a.jsx)(d.default,{src:s.candidate.photoUrl,alt:s.candidate.fullName,width:80,height:80,className:"rounded-lg object-cover"}):(0,a.jsx)("div",{className:"w-20 h-20 bg-gray-200 rounded-lg flex items-center justify-center",children:(0,a.jsx)(u.A,{className:"h-8 w-8 text-gray-400"})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h4",{className:"text-xl font-semibold text-gray-900",children:s.candidate.fullName}),(0,a.jsxs)("div",{className:"mt-2 space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,a.jsx)(j.A,{className:"h-4 w-4 mr-2"}),s.candidate.nationality]}),(0,a.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,a.jsx)(b.A,{className:"h-4 w-4 mr-2"}),"Test Date: ",new Date(s.candidate.testDate).toLocaleDateString()]}),(0,a.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,a.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"Test Center: ",s.candidate.testCenter]})]})]})]})]}),(0,a.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,a.jsx)("h2",{className:"text-lg font-medium text-gray-900 mb-4",children:"Result Status"}),(0,a.jsx)("div",{className:"flex items-center justify-between",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(e=>{switch(e){case"pending":return(0,a.jsx)(c.A,{className:"h-5 w-5 text-yellow-500"});case"completed":return(0,a.jsx)(o.A,{className:"h-5 w-5 text-green-500"});case"verified":return(0,a.jsx)(m.A,{className:"h-5 w-5 text-blue-500"});default:return(0,a.jsx)(x.A,{className:"h-5 w-5 text-gray-500"})}})(s.status),(0,a.jsx)("span",{className:`ml-3 px-3 py-1 rounded-full text-sm font-medium ${(e=>{switch(e){case"pending":return"text-yellow-800 bg-yellow-100";case"completed":return"text-green-800 bg-green-100";case"verified":return"text-blue-800 bg-blue-100";default:return"text-gray-800 bg-gray-100"}})(s.status)}`,children:s.status.charAt(0).toUpperCase()+s.status.slice(1)})]})}),(0,a.jsxs)("div",{className:"mt-4 text-sm text-gray-600",children:[(0,a.jsxs)("p",{children:["Result ID: ",s.id]}),s.certificateSerial&&(0,a.jsxs)("p",{children:["Certificate Serial: ",s.certificateSerial]}),(0,a.jsxs)("p",{children:["Generated: ",new Date(s.createdAt).toLocaleDateString()]})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-br from-indigo-500 to-purple-600 shadow rounded-lg p-6 text-white text-center",children:[(0,a.jsx)("h2",{className:"text-lg font-medium mb-4",children:"Overall Band Score"}),(0,a.jsx)("div",{className:"text-5xl font-bold mb-2",children:s.overallBandScore||"N/A"}),(0,a.jsx)("p",{className:"text-indigo-100",children:"IELTS Band Score"}),s.overallBandScore&&(0,a.jsxs)("div",{className:"mt-4 text-sm",children:[s.overallBandScore>=8.5&&(0,a.jsx)("p",{children:"Expert User"}),s.overallBandScore>=7.5&&s.overallBandScore<8.5&&(0,a.jsx)("p",{children:"Very Good User"}),s.overallBandScore>=6.5&&s.overallBandScore<7.5&&(0,a.jsx)("p",{children:"Good User"}),s.overallBandScore>=5.5&&s.overallBandScore<6.5&&(0,a.jsx)("p",{children:"Modest User"}),s.overallBandScore<5.5&&(0,a.jsx)("p",{children:"Limited User"})]})]})]}),(0,a.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 xl:grid-cols-2 gap-6",children:[(0,a.jsx)(N,{scores:{listening:s.listeningBandScore,reading:s.readingBandScore,writing:s.writingBandScore,speaking:s.speakingBandScore,overall:s.overallBandScore}}),(0,a.jsx)(S,{metrics:s.performanceMetrics,overallScore:s.overallBandScore})]}),(0,a.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,a.jsx)("h2",{className:"text-lg font-medium text-gray-900 mb-6",children:"Detailed Score Breakdown"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,a.jsx)("h3",{className:"text-md font-semibold text-blue-600 mb-3",children:"Listening"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Raw Score:"}),(0,a.jsxs)("span",{className:"font-medium",children:[s.listeningScore||"N/A","/40"]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Band Score:"}),(0,a.jsx)("span",{className:"text-lg font-bold text-blue-600",children:s.listeningBandScore||"N/A"})]})]})]}),(0,a.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,a.jsx)("h3",{className:"text-md font-semibold text-green-600 mb-3",children:"Reading"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Raw Score:"}),(0,a.jsxs)("span",{className:"font-medium",children:[s.readingScore||"N/A","/40"]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Band Score:"}),(0,a.jsx)("span",{className:"text-lg font-bold text-green-600",children:s.readingBandScore||"N/A"})]})]})]}),(0,a.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,a.jsx)("h3",{className:"text-md font-semibold text-yellow-600 mb-3",children:"Writing"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Task 1:"}),(0,a.jsxs)("span",{className:"font-medium",children:[s.writingTask1Score||"N/A","/9"]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Task 2:"}),(0,a.jsxs)("span",{className:"font-medium",children:[s.writingTask2Score||"N/A","/9"]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Band Score:"}),(0,a.jsx)("span",{className:"text-lg font-bold text-yellow-600",children:s.writingBandScore||"N/A"})]})]})]}),(0,a.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,a.jsx)("h3",{className:"text-md font-semibold text-purple-600 mb-3",children:"Speaking"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Fluency:"}),(0,a.jsxs)("span",{className:"font-medium",children:[s.speakingFluencyScore||"N/A","/9"]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Lexical:"}),(0,a.jsxs)("span",{className:"font-medium",children:[s.speakingLexicalScore||"N/A","/9"]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Grammar:"}),(0,a.jsxs)("span",{className:"font-medium",children:[s.speakingGrammarScore||"N/A","/9"]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Pronunciation:"}),(0,a.jsxs)("span",{className:"font-medium",children:[s.speakingPronunciationScore||"N/A","/9"]})]}),(0,a.jsxs)("div",{className:"flex justify-between border-t pt-2",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Band Score:"}),(0,a.jsx)("span",{className:"text-lg font-bold text-purple-600",children:s.speakingBandScore||"N/A"})]})]})]})]})]}),s.aiFeedbackGenerated&&(0,a.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,a.jsxs)("div",{className:"flex items-center mb-6",children:[(0,a.jsx)(f.A,{className:"h-6 w-6 text-purple-600 mr-2"}),(0,a.jsx)("h2",{className:"text-lg font-medium text-gray-900",children:"AI-Generated Feedback"})]}),P?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Loading feedback..."})]}):i?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[i.listeningFeedback&&(0,a.jsxs)("div",{className:"border-l-4 border-blue-500 pl-4",children:[(0,a.jsx)("h4",{className:"font-semibold text-blue-600 mb-2",children:"Listening Feedback"}),(0,a.jsx)("p",{className:"text-sm text-gray-700",children:i.listeningFeedback})]}),i.readingFeedback&&(0,a.jsxs)("div",{className:"border-l-4 border-green-500 pl-4",children:[(0,a.jsx)("h4",{className:"font-semibold text-green-600 mb-2",children:"Reading Feedback"}),(0,a.jsx)("p",{className:"text-sm text-gray-700",children:i.readingFeedback})]}),i.writingFeedback&&(0,a.jsxs)("div",{className:"border-l-4 border-yellow-500 pl-4",children:[(0,a.jsx)("h4",{className:"font-semibold text-yellow-600 mb-2",children:"Writing Feedback"}),(0,a.jsx)("p",{className:"text-sm text-gray-700",children:i.writingFeedback})]}),i.speakingFeedback&&(0,a.jsxs)("div",{className:"border-l-4 border-purple-500 pl-4",children:[(0,a.jsx)("h4",{className:"font-semibold text-purple-600 mb-2",children:"Speaking Feedback"}),(0,a.jsx)("p",{className:"text-sm text-gray-700",children:i.speakingFeedback})]})]}),i.overallFeedback&&(0,a.jsxs)("div",{className:"bg-gradient-to-r from-purple-50 to-indigo-50 rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"font-semibold text-purple-600 mb-2",children:"Overall Assessment"}),(0,a.jsx)("p",{className:"text-gray-700",children:i.overallFeedback})]}),i.studyRecommendations&&(0,a.jsxs)("div",{className:"bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"font-semibold text-green-600 mb-2",children:"Study Recommendations"}),(0,a.jsx)("p",{className:"text-gray-700",children:i.studyRecommendations})]}),(0,a.jsxs)("div",{className:"text-xs text-gray-500 text-center",children:["Feedback generated on ",new Date(i.generatedAt).toLocaleDateString()]})]}):(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)(f.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"AI feedback is not available at this time."})]})]})]})]}),(0,a.jsxs)("div",{className:"mt-12 bg-white shadow rounded-lg p-6 text-center",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center mb-4",children:[(0,a.jsx)(m.A,{className:"h-8 w-8 text-blue-600 mr-2"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Official IELTS Test Report"})]}),(0,a.jsxs)("p",{className:"text-gray-600 mb-4",children:["This is an official IELTS test result. For verification purposes, please use the result ID: ",s.id]}),s.certificateSerial&&(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["Certificate Serial Number: ",s.certificateSerial]}),(0,a.jsxs)("div",{className:"mt-4 flex justify-center space-x-4",children:[(0,a.jsx)(n(),{href:"/search",className:"text-blue-600 hover:text-blue-700 text-sm font-medium",children:"Search Other Results"}),s.certificateSerial&&(0,a.jsx)(n(),{href:`/verify/${s.certificateSerial}`,className:"text-green-600 hover:text-green-700 text-sm font-medium",children:"Verify Certificate"})]})]})]})]})}},71354:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>d,metadata:()=>n});var a=t(62740),r=t(85041),l=t.n(r),i=t(70452);t(61135);let n={title:"IELTS Certification System",description:"Professional IELTS test result management and certification system"};function d({children:e}){return(0,a.jsx)("html",{lang:"en",children:(0,a.jsx)("body",{className:l().className,children:(0,a.jsx)(i.SessionProvider,{children:e})})})}},18832:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\results\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\results\\[id]\\page.tsx","default")},70440:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});var a=t(88077);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},61135:()=>{}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[638,8338,2367,8324],()=>t(65688));module.exports=a})();