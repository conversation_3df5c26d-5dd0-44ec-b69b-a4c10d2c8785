(()=>{var e={};e.id=4619,e.ids=[4619],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},39420:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var r=s(70260),a=s(28203),l=s(25155),n=s.n(l),i=s(67292),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);s.d(t,o);let d=["",{children:["admin",{children:["search",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,13968)),"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\admin\\search\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,96038)),"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\admin\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,71354)),"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\admin\\search\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/search/page",pathname:"/admin/search",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},70308:(e,t,s)=>{Promise.resolve().then(s.bind(s,13968))},77924:(e,t,s)=>{Promise.resolve().then(s.bind(s,40900))},48857:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(41680).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},92557:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(41680).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},87798:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(41680).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},40900:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>g});var r=s(45512),a=s(58009),l=s(28531),n=s.n(l),i=s(45103),o=s(19473),d=s(16873),c=s(94889),u=s(92557),m=s(79660),x=s(87798),h=s(61075),p=s(45723),b=s(48857),y=s(21956),f=s(19904);function g(){let[e,t]=(0,a.useState)({query:"",searchType:"all",testCenter:"",testDateFrom:"",testDateTo:"",nationality:"",hasResults:"all",resultStatus:"all",bandScoreMin:"",bandScoreMax:""}),[s,l]=(0,a.useState)([]),[g,v]=(0,a.useState)(!1),[j,N]=(0,a.useState)(!1),[w,S]=(0,a.useState)(!1),[C,T]=(0,a.useState)(0),k=(e,s)=>{t(t=>({...t,[e]:s}))},A=async t=>{t?.preventDefault(),v(!0),N(!0);try{let t=await fetch("/api/admin/search",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(t.ok){let e=await t.json();l(e.results),T(e.total)}else l([]),T(0)}catch(e){console.error("Search error:",e),l([]),T(0)}finally{v(!1)}},D=async()=>{try{let t=await fetch("/api/admin/export",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({filters:e,format:"csv"})});if(t.ok){let e=await t.blob(),s=window.URL.createObjectURL(e),r=document.createElement("a");r.href=s,r.download=`ielts_search_results_${new Date().toISOString().split("T")[0]}.csv`,document.body.appendChild(r),r.click(),window.URL.revokeObjectURL(s),document.body.removeChild(r)}}catch(e){console.error("Export error:",e)}};return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Advanced Search"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Search and filter candidates and test results"})]}),s.length>0&&(0,r.jsxs)("button",{onClick:D,className:"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50",children:[(0,r.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"Export Results"]})]}),(0,r.jsx)("div",{className:"bg-white shadow rounded-lg p-6",children:(0,r.jsxs)("form",{onSubmit:A,className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"searchType",className:"block text-sm font-medium text-gray-700 mb-2",children:"Search Type"}),(0,r.jsxs)("select",{id:"searchType",value:e.searchType,onChange:e=>k("searchType",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,r.jsx)("option",{value:"all",children:"All Fields"}),(0,r.jsx)("option",{value:"name",children:"Full Name"}),(0,r.jsx)("option",{value:"email",children:"Email"}),(0,r.jsx)("option",{value:"passport",children:"Passport Number"})]})]}),(0,r.jsxs)("div",{className:"md:col-span-2",children:[(0,r.jsx)("label",{htmlFor:"query",className:"block text-sm font-medium text-gray-700 mb-2",children:"Search Query"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(d.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,r.jsx)("input",{type:"text",id:"query",value:e.query,onChange:e=>k("query",e.target.value),placeholder:"Enter search term...",className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]})]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between pt-4 border-t border-gray-200",children:[(0,r.jsxs)("button",{type:"button",onClick:()=>S(!w),className:"inline-flex items-center text-sm text-blue-600 hover:text-blue-700",children:[(0,r.jsx)(c.A,{className:"h-4 w-4 mr-1"}),w?"Hide":"Show"," Advanced Filters"]}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsxs)("button",{type:"button",onClick:()=>{t({query:"",searchType:"all",testCenter:"",testDateFrom:"",testDateTo:"",nationality:"",hasResults:"all",resultStatus:"all",bandScoreMin:"",bandScoreMax:""}),l([]),N(!1)},className:"px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50",children:[(0,r.jsx)(u.A,{className:"h-4 w-4 mr-2 inline"}),"Clear"]}),(0,r.jsx)("button",{type:"submit",disabled:g,className:"px-6 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50",children:g?"Searching...":"Search"})]})]}),w&&(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 pt-4 border-t border-gray-200",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"testCenter",className:"block text-sm font-medium text-gray-700 mb-2",children:"Test Center"}),(0,r.jsxs)("select",{id:"testCenter",value:e.testCenter,onChange:e=>k("testCenter",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,r.jsx)("option",{value:"",children:"All Centers"}),["IELTS Test Center - London","IELTS Test Center - Manchester","IELTS Test Center - Birmingham","IELTS Test Center - Edinburgh","IELTS Test Center - Cardiff","IELTS Test Center - Belfast","IELTS Test Center - Dublin","IELTS Test Center - Online"].map(e=>(0,r.jsx)("option",{value:e,children:e},e))]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"testDateFrom",className:"block text-sm font-medium text-gray-700 mb-2",children:"Test Date From"}),(0,r.jsx)("input",{type:"date",id:"testDateFrom",value:e.testDateFrom,onChange:e=>k("testDateFrom",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"testDateTo",className:"block text-sm font-medium text-gray-700 mb-2",children:"Test Date To"}),(0,r.jsx)("input",{type:"date",id:"testDateTo",value:e.testDateTo,onChange:e=>k("testDateTo",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"nationality",className:"block text-sm font-medium text-gray-700 mb-2",children:"Nationality"}),(0,r.jsx)("input",{type:"text",id:"nationality",value:e.nationality,onChange:e=>k("nationality",e.target.value),placeholder:"e.g., British, American",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"hasResults",className:"block text-sm font-medium text-gray-700 mb-2",children:"Has Test Results"}),(0,r.jsxs)("select",{id:"hasResults",value:e.hasResults,onChange:e=>k("hasResults",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,r.jsx)("option",{value:"all",children:"All Candidates"}),(0,r.jsx)("option",{value:"yes",children:"With Results"}),(0,r.jsx)("option",{value:"no",children:"Without Results"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"resultStatus",className:"block text-sm font-medium text-gray-700 mb-2",children:"Result Status"}),(0,r.jsxs)("select",{id:"resultStatus",value:e.resultStatus,onChange:e=>k("resultStatus",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,r.jsx)("option",{value:"all",children:"All Statuses"}),(0,r.jsx)("option",{value:"pending",children:"Pending"}),(0,r.jsx)("option",{value:"completed",children:"Completed"}),(0,r.jsx)("option",{value:"verified",children:"Verified"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"bandScoreMin",className:"block text-sm font-medium text-gray-700 mb-2",children:"Min Band Score"}),(0,r.jsx)("input",{type:"number",id:"bandScoreMin",value:e.bandScoreMin,onChange:e=>k("bandScoreMin",e.target.value),min:"1",max:"9",step:"0.5",placeholder:"1.0",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"bandScoreMax",className:"block text-sm font-medium text-gray-700 mb-2",children:"Max Band Score"}),(0,r.jsx)("input",{type:"number",id:"bandScoreMax",value:e.bandScoreMax,onChange:e=>k("bandScoreMax",e.target.value),min:"1",max:"9",step:"0.5",placeholder:"9.0",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]})]})]})}),j&&(0,r.jsxs)("div",{className:"bg-white shadow rounded-lg",children:[(0,r.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("h3",{className:"text-lg font-medium text-gray-900",children:["Search Results ",C>0&&`(${C})`]}),s.length>0&&(0,r.jsxs)("div",{className:"flex items-center text-sm text-gray-500",children:[(0,r.jsx)(m.A,{className:"h-4 w-4 mr-1"}),s.filter(e=>e.hasResults).length," with results"]})]})}),g?(0,r.jsxs)("div",{className:"flex items-center justify-center py-12",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),(0,r.jsx)("span",{className:"ml-2 text-gray-600",children:"Searching..."})]}):s.length>0?(0,r.jsx)("div",{className:"divide-y divide-gray-200",children:s.map(e=>(0,r.jsx)("div",{className:"p-6 hover:bg-gray-50",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[e.photoUrl?(0,r.jsx)(i.default,{className:"h-12 w-12 rounded-full object-cover",src:e.photoUrl,alt:e.fullName,width:48,height:48}):(0,r.jsx)("div",{className:"h-12 w-12 rounded-full bg-gray-200 flex items-center justify-center",children:(0,r.jsx)(x.A,{className:"h-6 w-6 text-gray-400"})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h4",{className:"text-lg font-medium text-gray-900",children:e.fullName}),(0,r.jsxs)("div",{className:"mt-1 flex items-center space-x-4 text-sm text-gray-500",children:[(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(h.A,{className:"h-4 w-4 mr-1"}),e.passportNumber]}),(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(p.A,{className:"h-4 w-4 mr-1"}),new Date(e.testDate).toLocaleDateString()]}),(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(b.A,{className:"h-4 w-4 mr-1"}),e.testCenter]})]}),(0,r.jsxs)("div",{className:"mt-1 text-sm text-gray-600",children:[e.email," • ",e.nationality]})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[e.hasResults&&e.result?(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsxs)("p",{className:"font-semibold text-gray-900",children:["Band: ",e.result.overallBandScore||"N/A"]}),(0,r.jsx)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${"completed"===e.result.status?"bg-green-100 text-green-800":"pending"===e.result.status?"bg-yellow-100 text-yellow-800":"bg-blue-100 text-blue-800"}`,children:e.result.status})]}):(0,r.jsx)("span",{className:"text-sm text-gray-500",children:"No results"}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)(n(),{href:`/admin/candidates/${e.id}`,className:"text-blue-600 hover:text-blue-900",title:"View Details",children:(0,r.jsx)(y.A,{className:"h-4 w-4"})}),(0,r.jsx)(n(),{href:`/admin/candidates/${e.id}/edit`,className:"text-green-600 hover:text-green-900",title:"Edit Candidate",children:(0,r.jsx)(f.A,{className:"h-4 w-4"})})]})]})]})},e.id))}):(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)(d.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No results found"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Try adjusting your search criteria or filters."})]})]})]})}},13968:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\admin\\search\\page.tsx","default")}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[638,8338,2367,8324,9807],()=>s(39420));module.exports=r})();