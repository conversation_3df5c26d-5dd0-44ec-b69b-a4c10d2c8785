(()=>{var e={};e.id=3698,e.ids=[3698],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},39160:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>h,tree:()=>o});var r=s(70260),a=s(28203),i=s(25155),n=s.n(i),l=s(67292),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);s.d(t,d);let o=["",{children:["admin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,17949)),"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\admin\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,96038)),"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\admin\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,71354)),"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\admin\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/page",pathname:"/admin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},97032:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,13219,23)),Promise.resolve().then(s.t.bind(s,34863,23)),Promise.resolve().then(s.t.bind(s,25155,23)),Promise.resolve().then(s.t.bind(s,40802,23)),Promise.resolve().then(s.t.bind(s,9350,23)),Promise.resolve().then(s.t.bind(s,48530,23)),Promise.resolve().then(s.t.bind(s,88921,23))},60584:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,66959,23)),Promise.resolve().then(s.t.bind(s,33875,23)),Promise.resolve().then(s.t.bind(s,88903,23)),Promise.resolve().then(s.t.bind(s,57174,23)),Promise.resolve().then(s.t.bind(s,84178,23)),Promise.resolve().then(s.t.bind(s,87190,23)),Promise.resolve().then(s.t.bind(s,61365,23))},71315:(e,t,s)=>{Promise.resolve().then(s.bind(s,70452))},8267:(e,t,s)=>{Promise.resolve().then(s.bind(s,98805))},67305:(e,t,s)=>{Promise.resolve().then(s.bind(s,96038))},57577:(e,t,s)=>{Promise.resolve().then(s.bind(s,53760))},35606:(e,t,s)=>{Promise.resolve().then(s.bind(s,17949))},30878:(e,t,s)=>{Promise.resolve().then(s.bind(s,96185))},41680:(e,t,s)=>{"use strict";s.d(t,{A:()=>m});var r=s(58009);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,s)=>s?s.toUpperCase():t.toLowerCase()),n=e=>{let t=i(e);return t.charAt(0).toUpperCase()+t.slice(1)},l=(...e)=>e.filter((e,t,s)=>!!e&&""!==e.trim()&&s.indexOf(e)===t).join(" ").trim(),d=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,r.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:s=2,absoluteStrokeWidth:a,className:i="",children:n,iconNode:c,...m},h)=>(0,r.createElement)("svg",{ref:h,...o,width:t,height:t,stroke:e,strokeWidth:a?24*Number(s)/Number(t):s,className:l("lucide",i),...!n&&!d(m)&&{"aria-hidden":"true"},...m},[...c.map(([e,t])=>(0,r.createElement)(e,t)),...Array.isArray(n)?n:[n]])),m=(e,t)=>{let s=(0,r.forwardRef)(({className:s,...i},d)=>(0,r.createElement)(c,{ref:d,iconNode:t,className:l(`lucide-${a(n(e))}`,`lucide-${e}`,s),...i}));return s.displayName=n(e),s}},79660:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(41680).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},4643:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(41680).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},19473:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(41680).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},61075:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(41680).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},87137:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(41680).A)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},30722:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(41680).A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},16873:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(41680).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},80832:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(41680).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},69855:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(41680).A)("user-plus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},64977:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(41680).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},79334:(e,t,s)=>{"use strict";var r=s(58686);s.o(r,"useParams")&&s.d(t,{useParams:function(){return r.useParams}}),s.o(r,"useRouter")&&s.d(t,{useRouter:function(){return r.useRouter}}),s.o(r,"useSearchParams")&&s.d(t,{useSearchParams:function(){return r.useSearchParams}})},53760:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>v});var r=s(45512),a=s(98805),i=s(79334);s(58009);var n=s(28531),l=s.n(n),d=s(87137),o=s(64977),c=s(69855),m=s(79660),h=s(16873),u=s(61075);let x=(0,s(41680).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);var p=s(30722);function v({children:e}){let{data:t,status:s}=(0,a.wV)();if((0,i.useRouter)(),"loading"===s)return(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})});if(!t||t.user?.role!=="admin")return null;let n=[{name:"Dashboard",href:"/admin",icon:d.A},{name:"Candidates",href:"/admin/candidates",icon:o.A},{name:"Add Candidate",href:"/admin/candidates/new",icon:c.A},{name:"Test Results",href:"/admin/results",icon:m.A},{name:"Advanced Search",href:"/admin/search",icon:h.A},{name:"Reports",href:"/admin/reports",icon:u.A},{name:"Settings",href:"/admin/settings",icon:x}];return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)("div",{className:"fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg",children:(0,r.jsxs)("div",{className:"flex flex-col h-full",children:[(0,r.jsxs)("div",{className:"flex items-center px-6 py-4 border-b border-gray-200",children:[(0,r.jsx)(u.A,{className:"h-8 w-8 text-blue-600 mr-3"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-lg font-semibold text-gray-900",children:"IELTS Admin"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Certification System"})]})]}),(0,r.jsx)("nav",{className:"flex-1 px-4 py-6 space-y-2",children:n.map(e=>{let t=e.icon;return(0,r.jsxs)(l(),{href:e.href,className:"flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-100 hover:text-gray-900 group",children:[(0,r.jsx)(t,{className:"h-5 w-5 mr-3 text-gray-400 group-hover:text-gray-500"}),e.name]},e.name)})}),(0,r.jsxs)("div",{className:"border-t border-gray-200 p-4",children:[(0,r.jsx)("div",{className:"flex items-center mb-3",children:(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900",children:t.user?.name}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:t.user?.email}),(0,r.jsx)("p",{className:"text-xs text-blue-600 font-medium",children:"Administrator"})]})}),(0,r.jsxs)("button",{onClick:()=>(0,a.CI)({callbackUrl:"/"}),className:"flex items-center w-full px-3 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-100 hover:text-gray-900",children:[(0,r.jsx)(p.A,{className:"h-4 w-4 mr-3"}),"Sign out"]})]})]})}),(0,r.jsx)("div",{className:"pl-64",children:(0,r.jsx)("main",{className:"py-6",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:e})})})]})}},96185:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>p});var r=s(45512),a=s(58009),i=s(28531),n=s.n(i),l=s(64977),d=s(61075),o=s(4643),c=s(80832),m=s(69855),h=s(79660),u=s(16873),x=s(19473);function p(){let[e,t]=(0,a.useState)({totalCandidates:0,totalResults:0,pendingResults:0,completedResults:0,recentCandidates:[],recentResults:[]}),[s,i]=(0,a.useState)(!0),p=[{name:"Total Candidates",value:e.totalCandidates,icon:l.A,color:"bg-blue-500",href:"/admin/candidates"},{name:"Test Results",value:e.totalResults,icon:d.A,color:"bg-green-500",href:"/admin/results"},{name:"Pending Results",value:e.pendingResults,icon:o.A,color:"bg-yellow-500",href:"/admin/results?status=pending"},{name:"Completed Results",value:e.completedResults,icon:c.A,color:"bg-purple-500",href:"/admin/results?status=completed"}],v=[{name:"Add New Candidate",description:"Register a new test candidate",href:"/admin/candidates/new",icon:m.A,color:"bg-blue-600 hover:bg-blue-700"},{name:"View All Results",description:"Browse all test results",href:"/admin/results",icon:h.A,color:"bg-green-600 hover:bg-green-700"},{name:"Search System",description:"Search candidates and results",href:"/admin/search",icon:u.A,color:"bg-purple-600 hover:bg-purple-700"},{name:"Export Data",description:"Download system reports",href:"/admin/export",icon:x.A,color:"bg-gray-600 hover:bg-gray-700"}];return s?(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Admin Dashboard"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Overview of the IELTS Certification System"})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:p.map(e=>{let t=e.icon;return(0,r.jsx)(n(),{href:e.href,className:"bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow",children:(0,r.jsx)("div",{className:"p-5",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:`${e.color} p-3 rounded-md`,children:(0,r.jsx)(t,{className:"h-6 w-6 text-white"})})}),(0,r.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,r.jsxs)("dl",{children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:e.name}),(0,r.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:e.value.toLocaleString()})]})})]})})},e.name)})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-lg font-medium text-gray-900 mb-4",children:"Quick Actions"}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:v.map(e=>{let t=e.icon;return(0,r.jsx)(n(),{href:e.href,className:`${e.color} text-white p-6 rounded-lg shadow hover:shadow-md transition-all`,children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(t,{className:"h-8 w-8 mr-4"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-medium",children:e.name}),(0,r.jsx)("p",{className:"text-sm opacity-90",children:e.description})]})]})},e.name)})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"bg-white shadow rounded-lg",children:[(0,r.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Recent Candidates"})}),(0,r.jsxs)("div",{className:"p-6",children:[e.recentCandidates.length>0?(0,r.jsx)("div",{className:"space-y-4",children:e.recentCandidates.slice(0,5).map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-gray-900",children:e.fullName}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:e.email})]}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:e.createdAt?new Date(e.createdAt).toLocaleDateString():"N/A"})]},e.id))}):(0,r.jsx)("p",{className:"text-gray-500 text-center py-4",children:"No recent candidates"}),(0,r.jsx)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:(0,r.jsx)(n(),{href:"/admin/candidates",className:"text-blue-600 hover:text-blue-700 text-sm font-medium",children:"View all candidates →"})})]})]}),(0,r.jsxs)("div",{className:"bg-white shadow rounded-lg",children:[(0,r.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Recent Test Results"})}),(0,r.jsxs)("div",{className:"p-6",children:[e.recentResults.length>0?(0,r.jsx)("div",{className:"space-y-4",children:e.recentResults.slice(0,5).map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-gray-900",children:e.candidate?.fullName}),(0,r.jsxs)("p",{className:"text-sm text-gray-500",children:["Overall: ",e.overallBandScore||"Pending"]})]}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:e.createdAt?new Date(e.createdAt).toLocaleDateString():"N/A"})]},e.id))}):(0,r.jsx)("p",{className:"text-gray-500 text-center py-4",children:"No recent results"}),(0,r.jsx)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:(0,r.jsx)(n(),{href:"/admin/results",className:"text-blue-600 hover:text-blue-700 text-sm font-medium",children:"View all results →"})})]})]})]})]})}},96038:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\admin\\layout.tsx","default")},17949:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\admin\\page.tsx","default")},71354:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d,metadata:()=>l});var r=s(62740),a=s(85041),i=s.n(a),n=s(70452);s(61135);let l={title:"IELTS Certification System",description:"Professional IELTS test result management and certification system"};function d({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsx)("body",{className:i().className,children:(0,r.jsx)(n.SessionProvider,{children:e})})})}},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(88077);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},61135:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[638,8338,2367],()=>s(39160));module.exports=r})();