(()=>{var e={};e.id=7378,e.ids=[7378],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},9422:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>c});var r=t(70260),a=t(28203),i=t(25155),l=t.n(i),d=t(67292),n={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>d[e]);t.d(s,n);let c=["",{children:["dashboard",{children:["results",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,66493)),"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\dashboard\\results\\[id]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,33405)),"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,71354)),"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],o=["C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\dashboard\\results\\[id]\\page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/results/[id]/page",pathname:"/dashboard/results/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},25750:(e,s,t)=>{Promise.resolve().then(t.bind(t,66493))},72606:(e,s,t)=>{Promise.resolve().then(t.bind(t,17665))},45037:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(41680).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},46583:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(41680).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},4643:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(41680).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},19473:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(41680).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},48857:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(41680).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},19904:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(41680).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},17665:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>k});var r=t(45512),a=t(58009),i=t(45103),l=t(79334),d=t(28531),n=t.n(d),c=t(46583),o=t(4643),x=t(45037),m=t(35668),h=t(19904),p=t(19473),u=t(87798),g=t(61075),f=t(45723),j=t(48857),b=t(64290),y=t(4269),N=t(42417),w=t(18814),v=t(79660),A=t(78397);function k(){let e=(0,l.useParams)().id,[s,t]=(0,a.useState)(null),[d,k]=(0,a.useState)(!0),[S,C]=(0,a.useState)(""),[L,P]=(0,a.useState)(!1);(0,a.useCallback)(async()=>{try{let s=await fetch(`/api/checker/results/${e}`);if(s.ok){let e=await s.json();t(e)}else C("Result not found")}catch(e){console.error("Error fetching result:",e),C("Failed to load result")}finally{k(!1)}},[e]);let R=async()=>{if(s){P(!0);try{let r=await fetch(`/api/certificate/${e}`,{method:"POST"});if(r.ok){let r=await fetch(`/api/certificate/${e}`);if(r.ok){let a=await r.blob(),i=window.URL.createObjectURL(a),l=document.createElement("a");l.href=i,l.download=`IELTS_Certificate_${s.candidate.fullName.replace(/\s+/g,"_")}_${e}.pdf`,document.body.appendChild(l),l.click(),window.URL.revokeObjectURL(i),document.body.removeChild(l),t(e=>e?{...e,certificateGenerated:!0}:null)}}else{let e=await r.json();alert(`Failed to generate certificate: ${e.error}`)}}catch(e){console.error("Certificate generation error:",e),alert("An error occurred while generating the certificate")}finally{P(!1)}}},_=async()=>{try{let t=await fetch(`/api/certificate/${e}`);if(t.ok){let r=await t.blob(),a=window.URL.createObjectURL(r),i=document.createElement("a");i.href=a,i.download=`IELTS_Certificate_${s?.candidate.fullName.replace(/\s+/g,"_")}_${e}.pdf`,document.body.appendChild(i),i.click(),window.URL.revokeObjectURL(a),document.body.removeChild(i)}}catch(e){console.error("Certificate download error:",e),alert("An error occurred while downloading the certificate")}};return d?(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):S||!s?(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)(x.A,{className:"h-12 w-12 text-red-400 mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Error Loading Result"}),(0,r.jsx)("p",{className:"text-gray-600",children:S||"Result not found"}),(0,r.jsxs)(n(),{href:"/dashboard/results/list",className:"mt-4 inline-flex items-center text-blue-600 hover:text-blue-700",children:[(0,r.jsx)(m.A,{className:"h-4 w-4 mr-1"}),"Back to Results"]})]}):(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(n(),{href:"/dashboard/results/list",className:"mr-4 p-2 text-gray-400 hover:text-gray-600",children:(0,r.jsx)(m.A,{className:"h-5 w-5"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Test Result Details"}),(0,r.jsx)("p",{className:"text-gray-600",children:"View complete IELTS test results"})]})]}),(0,r.jsxs)("div",{className:"flex gap-3",children:[(0,r.jsxs)(n(),{href:`/dashboard/results/${e}/edit`,className:"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50",children:[(0,r.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Edit Results"]}),s.certificateGenerated?(0,r.jsxs)("button",{onClick:_,className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700",children:[(0,r.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Download Certificate"]}):(0,r.jsx)("button",{onClick:R,disabled:L||"pending"===s.status,className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50",children:L?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Generating..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Generate Certificate"]})})]})]}),(0,r.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-medium text-gray-900 mb-4 flex items-center",children:[(0,r.jsx)(u.A,{className:"h-5 w-5 mr-2"}),"Candidate Information"]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[s.candidate.photoUrl?(0,r.jsx)(i.default,{className:"h-16 w-16 rounded-full object-cover",src:s.candidate.photoUrl,alt:s.candidate.fullName,width:64,height:64}):(0,r.jsx)("div",{className:"h-16 w-16 rounded-full bg-gray-200 flex items-center justify-center",children:(0,r.jsx)(u.A,{className:"h-8 w-8 text-gray-400"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"text-xl font-semibold text-gray-900",children:s.candidate.fullName}),(0,r.jsx)("p",{className:"text-gray-600",children:s.candidate.email}),(0,r.jsx)("p",{className:"text-gray-600",children:s.candidate.phoneNumber})]})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,r.jsx)(g.A,{className:"h-4 w-4 mr-2"}),"Passport: ",s.candidate.passportNumber]}),(0,r.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,r.jsx)(f.A,{className:"h-4 w-4 mr-2"}),"Test Date: ",new Date(s.candidate.testDate).toLocaleDateString()]}),(0,r.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,r.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"Test Center: ",s.candidate.testCenter]})]})]})]}),(0,r.jsx)("div",{className:"bg-white shadow rounded-lg p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(e=>{switch(e){case"completed":return(0,r.jsx)(c.A,{className:"h-5 w-5 text-green-500"});case"pending":return(0,r.jsx)(o.A,{className:"h-5 w-5 text-yellow-500"});default:return(0,r.jsx)(x.A,{className:"h-5 w-5 text-red-500"})}})(s.status),(0,r.jsx)("span",{className:`ml-3 ${(e=>{let s="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium";switch(e){case"completed":return`${s} bg-green-100 text-green-800`;case"pending":return`${s} bg-yellow-100 text-yellow-800`;default:return`${s} bg-red-100 text-red-800`}})(s.status)}`,children:s.status.charAt(0).toUpperCase()+s.status.slice(1)})]}),(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:["Entered: ",new Date(s.createdAt).toLocaleDateString(),s.updatedAt!==s.createdAt&&(0,r.jsxs)("span",{className:"ml-2",children:["• Updated: ",new Date(s.updatedAt).toLocaleDateString()]})]})]})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-medium text-gray-900 mb-4 flex items-center",children:[(0,r.jsx)(b.A,{className:"h-5 w-5 mr-2 text-blue-600"}),"Listening"]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Raw Score"}),(0,r.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[s.listeningScore||"N/A","/40"]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Band Score"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-blue-600",children:s.listeningBandScore||"N/A"})]})]})]}),(0,r.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-medium text-gray-900 mb-4 flex items-center",children:[(0,r.jsx)(y.A,{className:"h-5 w-5 mr-2 text-green-600"}),"Reading"]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Raw Score"}),(0,r.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[s.readingScore||"N/A","/40"]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Band Score"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-green-600",children:s.readingBandScore||"N/A"})]})]})]}),(0,r.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-medium text-gray-900 mb-4 flex items-center",children:[(0,r.jsx)(N.A,{className:"h-5 w-5 mr-2 text-purple-600"}),"Writing"]}),(0,r.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Task 1"}),(0,r.jsx)("p",{className:"text-xl font-bold text-gray-900",children:s.writingTask1Score||"N/A"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Task 2"}),(0,r.jsx)("p",{className:"text-xl font-bold text-gray-900",children:s.writingTask2Score||"N/A"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Band Score"}),(0,r.jsx)("p",{className:"text-xl font-bold text-purple-600",children:s.writingBandScore||"N/A"})]})]})]}),(0,r.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-medium text-gray-900 mb-4 flex items-center",children:[(0,r.jsx)(w.A,{className:"h-5 w-5 mr-2 text-red-600"}),"Speaking"]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4 mb-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"Fluency & Coherence"}),(0,r.jsx)("p",{className:"text-lg font-bold text-gray-900",children:s.speakingFluencyScore||"N/A"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"Lexical Resource"}),(0,r.jsx)("p",{className:"text-lg font-bold text-gray-900",children:s.speakingLexicalScore||"N/A"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"Grammar & Accuracy"}),(0,r.jsx)("p",{className:"text-lg font-bold text-gray-900",children:s.speakingGrammarScore||"N/A"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"Pronunciation"}),(0,r.jsx)("p",{className:"text-lg font-bold text-gray-900",children:s.speakingPronunciationScore||"N/A"})]})]}),(0,r.jsxs)("div",{className:"border-t pt-4",children:[(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Overall Band Score"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-red-600",children:s.speakingBandScore||"N/A"})]})]})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"bg-gradient-to-br from-blue-50 to-indigo-100 shadow rounded-lg p-8 text-center",children:[(0,r.jsxs)("h3",{className:"text-lg font-medium text-gray-900 mb-4 flex items-center justify-center",children:[(0,r.jsx)(v.A,{className:"h-5 w-5 mr-2 text-indigo-600"}),"Overall Band Score"]}),(0,r.jsx)("div",{className:"text-6xl font-bold text-indigo-600 mb-2",children:s.overallBandScore||"N/A"}),(0,r.jsx)("p",{className:"text-gray-600",children:"IELTS Band Score"})]}),(0,r.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Actions"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)(n(),{href:`/dashboard/feedback/generate?resultId=${e}`,className:"w-full flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700",children:[(0,r.jsx)(A.A,{className:"h-4 w-4 mr-2"}),"Generate AI Feedback"]}),s.certificateGenerated?(0,r.jsxs)("button",{onClick:_,className:"w-full flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50",children:[(0,r.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Download Certificate"]}):(0,r.jsx)("button",{onClick:R,disabled:L||"pending"===s.status,className:"w-full flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50",children:L?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600 mr-2"}),"Generating..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Generate Certificate"]})}),(0,r.jsxs)(n(),{href:`/dashboard/results/${e}/edit`,className:"w-full flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50",children:[(0,r.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Edit Results"]})]})]}),(0,r.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Score Summary"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-sm text-gray-600",children:"Listening"}),(0,r.jsx)("span",{className:"font-medium",children:s.listeningBandScore||"N/A"})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-sm text-gray-600",children:"Reading"}),(0,r.jsx)("span",{className:"font-medium",children:s.readingBandScore||"N/A"})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-sm text-gray-600",children:"Writing"}),(0,r.jsx)("span",{className:"font-medium",children:s.writingBandScore||"N/A"})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-sm text-gray-600",children:"Speaking"}),(0,r.jsx)("span",{className:"font-medium",children:s.speakingBandScore||"N/A"})]}),(0,r.jsxs)("div",{className:"border-t pt-3 flex justify-between",children:[(0,r.jsx)("span",{className:"font-medium text-gray-900",children:"Overall"}),(0,r.jsx)("span",{className:"font-bold text-indigo-600",children:s.overallBandScore||"N/A"})]})]})]})]})]})]})}},66493:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\dashboard\\results\\[id]\\page.tsx","default")}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[638,8338,2367,8324,7897],()=>t(9422));module.exports=r})();