(()=>{var e={};e.id=4824,e.ids=[4824],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},49140:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>o.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>l});var a=s(70260),t=s(28203),n=s(25155),o=s.n(n),i=s(67292),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);s.d(r,d);let l=["",{children:["dashboard",{children:["results",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,37623)),"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\dashboard\\results\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,33405)),"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,71354)),"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\dashboard\\results\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:t.RouteKind.APP_PAGE,page:"/dashboard/results/page",pathname:"/dashboard/results",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},45007:(e,r,s)=>{Promise.resolve().then(s.bind(s,37623))},4839:(e,r,s)=>{Promise.resolve().then(s.bind(s,29507))},41680:(e,r,s)=>{"use strict";s.d(r,{A:()=>m});var a=s(58009);let t=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),n=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,r,s)=>s?s.toUpperCase():r.toLowerCase()),o=e=>{let r=n(e);return r.charAt(0).toUpperCase()+r.slice(1)},i=(...e)=>e.filter((e,r,s)=>!!e&&""!==e.trim()&&s.indexOf(e)===r).join(" ").trim(),d=e=>{for(let r in e)if(r.startsWith("aria-")||"role"===r||"title"===r)return!0};var l={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,a.forwardRef)(({color:e="currentColor",size:r=24,strokeWidth:s=2,absoluteStrokeWidth:t,className:n="",children:o,iconNode:c,...m},u)=>(0,a.createElement)("svg",{ref:u,...l,width:r,height:r,stroke:e,strokeWidth:t?24*Number(s)/Number(r):s,className:i("lucide",n),...!o&&!d(m)&&{"aria-hidden":"true"},...m},[...c.map(([e,r])=>(0,a.createElement)(e,r)),...Array.isArray(o)?o:[o]])),m=(e,r)=>{let s=(0,a.forwardRef)(({className:s,...n},d)=>(0,a.createElement)(c,{ref:d,iconNode:r,className:i(`lucide-${t(o(e))}`,`lucide-${e}`,s),...n}));return s.displayName=o(e),s}},67497:(e,r,s)=>{"use strict";s.d(r,{A:()=>a});let a=(0,s(41680).A)("calculator",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",key:"1nb95v"}],["line",{x1:"8",x2:"16",y1:"6",y2:"6",key:"x4nwl0"}],["line",{x1:"16",x2:"16",y1:"14",y2:"18",key:"wjye3r"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M8 14h.01",key:"6423bh"}],["path",{d:"M12 18h.01",key:"mhygvu"}],["path",{d:"M8 18h.01",key:"lrp35t"}]])},61075:(e,r,s)=>{"use strict";s.d(r,{A:()=>a});let a=(0,s(41680).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},33680:(e,r,s)=>{"use strict";s.d(r,{A:()=>a});let a=(0,s(41680).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},79334:(e,r,s)=>{"use strict";var a=s(58686);s.o(a,"useParams")&&s.d(r,{useParams:function(){return a.useParams}}),s.o(a,"useRouter")&&s.d(r,{useRouter:function(){return a.useRouter}}),s.o(a,"useSearchParams")&&s.d(r,{useSearchParams:function(){return a.useSearchParams}})},29507:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>b});var a=s(45512),t=s(58009),n=s(79334),o=s(28531),i=s.n(o),d=s(35668),l=s(87798),c=s(16873),m=s(64290),u=s(4269),x=s(42417),p=s(18814),h=s(67497),g=s(33680);function b(){let e=(0,n.useRouter)(),r=(0,n.useSearchParams)().get("candidateId"),[s,o]=(0,t.useState)(null),[b,f]=(0,t.useState)(!1),[y,v]=(0,t.useState)(""),[j,S]=(0,t.useState)({candidateId:r||"",listeningScore:"",listeningBandScore:"",readingScore:"",readingBandScore:"",writingTask1Score:"",writingTask2Score:"",writingBandScore:"",speakingFluencyScore:"",speakingLexicalScore:"",speakingGrammarScore:"",speakingPronunciationScore:"",speakingBandScore:"",overallBandScore:""}),w=e=>{let{name:r,value:s}=e.target;S(e=>({...e,[r]:s}))};(0,t.useCallback)(()=>{let{listeningBandScore:e,readingBandScore:r,writingBandScore:s,speakingBandScore:a}=j;if(e&&r&&s&&a){let t=Math.round(2*((parseFloat(e)+parseFloat(r)+parseFloat(s)+parseFloat(a))/4))/2;S(e=>({...e,overallBandScore:t.toString()}))}},[j]);let k=async r=>{r.preventDefault(),f(!0),v("");try{let r=await fetch("/api/checker/results",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(j)});if(r.ok){let s=await r.json();e.push(`/dashboard/results/${s.id}`)}else{let e=await r.json();v(e.error||"Failed to save results")}}catch{v("An error occurred. Please try again.")}finally{f(!1)}};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"flex items-center justify-between",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(i(),{href:"/dashboard/search",className:"mr-4 p-2 text-gray-400 hover:text-gray-600",children:(0,a.jsx)(d.A,{className:"h-5 w-5"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Enter Test Results"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Record IELTS test scores for a candidate"})]})]})}),s?(0,a.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(l.A,{className:"h-5 w-5 text-blue-600 mr-2"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium text-blue-900",children:s.fullName}),(0,a.jsxs)("p",{className:"text-sm text-blue-700",children:[s.passportNumber," • Test Date: ",new Date(s.testDate).toLocaleDateString()]})]})]})}):r?(0,a.jsx)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4",children:(0,a.jsx)("div",{className:"animate-pulse",children:"Loading candidate information..."})}):(0,a.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(c.A,{className:"h-5 w-5 text-yellow-600 mr-2"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium text-yellow-900",children:"No candidate selected"}),(0,a.jsx)("p",{className:"text-sm text-yellow-700",children:"Please search for a candidate first or enter their ID below."})]})]})}),(0,a.jsx)("div",{className:"bg-white shadow rounded-lg",children:(0,a.jsxs)("form",{onSubmit:k,className:"p-6 space-y-8",children:[y&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm",children:y}),!r&&(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"candidateId",className:"block text-sm font-medium text-gray-700 mb-2",children:"Candidate ID *"}),(0,a.jsx)("input",{type:"text",id:"candidateId",name:"candidateId",value:j.candidateId,onChange:w,required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter candidate ID"})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg font-medium text-gray-900 mb-4 flex items-center",children:[(0,a.jsx)(m.A,{className:"h-5 w-5 mr-2 text-blue-600"}),"Listening"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"listeningScore",className:"block text-sm font-medium text-gray-700 mb-2",children:"Raw Score (0-40)"}),(0,a.jsx)("input",{type:"number",id:"listeningScore",name:"listeningScore",value:j.listeningScore,onChange:w,min:"0",max:"40",step:"1",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"listeningBandScore",className:"block text-sm font-medium text-gray-700 mb-2",children:"Band Score (1-9)"}),(0,a.jsx)("input",{type:"number",id:"listeningBandScore",name:"listeningBandScore",value:j.listeningBandScore,onChange:w,min:"1",max:"9",step:"0.5",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg font-medium text-gray-900 mb-4 flex items-center",children:[(0,a.jsx)(u.A,{className:"h-5 w-5 mr-2 text-green-600"}),"Reading"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"readingScore",className:"block text-sm font-medium text-gray-700 mb-2",children:"Raw Score (0-40)"}),(0,a.jsx)("input",{type:"number",id:"readingScore",name:"readingScore",value:j.readingScore,onChange:w,min:"0",max:"40",step:"1",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"readingBandScore",className:"block text-sm font-medium text-gray-700 mb-2",children:"Band Score (1-9)"}),(0,a.jsx)("input",{type:"number",id:"readingBandScore",name:"readingBandScore",value:j.readingBandScore,onChange:w,min:"1",max:"9",step:"0.5",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg font-medium text-gray-900 mb-4 flex items-center",children:[(0,a.jsx)(x.A,{className:"h-5 w-5 mr-2 text-purple-600"}),"Writing"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"writingTask1Score",className:"block text-sm font-medium text-gray-700 mb-2",children:"Task 1 Score (1-9)"}),(0,a.jsx)("input",{type:"number",id:"writingTask1Score",name:"writingTask1Score",value:j.writingTask1Score,onChange:w,min:"1",max:"9",step:"0.5",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"writingTask2Score",className:"block text-sm font-medium text-gray-700 mb-2",children:"Task 2 Score (1-9)"}),(0,a.jsx)("input",{type:"number",id:"writingTask2Score",name:"writingTask2Score",value:j.writingTask2Score,onChange:w,min:"1",max:"9",step:"0.5",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"writingBandScore",className:"block text-sm font-medium text-gray-700 mb-2",children:"Overall Band Score (1-9)"}),(0,a.jsx)("input",{type:"number",id:"writingBandScore",name:"writingBandScore",value:j.writingBandScore,onChange:w,min:"1",max:"9",step:"0.5",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg font-medium text-gray-900 mb-4 flex items-center",children:[(0,a.jsx)(p.A,{className:"h-5 w-5 mr-2 text-red-600"}),"Speaking"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"speakingFluencyScore",className:"block text-sm font-medium text-gray-700 mb-2",children:"Fluency & Coherence"}),(0,a.jsx)("input",{type:"number",id:"speakingFluencyScore",name:"speakingFluencyScore",value:j.speakingFluencyScore,onChange:w,min:"1",max:"9",step:"0.5",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"speakingLexicalScore",className:"block text-sm font-medium text-gray-700 mb-2",children:"Lexical Resource"}),(0,a.jsx)("input",{type:"number",id:"speakingLexicalScore",name:"speakingLexicalScore",value:j.speakingLexicalScore,onChange:w,min:"1",max:"9",step:"0.5",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"speakingGrammarScore",className:"block text-sm font-medium text-gray-700 mb-2",children:"Grammar & Accuracy"}),(0,a.jsx)("input",{type:"number",id:"speakingGrammarScore",name:"speakingGrammarScore",value:j.speakingGrammarScore,onChange:w,min:"1",max:"9",step:"0.5",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"speakingPronunciationScore",className:"block text-sm font-medium text-gray-700 mb-2",children:"Pronunciation"}),(0,a.jsx)("input",{type:"number",id:"speakingPronunciationScore",name:"speakingPronunciationScore",value:j.speakingPronunciationScore,onChange:w,min:"1",max:"9",step:"0.5",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"speakingBandScore",className:"block text-sm font-medium text-gray-700 mb-2",children:"Overall Band Score"}),(0,a.jsx)("input",{type:"number",id:"speakingBandScore",name:"speakingBandScore",value:j.speakingBandScore,onChange:w,min:"1",max:"9",step:"0.5",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]})]})]}),(0,a.jsxs)("div",{className:"bg-gray-50 p-6 rounded-lg",children:[(0,a.jsxs)("h3",{className:"text-lg font-medium text-gray-900 mb-4 flex items-center",children:[(0,a.jsx)(h.A,{className:"h-5 w-5 mr-2 text-indigo-600"}),"Overall Band Score"]}),(0,a.jsxs)("div",{className:"max-w-xs",children:[(0,a.jsx)("label",{htmlFor:"overallBandScore",className:"block text-sm font-medium text-gray-700 mb-2",children:"Calculated Overall Score"}),(0,a.jsx)("input",{type:"number",id:"overallBandScore",name:"overallBandScore",value:j.overallBandScore,onChange:w,min:"1",max:"9",step:"0.5",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white",readOnly:!0}),(0,a.jsx)("p",{className:"mt-1 text-xs text-gray-500",children:"Automatically calculated from individual band scores"})]})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-4 pt-6 border-t border-gray-200",children:[(0,a.jsx)(i(),{href:"/dashboard/search",className:"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50",children:"Cancel"}),(0,a.jsx)("button",{type:"submit",disabled:b||!j.candidateId,className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50",children:b?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Saving..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(g.A,{className:"h-4 w-4 mr-2"}),"Save Results"]})})]})]})})]})}},37623:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>a});let a=(0,s(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\dashboard\\results\\page.tsx","default")}};var r=require("../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),a=r.X(0,[638,8338,2367,7897],()=>s(49140));module.exports=a})();