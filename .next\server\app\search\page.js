(()=>{var e={};e.id=2959,e.ids=[2959],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},6696:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>h,tree:()=>d});var r=s(70260),a=s(28203),i=s(25155),n=s.n(i),l=s(67292),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let d=["",{children:["search",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,22524)),"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\search\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,71354)),"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\search\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/search/page",pathname:"/search",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},97032:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,13219,23)),Promise.resolve().then(s.t.bind(s,34863,23)),Promise.resolve().then(s.t.bind(s,25155,23)),Promise.resolve().then(s.t.bind(s,40802,23)),Promise.resolve().then(s.t.bind(s,9350,23)),Promise.resolve().then(s.t.bind(s,48530,23)),Promise.resolve().then(s.t.bind(s,88921,23))},60584:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,66959,23)),Promise.resolve().then(s.t.bind(s,33875,23)),Promise.resolve().then(s.t.bind(s,88903,23)),Promise.resolve().then(s.t.bind(s,57174,23)),Promise.resolve().then(s.t.bind(s,84178,23)),Promise.resolve().then(s.t.bind(s,87190,23)),Promise.resolve().then(s.t.bind(s,61365,23))},71315:(e,t,s)=>{Promise.resolve().then(s.bind(s,70452))},8267:(e,t,s)=>{Promise.resolve().then(s.bind(s,98805))},20531:(e,t,s)=>{Promise.resolve().then(s.bind(s,22524))},57483:(e,t,s)=>{Promise.resolve().then(s.bind(s,17200))},41680:(e,t,s)=>{"use strict";s.d(t,{A:()=>m});var r=s(58009);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,s)=>s?s.toUpperCase():t.toLowerCase()),n=e=>{let t=i(e);return t.charAt(0).toUpperCase()+t.slice(1)},l=(...e)=>e.filter((e,t,s)=>!!e&&""!==e.trim()&&s.indexOf(e)===t).join(" ").trim(),o=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,r.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:s=2,absoluteStrokeWidth:a,className:i="",children:n,iconNode:c,...m},h)=>(0,r.createElement)("svg",{ref:h,...d,width:t,height:t,stroke:e,strokeWidth:a?24*Number(s)/Number(t):s,className:l("lucide",i),...!n&&!o(m)&&{"aria-hidden":"true"},...m},[...c.map(([e,t])=>(0,r.createElement)(e,t)),...Array.isArray(n)?n:[n]])),m=(e,t)=>{let s=(0,r.forwardRef)(({className:s,...i},o)=>(0,r.createElement)(c,{ref:o,iconNode:t,className:l(`lucide-${a(n(e))}`,`lucide-${e}`,s),...i}));return s.displayName=n(e),s}},35668:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(41680).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},19473:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(41680).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},21956:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(41680).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},61075:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(41680).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},16873:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(41680).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},17200:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>h});var r=s(45512),a=s(58009),i=s(28531),n=s.n(i),l=s(35668),o=s(61075),d=s(16873),c=s(21956),m=s(19473);function h(){let[e,t]=(0,a.useState)(""),[s,i]=(0,a.useState)("name"),[h,x]=(0,a.useState)([]),[u,p]=(0,a.useState)(!1),[b,f]=(0,a.useState)(!1),g=async t=>{if(t.preventDefault(),e.trim()){p(!0),f(!0);try{let t=await fetch("/api/search",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({query:e,searchType:s})});if(t.ok){let e=await t.json();x(e)}else x([])}catch(e){console.error("Search error:",e),x([])}finally{p(!1)}}},y=async e=>{try{let t=await fetch(`/api/certificate/${e}`);if(t.ok){let s=await t.blob(),r=window.URL.createObjectURL(s),a=document.createElement("a");a.href=r,a.download=`IELTS_Certificate_${e}.pdf`,document.body.appendChild(a),a.click(),window.URL.revokeObjectURL(r),document.body.removeChild(a)}}catch(e){console.error("Download error:",e)}};return(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100",children:[(0,r.jsx)("header",{className:"bg-white shadow-sm",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsx)("div",{className:"flex justify-between items-center py-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsxs)(n(),{href:"/",className:"flex items-center text-blue-600 hover:text-blue-700 mr-4",children:[(0,r.jsx)(l.A,{className:"h-5 w-5 mr-1"}),"Back"]}),(0,r.jsx)(o.A,{className:"h-8 w-8 text-blue-600 mr-3"}),(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Search IELTS Results"})]})})})}),(0,r.jsxs)("main",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-8 mb-8",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Find Your Test Results"}),(0,r.jsxs)("form",{onSubmit:g,className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"searchType",className:"block text-sm font-medium text-gray-700 mb-2",children:"Search by"}),(0,r.jsxs)("select",{id:"searchType",value:s,onChange:e=>i(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,r.jsx)("option",{value:"name",children:"Full Name"}),(0,r.jsx)("option",{value:"email",children:"Email Address"}),(0,r.jsx)("option",{value:"passport",children:"Passport Number"}),(0,r.jsx)("option",{value:"certificate",children:"Certificate ID"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"searchQuery",className:"block text-sm font-medium text-gray-700 mb-2",children:["name"===s&&"Enter your full name","email"===s&&"Enter your email address","passport"===s&&"Enter your passport number","certificate"===s&&"Enter your certificate ID"]}),(0,r.jsx)("input",{type:"text",id:"searchQuery",value:e,onChange:e=>t(e.target.value),placeholder:"name"===s?"John Doe":"email"===s?"<EMAIL>":"passport"===s?"A12345678":"CERT123456",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500",required:!0})]}),(0,r.jsx)("button",{type:"submit",disabled:u,className:"w-full flex justify-center items-center px-4 py-2 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50",children:u?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Searching..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(d.A,{className:"h-4 w-4 mr-2"}),"Search Results"]})})]})]}),b&&(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-8",children:[(0,r.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-6",children:"Search Results"}),u?(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),(0,r.jsx)("p",{className:"mt-2 text-gray-600",children:"Searching..."})]}):h.length>0?(0,r.jsx)("div",{className:"space-y-6",children:h.map(e=>(0,r.jsx)("div",{className:"border border-gray-200 rounded-lg p-6",children:(0,r.jsxs)("div",{className:"flex justify-between items-start",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h4",{className:"text-lg font-semibold text-gray-900",children:e.candidate.fullName}),(0,r.jsxs)("p",{className:"text-gray-600",children:["Test Date: ",new Date(e.candidate.testDate).toLocaleDateString()]}),(0,r.jsxs)("p",{className:"text-gray-600",children:["Test Center: ",e.candidate.testCenter]}),(0,r.jsxs)("div",{className:"mt-4 grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Listening"}),(0,r.jsx)("p",{className:"text-lg font-semibold text-blue-600",children:e.listeningBandScore||"N/A"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Reading"}),(0,r.jsx)("p",{className:"text-lg font-semibold text-blue-600",children:e.readingBandScore||"N/A"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Writing"}),(0,r.jsx)("p",{className:"text-lg font-semibold text-blue-600",children:e.writingBandScore||"N/A"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Speaking"}),(0,r.jsx)("p",{className:"text-lg font-semibold text-blue-600",children:e.speakingBandScore||"N/A"})]})]}),(0,r.jsxs)("div",{className:"mt-4 text-center",children:[(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Overall Band Score"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-green-600",children:e.overallBandScore||"N/A"})]})]}),(0,r.jsxs)("div",{className:"ml-4 flex flex-col space-y-2",children:[(0,r.jsxs)(n(),{href:`/results/${e.id}`,className:"inline-flex items-center px-4 py-2 border border-blue-300 text-sm font-medium rounded-md text-blue-700 bg-blue-50 hover:bg-blue-100",children:[(0,r.jsx)(c.A,{className:"h-4 w-4 mr-2"}),"View Details"]}),e.certificateGenerated&&(0,r.jsxs)("button",{onClick:()=>y(e.id),className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700",children:[(0,r.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Download Certificate"]})]})]})},e.id))}):(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)(d.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600",children:"No results found. Please check your search criteria and try again."})]})]})]})]})}},71354:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o,metadata:()=>l});var r=s(62740),a=s(85041),i=s.n(a),n=s(70452);s(61135);let l={title:"IELTS Certification System",description:"Professional IELTS test result management and certification system"};function o({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsx)("body",{className:i().className,children:(0,r.jsx)(n.SessionProvider,{children:e})})})}},22524:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\search\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\search\\page.tsx","default")},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(88077);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},61135:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[638,8338,2367],()=>s(6696));module.exports=r})();