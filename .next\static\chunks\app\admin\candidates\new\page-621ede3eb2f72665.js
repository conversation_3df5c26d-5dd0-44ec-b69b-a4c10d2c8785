(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4604],{3056:(e,t,a)=>{Promise.resolve().then(a.bind(a,4428))},7364:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(7401).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},4081:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(7401).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},6462:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(7401).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},7780:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(7401).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},1466:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(7401).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},6046:(e,t,a)=>{"use strict";var r=a(6658);a.o(r,"useParams")&&a.d(t,{useParams:function(){return r.useParams}}),a.o(r,"useRouter")&&a.d(t,{useRouter:function(){return r.useRouter}}),a.o(r,"useSearchParams")&&a.d(t,{useSearchParams:function(){return r.useSearchParams}})},4428:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>N});var r=a(5155),s=a(2115),l=a(6046),d=a(8173),n=a.n(d),i=a(7364),o=a(1466),c=a(6462),m=a(2423),u=a(7401);let h=(0,u.A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]]);var x=a(7780),p=a(4081);let b=(0,u.A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),f=(0,u.A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]),g=(0,u.A)("image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]]);var y=a(5565);function j(e){let{type:t,onUpload:a,onRemove:l,currentFile:d,accept:n,maxSize:i=5,className:o="",disabled:c=!1}=e,[m,u]=(0,s.useState)(!1),[x,j]=(0,s.useState)(!1),[N,v]=(0,s.useState)(""),w=(0,s.useRef)(null),k=async e=>{if(!c){v(""),u(!0);try{if(e.size>1048576*i)throw Error("File size must be less than ".concat(i,"MB"));let r=new FormData;r.append("file",e),r.append("type",t);let s=await fetch("/api/upload",{method:"POST",body:r});if(!s.ok){let e=await s.json();throw Error(e.error||"Upload failed")}let l=await s.json();a(l.url)}catch(e){console.error("Upload error:",e),v(e instanceof Error?e.message:"Upload failed")}finally{u(!1)}}},C=async()=>{if(d&&!c)try{await fetch("/api/upload?url=".concat(encodeURIComponent(d)),{method:"DELETE"}),null==l||l()}catch(e){console.error("Remove error:",e)}};return d?(0,r.jsx)("div",{className:"relative ".concat(o),children:(0,r.jsx)("div",{className:"border-2 border-gray-200 rounded-lg p-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:["photo"===t?(0,r.jsx)("div",{className:"relative",children:(0,r.jsx)(y.default,{src:d,alt:"Uploaded file",className:"h-16 w-16 object-cover rounded-lg",width:64,height:64})}):(0,r.jsx)("div",{className:"h-16 w-16 bg-gray-100 rounded-lg flex items-center justify-center",children:(0,r.jsx)(p.A,{className:"h-8 w-8 text-gray-400"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"File uploaded"}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"Click remove to change file"})]})]}),!c&&(0,r.jsx)("button",{onClick:C,className:"p-1 text-gray-400 hover:text-red-500 transition-colors",title:"Remove file",children:(0,r.jsx)(b,{className:"h-5 w-5"})})]})})}):(0,r.jsxs)("div",{className:"relative ".concat(o),children:[(0,r.jsx)("input",{ref:w,type:"file",accept:n||("photo"===t?"image/jpeg,image/jpg,image/png,image/webp":"application/pdf,image/jpeg,image/jpg,image/png"),onChange:e=>{var t;let a=null===(t=e.target.files)||void 0===t?void 0:t[0];a&&k(a)},className:"hidden",disabled:c}),(0,r.jsx)("div",{onClick:()=>{if(!c){var e;null===(e=w.current)||void 0===e||e.click()}},onDrop:e=>{var t;if(e.preventDefault(),j(!1),c)return;let a=null===(t=e.dataTransfer.files)||void 0===t?void 0:t[0];a&&k(a)},onDragOver:e=>{e.preventDefault(),c||j(!0)},onDragLeave:e=>{e.preventDefault(),j(!1)},className:"\n          border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-all\n          ".concat(x?"border-blue-400 bg-blue-50":"border-gray-300 hover:border-gray-400","\n          ").concat(c?"opacity-50 cursor-not-allowed":"","\n          ").concat(N?"border-red-300 bg-red-50":"","\n        "),children:m?(0,r.jsxs)("div",{className:"flex flex-col items-center",children:[(0,r.jsx)(f,{className:"h-12 w-12 text-blue-500 animate-spin mb-4"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Uploading..."})]}):(0,r.jsxs)("div",{className:"flex flex-col items-center",children:["photo"===t?(0,r.jsx)(g,{className:"h-12 w-12 text-gray-400 mb-4"}):(0,r.jsx)(h,{className:"h-12 w-12 text-gray-400 mb-4"}),(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900 mb-2",children:"photo"===t?"Upload Photo":"Upload Document"}),(0,r.jsx)("p",{className:"text-xs text-gray-500 mb-2",children:"Drag and drop or click to select"}),(0,r.jsx)("p",{className:"text-xs text-gray-400",children:"photo"===t?"PNG, JPG, WEBP up to 5MB":"PDF, PNG, JPG up to 5MB"})]})}),N&&(0,r.jsx)("p",{className:"mt-2 text-sm text-red-600",children:N})]})}function N(){let e=(0,l.useRouter)(),[t,a]=(0,s.useState)(!1),[d,u]=(0,s.useState)(""),[p,b]=(0,s.useState)({fullName:"",email:"",phoneNumber:"",dateOfBirth:"",nationality:"",passportNumber:"",testDate:"",testCenter:"",photoUrl:""}),f=e=>{let{name:t,value:a}=e.target;b(e=>({...e,[t]:a}))},g=async t=>{t.preventDefault(),a(!0),u("");try{let t=await fetch("/api/admin/candidates",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(p)});if(t.ok)e.push("/admin/candidates");else{let e=await t.json();u(e.error||"Failed to create candidate")}}catch(e){u("An error occurred. Please try again.")}finally{a(!1)}};return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)("div",{className:"flex items-center justify-between",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(n(),{href:"/admin/candidates",className:"mr-4 p-2 text-gray-400 hover:text-gray-600",children:(0,r.jsx)(i.A,{className:"h-5 w-5"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Add New Candidate"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Register a new test candidate"})]})]})}),(0,r.jsx)("div",{className:"bg-white shadow rounded-lg",children:(0,r.jsxs)("form",{onSubmit:g,className:"p-6 space-y-6",children:[d&&(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm",children:d}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("h3",{className:"text-lg font-medium text-gray-900 mb-4 flex items-center",children:[(0,r.jsx)(o.A,{className:"h-5 w-5 mr-2"}),"Personal Information"]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"fullName",className:"block text-sm font-medium text-gray-700 mb-2",children:"Full Name *"}),(0,r.jsx)("input",{type:"text",id:"fullName",name:"fullName",value:p.fullName,onChange:f,required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter full name as on passport"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"dateOfBirth",className:"block text-sm font-medium text-gray-700 mb-2",children:"Date of Birth *"}),(0,r.jsx)("input",{type:"date",id:"dateOfBirth",name:"dateOfBirth",value:p.dateOfBirth,onChange:f,required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"nationality",className:"block text-sm font-medium text-gray-700 mb-2",children:"Nationality *"}),(0,r.jsx)("input",{type:"text",id:"nationality",name:"nationality",value:p.nationality,onChange:f,required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"e.g., British, American, etc."})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"passportNumber",className:"block text-sm font-medium text-gray-700 mb-2",children:"Passport Number *"}),(0,r.jsx)("input",{type:"text",id:"passportNumber",name:"passportNumber",value:p.passportNumber,onChange:f,required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter passport number"})]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("h3",{className:"text-lg font-medium text-gray-900 mb-4 flex items-center",children:[(0,r.jsx)(c.A,{className:"h-5 w-5 mr-2"}),"Contact Information"]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address *"}),(0,r.jsx)("input",{type:"email",id:"email",name:"email",value:p.email,onChange:f,required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"<EMAIL>"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"phoneNumber",className:"block text-sm font-medium text-gray-700 mb-2",children:"Phone Number *"}),(0,r.jsx)("input",{type:"tel",id:"phoneNumber",name:"phoneNumber",value:p.phoneNumber,onChange:f,required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"+44 ************"})]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("h3",{className:"text-lg font-medium text-gray-900 mb-4 flex items-center",children:[(0,r.jsx)(m.A,{className:"h-5 w-5 mr-2"}),"Test Information"]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"testDate",className:"block text-sm font-medium text-gray-700 mb-2",children:"Test Date *"}),(0,r.jsx)("input",{type:"date",id:"testDate",name:"testDate",value:p.testDate,onChange:f,required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"testCenter",className:"block text-sm font-medium text-gray-700 mb-2",children:"Test Center *"}),(0,r.jsxs)("select",{id:"testCenter",name:"testCenter",value:p.testCenter,onChange:f,required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,r.jsx)("option",{value:"",children:"Select test center"}),["IELTS Test Center - London","IELTS Test Center - Manchester","IELTS Test Center - Birmingham","IELTS Test Center - Edinburgh","IELTS Test Center - Cardiff","IELTS Test Center - Belfast","IELTS Test Center - Dublin","IELTS Test Center - Online"].map(e=>(0,r.jsx)("option",{value:e,children:e},e))]})]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("h3",{className:"text-lg font-medium text-gray-900 mb-4 flex items-center",children:[(0,r.jsx)(h,{className:"h-5 w-5 mr-2"}),"Candidate Photo"]}),(0,r.jsx)(j,{type:"photo",onUpload:e=>b(t=>({...t,photoUrl:e})),onRemove:()=>b(e=>({...e,photoUrl:""})),currentFile:p.photoUrl,className:"w-full"})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-4 pt-6 border-t border-gray-200",children:[(0,r.jsx)(n(),{href:"/admin/candidates",className:"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50",children:"Cancel"}),(0,r.jsx)("button",{type:"submit",disabled:t,className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50",children:t?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Creating..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Create Candidate"]})})]})]})})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[8173,7311,8441,1517,7358],()=>t(3056)),_N_E=e.O()}]);