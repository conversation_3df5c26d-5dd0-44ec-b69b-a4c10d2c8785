{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "rORHHhwc3NsgphI9ZKS87", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "hcvttE4qR5m80jNd9MQKTge/C51jAVC9BrNZDySonF8=", "__NEXT_PREVIEW_MODE_ID": "ea303d828f4666ad07e2d9bb805c4a56", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "c6ea69b90abacc6aeb9afec638e339888b4d531c057c2eb8b20db59f62829f90", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "18e3e3d84515775afc198b4939736d999f5e11ae595d28fee7354b9266ba0234"}}}, "functions": {}, "sortedMiddleware": ["/"]}