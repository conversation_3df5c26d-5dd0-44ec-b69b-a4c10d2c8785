{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "pXr5NvZG2o6RUsLF7OfOu", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "hcvttE4qR5m80jNd9MQKTge/C51jAVC9BrNZDySonF8=", "__NEXT_PREVIEW_MODE_ID": "c556cffd82ca62fc82dd3f87193c73d2", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "94a367e8424df37704d3dfd32156b46c096b09d920906811f4d2690683d25c0b", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "09163db85729e882ec2fe99262e79a237dd603f7a2715188a55126a0d8558ca7"}}}, "functions": {}, "sortedMiddleware": ["/"]}