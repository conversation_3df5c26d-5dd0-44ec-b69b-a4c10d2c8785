(()=>{var e={};e.id=526,e.ids=[526],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},55511:e=>{"use strict";e.exports=require("crypto")},2113:e=>{"use strict";e.exports=import("postgres")},77598:e=>{"use strict";e.exports=require("node:crypto")},87546:(e,t,s)=>{"use strict";s.a(e,async(e,r)=>{try{s.r(t),s.d(t,{patchFetch:()=>c,routeModule:()=>u,serverHooks:()=>p,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>h});var n=s(42706),i=s(28203),a=s(45994),o=s(83799),l=e([o]);o=(l.then?(await l)():l)[0];let u=new n.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/ai/generate-feedback/route",pathname:"/api/ai/generate-feedback",filename:"route",bundlePath:"app/api/ai/generate-feedback/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\api\\ai\\generate-feedback\\route.ts",nextConfigOutput:"",userland:o}),{workAsyncStorage:d,workUnitAsyncStorage:h,serverHooks:p}=u;function c(){return(0,a.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:h})}r()}catch(e){r(e)}})},96487:()=>{},78335:()=>{},83799:(e,t,s)=>{"use strict";s.a(e,async(e,r)=>{try{s.r(t),s.d(t,{POST:()=>l});var n=s(39187),i=s(37702),a=s(55406),o=e([i]);async function l(e){try{if(!await (0,i.j2)())return n.NextResponse.json({error:"Unauthorized"},{status:401});let{resultId:t,scores:s}=await e.json();if(!t||!s)return n.NextResponse.json({error:"Result ID and scores are required"},{status:400});let{listening:r,reading:o,writing:l,speaking:c,overall:u}=s;if(!u)return n.NextResponse.json({error:"Overall band score is required for feedback generation"},{status:400});let d=await (0,a.H)({listeningScore:r,readingScore:o,writingScore:l,speakingScore:c,overallScore:u});return n.NextResponse.json({feedback:d,resultId:t})}catch(e){if(console.error("Error generating AI feedback:",e),e instanceof Error){if(e.message.includes("API key"))return n.NextResponse.json({error:"AI service configuration error. Please contact administrator."},{status:500});if(e.message.includes("rate limit"))return n.NextResponse.json({error:"AI service temporarily unavailable. Please try again later."},{status:429})}return n.NextResponse.json({error:"Failed to generate feedback. Please try again."},{status:500})}}i=(o.then?(await o)():o)[0],r()}catch(e){r(e)}})},55406:(e,t,s)=>{"use strict";var r,n,i,a,o,l,c,u,d,h,p,f,m,g,y,b,w,_,v,k,S,x,R,q,A,P,E,N,M,T,I,$,j,O,L,U,W,F,C,D,Q,B,X,H,J,K,V,z,G;let Y,Z,ee;function et(e,t,s,r,n){if("m"===r)throw TypeError("Private method is not writable");if("a"===r&&!n)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!n:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===r?n.call(e,s):n?n.value=s:t.set(e,s),s}function es(e,t,s,r){if("a"===s&&!r)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!r:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===s?r:"a"===s?r.call(e):r?r.value:t.get(e)}s.d(t,{H:()=>tQ});let er=function(){let{crypto:e}=globalThis;if(e?.randomUUID)return er=e.randomUUID.bind(e),e.randomUUID();let t=new Uint8Array(1),s=e?()=>e.getRandomValues(t)[0]:()=>255*Math.random()&255;return"10000000-1000-4000-8000-100000000000".replace(/[018]/g,e=>(+e^s()&15>>+e/4).toString(16))};function en(e){return"object"==typeof e&&null!==e&&("name"in e&&"AbortError"===e.name||"message"in e&&String(e.message).includes("FetchRequestCanceledException"))}let ei=e=>{if(e instanceof Error)return e;if("object"==typeof e&&null!==e){try{if("[object Error]"===Object.prototype.toString.call(e)){let t=Error(e.message,e.cause?{cause:e.cause}:{});return e.stack&&(t.stack=e.stack),e.cause&&!t.cause&&(t.cause=e.cause),e.name&&(t.name=e.name),t}}catch{}try{return Error(JSON.stringify(e))}catch{}}return Error(e)};class ea extends Error{}class eo extends ea{constructor(e,t,s,r){super(`${eo.makeMessage(e,t,s)}`),this.status=e,this.headers=r,this.requestID=r?.get("request-id"),this.error=t}static makeMessage(e,t,s){let r=t?.message?"string"==typeof t.message?t.message:JSON.stringify(t.message):t?JSON.stringify(t):s;return e&&r?`${e} ${r}`:e?`${e} status code (no body)`:r||"(no status code or body)"}static generate(e,t,s,r){return e&&r?400===e?new ed(e,t,s,r):401===e?new eh(e,t,s,r):403===e?new ep(e,t,s,r):404===e?new ef(e,t,s,r):409===e?new em(e,t,s,r):422===e?new eg(e,t,s,r):429===e?new ey(e,t,s,r):e>=500?new eb(e,t,s,r):new eo(e,t,s,r):new ec({message:s,cause:ei(t)})}}class el extends eo{constructor({message:e}={}){super(void 0,void 0,e||"Request was aborted.",void 0)}}class ec extends eo{constructor({message:e,cause:t}){super(void 0,void 0,e||"Connection error.",void 0),t&&(this.cause=t)}}class eu extends ec{constructor({message:e}={}){super({message:e??"Request timed out."})}}class ed extends eo{}class eh extends eo{}class ep extends eo{}class ef extends eo{}class em extends eo{}class eg extends eo{}class ey extends eo{}class eb extends eo{}let ew=/^[a-z][a-z0-9+.-]*:/i,e_=e=>ew.test(e);function ev(e){return"object"!=typeof e?{}:e??{}}let ek=(e,t)=>{if("number"!=typeof t||!Number.isInteger(t))throw new ea(`${e} must be an integer`);if(t<0)throw new ea(`${e} must be a positive integer`);return t},eS=e=>{try{return JSON.parse(e)}catch(e){return}},ex=e=>new Promise(t=>setTimeout(t,e)),eR={off:0,error:200,warn:300,info:400,debug:500},eq=(e,t,s)=>{if(e){if(Object.prototype.hasOwnProperty.call(eR,e))return e;eM(s).warn(`${t} was set to ${JSON.stringify(e)}, expected one of ${JSON.stringify(Object.keys(eR))}`)}};function eA(){}function eP(e,t,s){return!t||eR[e]>eR[s]?eA:t[e].bind(t)}let eE={error:eA,warn:eA,info:eA,debug:eA},eN=new WeakMap;function eM(e){let t=e.logger,s=e.logLevel??"off";if(!t)return eE;let r=eN.get(t);if(r&&r[0]===s)return r[1];let n={error:eP("error",t,s),warn:eP("warn",t,s),info:eP("info",t,s),debug:eP("debug",t,s)};return eN.set(t,[s,n]),n}let eT=e=>(e.options&&(e.options={...e.options},delete e.options.headers),e.headers&&(e.headers=Object.fromEntries((e.headers instanceof Headers?[...e.headers]:Object.entries(e.headers)).map(([e,t])=>[e,"x-api-key"===e.toLowerCase()||"authorization"===e.toLowerCase()||"cookie"===e.toLowerCase()||"set-cookie"===e.toLowerCase()?"***":t]))),"retryOfRequestLogID"in e&&(e.retryOfRequestLogID&&(e.retryOf=e.retryOfRequestLogID),delete e.retryOfRequestLogID),e),eI="0.52.0",e$=()=>"undefined"!=typeof window&&void 0!==window.document&&"undefined"!=typeof navigator,ej=()=>{let e="undefined"!=typeof Deno&&null!=Deno.build?"deno":"undefined"!=typeof EdgeRuntime?"edge":"[object process]"===Object.prototype.toString.call(void 0!==globalThis.process?globalThis.process:0)?"node":"unknown";if("deno"===e)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":eI,"X-Stainless-OS":eL(Deno.build.os),"X-Stainless-Arch":eO(Deno.build.arch),"X-Stainless-Runtime":"deno","X-Stainless-Runtime-Version":"string"==typeof Deno.version?Deno.version:Deno.version?.deno??"unknown"};if("undefined"!=typeof EdgeRuntime)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":eI,"X-Stainless-OS":"Unknown","X-Stainless-Arch":`other:${EdgeRuntime}`,"X-Stainless-Runtime":"edge","X-Stainless-Runtime-Version":globalThis.process.version};if("node"===e)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":eI,"X-Stainless-OS":eL(globalThis.process.platform),"X-Stainless-Arch":eO(globalThis.process.arch),"X-Stainless-Runtime":"node","X-Stainless-Runtime-Version":globalThis.process.version};let t=function(){if("undefined"==typeof navigator||!navigator)return null;for(let{key:e,pattern:t}of[{key:"edge",pattern:/Edge(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/MSIE(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/Trident(?:.*rv\:(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"chrome",pattern:/Chrome(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"firefox",pattern:/Firefox(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"safari",pattern:/(?:Version\W+(\d+)\.(\d+)(?:\.(\d+))?)?(?:\W+Mobile\S*)?\W+Safari/}]){let s=t.exec(navigator.userAgent);if(s){let t=s[1]||0,r=s[2]||0,n=s[3]||0;return{browser:e,version:`${t}.${r}.${n}`}}}return null}();return t?{"X-Stainless-Lang":"js","X-Stainless-Package-Version":eI,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":`browser:${t.browser}`,"X-Stainless-Runtime-Version":t.version}:{"X-Stainless-Lang":"js","X-Stainless-Package-Version":eI,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":"unknown","X-Stainless-Runtime-Version":"unknown"}},eO=e=>"x32"===e?"x32":"x86_64"===e||"x64"===e?"x64":"arm"===e?"arm":"aarch64"===e||"arm64"===e?"arm64":e?`other:${e}`:"unknown",eL=e=>(e=e.toLowerCase()).includes("ios")?"iOS":"android"===e?"Android":"darwin"===e?"MacOS":"win32"===e?"Windows":"freebsd"===e?"FreeBSD":"openbsd"===e?"OpenBSD":"linux"===e?"Linux":e?`Other:${e}`:"Unknown",eU=()=>Y??(Y=ej());function eW(...e){let t=globalThis.ReadableStream;if(void 0===t)throw Error("`ReadableStream` is not defined as a global; You will need to polyfill it, `globalThis.ReadableStream = ReadableStream`");return new t(...e)}function eF(e){let t=Symbol.asyncIterator in e?e[Symbol.asyncIterator]():e[Symbol.iterator]();return eW({start(){},async pull(e){let{done:s,value:r}=await t.next();s?e.close():e.enqueue(r)},async cancel(){await t.return?.()}})}function eC(e){if(e[Symbol.asyncIterator])return e;let t=e.getReader();return{async next(){try{let e=await t.read();return e?.done&&t.releaseLock(),e}catch(e){throw t.releaseLock(),e}},async return(){let e=t.cancel();return t.releaseLock(),await e,{done:!0,value:void 0}},[Symbol.asyncIterator](){return this}}}async function eD(e){if(null===e||"object"!=typeof e)return;if(e[Symbol.asyncIterator]){await e[Symbol.asyncIterator]().return?.();return}let t=e.getReader(),s=t.cancel();t.releaseLock(),await s}let eQ=({headers:e,body:t})=>({bodyHeaders:{"content-type":"application/json"},body:JSON.stringify(t)});function eB(e){let t;return(Z??(Z=(t=new globalThis.TextEncoder).encode.bind(t)))(e)}function eX(e){let t;return(ee??(ee=(t=new globalThis.TextDecoder).decode.bind(t)))(e)}class eH{constructor(){r.set(this,void 0),n.set(this,void 0),et(this,r,new Uint8Array,"f"),et(this,n,null,"f")}decode(e){let t;if(null==e)return[];let s=e instanceof ArrayBuffer?new Uint8Array(e):"string"==typeof e?eB(e):e;et(this,r,function(e){let t=0;for(let s of e)t+=s.length;let s=new Uint8Array(t),r=0;for(let t of e)s.set(t,r),r+=t.length;return s}([es(this,r,"f"),s]),"f");let i=[];for(;null!=(t=function(e,t){for(let s=t??0;s<e.length;s++){if(10===e[s])return{preceding:s,index:s+1,carriage:!1};if(13===e[s])return{preceding:s,index:s+1,carriage:!0}}return null}(es(this,r,"f"),es(this,n,"f")));){if(t.carriage&&null==es(this,n,"f")){et(this,n,t.index,"f");continue}if(null!=es(this,n,"f")&&(t.index!==es(this,n,"f")+1||t.carriage)){i.push(eX(es(this,r,"f").subarray(0,es(this,n,"f")-1))),et(this,r,es(this,r,"f").subarray(es(this,n,"f")),"f"),et(this,n,null,"f");continue}let e=null!==es(this,n,"f")?t.preceding-1:t.preceding,s=eX(es(this,r,"f").subarray(0,e));i.push(s),et(this,r,es(this,r,"f").subarray(t.index),"f"),et(this,n,null,"f")}return i}flush(){return es(this,r,"f").length?this.decode("\n"):[]}}r=new WeakMap,n=new WeakMap,eH.NEWLINE_CHARS=new Set(["\n","\r"]),eH.NEWLINE_REGEXP=/\r\n|[\n\r]/g;class eJ{constructor(e,t){this.iterator=e,this.controller=t}static fromSSEResponse(e,t){let s=!1;return new eJ(async function*(){if(s)throw new ea("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");s=!0;let r=!1;try{for await(let s of eK(e,t)){if("completion"===s.event||"message_start"===s.event||"message_delta"===s.event||"message_stop"===s.event||"content_block_start"===s.event||"content_block_delta"===s.event||"content_block_stop"===s.event)try{yield JSON.parse(s.data)}catch(e){throw console.error("Could not parse message into JSON:",s.data),console.error("From chunk:",s.raw),e}if("ping"!==s.event&&"error"===s.event)throw new eo(void 0,eS(s.data)??s.data,void 0,e.headers)}r=!0}catch(e){if(en(e))return;throw e}finally{r||t.abort()}},t)}static fromReadableStream(e,t){let s=!1;async function*r(){let t=new eH;for await(let s of eC(e))for(let e of t.decode(s))yield e;for(let e of t.flush())yield e}return new eJ(async function*(){if(s)throw new ea("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");s=!0;let e=!1;try{for await(let t of r())!e&&t&&(yield JSON.parse(t));e=!0}catch(e){if(en(e))return;throw e}finally{e||t.abort()}},t)}[Symbol.asyncIterator](){return this.iterator()}tee(){let e=[],t=[],s=this.iterator(),r=r=>({next:()=>{if(0===r.length){let r=s.next();e.push(r),t.push(r)}return r.shift()}});return[new eJ(()=>r(e),this.controller),new eJ(()=>r(t),this.controller)]}toReadableStream(){let e;let t=this;return eW({async start(){e=t[Symbol.asyncIterator]()},async pull(t){try{let{value:s,done:r}=await e.next();if(r)return t.close();let n=eB(JSON.stringify(s)+"\n");t.enqueue(n)}catch(e){t.error(e)}},async cancel(){await e.return?.()}})}}async function*eK(e,t){if(!e.body){if(t.abort(),void 0!==globalThis.navigator&&"ReactNative"===globalThis.navigator.product)throw new ea("The default react-native fetch implementation does not support streaming. Please use expo/fetch: https://docs.expo.dev/versions/latest/sdk/expo/#expofetch-api");throw new ea("Attempted to iterate over a response with no body")}let s=new ez,r=new eH;for await(let t of eV(eC(e.body)))for(let e of r.decode(t)){let t=s.decode(e);t&&(yield t)}for(let e of r.flush()){let t=s.decode(e);t&&(yield t)}}async function*eV(e){let t=new Uint8Array;for await(let s of e){let e;if(null==s)continue;let r=s instanceof ArrayBuffer?new Uint8Array(s):"string"==typeof s?eB(s):s,n=new Uint8Array(t.length+r.length);for(n.set(t),n.set(r,t.length),t=n;-1!==(e=function(e){for(let t=0;t<e.length-1;t++){if(10===e[t]&&10===e[t+1]||13===e[t]&&13===e[t+1])return t+2;if(13===e[t]&&10===e[t+1]&&t+3<e.length&&13===e[t+2]&&10===e[t+3])return t+4}return -1}(t));)yield t.slice(0,e),t=t.slice(e)}t.length>0&&(yield t)}class ez{constructor(){this.event=null,this.data=[],this.chunks=[]}decode(e){if(e.endsWith("\r")&&(e=e.substring(0,e.length-1)),!e){if(!this.event&&!this.data.length)return null;let e={event:this.event,data:this.data.join("\n"),raw:this.chunks};return this.event=null,this.data=[],this.chunks=[],e}if(this.chunks.push(e),e.startsWith(":"))return null;let[t,s,r]=function(e,t){let s=e.indexOf(":");return -1!==s?[e.substring(0,s),":",e.substring(s+t.length)]:[e,"",""]}(e,":");return r.startsWith(" ")&&(r=r.substring(1)),"event"===t?this.event=r:"data"===t&&this.data.push(r),null}}async function eG(e,t){let{response:s,requestLogID:r,retryOfRequestLogID:n,startTime:i}=t,a=await (async()=>{if(t.options.stream)return(eM(e).debug("response",s.status,s.url,s.headers,s.body),t.options.__streamClass)?t.options.__streamClass.fromSSEResponse(s,t.controller):eJ.fromSSEResponse(s,t.controller);if(204===s.status)return null;if(t.options.__binaryResponse)return s;let r=s.headers.get("content-type"),n=r?.split(";")[0]?.trim();return n?.includes("application/json")||n?.endsWith("+json")?eY(await s.json(),s):await s.text()})();return eM(e).debug(`[${r}] response parsed`,eT({retryOfRequestLogID:n,url:s.url,status:s.status,body:a,durationMs:Date.now()-i})),a}function eY(e,t){return!e||"object"!=typeof e||Array.isArray(e)?e:Object.defineProperty(e,"_request_id",{value:t.headers.get("request-id"),enumerable:!1})}class eZ extends Promise{constructor(e,t,s=eG){super(e=>{e(null)}),this.responsePromise=t,this.parseResponse=s,i.set(this,void 0),et(this,i,e,"f")}_thenUnwrap(e){return new eZ(es(this,i,"f"),this.responsePromise,async(t,s)=>eY(e(await this.parseResponse(t,s),s),s.response))}asResponse(){return this.responsePromise.then(e=>e.response)}async withResponse(){let[e,t]=await Promise.all([this.parse(),this.asResponse()]);return{data:e,response:t,request_id:t.headers.get("request-id")}}parse(){return this.parsedPromise||(this.parsedPromise=this.responsePromise.then(e=>this.parseResponse(es(this,i,"f"),e))),this.parsedPromise}then(e,t){return this.parse().then(e,t)}catch(e){return this.parse().catch(e)}finally(e){return this.parse().finally(e)}}i=new WeakMap;class e0{constructor(e,t,s,r){a.set(this,void 0),et(this,a,e,"f"),this.options=r,this.response=t,this.body=s}hasNextPage(){return!!this.getPaginatedItems().length&&null!=this.nextPageRequestOptions()}async getNextPage(){let e=this.nextPageRequestOptions();if(!e)throw new ea("No next page expected; please check `.hasNextPage()` before calling `.getNextPage()`.");return await es(this,a,"f").requestAPIList(this.constructor,e)}async *iterPages(){let e=this;for(yield e;e.hasNextPage();)e=await e.getNextPage(),yield e}async *[(a=new WeakMap,Symbol.asyncIterator)](){for await(let e of this.iterPages())for(let t of e.getPaginatedItems())yield t}}class e1 extends eZ{constructor(e,t,s){super(e,t,async(e,t)=>new s(e,t.response,await eG(e,t),t.options))}async *[Symbol.asyncIterator](){for await(let e of(await this))yield e}}class e2 extends e0{constructor(e,t,s,r){super(e,t,s,r),this.data=s.data||[],this.has_more=s.has_more||!1,this.first_id=s.first_id||null,this.last_id=s.last_id||null}getPaginatedItems(){return this.data??[]}hasNextPage(){return!1!==this.has_more&&super.hasNextPage()}nextPageRequestOptions(){if(this.options.query?.before_id){let e=this.first_id;return e?{...this.options,query:{...ev(this.options.query),before_id:e}}:null}let e=this.last_id;return e?{...this.options,query:{...ev(this.options.query),after_id:e}}:null}}let e4=()=>{if("undefined"==typeof File){let{process:e}=globalThis;throw Error("`File` is not defined as a global, which is required for file uploads."+("string"==typeof e?.versions?.node&&20>parseInt(e.versions.node.split("."))?" Update to Node 20 LTS or newer, or set `globalThis.File` to `import('node:buffer').File`.":""))}};function e5(e,t,s){return e4(),new File(e,t??"unknown_file",s)}function e3(e){return("object"==typeof e&&null!==e&&("name"in e&&e.name&&String(e.name)||"url"in e&&e.url&&String(e.url)||"filename"in e&&e.filename&&String(e.filename)||"path"in e&&e.path&&String(e.path))||"").split(/[\\/]/).pop()||void 0}let e9=e=>null!=e&&"object"==typeof e&&"function"==typeof e[Symbol.asyncIterator],e6=async(e,t)=>({...e,body:await e7(e.body,t)}),e8=new WeakMap,e7=async(e,t)=>{if(!await function(e){let t="function"==typeof e?e:e.fetch,s=e8.get(t);if(s)return s;let r=(async()=>{try{let e="Response"in t?t.Response:(await t("data:,")).constructor,s=new FormData;if(s.toString()===await new e(s).text())return!1;return!0}catch{return!0}})();return e8.set(t,r),r}(t))throw TypeError("The provided fetch function does not support file uploads with the current global FormData class.");let s=new FormData;return await Promise.all(Object.entries(e||{}).map(([e,t])=>tr(s,e,t))),s},te=e=>e instanceof Blob&&"name"in e,tt=e=>"object"==typeof e&&null!==e&&(e instanceof Response||e9(e)||te(e)),ts=e=>{if(tt(e))return!0;if(Array.isArray(e))return e.some(ts);if(e&&"object"==typeof e){for(let t in e)if(ts(e[t]))return!0}return!1},tr=async(e,t,s)=>{if(void 0!==s){if(null==s)throw TypeError(`Received null for "${t}"; to pass null in FormData, you must use the string 'null'`);if("string"==typeof s||"number"==typeof s||"boolean"==typeof s)e.append(t,String(s));else if(s instanceof Response){let r={},n=s.headers.get("Content-Type");n&&(r={type:n}),e.append(t,e5([await s.blob()],e3(s),r))}else if(e9(s))e.append(t,e5([await new Response(eF(s)).blob()],e3(s)));else if(te(s))e.append(t,e5([s],e3(s),{type:s.type}));else if(Array.isArray(s))await Promise.all(s.map(s=>tr(e,t+"[]",s)));else if("object"==typeof s)await Promise.all(Object.entries(s).map(([s,r])=>tr(e,`${t}[${s}]`,r)));else throw TypeError(`Invalid value given to form, expected a string, number, boolean, object, Array, File or Blob but got ${s} instead`)}},tn=e=>null!=e&&"object"==typeof e&&"number"==typeof e.size&&"string"==typeof e.type&&"function"==typeof e.text&&"function"==typeof e.slice&&"function"==typeof e.arrayBuffer,ti=e=>null!=e&&"object"==typeof e&&"string"==typeof e.name&&"number"==typeof e.lastModified&&tn(e),ta=e=>null!=e&&"object"==typeof e&&"string"==typeof e.url&&"function"==typeof e.blob;async function to(e,t,s){if(e4(),e=await e,t||(t=e3(e)),ti(e))return e instanceof File&&null==t&&null==s?e:e5([await e.arrayBuffer()],t??e.name,{type:e.type,lastModified:e.lastModified,...s});if(ta(e)){let r=await e.blob();return t||(t=new URL(e.url).pathname.split(/[\\/]/).pop()),e5(await tl(r),t,s)}let r=await tl(e);if(!s?.type){let e=r.find(e=>"object"==typeof e&&"type"in e&&e.type);"string"==typeof e&&(s={...s,type:e})}return e5(r,t,s)}async function tl(e){let t=[];if("string"==typeof e||ArrayBuffer.isView(e)||e instanceof ArrayBuffer)t.push(e);else if(tn(e))t.push(e instanceof Blob?e:await e.arrayBuffer());else if(e9(e))for await(let s of e)t.push(...await tl(s));else{let t=e?.constructor?.name;throw Error(`Unexpected data type: ${typeof e}${t?`; constructor: ${t}`:""}${function(e){if("object"!=typeof e||null===e)return"";let t=Object.getOwnPropertyNames(e);return`; props: [${t.map(e=>`"${e}"`).join(", ")}]`}(e)}`)}return t}class tc{constructor(e){this._client=e}}let tu=Symbol.for("brand.privateNullableHeaders"),td=Array.isArray,th=e=>{let t=new Headers,s=new Set;for(let r of e){let e=new Set;for(let[n,i]of function*(e){let t;if(!e)return;if(tu in e){let{values:t,nulls:s}=e;for(let e of(yield*t.entries(),s))yield[e,null];return}let s=!1;for(let r of(e instanceof Headers?t=e.entries():td(e)?t=e:(s=!0,t=Object.entries(e??{})),t)){let e=r[0];if("string"!=typeof e)throw TypeError("expected header name to be a string");let t=td(r[1])?r[1]:[r[1]],n=!1;for(let r of t)void 0!==r&&(s&&!n&&(n=!0,yield[e,null]),yield[e,r])}}(r)){let r=n.toLowerCase();e.has(r)||(t.delete(n),e.add(r)),null===i?(t.delete(n),s.add(r)):(t.append(n,i),s.delete(r))}}return{[tu]:!0,values:t,nulls:s}};function tp(e){return e.replace(/[^A-Za-z0-9\-._~!$&'()*+,;=:@]+/g,encodeURIComponent)}let tf=((e=tp)=>function(t,...s){let r;if(1===t.length)return t[0];let n=!1,i=t.reduce((t,r,i)=>(/[?#]/.test(r)&&(n=!0),t+r+(i===s.length?"":(n?encodeURIComponent:e)(String(s[i])))),""),a=i.split(/[?#]/,1)[0],o=[],l=/(?<=^|\/)(?:\.|%2e){1,2}(?=\/|$)/gi;for(;null!==(r=l.exec(a));)o.push({start:r.index,length:r[0].length});if(o.length>0){let e=0,t=o.reduce((t,s)=>{let r=" ".repeat(s.start-e),n="^".repeat(s.length);return e=s.start+s.length,t+r+n},"");throw new ea(`Path parameters result in path with invalid segments:
${i}
${t}`)}return i})(tp);class tm extends tc{list(e={},t){let{betas:s,...r}=e??{};return this._client.getAPIList("/v1/files",e2,{query:r,...t,headers:th([{"anthropic-beta":[...s??[],"files-api-2025-04-14"].toString()},t?.headers])})}delete(e,t={},s){let{betas:r}=t??{};return this._client.delete(tf`/v1/files/${e}`,{...s,headers:th([{"anthropic-beta":[...r??[],"files-api-2025-04-14"].toString()},s?.headers])})}download(e,t={},s){let{betas:r}=t??{};return this._client.get(tf`/v1/files/${e}/content`,{...s,headers:th([{"anthropic-beta":[...r??[],"files-api-2025-04-14"].toString(),Accept:"application/binary"},s?.headers]),__binaryResponse:!0})}retrieveMetadata(e,t={},s){let{betas:r}=t??{};return this._client.get(tf`/v1/files/${e}`,{...s,headers:th([{"anthropic-beta":[...r??[],"files-api-2025-04-14"].toString()},s?.headers])})}upload(e,t){let{betas:s,...r}=e;return this._client.post("/v1/files",e6({body:r,...t,headers:th([{"anthropic-beta":[...s??[],"files-api-2025-04-14"].toString()},t?.headers])},this._client))}}class tg extends tc{retrieve(e,t={},s){let{betas:r}=t??{};return this._client.get(tf`/v1/models/${e}?beta=true`,{...s,headers:th([{...r?.toString()!=null?{"anthropic-beta":r?.toString()}:void 0},s?.headers])})}list(e={},t){let{betas:s,...r}=e??{};return this._client.getAPIList("/v1/models?beta=true",e2,{query:r,...t,headers:th([{...s?.toString()!=null?{"anthropic-beta":s?.toString()}:void 0},t?.headers])})}}class ty{constructor(e,t){this.iterator=e,this.controller=t}async *decoder(){let e=new eH;for await(let t of this.iterator)for(let s of e.decode(t))yield JSON.parse(s);for(let t of e.flush())yield JSON.parse(t)}[Symbol.asyncIterator](){return this.decoder()}static fromResponse(e,t){if(!e.body){if(t.abort(),void 0!==globalThis.navigator&&"ReactNative"===globalThis.navigator.product)throw new ea("The default react-native fetch implementation does not support streaming. Please use expo/fetch: https://docs.expo.dev/versions/latest/sdk/expo/#expofetch-api");throw new ea("Attempted to iterate over a response with no body")}return new ty(eC(e.body),t)}}class tb extends tc{create(e,t){let{betas:s,...r}=e;return this._client.post("/v1/messages/batches?beta=true",{body:r,...t,headers:th([{"anthropic-beta":[...s??[],"message-batches-2024-09-24"].toString()},t?.headers])})}retrieve(e,t={},s){let{betas:r}=t??{};return this._client.get(tf`/v1/messages/batches/${e}?beta=true`,{...s,headers:th([{"anthropic-beta":[...r??[],"message-batches-2024-09-24"].toString()},s?.headers])})}list(e={},t){let{betas:s,...r}=e??{};return this._client.getAPIList("/v1/messages/batches?beta=true",e2,{query:r,...t,headers:th([{"anthropic-beta":[...s??[],"message-batches-2024-09-24"].toString()},t?.headers])})}delete(e,t={},s){let{betas:r}=t??{};return this._client.delete(tf`/v1/messages/batches/${e}?beta=true`,{...s,headers:th([{"anthropic-beta":[...r??[],"message-batches-2024-09-24"].toString()},s?.headers])})}cancel(e,t={},s){let{betas:r}=t??{};return this._client.post(tf`/v1/messages/batches/${e}/cancel?beta=true`,{...s,headers:th([{"anthropic-beta":[...r??[],"message-batches-2024-09-24"].toString()},s?.headers])})}async results(e,t={},s){let r=await this.retrieve(e);if(!r.results_url)throw new ea(`No batch \`results_url\`; Has it finished processing? ${r.processing_status} - ${r.id}`);let{betas:n}=t??{};return this._client.get(r.results_url,{...s,headers:th([{"anthropic-beta":[...n??[],"message-batches-2024-09-24"].toString(),Accept:"application/binary"},s?.headers]),stream:!0,__binaryResponse:!0})._thenUnwrap((e,t)=>ty.fromResponse(t.response,t.controller))}}let tw=e=>{let t=0,s=[];for(;t<e.length;){let r=e[t];if("\\"===r){t++;continue}if("{"===r){s.push({type:"brace",value:"{"}),t++;continue}if("}"===r){s.push({type:"brace",value:"}"}),t++;continue}if("["===r){s.push({type:"paren",value:"["}),t++;continue}if("]"===r){s.push({type:"paren",value:"]"}),t++;continue}if(":"===r){s.push({type:"separator",value:":"}),t++;continue}if(","===r){s.push({type:"delimiter",value:","}),t++;continue}if('"'===r){let n="",i=!1;for(r=e[++t];'"'!==r;){if(t===e.length){i=!0;break}if("\\"===r){if(++t===e.length){i=!0;break}n+=r+e[t],r=e[++t]}else n+=r,r=e[++t]}r=e[++t],i||s.push({type:"string",value:n});continue}let n=/\s/;if(r&&n.test(r)){t++;continue}let i=/[0-9]/;if(r&&i.test(r)||"-"===r||"."===r){let n="";for("-"===r&&(n+=r,r=e[++t]);r&&i.test(r)||"."===r;)n+=r,r=e[++t];s.push({type:"number",value:n});continue}let a=/[a-z]/i;if(r&&a.test(r)){let n="";for(;r&&a.test(r)&&t!==e.length;)n+=r,r=e[++t];"true"==n||"false"==n||"null"===n?s.push({type:"name",value:n}):t++;continue}t++}return s},t_=e=>{if(0===e.length)return e;let t=e[e.length-1];switch(t.type){case"separator":return t_(e=e.slice(0,e.length-1));case"number":let s=t.value[t.value.length-1];if("."===s||"-"===s)return t_(e=e.slice(0,e.length-1));case"string":let r=e[e.length-2];if(r?.type==="delimiter"||r?.type==="brace"&&"{"===r.value)return t_(e=e.slice(0,e.length-1));break;case"delimiter":return t_(e=e.slice(0,e.length-1))}return e},tv=e=>{let t=[];return e.map(e=>{"brace"===e.type&&("{"===e.value?t.push("}"):t.splice(t.lastIndexOf("}"),1)),"paren"===e.type&&("["===e.value?t.push("]"):t.splice(t.lastIndexOf("]"),1))}),t.length>0&&t.reverse().map(t=>{"}"===t?e.push({type:"brace",value:"}"}):"]"===t&&e.push({type:"paren",value:"]"})}),e},tk=e=>{let t="";return e.map(e=>{"string"===e.type?t+='"'+e.value+'"':t+=e.value}),t},tS=e=>JSON.parse(tk(tv(t_(tw(e))))),tx="__json_buf";class tR{constructor(){o.add(this),this.messages=[],this.receivedMessages=[],l.set(this,void 0),this.controller=new AbortController,c.set(this,void 0),u.set(this,()=>{}),d.set(this,()=>{}),h.set(this,void 0),p.set(this,()=>{}),f.set(this,()=>{}),m.set(this,{}),g.set(this,!1),y.set(this,!1),b.set(this,!1),w.set(this,!1),_.set(this,void 0),v.set(this,void 0),x.set(this,e=>{if(et(this,y,!0,"f"),en(e)&&(e=new el),e instanceof el)return et(this,b,!0,"f"),this._emit("abort",e);if(e instanceof ea)return this._emit("error",e);if(e instanceof Error){let t=new ea(e.message);return t.cause=e,this._emit("error",t)}return this._emit("error",new ea(String(e)))}),et(this,c,new Promise((e,t)=>{et(this,u,e,"f"),et(this,d,t,"f")}),"f"),et(this,h,new Promise((e,t)=>{et(this,p,e,"f"),et(this,f,t,"f")}),"f"),es(this,c,"f").catch(()=>{}),es(this,h,"f").catch(()=>{})}get response(){return es(this,_,"f")}get request_id(){return es(this,v,"f")}async withResponse(){let e=await es(this,c,"f");if(!e)throw Error("Could not resolve a `Response` object");return{data:this,response:e,request_id:e.headers.get("request-id")}}static fromReadableStream(e){let t=new tR;return t._run(()=>t._fromReadableStream(e)),t}static createMessage(e,t,s){let r=new tR;for(let e of t.messages)r._addMessageParam(e);return r._run(()=>r._createMessage(e,{...t,stream:!0},{...s,headers:{...s?.headers,"X-Stainless-Helper-Method":"stream"}})),r}_run(e){e().then(()=>{this._emitFinal(),this._emit("end")},es(this,x,"f"))}_addMessageParam(e){this.messages.push(e)}_addMessage(e,t=!0){this.receivedMessages.push(e),t&&this._emit("message",e)}async _createMessage(e,t,s){let r=s?.signal;r&&(r.aborted&&this.controller.abort(),r.addEventListener("abort",()=>this.controller.abort())),es(this,o,"m",R).call(this);let{response:n,data:i}=await e.create({...t,stream:!0},{...s,signal:this.controller.signal}).withResponse();for await(let e of(this._connected(n),i))es(this,o,"m",q).call(this,e);if(i.controller.signal?.aborted)throw new el;es(this,o,"m",A).call(this)}_connected(e){this.ended||(et(this,_,e,"f"),et(this,v,e?.headers.get("request-id"),"f"),es(this,u,"f").call(this,e),this._emit("connect"))}get ended(){return es(this,g,"f")}get errored(){return es(this,y,"f")}get aborted(){return es(this,b,"f")}abort(){this.controller.abort()}on(e,t){return(es(this,m,"f")[e]||(es(this,m,"f")[e]=[])).push({listener:t}),this}off(e,t){let s=es(this,m,"f")[e];if(!s)return this;let r=s.findIndex(e=>e.listener===t);return r>=0&&s.splice(r,1),this}once(e,t){return(es(this,m,"f")[e]||(es(this,m,"f")[e]=[])).push({listener:t,once:!0}),this}emitted(e){return new Promise((t,s)=>{et(this,w,!0,"f"),"error"!==e&&this.once("error",s),this.once(e,t)})}async done(){et(this,w,!0,"f"),await es(this,h,"f")}get currentMessage(){return es(this,l,"f")}async finalMessage(){return await this.done(),es(this,o,"m",k).call(this)}async finalText(){return await this.done(),es(this,o,"m",S).call(this)}_emit(e,...t){if(es(this,g,"f"))return;"end"===e&&(et(this,g,!0,"f"),es(this,p,"f").call(this));let s=es(this,m,"f")[e];if(s&&(es(this,m,"f")[e]=s.filter(e=>!e.once),s.forEach(({listener:e})=>e(...t))),"abort"===e){let e=t[0];es(this,w,"f")||s?.length||Promise.reject(e),es(this,d,"f").call(this,e),es(this,f,"f").call(this,e),this._emit("end");return}if("error"===e){let e=t[0];es(this,w,"f")||s?.length||Promise.reject(e),es(this,d,"f").call(this,e),es(this,f,"f").call(this,e),this._emit("end")}}_emitFinal(){this.receivedMessages.at(-1)&&this._emit("finalMessage",es(this,o,"m",k).call(this))}async _fromReadableStream(e,t){let s=t?.signal;s&&(s.aborted&&this.controller.abort(),s.addEventListener("abort",()=>this.controller.abort())),es(this,o,"m",R).call(this),this._connected(null);let r=eJ.fromReadableStream(e,this.controller);for await(let e of r)es(this,o,"m",q).call(this,e);if(r.controller.signal?.aborted)throw new el;es(this,o,"m",A).call(this)}[(l=new WeakMap,c=new WeakMap,u=new WeakMap,d=new WeakMap,h=new WeakMap,p=new WeakMap,f=new WeakMap,m=new WeakMap,g=new WeakMap,y=new WeakMap,b=new WeakMap,w=new WeakMap,_=new WeakMap,v=new WeakMap,x=new WeakMap,o=new WeakSet,k=function(){if(0===this.receivedMessages.length)throw new ea("stream ended without producing a Message with role=assistant");return this.receivedMessages.at(-1)},S=function(){if(0===this.receivedMessages.length)throw new ea("stream ended without producing a Message with role=assistant");let e=this.receivedMessages.at(-1).content.filter(e=>"text"===e.type).map(e=>e.text);if(0===e.length)throw new ea("stream ended without producing a content block with type=text");return e.join(" ")},R=function(){this.ended||et(this,l,void 0,"f")},q=function(e){if(this.ended)return;let t=es(this,o,"m",P).call(this,e);switch(this._emit("streamEvent",e,t),e.type){case"content_block_delta":{let s=t.content.at(-1);switch(e.delta.type){case"text_delta":"text"===s.type&&this._emit("text",e.delta.text,s.text||"");break;case"citations_delta":"text"===s.type&&this._emit("citation",e.delta.citation,s.citations??[]);break;case"input_json_delta":("tool_use"===s.type||"mcp_tool_use"===s.type)&&s.input&&this._emit("inputJson",e.delta.partial_json,s.input);break;case"thinking_delta":"thinking"===s.type&&this._emit("thinking",e.delta.thinking,s.thinking);break;case"signature_delta":"thinking"===s.type&&this._emit("signature",s.signature);break;default:e.delta}break}case"message_stop":this._addMessageParam(t),this._addMessage(t,!0);break;case"content_block_stop":this._emit("contentBlock",t.content.at(-1));break;case"message_start":et(this,l,t,"f")}},A=function(){if(this.ended)throw new ea("stream has ended, this shouldn't happen");let e=es(this,l,"f");if(!e)throw new ea("request ended without sending any chunks");return et(this,l,void 0,"f"),e},P=function(e){let t=es(this,l,"f");if("message_start"===e.type){if(t)throw new ea(`Unexpected event order, got ${e.type} before receiving "message_stop"`);return e.message}if(!t)throw new ea(`Unexpected event order, got ${e.type} before "message_start"`);switch(e.type){case"message_stop":case"content_block_stop":return t;case"message_delta":return t.container=e.delta.container,t.stop_reason=e.delta.stop_reason,t.stop_sequence=e.delta.stop_sequence,t.usage.output_tokens=e.usage.output_tokens,null!=e.usage.input_tokens&&(t.usage.input_tokens=e.usage.input_tokens),null!=e.usage.cache_creation_input_tokens&&(t.usage.cache_creation_input_tokens=e.usage.cache_creation_input_tokens),null!=e.usage.cache_read_input_tokens&&(t.usage.cache_read_input_tokens=e.usage.cache_read_input_tokens),null!=e.usage.server_tool_use&&(t.usage.server_tool_use=e.usage.server_tool_use),t;case"content_block_start":return t.content.push(e.content_block),t;case"content_block_delta":{let s=t.content.at(e.index);switch(e.delta.type){case"text_delta":s?.type==="text"&&(s.text+=e.delta.text);break;case"citations_delta":s?.type==="text"&&(s.citations??(s.citations=[]),s.citations.push(e.delta.citation));break;case"input_json_delta":if(s?.type==="tool_use"||s?.type==="mcp_tool_use"){let t=s[tx]||"";Object.defineProperty(s,tx,{value:t+=e.delta.partial_json,enumerable:!1,writable:!0}),t&&(s.input=tS(t))}break;case"thinking_delta":s?.type==="thinking"&&(s.thinking+=e.delta.thinking);break;case"signature_delta":s?.type==="thinking"&&(s.signature=e.delta.signature);break;default:e.delta}return t}}},Symbol.asyncIterator)](){let e=[],t=[],s=!1;return this.on("streamEvent",s=>{let r=t.shift();r?r.resolve(s):e.push(s)}),this.on("end",()=>{for(let e of(s=!0,t))e.resolve(void 0);t.length=0}),this.on("abort",e=>{for(let r of(s=!0,t))r.reject(e);t.length=0}),this.on("error",e=>{for(let r of(s=!0,t))r.reject(e);t.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:s?{value:void 0,done:!0}:new Promise((e,s)=>t.push({resolve:e,reject:s})).then(e=>e?{value:e,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}toReadableStream(){return new eJ(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}}let tq={"claude-opus-4-20250514":8192,"claude-opus-4-0":8192,"claude-4-opus-20250514":8192,"anthropic.claude-opus-4-20250514-v1:0":8192,"claude-opus-4@20250514":8192},tA={"claude-1.3":"November 6th, 2024","claude-1.3-100k":"November 6th, 2024","claude-instant-1.1":"November 6th, 2024","claude-instant-1.1-100k":"November 6th, 2024","claude-instant-1.2":"November 6th, 2024","claude-3-sonnet-20240229":"July 21st, 2025","claude-2.1":"July 21st, 2025","claude-2.0":"July 21st, 2025"};class tP extends tc{constructor(){super(...arguments),this.batches=new tb(this._client)}create(e,t){let{betas:s,...r}=e;r.model in tA&&console.warn(`The model '${r.model}' is deprecated and will reach end-of-life on ${tA[r.model]}
Please migrate to a newer model. Visit https://docs.anthropic.com/en/docs/resources/model-deprecations for more information.`);let n=this._client._options.timeout;if(!r.stream&&null==n){let e=tq[r.model]??void 0;n=this._client.calculateNonstreamingTimeout(r.max_tokens,e)}return this._client.post("/v1/messages?beta=true",{body:r,timeout:n??6e5,...t,headers:th([{...s?.toString()!=null?{"anthropic-beta":s?.toString()}:void 0},t?.headers]),stream:e.stream??!1})}stream(e,t){return tR.createMessage(this,e,t)}countTokens(e,t){let{betas:s,...r}=e;return this._client.post("/v1/messages/count_tokens?beta=true",{body:r,...t,headers:th([{"anthropic-beta":[...s??[],"token-counting-2024-11-01"].toString()},t?.headers])})}}tP.Batches=tb;class tE extends tc{constructor(){super(...arguments),this.models=new tg(this._client),this.messages=new tP(this._client),this.files=new tm(this._client)}}tE.Models=tg,tE.Messages=tP,tE.Files=tm;class tN extends tc{create(e,t){let{betas:s,...r}=e;return this._client.post("/v1/complete",{body:r,timeout:this._client._options.timeout??6e5,...t,headers:th([{...s?.toString()!=null?{"anthropic-beta":s?.toString()}:void 0},t?.headers]),stream:e.stream??!1})}}let tM="__json_buf";class tT{constructor(){E.add(this),this.messages=[],this.receivedMessages=[],N.set(this,void 0),this.controller=new AbortController,M.set(this,void 0),T.set(this,()=>{}),I.set(this,()=>{}),$.set(this,void 0),j.set(this,()=>{}),O.set(this,()=>{}),L.set(this,{}),U.set(this,!1),W.set(this,!1),F.set(this,!1),C.set(this,!1),D.set(this,void 0),Q.set(this,void 0),H.set(this,e=>{if(et(this,W,!0,"f"),en(e)&&(e=new el),e instanceof el)return et(this,F,!0,"f"),this._emit("abort",e);if(e instanceof ea)return this._emit("error",e);if(e instanceof Error){let t=new ea(e.message);return t.cause=e,this._emit("error",t)}return this._emit("error",new ea(String(e)))}),et(this,M,new Promise((e,t)=>{et(this,T,e,"f"),et(this,I,t,"f")}),"f"),et(this,$,new Promise((e,t)=>{et(this,j,e,"f"),et(this,O,t,"f")}),"f"),es(this,M,"f").catch(()=>{}),es(this,$,"f").catch(()=>{})}get response(){return es(this,D,"f")}get request_id(){return es(this,Q,"f")}async withResponse(){let e=await es(this,M,"f");if(!e)throw Error("Could not resolve a `Response` object");return{data:this,response:e,request_id:e.headers.get("request-id")}}static fromReadableStream(e){let t=new tT;return t._run(()=>t._fromReadableStream(e)),t}static createMessage(e,t,s){let r=new tT;for(let e of t.messages)r._addMessageParam(e);return r._run(()=>r._createMessage(e,{...t,stream:!0},{...s,headers:{...s?.headers,"X-Stainless-Helper-Method":"stream"}})),r}_run(e){e().then(()=>{this._emitFinal(),this._emit("end")},es(this,H,"f"))}_addMessageParam(e){this.messages.push(e)}_addMessage(e,t=!0){this.receivedMessages.push(e),t&&this._emit("message",e)}async _createMessage(e,t,s){let r=s?.signal;r&&(r.aborted&&this.controller.abort(),r.addEventListener("abort",()=>this.controller.abort())),es(this,E,"m",J).call(this);let{response:n,data:i}=await e.create({...t,stream:!0},{...s,signal:this.controller.signal}).withResponse();for await(let e of(this._connected(n),i))es(this,E,"m",K).call(this,e);if(i.controller.signal?.aborted)throw new el;es(this,E,"m",V).call(this)}_connected(e){this.ended||(et(this,D,e,"f"),et(this,Q,e?.headers.get("request-id"),"f"),es(this,T,"f").call(this,e),this._emit("connect"))}get ended(){return es(this,U,"f")}get errored(){return es(this,W,"f")}get aborted(){return es(this,F,"f")}abort(){this.controller.abort()}on(e,t){return(es(this,L,"f")[e]||(es(this,L,"f")[e]=[])).push({listener:t}),this}off(e,t){let s=es(this,L,"f")[e];if(!s)return this;let r=s.findIndex(e=>e.listener===t);return r>=0&&s.splice(r,1),this}once(e,t){return(es(this,L,"f")[e]||(es(this,L,"f")[e]=[])).push({listener:t,once:!0}),this}emitted(e){return new Promise((t,s)=>{et(this,C,!0,"f"),"error"!==e&&this.once("error",s),this.once(e,t)})}async done(){et(this,C,!0,"f"),await es(this,$,"f")}get currentMessage(){return es(this,N,"f")}async finalMessage(){return await this.done(),es(this,E,"m",B).call(this)}async finalText(){return await this.done(),es(this,E,"m",X).call(this)}_emit(e,...t){if(es(this,U,"f"))return;"end"===e&&(et(this,U,!0,"f"),es(this,j,"f").call(this));let s=es(this,L,"f")[e];if(s&&(es(this,L,"f")[e]=s.filter(e=>!e.once),s.forEach(({listener:e})=>e(...t))),"abort"===e){let e=t[0];es(this,C,"f")||s?.length||Promise.reject(e),es(this,I,"f").call(this,e),es(this,O,"f").call(this,e),this._emit("end");return}if("error"===e){let e=t[0];es(this,C,"f")||s?.length||Promise.reject(e),es(this,I,"f").call(this,e),es(this,O,"f").call(this,e),this._emit("end")}}_emitFinal(){this.receivedMessages.at(-1)&&this._emit("finalMessage",es(this,E,"m",B).call(this))}async _fromReadableStream(e,t){let s=t?.signal;s&&(s.aborted&&this.controller.abort(),s.addEventListener("abort",()=>this.controller.abort())),es(this,E,"m",J).call(this),this._connected(null);let r=eJ.fromReadableStream(e,this.controller);for await(let e of r)es(this,E,"m",K).call(this,e);if(r.controller.signal?.aborted)throw new el;es(this,E,"m",V).call(this)}[(N=new WeakMap,M=new WeakMap,T=new WeakMap,I=new WeakMap,$=new WeakMap,j=new WeakMap,O=new WeakMap,L=new WeakMap,U=new WeakMap,W=new WeakMap,F=new WeakMap,C=new WeakMap,D=new WeakMap,Q=new WeakMap,H=new WeakMap,E=new WeakSet,B=function(){if(0===this.receivedMessages.length)throw new ea("stream ended without producing a Message with role=assistant");return this.receivedMessages.at(-1)},X=function(){if(0===this.receivedMessages.length)throw new ea("stream ended without producing a Message with role=assistant");let e=this.receivedMessages.at(-1).content.filter(e=>"text"===e.type).map(e=>e.text);if(0===e.length)throw new ea("stream ended without producing a content block with type=text");return e.join(" ")},J=function(){this.ended||et(this,N,void 0,"f")},K=function(e){if(this.ended)return;let t=es(this,E,"m",z).call(this,e);switch(this._emit("streamEvent",e,t),e.type){case"content_block_delta":{let s=t.content.at(-1);switch(e.delta.type){case"text_delta":"text"===s.type&&this._emit("text",e.delta.text,s.text||"");break;case"citations_delta":"text"===s.type&&this._emit("citation",e.delta.citation,s.citations??[]);break;case"input_json_delta":"tool_use"===s.type&&s.input&&this._emit("inputJson",e.delta.partial_json,s.input);break;case"thinking_delta":"thinking"===s.type&&this._emit("thinking",e.delta.thinking,s.thinking);break;case"signature_delta":"thinking"===s.type&&this._emit("signature",s.signature);break;default:e.delta}break}case"message_stop":this._addMessageParam(t),this._addMessage(t,!0);break;case"content_block_stop":this._emit("contentBlock",t.content.at(-1));break;case"message_start":et(this,N,t,"f")}},V=function(){if(this.ended)throw new ea("stream has ended, this shouldn't happen");let e=es(this,N,"f");if(!e)throw new ea("request ended without sending any chunks");return et(this,N,void 0,"f"),e},z=function(e){let t=es(this,N,"f");if("message_start"===e.type){if(t)throw new ea(`Unexpected event order, got ${e.type} before receiving "message_stop"`);return e.message}if(!t)throw new ea(`Unexpected event order, got ${e.type} before "message_start"`);switch(e.type){case"message_stop":case"content_block_stop":return t;case"message_delta":return t.stop_reason=e.delta.stop_reason,t.stop_sequence=e.delta.stop_sequence,t.usage.output_tokens=e.usage.output_tokens,null!=e.usage.input_tokens&&(t.usage.input_tokens=e.usage.input_tokens),null!=e.usage.cache_creation_input_tokens&&(t.usage.cache_creation_input_tokens=e.usage.cache_creation_input_tokens),null!=e.usage.cache_read_input_tokens&&(t.usage.cache_read_input_tokens=e.usage.cache_read_input_tokens),null!=e.usage.server_tool_use&&(t.usage.server_tool_use=e.usage.server_tool_use),t;case"content_block_start":return t.content.push(e.content_block),t;case"content_block_delta":{let s=t.content.at(e.index);switch(e.delta.type){case"text_delta":s?.type==="text"&&(s.text+=e.delta.text);break;case"citations_delta":s?.type==="text"&&(s.citations??(s.citations=[]),s.citations.push(e.delta.citation));break;case"input_json_delta":if(s?.type==="tool_use"){let t=s[tM]||"";Object.defineProperty(s,tM,{value:t+=e.delta.partial_json,enumerable:!1,writable:!0}),t&&(s.input=tS(t))}break;case"thinking_delta":s?.type==="thinking"&&(s.thinking+=e.delta.thinking);break;case"signature_delta":s?.type==="thinking"&&(s.signature=e.delta.signature);break;default:e.delta}return t}}},Symbol.asyncIterator)](){let e=[],t=[],s=!1;return this.on("streamEvent",s=>{let r=t.shift();r?r.resolve(s):e.push(s)}),this.on("end",()=>{for(let e of(s=!0,t))e.resolve(void 0);t.length=0}),this.on("abort",e=>{for(let r of(s=!0,t))r.reject(e);t.length=0}),this.on("error",e=>{for(let r of(s=!0,t))r.reject(e);t.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:s?{value:void 0,done:!0}:new Promise((e,s)=>t.push({resolve:e,reject:s})).then(e=>e?{value:e,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}toReadableStream(){return new eJ(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}}class tI extends tc{create(e,t){return this._client.post("/v1/messages/batches",{body:e,...t})}retrieve(e,t){return this._client.get(tf`/v1/messages/batches/${e}`,t)}list(e={},t){return this._client.getAPIList("/v1/messages/batches",e2,{query:e,...t})}delete(e,t){return this._client.delete(tf`/v1/messages/batches/${e}`,t)}cancel(e,t){return this._client.post(tf`/v1/messages/batches/${e}/cancel`,t)}async results(e,t){let s=await this.retrieve(e);if(!s.results_url)throw new ea(`No batch \`results_url\`; Has it finished processing? ${s.processing_status} - ${s.id}`);return this._client.get(s.results_url,{...t,headers:th([{Accept:"application/binary"},t?.headers]),stream:!0,__binaryResponse:!0})._thenUnwrap((e,t)=>ty.fromResponse(t.response,t.controller))}}class t$ extends tc{constructor(){super(...arguments),this.batches=new tI(this._client)}create(e,t){e.model in tj&&console.warn(`The model '${e.model}' is deprecated and will reach end-of-life on ${tj[e.model]}
Please migrate to a newer model. Visit https://docs.anthropic.com/en/docs/resources/model-deprecations for more information.`);let s=this._client._options.timeout;if(!e.stream&&null==s){let t=tq[e.model]??void 0;s=this._client.calculateNonstreamingTimeout(e.max_tokens,t)}return this._client.post("/v1/messages",{body:e,timeout:s??6e5,...t,stream:e.stream??!1})}stream(e,t){return tT.createMessage(this,e,t)}countTokens(e,t){return this._client.post("/v1/messages/count_tokens",{body:e,...t})}}let tj={"claude-1.3":"November 6th, 2024","claude-1.3-100k":"November 6th, 2024","claude-instant-1.1":"November 6th, 2024","claude-instant-1.1-100k":"November 6th, 2024","claude-instant-1.2":"November 6th, 2024","claude-3-sonnet-20240229":"July 21st, 2025","claude-2.1":"July 21st, 2025","claude-2.0":"July 21st, 2025"};t$.Batches=tI;class tO extends tc{retrieve(e,t={},s){let{betas:r}=t??{};return this._client.get(tf`/v1/models/${e}`,{...s,headers:th([{...r?.toString()!=null?{"anthropic-beta":r?.toString()}:void 0},s?.headers])})}list(e={},t){let{betas:s,...r}=e??{};return this._client.getAPIList("/v1/models",e2,{query:r,...t,headers:th([{...s?.toString()!=null?{"anthropic-beta":s?.toString()}:void 0},t?.headers])})}}let tL=e=>void 0!==globalThis.process?globalThis.process.env?.[e]?.trim()??void 0:void 0!==globalThis.Deno?globalThis.Deno.env?.get?.(e)?.trim():void 0;class tU{constructor({baseURL:e=tL("ANTHROPIC_BASE_URL"),apiKey:t=tL("ANTHROPIC_API_KEY")??null,authToken:s=tL("ANTHROPIC_AUTH_TOKEN")??null,...r}={}){G.set(this,void 0);let n={apiKey:t,authToken:s,...r,baseURL:e||"https://api.anthropic.com"};if(!n.dangerouslyAllowBrowser&&e$())throw new ea("It looks like you're running in a browser-like environment.\n\nThis is disabled by default, as it risks exposing your secret API credentials to attackers.\nIf you understand the risks and have appropriate mitigations in place,\nyou can set the `dangerouslyAllowBrowser` option to `true`, e.g.,\n\nnew Anthropic({ apiKey, dangerouslyAllowBrowser: true });\n");this.baseURL=n.baseURL,this.timeout=n.timeout??tW.DEFAULT_TIMEOUT,this.logger=n.logger??console;let i="warn";this.logLevel=i,this.logLevel=eq(n.logLevel,"ClientOptions.logLevel",this)??eq(tL("ANTHROPIC_LOG"),"process.env['ANTHROPIC_LOG']",this)??i,this.fetchOptions=n.fetchOptions,this.maxRetries=n.maxRetries??2,this.fetch=n.fetch??function(){if("undefined"!=typeof fetch)return fetch;throw Error("`fetch` is not defined as a global; Either pass `fetch` to the client, `new Anthropic({ fetch })` or polyfill the global, `globalThis.fetch = fetch`")}(),et(this,G,eQ,"f"),this._options=n,this.apiKey=t,this.authToken=s}withOptions(e){return new this.constructor({...this._options,baseURL:this.baseURL,maxRetries:this.maxRetries,timeout:this.timeout,logger:this.logger,logLevel:this.logLevel,fetchOptions:this.fetchOptions,apiKey:this.apiKey,authToken:this.authToken,...e})}defaultQuery(){return this._options.defaultQuery}validateHeaders({values:e,nulls:t}){if(!(this.apiKey&&e.get("x-api-key")||t.has("x-api-key")||this.authToken&&e.get("authorization")||t.has("authorization")))throw Error('Could not resolve authentication method. Expected either apiKey or authToken to be set. Or for one of the "X-Api-Key" or "Authorization" headers to be explicitly omitted')}authHeaders(e){return th([this.apiKeyAuth(e),this.bearerAuth(e)])}apiKeyAuth(e){if(null!=this.apiKey)return th([{"X-Api-Key":this.apiKey}])}bearerAuth(e){if(null!=this.authToken)return th([{Authorization:`Bearer ${this.authToken}`}])}stringifyQuery(e){return Object.entries(e).filter(([e,t])=>void 0!==t).map(([e,t])=>{if("string"==typeof t||"number"==typeof t||"boolean"==typeof t)return`${encodeURIComponent(e)}=${encodeURIComponent(t)}`;if(null===t)return`${encodeURIComponent(e)}=`;throw new ea(`Cannot stringify type ${typeof t}; Expected string, number, boolean, or null. If you need to pass nested query parameters, you can manually encode them, e.g. { query: { 'foo[key1]': value1, 'foo[key2]': value2 } }, and please open a GitHub issue requesting better support for your use case.`)}).join("&")}getUserAgent(){return`${this.constructor.name}/JS ${eI}`}defaultIdempotencyKey(){return`stainless-node-retry-${er()}`}makeStatusError(e,t,s,r){return eo.generate(e,t,s,r)}buildURL(e,t){let s=new URL(e_(e)?e:this.baseURL+(this.baseURL.endsWith("/")&&e.startsWith("/")?e.slice(1):e)),r=this.defaultQuery();return!function(e){if(!e)return!0;for(let t in e)return!1;return!0}(r)&&(t={...r,...t}),"object"==typeof t&&t&&!Array.isArray(t)&&(s.search=this.stringifyQuery(t)),s.toString()}_calculateNonstreamingTimeout(e){if(3600*e/128e3>600)throw new ea("Streaming is strongly recommended for operations that may take longer than 10 minutes. See https://github.com/anthropics/anthropic-sdk-python#streaming-responses for more details");return 6e5}async prepareOptions(e){}async prepareRequest(e,{url:t,options:s}){}get(e,t){return this.methodRequest("get",e,t)}post(e,t){return this.methodRequest("post",e,t)}patch(e,t){return this.methodRequest("patch",e,t)}put(e,t){return this.methodRequest("put",e,t)}delete(e,t){return this.methodRequest("delete",e,t)}methodRequest(e,t,s){return this.request(Promise.resolve(s).then(s=>({method:e,path:t,...s})))}request(e,t=null){return new eZ(this,this.makeRequest(e,t,void 0))}async makeRequest(e,t,s){let r=await e,n=r.maxRetries??this.maxRetries;null==t&&(t=n),await this.prepareOptions(r);let{req:i,url:a,timeout:o}=this.buildRequest(r,{retryCount:n-t});await this.prepareRequest(i,{url:a,options:r});let l="log_"+(0x1000000*Math.random()|0).toString(16).padStart(6,"0"),c=void 0===s?"":`, retryOf: ${s}`,u=Date.now();if(eM(this).debug(`[${l}] sending request`,eT({retryOfRequestLogID:s,method:r.method,url:a,options:r,headers:i.headers})),r.signal?.aborted)throw new el;let d=new AbortController,h=await this.fetchWithTimeout(a,i,o,d).catch(ei),p=Date.now();if(h instanceof Error){let e=`retrying, ${t} attempts remaining`;if(r.signal?.aborted)throw new el;let n=en(h)||/timed? ?out/i.test(String(h)+("cause"in h?String(h.cause):""));if(t)return eM(this).info(`[${l}] connection ${n?"timed out":"failed"} - ${e}`),eM(this).debug(`[${l}] connection ${n?"timed out":"failed"} (${e})`,eT({retryOfRequestLogID:s,url:a,durationMs:p-u,message:h.message})),this.retryRequest(r,t,s??l);if(eM(this).info(`[${l}] connection ${n?"timed out":"failed"} - error; no more retries left`),eM(this).debug(`[${l}] connection ${n?"timed out":"failed"} (error; no more retries left)`,eT({retryOfRequestLogID:s,url:a,durationMs:p-u,message:h.message})),n)throw new eu;throw new ec({cause:h})}let f=[...h.headers.entries()].filter(([e])=>"request-id"===e).map(([e,t])=>", "+e+": "+JSON.stringify(t)).join(""),m=`[${l}${c}${f}] ${i.method} ${a} ${h.ok?"succeeded":"failed"} with status ${h.status} in ${p-u}ms`;if(!h.ok){let e=this.shouldRetry(h);if(t&&e){let e=`retrying, ${t} attempts remaining`;return await eD(h.body),eM(this).info(`${m} - ${e}`),eM(this).debug(`[${l}] response error (${e})`,eT({retryOfRequestLogID:s,url:h.url,status:h.status,headers:h.headers,durationMs:p-u})),this.retryRequest(r,t,s??l,h.headers)}let n=e?"error; no more retries left":"error; not retryable";eM(this).info(`${m} - ${n}`);let i=await h.text().catch(e=>ei(e).message),a=eS(i),o=a?void 0:i;throw eM(this).debug(`[${l}] response error (${n})`,eT({retryOfRequestLogID:s,url:h.url,status:h.status,headers:h.headers,message:o,durationMs:Date.now()-u})),this.makeStatusError(h.status,a,o,h.headers)}return eM(this).info(m),eM(this).debug(`[${l}] response start`,eT({retryOfRequestLogID:s,url:h.url,status:h.status,headers:h.headers,durationMs:p-u})),{response:h,options:r,controller:d,requestLogID:l,retryOfRequestLogID:s,startTime:u}}getAPIList(e,t,s){return this.requestAPIList(t,{method:"get",path:e,...s})}requestAPIList(e,t){return new e1(this,this.makeRequest(t,null,void 0),e)}async fetchWithTimeout(e,t,s,r){let{signal:n,method:i,...a}=t||{};n&&n.addEventListener("abort",()=>r.abort());let o=setTimeout(()=>r.abort(),s),l=globalThis.ReadableStream&&a.body instanceof globalThis.ReadableStream||"object"==typeof a.body&&null!==a.body&&Symbol.asyncIterator in a.body,c={signal:r.signal,...l?{duplex:"half"}:{},method:"GET",...a};i&&(c.method=i.toUpperCase());try{return await this.fetch.call(void 0,e,c)}finally{clearTimeout(o)}}shouldRetry(e){let t=e.headers.get("x-should-retry");return"true"===t||"false"!==t&&(408===e.status||409===e.status||429===e.status||e.status>=500)}async retryRequest(e,t,s,r){let n;let i=r?.get("retry-after-ms");if(i){let e=parseFloat(i);Number.isNaN(e)||(n=e)}let a=r?.get("retry-after");if(a&&!n){let e=parseFloat(a);n=Number.isNaN(e)?Date.parse(a)-Date.now():1e3*e}if(!(n&&0<=n&&n<6e4)){let s=e.maxRetries??this.maxRetries;n=this.calculateDefaultRetryTimeoutMillis(t,s)}return await ex(n),this.makeRequest(e,t-1,s)}calculateDefaultRetryTimeoutMillis(e,t){return Math.min(.5*Math.pow(2,t-e),8)*(1-.25*Math.random())*1e3}calculateNonstreamingTimeout(e,t){if(36e5*e/128e3>6e5||null!=t&&e>t)throw new ea("Streaming is strongly recommended for operations that may token longer than 10 minutes. See https://github.com/anthropics/anthropic-sdk-typescript#long-requests for more details");return 6e5}buildRequest(e,{retryCount:t=0}={}){let s={...e},{method:r,path:n,query:i}=s,a=this.buildURL(n,i);"timeout"in s&&ek("timeout",s.timeout),s.timeout=s.timeout??this.timeout;let{bodyHeaders:o,body:l}=this.buildBody({options:s}),c=this.buildHeaders({options:e,method:r,bodyHeaders:o,retryCount:t});return{req:{method:r,headers:c,...s.signal&&{signal:s.signal},...globalThis.ReadableStream&&l instanceof globalThis.ReadableStream&&{duplex:"half"},...l&&{body:l},...this.fetchOptions??{},...s.fetchOptions??{}},url:a,timeout:s.timeout}}buildHeaders({options:e,method:t,bodyHeaders:s,retryCount:r}){let n={};this.idempotencyHeader&&"get"!==t&&(e.idempotencyKey||(e.idempotencyKey=this.defaultIdempotencyKey()),n[this.idempotencyHeader]=e.idempotencyKey);let i=th([n,{Accept:"application/json","User-Agent":this.getUserAgent(),"X-Stainless-Retry-Count":String(r),...e.timeout?{"X-Stainless-Timeout":String(Math.trunc(e.timeout/1e3))}:{},...eU(),...this._options.dangerouslyAllowBrowser?{"anthropic-dangerous-direct-browser-access":"true"}:void 0,"anthropic-version":"2023-06-01"},this.authHeaders(e),this._options.defaultHeaders,s,e.headers]);return this.validateHeaders(i),i.values}buildBody({options:{body:e,headers:t}}){if(!e)return{bodyHeaders:void 0,body:void 0};let s=th([t]);return ArrayBuffer.isView(e)||e instanceof ArrayBuffer||e instanceof DataView||"string"==typeof e&&s.values.has("content-type")||e instanceof Blob||e instanceof FormData||e instanceof URLSearchParams||globalThis.ReadableStream&&e instanceof globalThis.ReadableStream?{bodyHeaders:void 0,body:e}:"object"==typeof e&&(Symbol.asyncIterator in e||Symbol.iterator in e&&"next"in e&&"function"==typeof e.next)?{bodyHeaders:void 0,body:eF(e)}:es(this,G,"f").call(this,{body:e,headers:s})}}G=new WeakMap,tU.Anthropic=tU,tU.HUMAN_PROMPT="\n\nHuman:",tU.AI_PROMPT="\n\nAssistant:",tU.DEFAULT_TIMEOUT=6e5,tU.AnthropicError=ea,tU.APIError=eo,tU.APIConnectionError=ec,tU.APIConnectionTimeoutError=eu,tU.APIUserAbortError=el,tU.NotFoundError=ef,tU.ConflictError=em,tU.RateLimitError=ey,tU.BadRequestError=ed,tU.AuthenticationError=eh,tU.InternalServerError=eb,tU.PermissionDeniedError=ep,tU.UnprocessableEntityError=eg,tU.toFile=to;class tW extends tU{constructor(){super(...arguments),this.completions=new tN(this),this.messages=new t$(this),this.models=new tO(this),this.beta=new tE(this)}}tW.Completions=tN,tW.Messages=t$,tW.Models=tO,tW.Beta=tE;let{HUMAN_PROMPT:tF,AI_PROMPT:tC}=tW,tD=new tW({apiKey:process.env.ANTHROPIC_API_KEY});async function tQ(e){let{overallScore:t,listeningScore:s,readingScore:r,writingScore:n,speakingScore:i}=e;if(!process.env.ANTHROPIC_API_KEY)return console.warn("ANTHROPIC_API_KEY not found, using mock feedback"),tB(e);try{let e=`You are an expert IELTS examiner and English language teacher. Generate personalized feedback for a student based on their IELTS test scores.

Test Scores:
- Overall Band Score: ${t}
- Listening: ${s||"Not provided"}
- Reading: ${r||"Not provided"}
- Writing: ${n||"Not provided"}
- Speaking: ${i||"Not provided"}

Please provide detailed feedback in the following JSON format:
{
  "overallAssessment": "A comprehensive assessment of the student's English proficiency level",
  "strengths": ["List of 3-4 specific strengths based on the scores"],
  "areasForImprovement": ["List of 3-4 specific areas that need improvement"],
  "specificRecommendations": {
    "listening": "Specific advice for improving listening skills",
    "reading": "Specific advice for improving reading skills",
    "writing": "Specific advice for improving writing skills",
    "speaking": "Specific advice for improving speaking skills"
  },
  "studyPlan": "A detailed study plan recommendation based on the current level",
  "nextSteps": ["List of 4-5 actionable next steps for improvement"]
}

Make the feedback encouraging but honest, specific to the scores provided, and actionable. Consider the IELTS band descriptors when providing recommendations.`,a=(await tD.messages.create({model:"claude-3-sonnet-20240229",max_tokens:2e3,messages:[{role:"user",content:e}]})).content[0];if("text"!==a.type)throw Error("Unexpected response type from Claude");try{return JSON.parse(a.text)}catch(e){throw console.error("Failed to parse Claude response:",a.text,e),Error("Invalid response format from AI service")}}catch(t){return console.error("Claude API error:",t),tB(e)}}function tB(e){let{overallScore:t}=e;return t>=7?{overallAssessment:"Excellent performance! You have demonstrated strong English proficiency across all skills. Your overall band score of "+t+" indicates you are a competent user of English with good operational command of the language.",strengths:["Strong overall command of English language","Good vocabulary range and accuracy in most contexts","Effective communication skills with minor inaccuracies","Ability to handle complex language situations"],areasForImprovement:["Fine-tune advanced grammar structures for academic contexts","Expand specialized academic and professional vocabulary","Practice complex sentence formations and cohesive devices","Work on consistency across all four skills"],specificRecommendations:{listening:"Focus on academic lectures, complex discussions, and various English accents. Practice note-taking while listening to improve comprehension of detailed information.",reading:"Practice with academic texts, research papers, and complex argumentative essays. Work on speed reading techniques while maintaining comprehension.",writing:"Work on advanced essay structures, sophisticated argumentation, and academic writing conventions. Focus on task achievement and coherence.",speaking:"Practice formal presentations, debates, and discussions on abstract topics. Work on fluency and natural expression of complex ideas."},studyPlan:"Continue with advanced materials focusing on academic English. Dedicate 2-3 hours daily to practice, with emphasis on maintaining consistency across all skills. Use authentic materials like academic journals, TED talks, and formal debates.",nextSteps:["Take regular practice tests to maintain performance level","Focus on any weaker skills to achieve balance across all areas","Consider advanced English courses or academic preparation programs","Practice with time constraints to improve efficiency","Engage with native speakers in academic or professional contexts"]}:t>=5.5?{overallAssessment:"Good foundation with room for improvement in specific areas. Your overall band score of "+t+" shows you are a modest user of English with partial command of the language, coping with overall meaning in most situations.",strengths:["Basic communication skills are well-established","Understanding of fundamental grammar structures","Adequate vocabulary for everyday and familiar situations","Ability to express basic ideas and opinions clearly"],areasForImprovement:["Expand vocabulary range for academic and professional contexts","Improve complex grammar usage and sentence structures","Enhance fluency and coherence in extended discourse","Develop better accuracy in language use"],specificRecommendations:{listening:"Practice with various accents, speeds, and contexts. Focus on understanding main ideas and specific details in academic and social situations.",reading:"Work on skimming and scanning techniques. Practice with longer texts and improve vocabulary through extensive reading.",writing:"Focus on paragraph structure, linking words, and task response. Practice both formal and informal writing styles with attention to coherence.",speaking:"Practice pronunciation, intonation, and natural speech patterns. Work on expressing ideas clearly and developing responses fully."},studyPlan:"Structured study plan focusing on intermediate to upper-intermediate materials. Dedicate 1-2 hours daily with balanced practice across all four skills. Use IELTS preparation materials and general English improvement resources.",nextSteps:["Daily practice with all four skills using varied materials","Join English conversation groups or language exchange programs","Use IELTS preparation books and online resources systematically","Focus on building vocabulary through reading and listening","Take regular practice tests to track improvement"]}:{overallAssessment:"Foundation level with significant room for improvement across all skills. Your overall band score of "+t+" indicates limited user level, with basic competence limited to familiar situations and frequent communication breakdowns.",strengths:["Basic understanding of English structure and patterns","Willingness to communicate despite limitations","Some vocabulary knowledge for familiar topics","Ability to convey basic information in simple situations"],areasForImprovement:["Build fundamental vocabulary for everyday situations","Strengthen basic grammar and sentence construction","Improve listening comprehension for simple conversations","Develop basic writing skills and paragraph organization"],specificRecommendations:{listening:"Start with simple conversations, basic instructions, and familiar topics. Use visual aids and context clues to support understanding.",reading:"Begin with short, simple texts on familiar topics. Focus on building sight vocabulary and basic comprehension skills.",writing:"Focus on basic sentence structure, simple paragraphs, and essential grammar patterns. Practice writing about familiar topics.",speaking:"Practice basic conversations, pronunciation of common words, and expressing simple ideas clearly and confidently."},studyPlan:"Intensive foundation course focusing on basic English skills. Dedicate 1-2 hours daily to structured learning with emphasis on building confidence and fundamental skills. Use beginner-level materials and seek guidance from qualified teachers.",nextSteps:["Enroll in a basic English course with qualified instruction","Practice daily with simple, structured materials","Focus on building confidence through successful communication","Use visual and audio aids to support learning","Set small, achievable goals to maintain motivation"]}}},37702:(e,t,s)=>{"use strict";s.a(e,async(e,r)=>{try{s.d(t,{Y9:()=>d,j2:()=>h});var n=s(32221),i=s(31648),a=s(62693),o=s(48590),l=s(47579),c=s(34926),u=e([a]);a=(u.then?(await u)():u)[0];let{handlers:d,auth:h,signIn:p,signOut:f}=(0,n.Ay)({session:{strategy:"jwt"},providers:[(0,i.A)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;try{let t=await a.db.select().from(o.users).where((0,l.eq)(o.users.email,e.email)).limit(1);if(0===t.length)return null;let s=t[0];if(!s.password||!await c.Ay.compare(e.password,s.password))return null;return{id:s.id,email:s.email,name:s.name,role:s.role}}catch(e){return console.error("Authentication error:",e),null}}})],callbacks:{jwt:async({token:e,user:t})=>(t&&(e.id=t.id,e.role=t.role,e.email=t.email,e.name=t.name),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.id,e.user.role=t.role,e.user.email=t.email,e.user.name=t.name),e)},pages:{signIn:"/auth/signin"}});r()}catch(e){r(e)}})},62693:(e,t,s)=>{"use strict";s.a(e,async(e,r)=>{try{s.d(t,{db:()=>u});var n=s(10072),i=s(2113),a=s(48590),o=e([i,n]);[i,n]=o.then?(await o)():o;let l=process.env.DATABASE_URL,c=(0,i.default)(l,{prepare:!1}),u=(0,n.f)(c,{schema:a});r()}catch(e){r(e)}})},48590:(e,t,s)=>{"use strict";s.r(t),s.d(t,{accounts:()=>h,aiFeedback:()=>y,candidates:()=>m,sessions:()=>p,testResults:()=>g,users:()=>d,verificationTokens:()=>f});var r=s(87858),n=s(44799),i=s(32590),a=s(9848),o=s(70009),l=s(27390),c=s(32190),u=s(4502);let d=(0,r.cJ)("users",{id:(0,n.Qq)("id").primaryKey().$defaultFn(()=>(0,u.sX)()),name:(0,n.Qq)("name"),email:(0,n.Qq)("email").notNull().unique(),emailVerified:(0,i.vE)("emailVerified",{mode:"date"}),image:(0,n.Qq)("image"),password:(0,n.Qq)("password"),role:(0,n.Qq)("role",{enum:["admin","test_checker"]}).notNull().default("test_checker"),createdAt:(0,i.vE)("created_at").defaultNow().notNull(),updatedAt:(0,i.vE)("updated_at").defaultNow().notNull()}),h=(0,r.cJ)("accounts",{userId:(0,n.Qq)("userId").notNull().references(()=>d.id,{onDelete:"cascade"}),type:(0,n.Qq)("type").notNull(),provider:(0,n.Qq)("provider").notNull(),providerAccountId:(0,n.Qq)("providerAccountId").notNull(),refresh_token:(0,n.Qq)("refresh_token"),access_token:(0,n.Qq)("access_token"),expires_at:(0,a.nd)("expires_at"),token_type:(0,n.Qq)("token_type"),scope:(0,n.Qq)("scope"),id_token:(0,n.Qq)("id_token"),session_state:(0,n.Qq)("session_state")}),p=(0,r.cJ)("sessions",{sessionToken:(0,n.Qq)("sessionToken").primaryKey(),userId:(0,n.Qq)("userId").notNull().references(()=>d.id,{onDelete:"cascade"}),expires:(0,i.vE)("expires",{mode:"date"}).notNull()}),f=(0,r.cJ)("verificationTokens",{identifier:(0,n.Qq)("identifier").notNull(),token:(0,n.Qq)("token").notNull(),expires:(0,i.vE)("expires",{mode:"date"}).notNull()}),m=(0,r.cJ)("candidates",{id:(0,n.Qq)("id").primaryKey().$defaultFn(()=>(0,u.sX)()),fullName:(0,n.Qq)("full_name").notNull(),email:(0,n.Qq)("email").notNull().unique(),phoneNumber:(0,n.Qq)("phone_number").notNull(),dateOfBirth:(0,i.vE)("date_of_birth",{mode:"date"}).notNull(),nationality:(0,n.Qq)("nationality").notNull(),passportNumber:(0,n.Qq)("passport_number").notNull().unique(),testDate:(0,i.vE)("test_date",{mode:"date"}).notNull(),testCenter:(0,n.Qq)("test_center").notNull(),photoUrl:(0,n.Qq)("photo_url"),createdAt:(0,i.vE)("created_at").defaultNow().notNull(),updatedAt:(0,i.vE)("updated_at").defaultNow().notNull()}),g=(0,r.cJ)("test_results",{id:(0,n.Qq)("id").primaryKey().$defaultFn(()=>(0,u.sX)()),candidateId:(0,n.Qq)("candidate_id").notNull().references(()=>m.id,{onDelete:"cascade"}),listeningScore:(0,o._)("listening_score",{precision:3,scale:1}),listeningBandScore:(0,o._)("listening_band_score",{precision:2,scale:1}),readingScore:(0,o._)("reading_score",{precision:3,scale:1}),readingBandScore:(0,o._)("reading_band_score",{precision:2,scale:1}),writingTask1Score:(0,o._)("writing_task1_score",{precision:2,scale:1}),writingTask2Score:(0,o._)("writing_task2_score",{precision:2,scale:1}),writingBandScore:(0,o._)("writing_band_score",{precision:2,scale:1}),speakingFluencyScore:(0,o._)("speaking_fluency_score",{precision:2,scale:1}),speakingLexicalScore:(0,o._)("speaking_lexical_score",{precision:2,scale:1}),speakingGrammarScore:(0,o._)("speaking_grammar_score",{precision:2,scale:1}),speakingPronunciationScore:(0,o._)("speaking_pronunciation_score",{precision:2,scale:1}),speakingBandScore:(0,o._)("speaking_band_score",{precision:2,scale:1}),overallBandScore:(0,o._)("overall_band_score",{precision:2,scale:1}),status:(0,n.Qq)("status",{enum:["pending","completed","verified"]}).notNull().default("pending"),enteredBy:(0,n.Qq)("entered_by").references(()=>d.id),verifiedBy:(0,n.Qq)("verified_by").references(()=>d.id),certificateGenerated:(0,l.zM)("certificate_generated").default(!1),certificateSerial:(0,n.Qq)("certificate_serial").unique(),certificateUrl:(0,n.Qq)("certificate_url"),aiFeedbackGenerated:(0,l.zM)("ai_feedback_generated").default(!1),testDate:(0,i.vE)("test_date",{mode:"date"}),createdAt:(0,i.vE)("created_at").defaultNow().notNull(),updatedAt:(0,i.vE)("updated_at").defaultNow().notNull()}),y=(0,r.cJ)("ai_feedback",{id:(0,n.Qq)("id").primaryKey().$defaultFn(()=>(0,u.sX)()),testResultId:(0,n.Qq)("test_result_id").notNull().references(()=>g.id,{onDelete:"cascade"}),listeningFeedback:(0,n.Qq)("listening_feedback"),readingFeedback:(0,n.Qq)("reading_feedback"),writingFeedback:(0,n.Qq)("writing_feedback"),speakingFeedback:(0,n.Qq)("speaking_feedback"),overallFeedback:(0,n.Qq)("overall_feedback"),studyRecommendations:(0,n.Qq)("study_recommendations"),strengths:(0,c.Pq)("strengths").$type(),weaknesses:(0,c.Pq)("weaknesses").$type(),studyPlan:(0,c.Pq)("study_plan").$type(),generatedAt:(0,i.vE)("generated_at").defaultNow().notNull()})}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[638,5452,9757,4681],()=>s(87546));module.exports=r})();