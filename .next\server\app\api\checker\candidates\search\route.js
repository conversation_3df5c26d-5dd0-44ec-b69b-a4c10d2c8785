(()=>{var e={};e.id=4355,e.ids=[4355],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},55511:e=>{"use strict";e.exports=require("crypto")},2113:e=>{"use strict";e.exports=import("postgres")},77598:e=>{"use strict";e.exports=require("node:crypto")},1120:(e,t,a)=>{"use strict";a.a(e,async(e,r)=>{try{a.r(t),a.d(t,{patchFetch:()=>c,routeModule:()=>l,serverHooks:()=>m,workAsyncStorage:()=>u,workUnitAsyncStorage:()=>p});var s=a(42706),n=a(28203),i=a(45994),o=a(29888),d=e([o]);o=(d.then?(await d)():d)[0];let l=new s.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/checker/candidates/search/route",pathname:"/api/checker/candidates/search",filename:"route",bundlePath:"app/api/checker/candidates/search/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\api\\checker\\candidates\\search\\route.ts",nextConfigOutput:"",userland:o}),{workAsyncStorage:u,workUnitAsyncStorage:p,serverHooks:m}=l;function c(){return(0,i.patchFetch)({workAsyncStorage:u,workUnitAsyncStorage:p})}r()}catch(e){r(e)}})},96487:()=>{},78335:()=>{},29888:(e,t,a)=>{"use strict";a.a(e,async(e,r)=>{try{a.r(t),a.d(t,{POST:()=>l});var s=a(39187),n=a(37702),i=a(62693),o=a(48590),d=a(47579),c=e([n,i]);async function l(e){try{let t;if(!await (0,n.j2)())return s.NextResponse.json({error:"Unauthorized"},{status:401});let{query:a,searchType:r}=await e.json();if(!a||!r)return s.NextResponse.json({error:"Query and search type are required"},{status:400});switch(r){case"name":t=(0,d.B3)(o.candidates.fullName,`%${a}%`);break;case"email":t=(0,d.eq)(o.candidates.email,a);break;case"passport":t=(0,d.eq)(o.candidates.passportNumber,a);break;default:return s.NextResponse.json({error:"Invalid search type"},{status:400})}let c=await i.db.select({id:o.candidates.id,fullName:o.candidates.fullName,email:o.candidates.email,phoneNumber:o.candidates.phoneNumber,dateOfBirth:o.candidates.dateOfBirth,nationality:o.candidates.nationality,passportNumber:o.candidates.passportNumber,testDate:o.candidates.testDate,testCenter:o.candidates.testCenter,photoUrl:o.candidates.photoUrl,resultId:o.testResults.id,hasResults:o.testResults.id}).from(o.candidates).leftJoin(o.testResults,(0,d.eq)(o.candidates.id,o.testResults.candidateId)).where(t).orderBy(o.candidates.fullName),l=new Map;c.forEach(e=>{l.has(e.id)||l.set(e.id,{id:e.id,fullName:e.fullName,email:e.email,phoneNumber:e.phoneNumber,dateOfBirth:e.dateOfBirth,nationality:e.nationality,passportNumber:e.passportNumber,testDate:e.testDate,testCenter:e.testCenter,photoUrl:e.photoUrl,hasResults:!!e.hasResults,resultId:e.resultId})});let u=Array.from(l.values());return s.NextResponse.json(u)}catch(e){return console.error("Candidate search error:",e),s.NextResponse.json({error:"Internal server error"},{status:500})}}[n,i]=c.then?(await c)():c,r()}catch(e){r(e)}})},37702:(e,t,a)=>{"use strict";a.a(e,async(e,r)=>{try{a.d(t,{Y9:()=>u,j2:()=>p});var s=a(32221),n=a(31648),i=a(62693),o=a(48590),d=a(47579),c=a(34926),l=e([i]);i=(l.then?(await l)():l)[0];let{handlers:u,auth:p,signIn:m,signOut:_}=(0,s.Ay)({session:{strategy:"jwt"},providers:[(0,n.A)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;try{let t=await i.db.select().from(o.users).where((0,d.eq)(o.users.email,e.email)).limit(1);if(0===t.length)return null;let a=t[0];if(!a.password||!await c.Ay.compare(e.password,a.password))return null;return{id:a.id,email:a.email,name:a.name,role:a.role}}catch(e){return console.error("Authentication error:",e),null}}})],callbacks:{jwt:async({token:e,user:t})=>(t&&(e.role=t.role),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role),e)},pages:{signIn:"/auth/signin"}});r()}catch(e){r(e)}})},62693:(e,t,a)=>{"use strict";a.a(e,async(e,r)=>{try{a.d(t,{db:()=>l});var s=a(10072),n=a(2113),i=a(48590),o=e([n,s]);[n,s]=o.then?(await o)():o;let d=process.env.DATABASE_URL,c=(0,n.default)(d,{prepare:!1}),l=(0,s.f)(c,{schema:i});r()}catch(e){r(e)}})},48590:(e,t,a)=>{"use strict";a.r(t),a.d(t,{accounts:()=>p,aiFeedback:()=>y,candidates:()=>f,sessions:()=>m,testResults:()=>h,users:()=>u,verificationTokens:()=>_});var r=a(87858),s=a(44799),n=a(32590),i=a(9848),o=a(70009),d=a(27390),c=a(32190),l=a(4502);let u=(0,r.cJ)("users",{id:(0,s.Qq)("id").primaryKey().$defaultFn(()=>(0,l.sX)()),name:(0,s.Qq)("name"),email:(0,s.Qq)("email").notNull().unique(),emailVerified:(0,n.vE)("emailVerified",{mode:"date"}),image:(0,s.Qq)("image"),password:(0,s.Qq)("password"),role:(0,s.Qq)("role",{enum:["admin","test_checker"]}).notNull().default("test_checker"),createdAt:(0,n.vE)("created_at").defaultNow().notNull(),updatedAt:(0,n.vE)("updated_at").defaultNow().notNull()}),p=(0,r.cJ)("accounts",{userId:(0,s.Qq)("userId").notNull().references(()=>u.id,{onDelete:"cascade"}),type:(0,s.Qq)("type").notNull(),provider:(0,s.Qq)("provider").notNull(),providerAccountId:(0,s.Qq)("providerAccountId").notNull(),refresh_token:(0,s.Qq)("refresh_token"),access_token:(0,s.Qq)("access_token"),expires_at:(0,i.nd)("expires_at"),token_type:(0,s.Qq)("token_type"),scope:(0,s.Qq)("scope"),id_token:(0,s.Qq)("id_token"),session_state:(0,s.Qq)("session_state")}),m=(0,r.cJ)("sessions",{sessionToken:(0,s.Qq)("sessionToken").primaryKey(),userId:(0,s.Qq)("userId").notNull().references(()=>u.id,{onDelete:"cascade"}),expires:(0,n.vE)("expires",{mode:"date"}).notNull()}),_=(0,r.cJ)("verificationTokens",{identifier:(0,s.Qq)("identifier").notNull(),token:(0,s.Qq)("token").notNull(),expires:(0,n.vE)("expires",{mode:"date"}).notNull()}),f=(0,r.cJ)("candidates",{id:(0,s.Qq)("id").primaryKey().$defaultFn(()=>(0,l.sX)()),fullName:(0,s.Qq)("full_name").notNull(),email:(0,s.Qq)("email").notNull().unique(),phoneNumber:(0,s.Qq)("phone_number").notNull(),dateOfBirth:(0,n.vE)("date_of_birth",{mode:"date"}).notNull(),nationality:(0,s.Qq)("nationality").notNull(),passportNumber:(0,s.Qq)("passport_number").notNull().unique(),testDate:(0,n.vE)("test_date",{mode:"date"}).notNull(),testCenter:(0,s.Qq)("test_center").notNull(),photoUrl:(0,s.Qq)("photo_url"),createdAt:(0,n.vE)("created_at").defaultNow().notNull(),updatedAt:(0,n.vE)("updated_at").defaultNow().notNull()}),h=(0,r.cJ)("test_results",{id:(0,s.Qq)("id").primaryKey().$defaultFn(()=>(0,l.sX)()),candidateId:(0,s.Qq)("candidate_id").notNull().references(()=>f.id,{onDelete:"cascade"}),listeningScore:(0,o._)("listening_score",{precision:3,scale:1}),listeningBandScore:(0,o._)("listening_band_score",{precision:2,scale:1}),readingScore:(0,o._)("reading_score",{precision:3,scale:1}),readingBandScore:(0,o._)("reading_band_score",{precision:2,scale:1}),writingTask1Score:(0,o._)("writing_task1_score",{precision:2,scale:1}),writingTask2Score:(0,o._)("writing_task2_score",{precision:2,scale:1}),writingBandScore:(0,o._)("writing_band_score",{precision:2,scale:1}),speakingFluencyScore:(0,o._)("speaking_fluency_score",{precision:2,scale:1}),speakingLexicalScore:(0,o._)("speaking_lexical_score",{precision:2,scale:1}),speakingGrammarScore:(0,o._)("speaking_grammar_score",{precision:2,scale:1}),speakingPronunciationScore:(0,o._)("speaking_pronunciation_score",{precision:2,scale:1}),speakingBandScore:(0,o._)("speaking_band_score",{precision:2,scale:1}),overallBandScore:(0,o._)("overall_band_score",{precision:2,scale:1}),status:(0,s.Qq)("status",{enum:["pending","completed","verified"]}).notNull().default("pending"),enteredBy:(0,s.Qq)("entered_by").references(()=>u.id),verifiedBy:(0,s.Qq)("verified_by").references(()=>u.id),certificateGenerated:(0,d.zM)("certificate_generated").default(!1),certificateSerial:(0,s.Qq)("certificate_serial").unique(),certificateUrl:(0,s.Qq)("certificate_url"),aiFeedbackGenerated:(0,d.zM)("ai_feedback_generated").default(!1),testDate:(0,n.vE)("test_date",{mode:"date"}),createdAt:(0,n.vE)("created_at").defaultNow().notNull(),updatedAt:(0,n.vE)("updated_at").defaultNow().notNull()}),y=(0,r.cJ)("ai_feedback",{id:(0,s.Qq)("id").primaryKey().$defaultFn(()=>(0,l.sX)()),testResultId:(0,s.Qq)("test_result_id").notNull().references(()=>h.id,{onDelete:"cascade"}),listeningFeedback:(0,s.Qq)("listening_feedback"),readingFeedback:(0,s.Qq)("reading_feedback"),writingFeedback:(0,s.Qq)("writing_feedback"),speakingFeedback:(0,s.Qq)("speaking_feedback"),overallFeedback:(0,s.Qq)("overall_feedback"),studyRecommendations:(0,s.Qq)("study_recommendations"),strengths:(0,c.Pq)("strengths").$type(),weaknesses:(0,c.Pq)("weaknesses").$type(),studyPlan:(0,c.Pq)("study_plan").$type(),generatedAt:(0,n.vE)("generated_at").defaultNow().notNull()})}};var t=require("../../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[638,5452,9757,4681],()=>a(1120));module.exports=r})();