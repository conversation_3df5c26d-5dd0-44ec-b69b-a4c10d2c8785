(()=>{var e={};e.id=5980,e.ids=[5980],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},55511:e=>{"use strict";e.exports=require("crypto")},2113:e=>{"use strict";e.exports=import("postgres")},77598:e=>{"use strict";e.exports=require("node:crypto")},9506:(e,t,a)=>{"use strict";a.a(e,async(e,r)=>{try{a.r(t),a.d(t,{patchFetch:()=>l,routeModule:()=>c,serverHooks:()=>m,workAsyncStorage:()=>u,workUnitAsyncStorage:()=>p});var s=a(42706),n=a(28203),i=a(45994),o=a(72877),d=e([o]);o=(d.then?(await d)():d)[0];let c=new s.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/admin/export/route",pathname:"/api/admin/export",filename:"route",bundlePath:"app/api/admin/export/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\api\\admin\\export\\route.ts",nextConfigOutput:"",userland:o}),{workAsyncStorage:u,workUnitAsyncStorage:p,serverHooks:m}=c;function l(){return(0,i.patchFetch)({workAsyncStorage:u,workUnitAsyncStorage:p})}r()}catch(e){r(e)}})},96487:()=>{},78335:()=>{},72877:(e,t,a)=>{"use strict";a.a(e,async(e,r)=>{try{a.r(t),a.d(t,{POST:()=>c});var s=a(39187),n=a(37702),i=a(62693),o=a(48590),d=a(47579),l=e([n,i]);async function c(e){try{let t=await (0,n.j2)();if(!t||t.user?.role!=="admin")return s.NextResponse.json({error:"Unauthorized"},{status:401});let{filters:a,format:r="csv"}=await e.json(),l=[];if(a.query){let e=a.query.trim();if(e)switch(a.searchType){case"name":l.push((0,d.B3)(o.candidates.fullName,`%${e}%`));break;case"email":l.push((0,d.B3)(o.candidates.email,`%${e}%`));break;case"passport":l.push((0,d.B3)(o.candidates.passportNumber,`%${e}%`));break;default:l.push((0,d.or)((0,d.B3)(o.candidates.fullName,`%${e}%`),(0,d.B3)(o.candidates.email,`%${e}%`),(0,d.B3)(o.candidates.passportNumber,`%${e}%`)))}}a.testCenter&&l.push((0,d.eq)(o.candidates.testCenter,a.testCenter)),a.testDateFrom&&l.push((0,d.RO)(o.candidates.testDate,new Date(a.testDateFrom))),a.testDateTo&&l.push((0,d.wJ)(o.candidates.testDate,new Date(a.testDateTo))),a.nationality&&l.push((0,d.B3)(o.candidates.nationality,`%${a.nationality}%`));let c=l.length>0?(0,d.Uo)(...l):void 0,u=await i.db.select({candidateId:o.candidates.id,fullName:o.candidates.fullName,email:o.candidates.email,phoneNumber:o.candidates.phoneNumber,passportNumber:o.candidates.passportNumber,nationality:o.candidates.nationality,dateOfBirth:o.candidates.dateOfBirth,testDate:o.candidates.testDate,testCenter:o.candidates.testCenter,registrationDate:o.candidates.createdAt,resultId:o.testResults.id,listeningBandScore:o.testResults.listeningBandScore,readingBandScore:o.testResults.readingBandScore,writingBandScore:o.testResults.writingBandScore,speakingBandScore:o.testResults.speakingBandScore,overallBandScore:o.testResults.overallBandScore,resultStatus:o.testResults.status,certificateGenerated:o.testResults.certificateGenerated,resultCreatedAt:o.testResults.createdAt}).from(o.candidates).leftJoin(o.testResults,(0,d.eq)(o.candidates.id,o.testResults.candidateId)).where(c).orderBy(o.candidates.fullName);if("all"!==a.hasResults&&("yes"===a.hasResults?u=u.filter(e=>e.resultId):"no"===a.hasResults&&(u=u.filter(e=>!e.resultId))),"all"!==a.resultStatus&&a.resultStatus&&(u=u.filter(e=>e.resultStatus===a.resultStatus)),a.bandScoreMin){let e=parseFloat(a.bandScoreMin);u=u.filter(t=>t.overallBandScore&&t.overallBandScore>=e)}if(a.bandScoreMax){let e=parseFloat(a.bandScoreMax);u=u.filter(t=>t.overallBandScore&&t.overallBandScore<=e)}if("csv"===r){let e=["Candidate ID,Full Name,Email,Phone Number,Passport Number,Nationality,Date of Birth,Test Date,Test Center,Registration Date,Has Results,Result ID,Listening Band Score,Reading Band Score,Writing Band Score,Speaking Band Score,Overall Band Score,Result Status,Certificate Generated,Result Date",...u.map(e=>[e.candidateId,`"${e.fullName}"`,e.email,e.phoneNumber,e.passportNumber,`"${e.nationality}"`,e.dateOfBirth?new Date(e.dateOfBirth).toISOString().split("T")[0]:"",new Date(e.testDate).toISOString().split("T")[0],`"${e.testCenter}"`,new Date(e.registrationDate).toISOString().split("T")[0],e.resultId?"Yes":"No",e.resultId||"",e.listeningBandScore||"",e.readingBandScore||"",e.writingBandScore||"",e.speakingBandScore||"",e.overallBandScore||"",e.resultStatus||"",e.certificateGenerated?"Yes":"No",e.resultCreatedAt?new Date(e.resultCreatedAt).toISOString().split("T")[0]:""].join(","))].join("\n"),t=Buffer.from(e,"utf-8");return new s.NextResponse(t,{status:200,headers:{"Content-Type":"text/csv","Content-Disposition":`attachment; filename="ielts_export_${new Date().toISOString().split("T")[0]}.csv"`,"Content-Length":t.length.toString()}})}return s.NextResponse.json({data:u,total:u.length,exportDate:new Date().toISOString(),filters:a})}catch(e){return console.error("Export error:",e),s.NextResponse.json({error:"Failed to export data"},{status:500})}}[n,i]=l.then?(await l)():l,r()}catch(e){r(e)}})},37702:(e,t,a)=>{"use strict";a.a(e,async(e,r)=>{try{a.d(t,{Y9:()=>u,j2:()=>p});var s=a(32221),n=a(31648),i=a(62693),o=a(48590),d=a(47579),l=a(34926),c=e([i]);i=(c.then?(await c)():c)[0];let{handlers:u,auth:p,signIn:m,signOut:f}=(0,s.Ay)({session:{strategy:"jwt"},providers:[(0,n.A)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;try{let t=await i.db.select().from(o.users).where((0,d.eq)(o.users.email,e.email)).limit(1);if(0===t.length)return null;let a=t[0];if(!a.password||!await l.Ay.compare(e.password,a.password))return null;return{id:a.id,email:a.email,name:a.name,role:a.role}}catch(e){return console.error("Authentication error:",e),null}}})],callbacks:{jwt:async({token:e,user:t})=>(t&&(e.role=t.role),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role),e)},pages:{signIn:"/auth/signin"}});r()}catch(e){r(e)}})},62693:(e,t,a)=>{"use strict";a.a(e,async(e,r)=>{try{a.d(t,{db:()=>c});var s=a(10072),n=a(2113),i=a(48590),o=e([n,s]);[n,s]=o.then?(await o)():o;let d=process.env.DATABASE_URL,l=(0,n.default)(d,{prepare:!1}),c=(0,s.f)(l,{schema:i});r()}catch(e){r(e)}})},48590:(e,t,a)=>{"use strict";a.r(t),a.d(t,{accounts:()=>p,aiFeedback:()=>y,candidates:()=>_,sessions:()=>m,testResults:()=>g,users:()=>u,verificationTokens:()=>f});var r=a(87858),s=a(44799),n=a(32590),i=a(9848),o=a(70009),d=a(27390),l=a(32190),c=a(4502);let u=(0,r.cJ)("users",{id:(0,s.Qq)("id").primaryKey().$defaultFn(()=>(0,c.sX)()),name:(0,s.Qq)("name"),email:(0,s.Qq)("email").notNull().unique(),emailVerified:(0,n.vE)("emailVerified",{mode:"date"}),image:(0,s.Qq)("image"),password:(0,s.Qq)("password"),role:(0,s.Qq)("role",{enum:["admin","test_checker"]}).notNull().default("test_checker"),createdAt:(0,n.vE)("created_at").defaultNow().notNull(),updatedAt:(0,n.vE)("updated_at").defaultNow().notNull()}),p=(0,r.cJ)("accounts",{userId:(0,s.Qq)("userId").notNull().references(()=>u.id,{onDelete:"cascade"}),type:(0,s.Qq)("type").notNull(),provider:(0,s.Qq)("provider").notNull(),providerAccountId:(0,s.Qq)("providerAccountId").notNull(),refresh_token:(0,s.Qq)("refresh_token"),access_token:(0,s.Qq)("access_token"),expires_at:(0,i.nd)("expires_at"),token_type:(0,s.Qq)("token_type"),scope:(0,s.Qq)("scope"),id_token:(0,s.Qq)("id_token"),session_state:(0,s.Qq)("session_state")}),m=(0,r.cJ)("sessions",{sessionToken:(0,s.Qq)("sessionToken").primaryKey(),userId:(0,s.Qq)("userId").notNull().references(()=>u.id,{onDelete:"cascade"}),expires:(0,n.vE)("expires",{mode:"date"}).notNull()}),f=(0,r.cJ)("verificationTokens",{identifier:(0,s.Qq)("identifier").notNull(),token:(0,s.Qq)("token").notNull(),expires:(0,n.vE)("expires",{mode:"date"}).notNull()}),_=(0,r.cJ)("candidates",{id:(0,s.Qq)("id").primaryKey().$defaultFn(()=>(0,c.sX)()),fullName:(0,s.Qq)("full_name").notNull(),email:(0,s.Qq)("email").notNull().unique(),phoneNumber:(0,s.Qq)("phone_number").notNull(),dateOfBirth:(0,n.vE)("date_of_birth",{mode:"date"}).notNull(),nationality:(0,s.Qq)("nationality").notNull(),passportNumber:(0,s.Qq)("passport_number").notNull().unique(),testDate:(0,n.vE)("test_date",{mode:"date"}).notNull(),testCenter:(0,s.Qq)("test_center").notNull(),photoUrl:(0,s.Qq)("photo_url"),createdAt:(0,n.vE)("created_at").defaultNow().notNull(),updatedAt:(0,n.vE)("updated_at").defaultNow().notNull()}),g=(0,r.cJ)("test_results",{id:(0,s.Qq)("id").primaryKey().$defaultFn(()=>(0,c.sX)()),candidateId:(0,s.Qq)("candidate_id").notNull().references(()=>_.id,{onDelete:"cascade"}),listeningScore:(0,o._)("listening_score",{precision:3,scale:1}),listeningBandScore:(0,o._)("listening_band_score",{precision:2,scale:1}),readingScore:(0,o._)("reading_score",{precision:3,scale:1}),readingBandScore:(0,o._)("reading_band_score",{precision:2,scale:1}),writingTask1Score:(0,o._)("writing_task1_score",{precision:2,scale:1}),writingTask2Score:(0,o._)("writing_task2_score",{precision:2,scale:1}),writingBandScore:(0,o._)("writing_band_score",{precision:2,scale:1}),speakingFluencyScore:(0,o._)("speaking_fluency_score",{precision:2,scale:1}),speakingLexicalScore:(0,o._)("speaking_lexical_score",{precision:2,scale:1}),speakingGrammarScore:(0,o._)("speaking_grammar_score",{precision:2,scale:1}),speakingPronunciationScore:(0,o._)("speaking_pronunciation_score",{precision:2,scale:1}),speakingBandScore:(0,o._)("speaking_band_score",{precision:2,scale:1}),overallBandScore:(0,o._)("overall_band_score",{precision:2,scale:1}),status:(0,s.Qq)("status",{enum:["pending","completed","verified"]}).notNull().default("pending"),enteredBy:(0,s.Qq)("entered_by").references(()=>u.id),verifiedBy:(0,s.Qq)("verified_by").references(()=>u.id),certificateGenerated:(0,d.zM)("certificate_generated").default(!1),certificateSerial:(0,s.Qq)("certificate_serial").unique(),certificateUrl:(0,s.Qq)("certificate_url"),aiFeedbackGenerated:(0,d.zM)("ai_feedback_generated").default(!1),testDate:(0,n.vE)("test_date",{mode:"date"}),createdAt:(0,n.vE)("created_at").defaultNow().notNull(),updatedAt:(0,n.vE)("updated_at").defaultNow().notNull()}),y=(0,r.cJ)("ai_feedback",{id:(0,s.Qq)("id").primaryKey().$defaultFn(()=>(0,c.sX)()),testResultId:(0,s.Qq)("test_result_id").notNull().references(()=>g.id,{onDelete:"cascade"}),listeningFeedback:(0,s.Qq)("listening_feedback"),readingFeedback:(0,s.Qq)("reading_feedback"),writingFeedback:(0,s.Qq)("writing_feedback"),speakingFeedback:(0,s.Qq)("speaking_feedback"),overallFeedback:(0,s.Qq)("overall_feedback"),studyRecommendations:(0,s.Qq)("study_recommendations"),strengths:(0,l.Pq)("strengths").$type(),weaknesses:(0,l.Pq)("weaknesses").$type(),studyPlan:(0,l.Pq)("study_plan").$type(),generatedAt:(0,n.vE)("generated_at").defaultNow().notNull()})}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[638,5452,9757,4681],()=>a(9506));module.exports=r})();