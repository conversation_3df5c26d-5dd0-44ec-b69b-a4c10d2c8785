(()=>{var e={};e.id=4297,e.ids=[4297],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},87244:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>h,tree:()=>o});var r=t(70260),a=t(28203),i=t(25155),n=t.n(i),d=t(67292),l={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>d[e]);t.d(s,l);let o=["",{children:["dashboard",{children:["feedback",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,85914)),"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\dashboard\\feedback\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,33405)),"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,71354)),"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\dashboard\\feedback\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/feedback/page",pathname:"/dashboard/feedback",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},97032:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,13219,23)),Promise.resolve().then(t.t.bind(t,34863,23)),Promise.resolve().then(t.t.bind(t,25155,23)),Promise.resolve().then(t.t.bind(t,40802,23)),Promise.resolve().then(t.t.bind(t,9350,23)),Promise.resolve().then(t.t.bind(t,48530,23)),Promise.resolve().then(t.t.bind(t,88921,23))},60584:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,66959,23)),Promise.resolve().then(t.t.bind(t,33875,23)),Promise.resolve().then(t.t.bind(t,88903,23)),Promise.resolve().then(t.t.bind(t,57174,23)),Promise.resolve().then(t.t.bind(t,84178,23)),Promise.resolve().then(t.t.bind(t,87190,23)),Promise.resolve().then(t.t.bind(t,61365,23))},71315:(e,s,t)=>{Promise.resolve().then(t.bind(t,70452))},8267:(e,s,t)=>{Promise.resolve().then(t.bind(t,98805))},88388:(e,s,t)=>{Promise.resolve().then(t.bind(t,85914))},18244:(e,s,t)=>{Promise.resolve().then(t.bind(t,78685))},63508:(e,s,t)=>{Promise.resolve().then(t.bind(t,33405))},16244:(e,s,t)=>{Promise.resolve().then(t.bind(t,12361))},41680:(e,s,t)=>{"use strict";t.d(s,{A:()=>m});var r=t(58009);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,s,t)=>t?t.toUpperCase():s.toLowerCase()),n=e=>{let s=i(e);return s.charAt(0).toUpperCase()+s.slice(1)},d=(...e)=>e.filter((e,s,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===s).join(" ").trim(),l=e=>{for(let s in e)if(s.startsWith("aria-")||"role"===s||"title"===s)return!0};var o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,r.forwardRef)(({color:e="currentColor",size:s=24,strokeWidth:t=2,absoluteStrokeWidth:a,className:i="",children:n,iconNode:c,...m},h)=>(0,r.createElement)("svg",{ref:h,...o,width:s,height:s,stroke:e,strokeWidth:a?24*Number(t)/Number(s):t,className:d("lucide",i),...!n&&!l(m)&&{"aria-hidden":"true"},...m},[...c.map(([e,s])=>(0,r.createElement)(e,s)),...Array.isArray(n)?n:[n]])),m=(e,s)=>{let t=(0,r.forwardRef)(({className:t,...i},l)=>(0,r.createElement)(c,{ref:l,iconNode:s,className:d(`lucide-${a(n(e))}`,`lucide-${e}`,t),...i}));return t.displayName=n(e),t}},35668:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(41680).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},4269:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(41680).A)("book-open",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},78397:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(41680).A)("brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]])},79660:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(41680).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},94172:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(41680).A)("clipboard-list",[["rect",{width:"8",height:"4",x:"8",y:"2",rx:"1",ry:"1",key:"tgr4d6"}],["path",{d:"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2",key:"116196"}],["path",{d:"M12 11h4",key:"1jrz19"}],["path",{d:"M12 16h4",key:"n85exb"}],["path",{d:"M8 11h.01",key:"1dfujw"}],["path",{d:"M8 16h.01",key:"18s6g9"}]])},61075:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(41680).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},87137:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(41680).A)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},30722:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(41680).A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},92557:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(41680).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},33680:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(41680).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},16873:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(41680).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},80832:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(41680).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},79334:(e,s,t)=>{"use strict";var r=t(58686);t.o(r,"useParams")&&t.d(s,{useParams:function(){return r.useParams}}),t.o(r,"useRouter")&&t.d(s,{useRouter:function(){return r.useRouter}}),t.o(r,"useSearchParams")&&t.d(s,{useSearchParams:function(){return r.useSearchParams}})},78685:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>y});var r=t(45512),a=t(58009),i=t(79334),n=t(28531),d=t.n(n),l=t(35668),o=t(16873),c=t(78397),m=t(92557),h=t(41680);let x=(0,h.A)("sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]]);var p=t(61075),u=t(80832);let b=(0,h.A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]);var f=t(4269),g=t(33680);function y(){let e=(0,i.useSearchParams)().get("resultId"),[s,t]=(0,a.useState)(e||""),[n,h]=(0,a.useState)(null),[y,v]=(0,a.useState)(null),[j,k]=(0,a.useState)(!1),[N,w]=(0,a.useState)(!1),[A,S]=(0,a.useState)(""),[P,C]=(0,a.useState)(""),[M,E]=(0,a.useState)([]),I=async()=>{if(P.trim())try{let e=await fetch("/api/checker/results/search",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({query:P})});if(e.ok){let s=await e.json();E(s)}}catch(e){console.error("Error searching results:",e)}},L=async()=>{if(n){k(!0),S("");try{let e=await fetch("/api/ai/generate-feedback",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({resultId:s,scores:{listening:n.listeningBandScore,reading:n.readingBandScore,writing:n.writingBandScore,speaking:n.speakingBandScore,overall:n.overallBandScore}})});if(e.ok){let s=await e.json();v(s.feedback)}else{let s=await e.json();S(s.error||"Failed to generate feedback")}}catch(e){console.error("Error generating feedback:",e),S("An error occurred while generating feedback")}finally{k(!1)}}},R=async()=>{if(y&&s){w(!0);try{(await fetch("/api/ai/save-feedback",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({resultId:s,feedback:y})})).ok?alert("Feedback saved successfully!"):S("Failed to save feedback")}catch(e){console.error("Error saving feedback:",e),S("An error occurred while saving feedback")}finally{w(!1)}}};return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)("div",{className:"flex items-center justify-between",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(d(),{href:"/dashboard",className:"mr-4 p-2 text-gray-400 hover:text-gray-600",children:(0,r.jsx)(l.A,{className:"h-5 w-5"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"AI Feedback Generator"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Generate personalized feedback for IELTS test results"})]})]})}),!s&&(0,r.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-medium text-gray-900 mb-4 flex items-center",children:[(0,r.jsx)(o.A,{className:"h-5 w-5 mr-2"}),"Select Test Result"]}),(0,r.jsxs)("div",{className:"flex gap-4 mb-4",children:[(0,r.jsx)("input",{type:"text",value:P,onChange:e=>C(e.target.value),placeholder:"Search by candidate name or passport number...",className:"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"}),(0,r.jsx)("button",{onClick:I,className:"px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700",children:"Search"})]}),M.length>0&&(0,r.jsx)("div",{className:"space-y-2",children:M.map(e=>(0,r.jsx)("div",{onClick:()=>t(e.id),className:"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer",children:(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900",children:e.candidate.fullName}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:e.candidate.passportNumber})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsxs)("p",{className:"font-semibold text-gray-900",children:["Overall: ",e.overallBandScore||"N/A"]}),(0,r.jsxs)("p",{className:"text-sm text-gray-500",children:["L:",e.listeningBandScore," R:",e.readingBandScore," W:",e.writingBandScore," S:",e.speakingBandScore]})]})]})},e.id))})]}),n&&(0,r.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-medium text-blue-900",children:n.candidate.fullName}),(0,r.jsxs)("p",{className:"text-sm text-blue-700",children:[n.candidate.passportNumber," • Overall Band: ",n.overallBandScore||"N/A"]})]}),(0,r.jsx)("button",{onClick:()=>{t(""),h(null),v(null)},className:"text-blue-600 hover:text-blue-800",children:"Change Result"})]})}),n&&!y&&(0,r.jsxs)("div",{className:"bg-white shadow rounded-lg p-6 text-center",children:[(0,r.jsx)(c.A,{className:"h-16 w-16 text-purple-600 mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Generate AI Feedback"}),(0,r.jsx)("p",{className:"text-gray-600 mb-6",children:"Create personalized feedback and recommendations based on the test scores"}),A&&(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm mb-4",children:A}),(0,r.jsx)("button",{onClick:L,disabled:j,className:"inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 disabled:opacity-50",children:j?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(m.A,{className:"animate-spin h-5 w-5 mr-2"}),"Generating Feedback..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(x,{className:"h-5 w-5 mr-2"}),"Generate AI Feedback"]})})]}),y&&(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-medium text-gray-900 mb-4 flex items-center",children:[(0,r.jsx)(p.A,{className:"h-5 w-5 mr-2 text-blue-600"}),"Overall Assessment"]}),(0,r.jsx)("p",{className:"text-gray-700 leading-relaxed",children:y.overallAssessment})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-medium text-gray-900 mb-4 flex items-center",children:[(0,r.jsx)(u.A,{className:"h-5 w-5 mr-2 text-green-600"}),"Strengths"]}),(0,r.jsx)("ul",{className:"space-y-2",children:y.strengths.map((e,s)=>(0,r.jsxs)("li",{className:"flex items-start",children:[(0,r.jsx)("span",{className:"flex-shrink-0 h-2 w-2 bg-green-400 rounded-full mt-2 mr-3"}),(0,r.jsx)("span",{className:"text-gray-700",children:e})]},s))})]}),(0,r.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-medium text-gray-900 mb-4 flex items-center",children:[(0,r.jsx)(b,{className:"h-5 w-5 mr-2 text-orange-600"}),"Areas for Improvement"]}),(0,r.jsx)("ul",{className:"space-y-2",children:y.areasForImprovement.map((e,s)=>(0,r.jsxs)("li",{className:"flex items-start",children:[(0,r.jsx)("span",{className:"flex-shrink-0 h-2 w-2 bg-orange-400 rounded-full mt-2 mr-3"}),(0,r.jsx)("span",{className:"text-gray-700",children:e})]},s))})]})]}),(0,r.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-medium text-gray-900 mb-4 flex items-center",children:[(0,r.jsx)(f.A,{className:"h-5 w-5 mr-2 text-purple-600"}),"Specific Recommendations"]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium text-blue-900 mb-2",children:"Listening"}),(0,r.jsx)("p",{className:"text-sm text-gray-700 mb-4",children:y.specificRecommendations.listening}),(0,r.jsx)("h4",{className:"font-medium text-green-900 mb-2",children:"Reading"}),(0,r.jsx)("p",{className:"text-sm text-gray-700",children:y.specificRecommendations.reading})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium text-purple-900 mb-2",children:"Writing"}),(0,r.jsx)("p",{className:"text-sm text-gray-700 mb-4",children:y.specificRecommendations.writing}),(0,r.jsx)("h4",{className:"font-medium text-red-900 mb-2",children:"Speaking"}),(0,r.jsx)("p",{className:"text-sm text-gray-700",children:y.specificRecommendations.speaking})]})]})]}),(0,r.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Recommended Study Plan"}),(0,r.jsx)("p",{className:"text-gray-700 leading-relaxed mb-4",children:y.studyPlan}),(0,r.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Next Steps"}),(0,r.jsx)("ul",{className:"space-y-2",children:y.nextSteps.map((e,s)=>(0,r.jsxs)("li",{className:"flex items-start",children:[(0,r.jsx)("span",{className:"flex-shrink-0 h-6 w-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium mr-3 mt-0.5",children:s+1}),(0,r.jsx)("span",{className:"text-gray-700",children:e})]},s))})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-4",children:[(0,r.jsxs)("button",{onClick:L,disabled:j,className:"px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50",children:[(0,r.jsx)(m.A,{className:"h-4 w-4 mr-2 inline"}),"Regenerate"]}),(0,r.jsx)("button",{onClick:R,disabled:N,className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50",children:N?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Saving..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(g.A,{className:"h-4 w-4 mr-2"}),"Save Feedback"]})})]})]})]})}},12361:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>u});var r=t(45512),a=t(98805),i=t(79334);t(58009);var n=t(28531),d=t.n(n),l=t(87137),o=t(16873),c=t(94172),m=t(79660),h=t(78397),x=t(61075),p=t(30722);function u({children:e}){let{data:s,status:t}=(0,a.wV)();if((0,i.useRouter)(),"loading"===t)return(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})});if(!s)return null;let n=[{name:"Dashboard",href:"/dashboard",icon:l.A},{name:"Search Candidates",href:"/dashboard/search",icon:o.A},{name:"Enter Results",href:"/dashboard/results",icon:c.A},{name:"Test Results",href:"/dashboard/results/list",icon:m.A},{name:"AI Feedback",href:"/dashboard/feedback",icon:h.A}];return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)("div",{className:"fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg",children:(0,r.jsxs)("div",{className:"flex flex-col h-full",children:[(0,r.jsxs)("div",{className:"flex items-center px-6 py-4 border-b border-gray-200",children:[(0,r.jsx)(x.A,{className:"h-8 w-8 text-blue-600 mr-3"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-lg font-semibold text-gray-900",children:"IELTS Checker"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Test Results Portal"})]})]}),(0,r.jsx)("nav",{className:"flex-1 px-4 py-6 space-y-2",children:n.map(e=>{let s=e.icon;return(0,r.jsxs)(d(),{href:e.href,className:"flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-100 hover:text-gray-900 group",children:[(0,r.jsx)(s,{className:"h-5 w-5 mr-3 text-gray-400 group-hover:text-gray-500"}),e.name]},e.name)})}),(0,r.jsxs)("div",{className:"border-t border-gray-200 p-4",children:[(0,r.jsx)("div",{className:"flex items-center mb-3",children:(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900",children:s.user?.name}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:s.user?.email}),(0,r.jsx)("p",{className:"text-xs text-green-600 font-medium",children:"Test Checker"})]})}),(0,r.jsxs)("button",{onClick:()=>(0,a.CI)({callbackUrl:"/"}),className:"flex items-center w-full px-3 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-100 hover:text-gray-900",children:[(0,r.jsx)(p.A,{className:"h-4 w-4 mr-3"}),"Sign out"]})]})]})}),(0,r.jsx)("div",{className:"pl-64",children:(0,r.jsx)("main",{className:"py-6",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:e})})})]})}},85914:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\feedback\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\dashboard\\feedback\\page.tsx","default")},33405:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\dashboard\\layout.tsx","default")},71354:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>l,metadata:()=>d});var r=t(62740),a=t(85041),i=t.n(a),n=t(70452);t(61135);let d={title:"IELTS Certification System",description:"Professional IELTS test result management and certification system"};function l({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsx)("body",{className:i().className,children:(0,r.jsx)(n.SessionProvider,{children:e})})})}},70440:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});var r=t(88077);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},61135:()=>{}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[638,8338,2367],()=>t(87244));module.exports=r})();