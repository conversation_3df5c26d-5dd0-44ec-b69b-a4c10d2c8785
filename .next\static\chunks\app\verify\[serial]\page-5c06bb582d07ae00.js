(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8675],{208:(e,s,t)=>{Promise.resolve().then(t.bind(t,3285))},7401:(e,s,t)=>{"use strict";t.d(s,{A:()=>x});var a=t(2115);let r=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,s,t)=>t?t.toUpperCase():s.toLowerCase()),l=e=>{let s=i(e);return s.charAt(0).toUpperCase()+s.slice(1)},c=function(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return s.filter((e,s,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===s).join(" ").trim()},d=e=>{for(let s in e)if(s.startsWith("aria-")||"role"===s||"title"===s)return!0};var n={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let m=(0,a.forwardRef)((e,s)=>{let{color:t="currentColor",size:r=24,strokeWidth:i=2,absoluteStrokeWidth:l,className:m="",children:x,iconNode:o,...h}=e;return(0,a.createElement)("svg",{ref:s,...n,width:r,height:r,stroke:t,strokeWidth:l?24*Number(i)/Number(r):i,className:c("lucide",m),...!x&&!d(h)&&{"aria-hidden":"true"},...h},[...o.map(e=>{let[s,t]=e;return(0,a.createElement)(s,t)}),...Array.isArray(x)?x:[x]])}),x=(e,s)=>{let t=(0,a.forwardRef)((t,i)=>{let{className:d,...n}=t;return(0,a.createElement)(m,{ref:i,iconNode:s,className:c("lucide-".concat(r(l(e))),"lucide-".concat(e),d),...n})});return t.displayName=l(e),t}},7364:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(7401).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},7508:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(7401).A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},2423:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(7401).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},1594:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(7401).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},3239:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(7401).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},4081:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(7401).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},7223:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(7401).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},2640:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(7401).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},1466:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(7401).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},6046:(e,s,t)=>{"use strict";var a=t(6658);t.o(a,"useParams")&&t.d(s,{useParams:function(){return a.useParams}}),t.o(a,"useRouter")&&t.d(s,{useRouter:function(){return a.useRouter}}),t.o(a,"useSearchParams")&&t.d(s,{useSearchParams:function(){return a.useSearchParams}})},3285:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>p});var a=t(5155),r=t(2115),i=t(6046),l=t(8173),c=t.n(l),d=t(7364),n=t(2640),m=t(3239);let x=(0,t(7401).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]);var o=t(7508),h=t(1466),u=t(7223),f=t(2423),j=t(4081),v=t(1594);function p(){var e;let s=(0,i.useParams)().serial,[t,l]=(0,r.useState)(null),[p,y]=(0,r.useState)(!0),N=(0,r.useCallback)(async()=>{try{let e=await fetch("/api/certificate/verify/".concat(s)),t=await e.json();e.ok,l(t)}catch(e){console.error("Error verifying certificate:",e),l({valid:!1,error:"Network error",message:"Unable to connect to verification service"})}finally{y(!1)}},[s]);return((0,r.useEffect)(()=>{s&&N()},[N,s]),p)?(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Verifying certificate..."})]})}):(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100",children:[(0,a.jsx)("header",{className:"bg-white shadow-sm",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsx)("div",{className:"flex justify-between items-center py-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsxs)(c(),{href:"/search",className:"flex items-center text-blue-600 hover:text-blue-700 mr-4",children:[(0,a.jsx)(d.A,{className:"h-5 w-5 mr-1"}),"Back to Search"]}),(0,a.jsx)(n.A,{className:"h-8 w-8 text-blue-600 mr-3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Certificate Verification"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Verify IELTS Certificate Authenticity"})]})]})})})}),(0,a.jsxs)("main",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-lg p-8 mb-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"mb-6",children:(null==t?void 0:t.valid)?(0,a.jsx)(m.A,{className:"h-16 w-16 text-green-500 mx-auto"}):(0,a.jsx)(x,{className:"h-16 w-16 text-red-500 mx-auto"})}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:(null==t?void 0:t.valid)?"Certificate Verified":"Verification Failed"}),(0,a.jsx)("div",{className:"inline-flex items-center px-4 py-2 rounded-full text-sm font-medium ".concat((null==t?void 0:t.valid)?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:(null==t?void 0:t.valid)?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Valid Certificate"]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(x,{className:"h-4 w-4 mr-2"}),"Invalid Certificate"]})}),(0,a.jsxs)("p",{className:"mt-4 text-gray-600",children:["Serial Number: ",(0,a.jsx)("span",{className:"font-mono font-semibold",children:s})]}),(0,a.jsx)("p",{className:"mt-2 text-sm text-gray-500",children:(null==t?void 0:null===(e=t.verification)||void 0===e?void 0:e.message)||(null==t?void 0:t.message)})]})}),(null==t?void 0:t.valid)&&t.certificate&&(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-8",children:[(0,a.jsxs)("div",{className:"flex items-center mb-6",children:[(0,a.jsx)(o.A,{className:"h-6 w-6 text-blue-600 mr-2"}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900",children:"Certificate Details"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h4",{className:"text-lg font-medium text-gray-900 border-b pb-2",children:"Candidate Information"}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(h.A,{className:"h-5 w-5 text-gray-400 mr-3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Full Name"}),(0,a.jsx)("p",{className:"font-semibold",children:t.certificate.candidateName})]})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(u.A,{className:"h-5 w-5 text-gray-400 mr-3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Nationality"}),(0,a.jsx)("p",{className:"font-semibold",children:t.certificate.nationality})]})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h4",{className:"text-lg font-medium text-gray-900 border-b pb-2",children:"Test Information"}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(f.A,{className:"h-5 w-5 text-gray-400 mr-3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Test Date"}),(0,a.jsx)("p",{className:"font-semibold",children:new Date(t.certificate.testDate).toLocaleDateString()})]})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(u.A,{className:"h-5 w-5 text-gray-400 mr-3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Test Center"}),(0,a.jsx)("p",{className:"font-semibold",children:t.certificate.testCenter})]})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(j.A,{className:"h-5 w-5 text-gray-400 mr-3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Overall Band Score"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-indigo-600",children:t.certificate.overallBandScore||"N/A"})]})]})]})]}),(0,a.jsxs)("div",{className:"mt-8 pt-6 border-t border-gray-200",children:[(0,a.jsx)("h4",{className:"text-lg font-medium text-gray-900 mb-4",children:"Certificate Metadata"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-600",children:"Certificate Serial"}),(0,a.jsx)("p",{className:"font-mono font-semibold",children:t.certificate.serial})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-600",children:"Result ID"}),(0,a.jsx)("p",{className:"font-mono font-semibold",children:t.certificate.resultId})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-600",children:"Issue Date"}),(0,a.jsx)("p",{className:"font-semibold",children:new Date(t.certificate.issueDate).toLocaleDateString()})]})]})]}),(0,a.jsx)("div",{className:"mt-8 flex justify-center space-x-4",children:(0,a.jsxs)(c(),{href:"/results/".concat(t.certificate.resultId),className:"inline-flex items-center px-4 py-2 border border-blue-300 text-sm font-medium rounded-md text-blue-700 bg-blue-50 hover:bg-blue-100",children:[(0,a.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"View Full Results"]})})]}),!(null==t?void 0:t.valid)&&(0,a.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-6",children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[(0,a.jsx)(v.A,{className:"h-6 w-6 text-red-600 mr-2"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-red-800",children:"Verification Failed"})]}),(0,a.jsx)("p",{className:"text-red-700 mb-4",children:(null==t?void 0:t.message)||"The certificate could not be verified."}),(0,a.jsxs)("div",{className:"text-sm text-red-600",children:[(0,a.jsx)("p",{children:"Possible reasons:"}),(0,a.jsxs)("ul",{className:"list-disc list-inside mt-2 space-y-1",children:[(0,a.jsx)("li",{children:"Invalid or incorrect serial number"}),(0,a.jsx)("li",{children:"Certificate has not been generated"}),(0,a.jsx)("li",{children:"Test result is still pending"}),(0,a.jsx)("li",{children:"Certificate has been revoked"})]})]})]}),(0,a.jsxs)("div",{className:"mt-12 text-center",children:[(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"For additional verification or support, please contact the IELTS administration office."}),(0,a.jsx)("div",{className:"mt-4",children:(0,a.jsx)(c(),{href:"/search",className:"text-blue-600 hover:text-blue-700 text-sm font-medium",children:"Search for Results"})})]})]})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[8173,8441,1517,7358],()=>s(208)),_N_E=e.O()}]);