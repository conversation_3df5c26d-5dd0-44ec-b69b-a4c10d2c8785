(()=>{var e={};e.id=3967,e.ids=[3967],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},14738:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>o,routeModule:()=>x,tree:()=>c});var r=t(70260),a=t(28203),l=t(25155),i=t.n(l),d=t(67292),n={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>d[e]);t.d(s,n);let c=["",{children:["admin",{children:["results",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,39500)),"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\admin\\results\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,96038)),"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\admin\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,71354)),"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],o=["C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\admin\\results\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/results/page",pathname:"/admin/results",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},74992:(e,s,t)=>{Promise.resolve().then(t.bind(t,39500))},84720:(e,s,t)=>{Promise.resolve().then(t.bind(t,35568))},41680:(e,s,t)=>{"use strict";t.d(s,{A:()=>m});var r=t(58009);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,s,t)=>t?t.toUpperCase():s.toLowerCase()),i=e=>{let s=l(e);return s.charAt(0).toUpperCase()+s.slice(1)},d=(...e)=>e.filter((e,s,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===s).join(" ").trim(),n=e=>{for(let s in e)if(s.startsWith("aria-")||"role"===s||"title"===s)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=(0,r.forwardRef)(({color:e="currentColor",size:s=24,strokeWidth:t=2,absoluteStrokeWidth:a,className:l="",children:i,iconNode:o,...m},x)=>(0,r.createElement)("svg",{ref:x,...c,width:s,height:s,stroke:e,strokeWidth:a?24*Number(t)/Number(s):t,className:d("lucide",l),...!i&&!n(m)&&{"aria-hidden":"true"},...m},[...o.map(([e,s])=>(0,r.createElement)(e,s)),...Array.isArray(i)?i:[i]])),m=(e,s)=>{let t=(0,r.forwardRef)(({className:t,...l},n)=>(0,r.createElement)(o,{ref:n,iconNode:s,className:d(`lucide-${a(i(e))}`,`lucide-${e}`,t),...l}));return t.displayName=i(e),t}},43464:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(41680).A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},45037:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(41680).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},46583:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(41680).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},4643:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(41680).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},61075:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(41680).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},80832:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(41680).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},79334:(e,s,t)=>{"use strict";var r=t(58686);t.o(r,"useParams")&&t.d(s,{useParams:function(){return r.useParams}}),t.o(r,"useRouter")&&t.d(s,{useRouter:function(){return r.useRouter}}),t.o(r,"useSearchParams")&&t.d(s,{useSearchParams:function(){return r.useSearchParams}})},35568:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>y});var r=t(45512),a=t(58009),l=t(28531),i=t.n(l),d=t(4643),n=t(46583),c=t(43464),o=t(45037),m=t(19473),x=t(79660),h=t(64977),u=t(80832),p=t(16873),g=t(94889),f=t(21956),j=t(19904);function y(){let[e,s]=(0,a.useState)([]),[t,l]=(0,a.useState)(null),[y,v]=(0,a.useState)(!0),[b,N]=(0,a.useState)(""),[w,k]=(0,a.useState)(""),[A,C]=(0,a.useState)("all"),[S,P]=(0,a.useState)(1),[E,L]=(0,a.useState)(1);(0,a.useCallback)(async()=>{try{let e=await fetch("/api/admin/dashboard");if(e.ok){let s=await e.json();l(s)}}catch(e){console.error("Error fetching stats:",e)}},[]),(0,a.useCallback)(async()=>{try{let e=new URLSearchParams({page:S.toString(),limit:"20",..."all"!==A&&{status:A},...w&&{search:w}}),t=await fetch(`/api/admin/results?${e}`);if(t.ok){let e=await t.json();s(e.results||[]),L(Math.ceil((e.total||0)/20))}else N("Failed to fetch results")}catch(e){console.error("Error fetching results:",e),N("An error occurred while fetching results")}finally{v(!1)}},[S,A,w]);let R=e=>{switch(e){case"pending":return(0,r.jsx)(d.A,{className:"h-4 w-4 text-yellow-500"});case"completed":return(0,r.jsx)(n.A,{className:"h-4 w-4 text-green-500"});case"verified":return(0,r.jsx)(c.A,{className:"h-4 w-4 text-blue-500"});default:return(0,r.jsx)(o.A,{className:"h-4 w-4 text-gray-500"})}},M=e=>{switch(e){case"pending":return"text-yellow-800 bg-yellow-100";case"completed":return"text-green-800 bg-green-100";case"verified":return"text-blue-800 bg-blue-100";default:return"text-gray-800 bg-gray-100"}},_=async()=>{try{let e=new URLSearchParams({..."all"!==A&&{status:A},...w&&{search:w},export:"true"}),s=await fetch(`/api/admin/results/export?${e}`);if(s.ok){let e=await s.blob(),t=window.URL.createObjectURL(e),r=document.createElement("a");r.href=t,r.download=`ielts_results_${new Date().toISOString().split("T")[0]}.csv`,document.body.appendChild(r),r.click(),window.URL.revokeObjectURL(t),document.body.removeChild(r)}}catch(e){console.error("Export error:",e),alert("An error occurred while exporting results")}};return y?(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Test Results Management"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Monitor and manage all IELTS test results"})]}),(0,r.jsx)("div",{className:"flex space-x-3",children:(0,r.jsxs)("button",{onClick:_,className:"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50",children:[(0,r.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Export Results"]})})]}),t&&(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4",children:[(0,r.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,r.jsx)("div",{className:"p-5",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(x.A,{className:"h-6 w-6 text-gray-400"})}),(0,r.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,r.jsxs)("dl",{children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Total Results"}),(0,r.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:t.totalResults})]})})]})})}),(0,r.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,r.jsx)("div",{className:"p-5",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(d.A,{className:"h-6 w-6 text-yellow-400"})}),(0,r.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,r.jsxs)("dl",{children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Pending"}),(0,r.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:t.pendingResults})]})})]})})}),(0,r.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,r.jsx)("div",{className:"p-5",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(n.A,{className:"h-6 w-6 text-green-400"})}),(0,r.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,r.jsxs)("dl",{children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Completed"}),(0,r.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:t.completedResults})]})})]})})}),(0,r.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,r.jsx)("div",{className:"p-5",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(c.A,{className:"h-6 w-6 text-blue-400"})}),(0,r.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,r.jsxs)("dl",{children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Verified"}),(0,r.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:t.verifiedResults})]})})]})})}),(0,r.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,r.jsx)("div",{className:"p-5",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(h.A,{className:"h-6 w-6 text-purple-400"})}),(0,r.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,r.jsxs)("dl",{children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Certificates"}),(0,r.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:t.certificatesGenerated})]})})]})})}),(0,r.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,r.jsx)("div",{className:"p-5",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(u.A,{className:"h-6 w-6 text-indigo-400"})}),(0,r.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,r.jsxs)("dl",{children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Avg Score"}),(0,r.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:t.averageScore?.toFixed(1)||"N/A"})]})})]})})})]}),(0,r.jsx)("div",{className:"bg-white shadow rounded-lg p-6",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"search",className:"block text-sm font-medium text-gray-700 mb-2",children:"Search"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(p.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,r.jsx)("input",{type:"text",id:"search",value:w,onChange:e=>k(e.target.value),placeholder:"Search by candidate name, passport, or email...",className:"pl-10 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"status",className:"block text-sm font-medium text-gray-700 mb-2",children:"Status Filter"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(g.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,r.jsxs)("select",{id:"status",value:A,onChange:e=>C(e.target.value),className:"pl-10 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,r.jsx)("option",{value:"all",children:"All Statuses"}),(0,r.jsx)("option",{value:"pending",children:"Pending"}),(0,r.jsx)("option",{value:"completed",children:"Completed"}),(0,r.jsx)("option",{value:"verified",children:"Verified"})]})]})]}),(0,r.jsx)("div",{className:"flex items-end",children:(0,r.jsx)("button",{onClick:()=>{k(""),C("all"),P(1)},className:"w-full px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50",children:"Clear Filters"})})]})}),b&&(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-md p-4",children:(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)(o.A,{className:"h-5 w-5 text-red-400"}),(0,r.jsxs)("div",{className:"ml-3",children:[(0,r.jsx)("h3",{className:"text-sm font-medium text-red-800",children:"Error"}),(0,r.jsx)("div",{className:"mt-2 text-sm text-red-700",children:b})]})]})}),(0,r.jsxs)("div",{className:"bg-white shadow overflow-hidden sm:rounded-md",children:[(0,r.jsxs)("div",{className:"px-4 py-5 sm:px-6 border-b border-gray-200",children:[(0,r.jsxs)("h3",{className:"text-lg leading-6 font-medium text-gray-900",children:["Test Results (",e.length," of ",20*E,")"]}),(0,r.jsx)("p",{className:"mt-1 max-w-2xl text-sm text-gray-500",children:"Complete list of all IELTS test results in the system"})]}),0===e.length?(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)(x.A,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,r.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"No results found"}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:w||"all"!==A?"Try adjusting your search criteria or filters.":"No test results have been entered yet."})]}):(0,r.jsx)("ul",{className:"divide-y divide-gray-200",children:e.map(e=>(0,r.jsx)("li",{children:(0,r.jsx)("div",{className:"px-4 py-4 sm:px-6 hover:bg-gray-50",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:R(e.status)}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900 truncate",children:e.candidate.fullName}),(0,r.jsx)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${M(e.status)}`,children:e.status.charAt(0).toUpperCase()+e.status.slice(1)}),e.certificateGenerated&&(0,r.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium text-green-800 bg-green-100",children:"Certificate Generated"})]}),(0,r.jsxs)("div",{className:"mt-1 flex items-center space-x-4 text-sm text-gray-500",children:[(0,r.jsxs)("span",{children:["Passport: ",e.candidate.passportNumber]}),(0,r.jsx)("span",{children:"•"}),(0,r.jsxs)("span",{children:["Test Date: ",e.candidate.testDate?new Date(e.candidate.testDate).toLocaleDateString():"N/A"]}),(0,r.jsx)("span",{children:"•"}),(0,r.jsxs)("span",{children:["Entered by: ",e.checker.name]})]}),(0,r.jsxs)("div",{className:"mt-1 flex items-center space-x-4 text-sm text-gray-500",children:[(0,r.jsxs)("span",{children:["Nationality: ",e.candidate.nationality]}),(0,r.jsx)("span",{children:"•"}),(0,r.jsxs)("span",{children:["Test Center: ",e.candidate.testCenter]}),(0,r.jsx)("span",{children:"•"}),(0,r.jsxs)("span",{children:["Created: ",e.createdAt?new Date(e.createdAt).toLocaleDateString():"N/A"]})]})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsxs)("div",{className:"text-sm font-medium text-gray-900",children:["Overall: ",e.overallBandScore||"N/A"]}),(0,r.jsxs)("div",{className:"text-xs text-gray-500",children:["L:",e.listeningBandScore||"-"," R:",e.readingBandScore||"-"," W:",e.writingBandScore||"-"," S:",e.speakingBandScore||"-"]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(i(),{href:`/admin/results/${e.id}`,className:"inline-flex items-center p-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50",children:(0,r.jsx)(f.A,{className:"h-4 w-4"})}),(0,r.jsx)(i(),{href:`/admin/results/${e.id}/edit`,className:"inline-flex items-center p-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50",children:(0,r.jsx)(j.A,{className:"h-4 w-4"})})]})]})]})})},e.id))})]}),E>1&&(0,r.jsxs)("div",{className:"bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6 rounded-lg shadow",children:[(0,r.jsxs)("div",{className:"flex-1 flex justify-between sm:hidden",children:[(0,r.jsx)("button",{onClick:()=>P(Math.max(1,S-1)),disabled:1===S,className:"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:"Previous"}),(0,r.jsx)("button",{onClick:()=>P(Math.min(E,S+1)),disabled:S===E,className:"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:"Next"})]}),(0,r.jsxs)("div",{className:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between",children:[(0,r.jsx)("div",{children:(0,r.jsxs)("p",{className:"text-sm text-gray-700",children:["Showing page ",(0,r.jsx)("span",{className:"font-medium",children:S})," of"," ",(0,r.jsx)("span",{className:"font-medium",children:E})]})}),(0,r.jsx)("div",{children:(0,r.jsxs)("nav",{className:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px","aria-label":"Pagination",children:[(0,r.jsx)("button",{onClick:()=>P(Math.max(1,S-1)),disabled:1===S,className:"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:"Previous"}),Array.from({length:Math.min(5,E)},(e,s)=>{let t=s+1;return(0,r.jsx)("button",{onClick:()=>P(t),className:`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${S===t?"z-10 bg-blue-50 border-blue-500 text-blue-600":"bg-white border-gray-300 text-gray-500 hover:bg-gray-50"}`,children:t},t)}),(0,r.jsx)("button",{onClick:()=>P(Math.min(E,S+1)),disabled:S===E,className:"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:"Next"})]})})]})]})]})}},39500:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\results\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\admin\\results\\page.tsx","default")}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[638,8338,2367,9807],()=>t(14738));module.exports=r})();