(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[550],{521:e=>{"use strict";e.exports=require("node:async_hooks")},356:e=>{"use strict";e.exports=require("node:buffer")},825:()=>{},884:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.toBig=t.shrSL=t.shrSH=t.rotrSL=t.rotrSH=t.rotrBL=t.rotrBH=t.rotr32L=t.rotr32H=t.rotlSL=t.rotlSH=t.rotlBL=t.rotlBH=t.add5L=t.add5H=t.add4L=t.add4H=t.add3L=t.add3H=void 0,t.add=w,t.fromBig=i,t.split=a;let r=BigInt(0x100000000-1),n=BigInt(32);function i(e,t=!1){return t?{h:Number(e&r),l:Number(e>>n&r)}:{h:0|Number(e>>n&r),l:0|Number(e&r)}}function a(e,t=!1){let r=e.length,n=new Uint32Array(r),s=new Uint32Array(r);for(let a=0;a<r;a++){let{h:r,l:o}=i(e[a],t);[n[a],s[a]]=[r,o]}return[n,s]}let s=(e,t)=>BigInt(e>>>0)<<n|BigInt(t>>>0);t.toBig=s;let o=(e,t,r)=>e>>>r;t.shrSH=o;let c=(e,t,r)=>e<<32-r|t>>>r;t.shrSL=c;let l=(e,t,r)=>e>>>r|t<<32-r;t.rotrSH=l;let u=(e,t,r)=>e<<32-r|t>>>r;t.rotrSL=u;let d=(e,t,r)=>e<<64-r|t>>>r-32;t.rotrBH=d;let f=(e,t,r)=>e>>>r-32|t<<64-r;t.rotrBL=f;let h=(e,t)=>t;t.rotr32H=h;let p=(e,t)=>e;t.rotr32L=p;let g=(e,t,r)=>e<<r|t>>>32-r;t.rotlSH=g;let m=(e,t,r)=>t<<r|e>>>32-r;t.rotlSL=m;let y=(e,t,r)=>t<<r-32|e>>>64-r;t.rotlBH=y;let b=(e,t,r)=>e<<r-32|t>>>64-r;function w(e,t,r,n){let i=(t>>>0)+(n>>>0);return{h:e+r+(i/0x100000000|0)|0,l:0|i}}t.rotlBL=b;let v=(e,t,r)=>(e>>>0)+(t>>>0)+(r>>>0);t.add3L=v;let x=(e,t,r,n)=>t+r+n+(e/0x100000000|0)|0;t.add3H=x;let _=(e,t,r,n)=>(e>>>0)+(t>>>0)+(r>>>0)+(n>>>0);t.add4L=_;let S=(e,t,r,n,i)=>t+r+n+i+(e/0x100000000|0)|0;t.add4H=S;let k=(e,t,r,n,i)=>(e>>>0)+(t>>>0)+(r>>>0)+(n>>>0)+(i>>>0);t.add5L=k;let E=(e,t,r,n,i,a)=>t+r+n+i+a+(e/0x100000000|0)|0;t.add5H=E,t.default={fromBig:i,split:a,toBig:s,shrSH:o,shrSL:c,rotrSH:l,rotrSL:u,rotrBH:d,rotrBL:f,rotr32H:h,rotr32L:p,rotlSH:g,rotlSL:m,rotlBH:y,rotlBL:b,add:w,add3L:v,add3H:x,add4L:_,add4H:S,add5H:E,add5L:k}},278:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.crypto=void 0,t.crypto="object"==typeof globalThis&&"crypto"in globalThis?globalThis.crypto:void 0},60:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.shake256=t.shake128=t.keccak_512=t.keccak_384=t.keccak_256=t.keccak_224=t.sha3_512=t.sha3_384=t.sha3_256=t.sha3_224=t.Keccak=void 0,t.keccakP=w;let n=r(884),i=r(506),a=BigInt(0),s=BigInt(1),o=BigInt(2),c=BigInt(7),l=BigInt(256),u=BigInt(113),d=[],f=[],h=[];for(let e=0,t=s,r=1,n=0;e<24;e++){[r,n]=[n,(2*r+3*n)%5],d.push(2*(5*n+r)),f.push((e+1)*(e+2)/2%64);let i=a;for(let e=0;e<7;e++)(t=(t<<s^(t>>c)*u)%l)&o&&(i^=s<<(s<<BigInt(e))-s);h.push(i)}let p=(0,n.split)(h,!0),g=p[0],m=p[1],y=(e,t,r)=>r>32?(0,n.rotlBH)(e,t,r):(0,n.rotlSH)(e,t,r),b=(e,t,r)=>r>32?(0,n.rotlBL)(e,t,r):(0,n.rotlSL)(e,t,r);function w(e,t=24){let r=new Uint32Array(10);for(let n=24-t;n<24;n++){for(let t=0;t<10;t++)r[t]=e[t]^e[t+10]^e[t+20]^e[t+30]^e[t+40];for(let t=0;t<10;t+=2){let n=(t+8)%10,i=(t+2)%10,a=r[i],s=r[i+1],o=y(a,s,1)^r[n],c=b(a,s,1)^r[n+1];for(let r=0;r<50;r+=10)e[t+r]^=o,e[t+r+1]^=c}let t=e[2],i=e[3];for(let r=0;r<24;r++){let n=f[r],a=y(t,i,n),s=b(t,i,n),o=d[r];t=e[o],i=e[o+1],e[o]=a,e[o+1]=s}for(let t=0;t<50;t+=10){for(let n=0;n<10;n++)r[n]=e[t+n];for(let n=0;n<10;n++)e[t+n]^=~r[(n+2)%10]&r[(n+4)%10]}e[0]^=g[n],e[1]^=m[n]}(0,i.clean)(r)}class v extends i.Hash{constructor(e,t,r,n=!1,a=24){if(super(),this.pos=0,this.posOut=0,this.finished=!1,this.destroyed=!1,this.enableXOF=!1,this.blockLen=e,this.suffix=t,this.outputLen=r,this.enableXOF=n,this.rounds=a,(0,i.anumber)(r),!(0<e&&e<200))throw Error("only keccak-f1600 function is supported");this.state=new Uint8Array(200),this.state32=(0,i.u32)(this.state)}clone(){return this._cloneInto()}keccak(){(0,i.swap32IfBE)(this.state32),w(this.state32,this.rounds),(0,i.swap32IfBE)(this.state32),this.posOut=0,this.pos=0}update(e){(0,i.aexists)(this),e=(0,i.toBytes)(e),(0,i.abytes)(e);let{blockLen:t,state:r}=this,n=e.length;for(let i=0;i<n;){let a=Math.min(t-this.pos,n-i);for(let t=0;t<a;t++)r[this.pos++]^=e[i++];this.pos===t&&this.keccak()}return this}finish(){if(this.finished)return;this.finished=!0;let{state:e,suffix:t,pos:r,blockLen:n}=this;e[r]^=t,(128&t)!=0&&r===n-1&&this.keccak(),e[n-1]^=128,this.keccak()}writeInto(e){(0,i.aexists)(this,!1),(0,i.abytes)(e),this.finish();let t=this.state,{blockLen:r}=this;for(let n=0,i=e.length;n<i;){this.posOut>=r&&this.keccak();let a=Math.min(r-this.posOut,i-n);e.set(t.subarray(this.posOut,this.posOut+a),n),this.posOut+=a,n+=a}return e}xofInto(e){if(!this.enableXOF)throw Error("XOF is not possible for this instance");return this.writeInto(e)}xof(e){return(0,i.anumber)(e),this.xofInto(new Uint8Array(e))}digestInto(e){if((0,i.aoutput)(e,this),this.finished)throw Error("digest() was already called");return this.writeInto(e),this.destroy(),e}digest(){return this.digestInto(new Uint8Array(this.outputLen))}destroy(){this.destroyed=!0,(0,i.clean)(this.state)}_cloneInto(e){let{blockLen:t,suffix:r,outputLen:n,rounds:i,enableXOF:a}=this;return e||(e=new v(t,r,n,a,i)),e.state32.set(this.state32),e.pos=this.pos,e.posOut=this.posOut,e.finished=this.finished,e.rounds=i,e.suffix=r,e.outputLen=n,e.enableXOF=a,e.destroyed=this.destroyed,e}}t.Keccak=v;let x=(e,t,r)=>(0,i.createHasher)(()=>new v(t,e,r));t.sha3_224=x(6,144,28),t.sha3_256=x(6,136,32),t.sha3_384=x(6,104,48),t.sha3_512=x(6,72,64),t.keccak_224=x(1,144,28),t.keccak_256=x(1,136,32),t.keccak_384=x(1,104,48),t.keccak_512=x(1,72,64);let _=(e,t,r)=>(0,i.createXOFer)((n={})=>new v(t,e,void 0===n.dkLen?r:n.dkLen,!0));t.shake128=_(31,168,16),t.shake256=_(31,136,32)},506:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.wrapXOFConstructorWithOpts=t.wrapConstructorWithOpts=t.wrapConstructor=t.Hash=t.nextTick=t.swap32IfBE=t.byteSwapIfBE=t.swap8IfBE=t.isLE=void 0,t.isBytes=i,t.anumber=a,t.abytes=s,t.ahash=function(e){if("function"!=typeof e||"function"!=typeof e.create)throw Error("Hash should be wrapped by utils.createHasher");a(e.outputLen),a(e.blockLen)},t.aexists=function(e,t=!0){if(e.destroyed)throw Error("Hash instance has been destroyed");if(t&&e.finished)throw Error("Hash#digest() has already been called")},t.aoutput=function(e,t){s(e);let r=t.outputLen;if(e.length<r)throw Error("digestInto() expects output buffer of length at least "+r)},t.u8=function(e){return new Uint8Array(e.buffer,e.byteOffset,e.byteLength)},t.u32=function(e){return new Uint32Array(e.buffer,e.byteOffset,Math.floor(e.byteLength/4))},t.clean=function(...e){for(let t=0;t<e.length;t++)e[t].fill(0)},t.createView=function(e){return new DataView(e.buffer,e.byteOffset,e.byteLength)},t.rotr=function(e,t){return e<<32-t|e>>>t},t.rotl=function(e,t){return e<<t|e>>>32-t>>>0},t.byteSwap=o,t.byteSwap32=c,t.bytesToHex=function(e){if(s(e),l)return e.toHex();let t="";for(let r=0;r<e.length;r++)t+=u[e[r]];return t},t.hexToBytes=function(e){if("string"!=typeof e)throw Error("hex string expected, got "+typeof e);if(l)return Uint8Array.fromHex(e);let t=e.length,r=t/2;if(t%2)throw Error("hex string expected, got unpadded hex of length "+t);let n=new Uint8Array(r);for(let t=0,i=0;t<r;t++,i+=2){let r=f(e.charCodeAt(i)),a=f(e.charCodeAt(i+1));if(void 0===r||void 0===a)throw Error('hex string expected, got non-hex character "'+(e[i]+e[i+1])+'" at index '+i);n[t]=16*r+a}return n},t.asyncLoop=p,t.utf8ToBytes=g,t.bytesToUtf8=function(e){return new TextDecoder().decode(e)},t.toBytes=m,t.kdfInputToBytes=function(e){return"string"==typeof e&&(e=g(e)),s(e),e},t.concatBytes=function(...e){let t=0;for(let r=0;r<e.length;r++){let n=e[r];s(n),t+=n.length}let r=new Uint8Array(t);for(let t=0,n=0;t<e.length;t++){let i=e[t];r.set(i,n),n+=i.length}return r},t.checkOpts=function(e,t){if(void 0!==t&&"[object Object]"!==({}).toString.call(t))throw Error("options should be object or undefined");return Object.assign(e,t)},t.createHasher=b,t.createOptHasher=w,t.createXOFer=v,t.randomBytes=function(e=32){if(n.crypto&&"function"==typeof n.crypto.getRandomValues)return n.crypto.getRandomValues(new Uint8Array(e));if(n.crypto&&"function"==typeof n.crypto.randomBytes)return Uint8Array.from(n.crypto.randomBytes(e));throw Error("crypto.getRandomValues must be defined")};let n=r(278);function i(e){return e instanceof Uint8Array||ArrayBuffer.isView(e)&&"Uint8Array"===e.constructor.name}function a(e){if(!Number.isSafeInteger(e)||e<0)throw Error("positive integer expected, got "+e)}function s(e,...t){if(!i(e))throw Error("Uint8Array expected");if(t.length>0&&!t.includes(e.length))throw Error("Uint8Array expected of length "+t+", got length="+e.length)}function o(e){return e<<24&0xff000000|e<<8&0xff0000|e>>>8&65280|e>>>24&255}function c(e){for(let t=0;t<e.length;t++)e[t]=o(e[t]);return e}t.isLE=68===new Uint8Array(new Uint32Array([0x11223344]).buffer)[0],t.swap8IfBE=t.isLE?e=>e:e=>o(e),t.byteSwapIfBE=t.swap8IfBE,t.swap32IfBE=t.isLE?e=>e:c;let l="function"==typeof Uint8Array.from([]).toHex&&"function"==typeof Uint8Array.fromHex,u=Array.from({length:256},(e,t)=>t.toString(16).padStart(2,"0")),d={_0:48,_9:57,A:65,F:70,a:97,f:102};function f(e){return e>=d._0&&e<=d._9?e-d._0:e>=d.A&&e<=d.F?e-(d.A-10):e>=d.a&&e<=d.f?e-(d.a-10):void 0}let h=async()=>{};async function p(e,r,n){let i=Date.now();for(let a=0;a<e;a++){n(a);let e=Date.now()-i;e>=0&&e<r||(await (0,t.nextTick)(),i+=e)}}function g(e){if("string"!=typeof e)throw Error("string expected");return new Uint8Array(new TextEncoder().encode(e))}function m(e){return"string"==typeof e&&(e=g(e)),s(e),e}t.nextTick=h;class y{}function b(e){let t=t=>e().update(m(t)).digest(),r=e();return t.outputLen=r.outputLen,t.blockLen=r.blockLen,t.create=()=>e(),t}function w(e){let t=(t,r)=>e(r).update(m(t)).digest(),r=e({});return t.outputLen=r.outputLen,t.blockLen=r.blockLen,t.create=t=>e(t),t}function v(e){let t=(t,r)=>e(r).update(m(t)).digest(),r=e({});return t.outputLen=r.outputLen,t.blockLen=r.blockLen,t.create=t=>e(t),t}t.Hash=y,t.wrapConstructor=b,t.wrapConstructorWithOpts=w,t.wrapXOFConstructorWithOpts=v},791:(e,t,r)=>{let{createId:n,init:i,getConstants:a,isCuid:s}=r(492);e.exports.sX=n},492:(e,t,r)=>{let{sha3_512:n}=r(60),i=24,a=32,s=(e=4,t=Math.random)=>{let r="";for(;r.length<e;)r+=Math.floor(36*t()).toString(36);return r};function o(e){let t=0n;for(let r of e.values())t=(t<<8n)+BigInt(r);return t}let c=(e="")=>o(n(e)).toString(36).slice(1),l=Array.from({length:26},(e,t)=>String.fromCharCode(t+97)),u=e=>l[Math.floor(e()*l.length)],d=({globalObj:e=void 0!==r.g?r.g:"undefined"!=typeof window?window:{},random:t=Math.random}={})=>{let n=Object.keys(e).toString();return c(n.length?n+s(a,t):s(a,t)).substring(0,a)},f=e=>()=>e++,h=0x1c6b1f1f,p=({random:e=Math.random,counter:t=f(Math.floor(e()*h)),length:r=i,fingerprint:n=d({random:e})}={})=>function(){let i=u(e),a=Date.now().toString(36),o=t().toString(36),l=s(r,e),d=`${a+l+o+n}`;return`${i+c(d).substring(1,r)}`},g=p();e.exports.getConstants=()=>({defaultLength:i,bigLength:a}),e.exports.init=p,e.exports.createId=g,e.exports.bufToBigInt=o,e.exports.createCounter=f,e.exports.createFingerprint=d,e.exports.isCuid=(e,{minLength:t=2,maxLength:r=a}={})=>{let n=e.length;return!!("string"==typeof e&&n>=t&&n<=r&&/^[0-9a-z]+$/.test(e))}},555:e=>{"use strict";var t=Object.defineProperty,r=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,a={};function s(e){var t;let r=["path"in e&&e.path&&`Path=${e.path}`,"expires"in e&&(e.expires||0===e.expires)&&`Expires=${("number"==typeof e.expires?new Date(e.expires):e.expires).toUTCString()}`,"maxAge"in e&&"number"==typeof e.maxAge&&`Max-Age=${e.maxAge}`,"domain"in e&&e.domain&&`Domain=${e.domain}`,"secure"in e&&e.secure&&"Secure","httpOnly"in e&&e.httpOnly&&"HttpOnly","sameSite"in e&&e.sameSite&&`SameSite=${e.sameSite}`,"partitioned"in e&&e.partitioned&&"Partitioned","priority"in e&&e.priority&&`Priority=${e.priority}`].filter(Boolean),n=`${e.name}=${encodeURIComponent(null!=(t=e.value)?t:"")}`;return 0===r.length?n:`${n}; ${r.join("; ")}`}function o(e){let t=new Map;for(let r of e.split(/; */)){if(!r)continue;let e=r.indexOf("=");if(-1===e){t.set(r,"true");continue}let[n,i]=[r.slice(0,e),r.slice(e+1)];try{t.set(n,decodeURIComponent(null!=i?i:"true"))}catch{}}return t}function c(e){var t,r;if(!e)return;let[[n,i],...a]=o(e),{domain:s,expires:c,httponly:d,maxage:f,path:h,samesite:p,secure:g,partitioned:m,priority:y}=Object.fromEntries(a.map(([e,t])=>[e.toLowerCase().replace(/-/g,""),t]));return function(e){let t={};for(let r in e)e[r]&&(t[r]=e[r]);return t}({name:n,value:decodeURIComponent(i),domain:s,...c&&{expires:new Date(c)},...d&&{httpOnly:!0},..."string"==typeof f&&{maxAge:Number(f)},path:h,...p&&{sameSite:l.includes(t=(t=p).toLowerCase())?t:void 0},...g&&{secure:!0},...y&&{priority:u.includes(r=(r=y).toLowerCase())?r:void 0},...m&&{partitioned:!0}})}((e,r)=>{for(var n in r)t(e,n,{get:r[n],enumerable:!0})})(a,{RequestCookies:()=>d,ResponseCookies:()=>f,parseCookie:()=>o,parseSetCookie:()=>c,stringifyCookie:()=>s}),e.exports=((e,a,s,o)=>{if(a&&"object"==typeof a||"function"==typeof a)for(let c of n(a))i.call(e,c)||c===s||t(e,c,{get:()=>a[c],enumerable:!(o=r(a,c))||o.enumerable});return e})(t({},"__esModule",{value:!0}),a);var l=["strict","lax","none"],u=["low","medium","high"],d=class{constructor(e){this._parsed=new Map,this._headers=e;let t=e.get("cookie");if(t)for(let[e,r]of o(t))this._parsed.set(e,{name:e,value:r})}[Symbol.iterator](){return this._parsed[Symbol.iterator]()}get size(){return this._parsed.size}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed);if(!e.length)return r.map(([e,t])=>t);let n="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(([e])=>e===n).map(([e,t])=>t)}has(e){return this._parsed.has(e)}set(...e){let[t,r]=1===e.length?[e[0].name,e[0].value]:e,n=this._parsed;return n.set(t,{name:t,value:r}),this._headers.set("cookie",Array.from(n).map(([e,t])=>s(t)).join("; ")),this}delete(e){let t=this._parsed,r=Array.isArray(e)?e.map(e=>t.delete(e)):t.delete(e);return this._headers.set("cookie",Array.from(t).map(([e,t])=>s(t)).join("; ")),r}clear(){return this.delete(Array.from(this._parsed.keys())),this}[Symbol.for("edge-runtime.inspect.custom")](){return`RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(e=>`${e.name}=${encodeURIComponent(e.value)}`).join("; ")}},f=class{constructor(e){var t,r,n;this._parsed=new Map,this._headers=e;let i=null!=(n=null!=(r=null==(t=e.getSetCookie)?void 0:t.call(e))?r:e.get("set-cookie"))?n:[];for(let e of Array.isArray(i)?i:function(e){if(!e)return[];var t,r,n,i,a,s=[],o=0;function c(){for(;o<e.length&&/\s/.test(e.charAt(o));)o+=1;return o<e.length}for(;o<e.length;){for(t=o,a=!1;c();)if(","===(r=e.charAt(o))){for(n=o,o+=1,c(),i=o;o<e.length&&"="!==(r=e.charAt(o))&&";"!==r&&","!==r;)o+=1;o<e.length&&"="===e.charAt(o)?(a=!0,o=i,s.push(e.substring(t,n)),t=o):o=n+1}else o+=1;(!a||o>=e.length)&&s.push(e.substring(t,e.length))}return s}(i)){let t=c(e);t&&this._parsed.set(t.name,t)}}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed.values());if(!e.length)return r;let n="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(e=>e.name===n)}has(e){return this._parsed.has(e)}set(...e){let[t,r,n]=1===e.length?[e[0].name,e[0].value,e[0]]:e,i=this._parsed;return i.set(t,function(e={name:"",value:""}){return"number"==typeof e.expires&&(e.expires=new Date(e.expires)),e.maxAge&&(e.expires=new Date(Date.now()+1e3*e.maxAge)),(null===e.path||void 0===e.path)&&(e.path="/"),e}({name:t,value:r,...n})),function(e,t){for(let[,r]of(t.delete("set-cookie"),e)){let e=s(r);t.append("set-cookie",e)}}(i,this._headers),this}delete(...e){let[t,r]="string"==typeof e[0]?[e[0]]:[e[0].name,e[0]];return this.set({...r,name:t,value:"",expires:new Date(0)})}[Symbol.for("edge-runtime.inspect.custom")](){return`ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(s).join("; ")}}},777:(e,t,r)=>{(()=>{"use strict";var t={491:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ContextAPI=void 0;let n=r(223),i=r(172),a=r(930),s="context",o=new n.NoopContextManager;class c{constructor(){}static getInstance(){return this._instance||(this._instance=new c),this._instance}setGlobalContextManager(e){return(0,i.registerGlobal)(s,e,a.DiagAPI.instance())}active(){return this._getContextManager().active()}with(e,t,r,...n){return this._getContextManager().with(e,t,r,...n)}bind(e,t){return this._getContextManager().bind(e,t)}_getContextManager(){return(0,i.getGlobal)(s)||o}disable(){this._getContextManager().disable(),(0,i.unregisterGlobal)(s,a.DiagAPI.instance())}}t.ContextAPI=c},930:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagAPI=void 0;let n=r(56),i=r(912),a=r(957),s=r(172);class o{constructor(){function e(e){return function(...t){let r=(0,s.getGlobal)("diag");if(r)return r[e](...t)}}let t=this;t.setLogger=(e,r={logLevel:a.DiagLogLevel.INFO})=>{var n,o,c;if(e===t){let e=Error("Cannot use diag as the logger for itself. Please use a DiagLogger implementation like ConsoleDiagLogger or a custom implementation");return t.error(null!==(n=e.stack)&&void 0!==n?n:e.message),!1}"number"==typeof r&&(r={logLevel:r});let l=(0,s.getGlobal)("diag"),u=(0,i.createLogLevelDiagLogger)(null!==(o=r.logLevel)&&void 0!==o?o:a.DiagLogLevel.INFO,e);if(l&&!r.suppressOverrideMessage){let e=null!==(c=Error().stack)&&void 0!==c?c:"<failed to generate stacktrace>";l.warn(`Current logger will be overwritten from ${e}`),u.warn(`Current logger will overwrite one already registered from ${e}`)}return(0,s.registerGlobal)("diag",u,t,!0)},t.disable=()=>{(0,s.unregisterGlobal)("diag",t)},t.createComponentLogger=e=>new n.DiagComponentLogger(e),t.verbose=e("verbose"),t.debug=e("debug"),t.info=e("info"),t.warn=e("warn"),t.error=e("error")}static instance(){return this._instance||(this._instance=new o),this._instance}}t.DiagAPI=o},653:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.MetricsAPI=void 0;let n=r(660),i=r(172),a=r(930),s="metrics";class o{constructor(){}static getInstance(){return this._instance||(this._instance=new o),this._instance}setGlobalMeterProvider(e){return(0,i.registerGlobal)(s,e,a.DiagAPI.instance())}getMeterProvider(){return(0,i.getGlobal)(s)||n.NOOP_METER_PROVIDER}getMeter(e,t,r){return this.getMeterProvider().getMeter(e,t,r)}disable(){(0,i.unregisterGlobal)(s,a.DiagAPI.instance())}}t.MetricsAPI=o},181:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PropagationAPI=void 0;let n=r(172),i=r(874),a=r(194),s=r(277),o=r(369),c=r(930),l="propagation",u=new i.NoopTextMapPropagator;class d{constructor(){this.createBaggage=o.createBaggage,this.getBaggage=s.getBaggage,this.getActiveBaggage=s.getActiveBaggage,this.setBaggage=s.setBaggage,this.deleteBaggage=s.deleteBaggage}static getInstance(){return this._instance||(this._instance=new d),this._instance}setGlobalPropagator(e){return(0,n.registerGlobal)(l,e,c.DiagAPI.instance())}inject(e,t,r=a.defaultTextMapSetter){return this._getGlobalPropagator().inject(e,t,r)}extract(e,t,r=a.defaultTextMapGetter){return this._getGlobalPropagator().extract(e,t,r)}fields(){return this._getGlobalPropagator().fields()}disable(){(0,n.unregisterGlobal)(l,c.DiagAPI.instance())}_getGlobalPropagator(){return(0,n.getGlobal)(l)||u}}t.PropagationAPI=d},997:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TraceAPI=void 0;let n=r(172),i=r(846),a=r(139),s=r(607),o=r(930),c="trace";class l{constructor(){this._proxyTracerProvider=new i.ProxyTracerProvider,this.wrapSpanContext=a.wrapSpanContext,this.isSpanContextValid=a.isSpanContextValid,this.deleteSpan=s.deleteSpan,this.getSpan=s.getSpan,this.getActiveSpan=s.getActiveSpan,this.getSpanContext=s.getSpanContext,this.setSpan=s.setSpan,this.setSpanContext=s.setSpanContext}static getInstance(){return this._instance||(this._instance=new l),this._instance}setGlobalTracerProvider(e){let t=(0,n.registerGlobal)(c,this._proxyTracerProvider,o.DiagAPI.instance());return t&&this._proxyTracerProvider.setDelegate(e),t}getTracerProvider(){return(0,n.getGlobal)(c)||this._proxyTracerProvider}getTracer(e,t){return this.getTracerProvider().getTracer(e,t)}disable(){(0,n.unregisterGlobal)(c,o.DiagAPI.instance()),this._proxyTracerProvider=new i.ProxyTracerProvider}}t.TraceAPI=l},277:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.deleteBaggage=t.setBaggage=t.getActiveBaggage=t.getBaggage=void 0;let n=r(491),i=(0,r(780).createContextKey)("OpenTelemetry Baggage Key");function a(e){return e.getValue(i)||void 0}t.getBaggage=a,t.getActiveBaggage=function(){return a(n.ContextAPI.getInstance().active())},t.setBaggage=function(e,t){return e.setValue(i,t)},t.deleteBaggage=function(e){return e.deleteValue(i)}},993:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.BaggageImpl=void 0;class r{constructor(e){this._entries=e?new Map(e):new Map}getEntry(e){let t=this._entries.get(e);if(t)return Object.assign({},t)}getAllEntries(){return Array.from(this._entries.entries()).map(([e,t])=>[e,t])}setEntry(e,t){let n=new r(this._entries);return n._entries.set(e,t),n}removeEntry(e){let t=new r(this._entries);return t._entries.delete(e),t}removeEntries(...e){let t=new r(this._entries);for(let r of e)t._entries.delete(r);return t}clear(){return new r}}t.BaggageImpl=r},830:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.baggageEntryMetadataSymbol=void 0,t.baggageEntryMetadataSymbol=Symbol("BaggageEntryMetadata")},369:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.baggageEntryMetadataFromString=t.createBaggage=void 0;let n=r(930),i=r(993),a=r(830),s=n.DiagAPI.instance();t.createBaggage=function(e={}){return new i.BaggageImpl(new Map(Object.entries(e)))},t.baggageEntryMetadataFromString=function(e){return"string"!=typeof e&&(s.error(`Cannot create baggage metadata from unknown type: ${typeof e}`),e=""),{__TYPE__:a.baggageEntryMetadataSymbol,toString:()=>e}}},67:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.context=void 0;let n=r(491);t.context=n.ContextAPI.getInstance()},223:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopContextManager=void 0;let n=r(780);class i{active(){return n.ROOT_CONTEXT}with(e,t,r,...n){return t.call(r,...n)}bind(e,t){return t}enable(){return this}disable(){return this}}t.NoopContextManager=i},780:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ROOT_CONTEXT=t.createContextKey=void 0,t.createContextKey=function(e){return Symbol.for(e)};class r{constructor(e){let t=this;t._currentContext=e?new Map(e):new Map,t.getValue=e=>t._currentContext.get(e),t.setValue=(e,n)=>{let i=new r(t._currentContext);return i._currentContext.set(e,n),i},t.deleteValue=e=>{let n=new r(t._currentContext);return n._currentContext.delete(e),n}}}t.ROOT_CONTEXT=new r},506:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.diag=void 0;let n=r(930);t.diag=n.DiagAPI.instance()},56:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagComponentLogger=void 0;let n=r(172);class i{constructor(e){this._namespace=e.namespace||"DiagComponentLogger"}debug(...e){return a("debug",this._namespace,e)}error(...e){return a("error",this._namespace,e)}info(...e){return a("info",this._namespace,e)}warn(...e){return a("warn",this._namespace,e)}verbose(...e){return a("verbose",this._namespace,e)}}function a(e,t,r){let i=(0,n.getGlobal)("diag");if(i)return r.unshift(t),i[e](...r)}t.DiagComponentLogger=i},972:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagConsoleLogger=void 0;let r=[{n:"error",c:"error"},{n:"warn",c:"warn"},{n:"info",c:"info"},{n:"debug",c:"debug"},{n:"verbose",c:"trace"}];class n{constructor(){for(let e=0;e<r.length;e++)this[r[e].n]=function(e){return function(...t){if(console){let r=console[e];if("function"!=typeof r&&(r=console.log),"function"==typeof r)return r.apply(console,t)}}}(r[e].c)}}t.DiagConsoleLogger=n},912:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createLogLevelDiagLogger=void 0;let n=r(957);t.createLogLevelDiagLogger=function(e,t){function r(r,n){let i=t[r];return"function"==typeof i&&e>=n?i.bind(t):function(){}}return e<n.DiagLogLevel.NONE?e=n.DiagLogLevel.NONE:e>n.DiagLogLevel.ALL&&(e=n.DiagLogLevel.ALL),t=t||{},{error:r("error",n.DiagLogLevel.ERROR),warn:r("warn",n.DiagLogLevel.WARN),info:r("info",n.DiagLogLevel.INFO),debug:r("debug",n.DiagLogLevel.DEBUG),verbose:r("verbose",n.DiagLogLevel.VERBOSE)}}},957:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagLogLevel=void 0,function(e){e[e.NONE=0]="NONE",e[e.ERROR=30]="ERROR",e[e.WARN=50]="WARN",e[e.INFO=60]="INFO",e[e.DEBUG=70]="DEBUG",e[e.VERBOSE=80]="VERBOSE",e[e.ALL=9999]="ALL"}(t.DiagLogLevel||(t.DiagLogLevel={}))},172:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.unregisterGlobal=t.getGlobal=t.registerGlobal=void 0;let n=r(200),i=r(521),a=r(130),s=i.VERSION.split(".")[0],o=Symbol.for(`opentelemetry.js.api.${s}`),c=n._globalThis;t.registerGlobal=function(e,t,r,n=!1){var a;let s=c[o]=null!==(a=c[o])&&void 0!==a?a:{version:i.VERSION};if(!n&&s[e]){let t=Error(`@opentelemetry/api: Attempted duplicate registration of API: ${e}`);return r.error(t.stack||t.message),!1}if(s.version!==i.VERSION){let t=Error(`@opentelemetry/api: Registration of version v${s.version} for ${e} does not match previously registered API v${i.VERSION}`);return r.error(t.stack||t.message),!1}return s[e]=t,r.debug(`@opentelemetry/api: Registered a global for ${e} v${i.VERSION}.`),!0},t.getGlobal=function(e){var t,r;let n=null===(t=c[o])||void 0===t?void 0:t.version;if(n&&(0,a.isCompatible)(n))return null===(r=c[o])||void 0===r?void 0:r[e]},t.unregisterGlobal=function(e,t){t.debug(`@opentelemetry/api: Unregistering a global for ${e} v${i.VERSION}.`);let r=c[o];r&&delete r[e]}},130:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.isCompatible=t._makeCompatibilityCheck=void 0;let n=r(521),i=/^(\d+)\.(\d+)\.(\d+)(-(.+))?$/;function a(e){let t=new Set([e]),r=new Set,n=e.match(i);if(!n)return()=>!1;let a={major:+n[1],minor:+n[2],patch:+n[3],prerelease:n[4]};if(null!=a.prerelease)return function(t){return t===e};function s(e){return r.add(e),!1}return function(e){if(t.has(e))return!0;if(r.has(e))return!1;let n=e.match(i);if(!n)return s(e);let o={major:+n[1],minor:+n[2],patch:+n[3],prerelease:n[4]};return null!=o.prerelease||a.major!==o.major?s(e):0===a.major?a.minor===o.minor&&a.patch<=o.patch?(t.add(e),!0):s(e):a.minor<=o.minor?(t.add(e),!0):s(e)}}t._makeCompatibilityCheck=a,t.isCompatible=a(n.VERSION)},886:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.metrics=void 0;let n=r(653);t.metrics=n.MetricsAPI.getInstance()},901:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ValueType=void 0,function(e){e[e.INT=0]="INT",e[e.DOUBLE=1]="DOUBLE"}(t.ValueType||(t.ValueType={}))},102:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createNoopMeter=t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC=t.NOOP_OBSERVABLE_GAUGE_METRIC=t.NOOP_OBSERVABLE_COUNTER_METRIC=t.NOOP_UP_DOWN_COUNTER_METRIC=t.NOOP_HISTOGRAM_METRIC=t.NOOP_COUNTER_METRIC=t.NOOP_METER=t.NoopObservableUpDownCounterMetric=t.NoopObservableGaugeMetric=t.NoopObservableCounterMetric=t.NoopObservableMetric=t.NoopHistogramMetric=t.NoopUpDownCounterMetric=t.NoopCounterMetric=t.NoopMetric=t.NoopMeter=void 0;class r{constructor(){}createHistogram(e,r){return t.NOOP_HISTOGRAM_METRIC}createCounter(e,r){return t.NOOP_COUNTER_METRIC}createUpDownCounter(e,r){return t.NOOP_UP_DOWN_COUNTER_METRIC}createObservableGauge(e,r){return t.NOOP_OBSERVABLE_GAUGE_METRIC}createObservableCounter(e,r){return t.NOOP_OBSERVABLE_COUNTER_METRIC}createObservableUpDownCounter(e,r){return t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC}addBatchObservableCallback(e,t){}removeBatchObservableCallback(e){}}t.NoopMeter=r;class n{}t.NoopMetric=n;class i extends n{add(e,t){}}t.NoopCounterMetric=i;class a extends n{add(e,t){}}t.NoopUpDownCounterMetric=a;class s extends n{record(e,t){}}t.NoopHistogramMetric=s;class o{addCallback(e){}removeCallback(e){}}t.NoopObservableMetric=o;class c extends o{}t.NoopObservableCounterMetric=c;class l extends o{}t.NoopObservableGaugeMetric=l;class u extends o{}t.NoopObservableUpDownCounterMetric=u,t.NOOP_METER=new r,t.NOOP_COUNTER_METRIC=new i,t.NOOP_HISTOGRAM_METRIC=new s,t.NOOP_UP_DOWN_COUNTER_METRIC=new a,t.NOOP_OBSERVABLE_COUNTER_METRIC=new c,t.NOOP_OBSERVABLE_GAUGE_METRIC=new l,t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC=new u,t.createNoopMeter=function(){return t.NOOP_METER}},660:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NOOP_METER_PROVIDER=t.NoopMeterProvider=void 0;let n=r(102);class i{getMeter(e,t,r){return n.NOOP_METER}}t.NoopMeterProvider=i,t.NOOP_METER_PROVIDER=new i},200:function(e,t,r){var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),i=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||n(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),i(r(46),t)},651:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t._globalThis=void 0,t._globalThis="object"==typeof globalThis?globalThis:r.g},46:function(e,t,r){var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),i=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||n(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),i(r(651),t)},939:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.propagation=void 0;let n=r(181);t.propagation=n.PropagationAPI.getInstance()},874:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopTextMapPropagator=void 0;class r{inject(e,t){}extract(e,t){return e}fields(){return[]}}t.NoopTextMapPropagator=r},194:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.defaultTextMapSetter=t.defaultTextMapGetter=void 0,t.defaultTextMapGetter={get(e,t){if(null!=e)return e[t]},keys:e=>null==e?[]:Object.keys(e)},t.defaultTextMapSetter={set(e,t,r){null!=e&&(e[t]=r)}}},845:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.trace=void 0;let n=r(997);t.trace=n.TraceAPI.getInstance()},403:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NonRecordingSpan=void 0;let n=r(476);class i{constructor(e=n.INVALID_SPAN_CONTEXT){this._spanContext=e}spanContext(){return this._spanContext}setAttribute(e,t){return this}setAttributes(e){return this}addEvent(e,t){return this}setStatus(e){return this}updateName(e){return this}end(e){}isRecording(){return!1}recordException(e,t){}}t.NonRecordingSpan=i},614:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopTracer=void 0;let n=r(491),i=r(607),a=r(403),s=r(139),o=n.ContextAPI.getInstance();class c{startSpan(e,t,r=o.active()){if(null==t?void 0:t.root)return new a.NonRecordingSpan;let n=r&&(0,i.getSpanContext)(r);return"object"==typeof n&&"string"==typeof n.spanId&&"string"==typeof n.traceId&&"number"==typeof n.traceFlags&&(0,s.isSpanContextValid)(n)?new a.NonRecordingSpan(n):new a.NonRecordingSpan}startActiveSpan(e,t,r,n){let a,s,c;if(arguments.length<2)return;2==arguments.length?c=t:3==arguments.length?(a=t,c=r):(a=t,s=r,c=n);let l=null!=s?s:o.active(),u=this.startSpan(e,a,l),d=(0,i.setSpan)(l,u);return o.with(d,c,void 0,u)}}t.NoopTracer=c},124:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopTracerProvider=void 0;let n=r(614);class i{getTracer(e,t,r){return new n.NoopTracer}}t.NoopTracerProvider=i},125:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ProxyTracer=void 0;let n=new(r(614)).NoopTracer;class i{constructor(e,t,r,n){this._provider=e,this.name=t,this.version=r,this.options=n}startSpan(e,t,r){return this._getTracer().startSpan(e,t,r)}startActiveSpan(e,t,r,n){let i=this._getTracer();return Reflect.apply(i.startActiveSpan,i,arguments)}_getTracer(){if(this._delegate)return this._delegate;let e=this._provider.getDelegateTracer(this.name,this.version,this.options);return e?(this._delegate=e,this._delegate):n}}t.ProxyTracer=i},846:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ProxyTracerProvider=void 0;let n=r(125),i=new(r(124)).NoopTracerProvider;class a{getTracer(e,t,r){var i;return null!==(i=this.getDelegateTracer(e,t,r))&&void 0!==i?i:new n.ProxyTracer(this,e,t,r)}getDelegate(){var e;return null!==(e=this._delegate)&&void 0!==e?e:i}setDelegate(e){this._delegate=e}getDelegateTracer(e,t,r){var n;return null===(n=this._delegate)||void 0===n?void 0:n.getTracer(e,t,r)}}t.ProxyTracerProvider=a},996:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SamplingDecision=void 0,function(e){e[e.NOT_RECORD=0]="NOT_RECORD",e[e.RECORD=1]="RECORD",e[e.RECORD_AND_SAMPLED=2]="RECORD_AND_SAMPLED"}(t.SamplingDecision||(t.SamplingDecision={}))},607:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.getSpanContext=t.setSpanContext=t.deleteSpan=t.setSpan=t.getActiveSpan=t.getSpan=void 0;let n=r(780),i=r(403),a=r(491),s=(0,n.createContextKey)("OpenTelemetry Context Key SPAN");function o(e){return e.getValue(s)||void 0}function c(e,t){return e.setValue(s,t)}t.getSpan=o,t.getActiveSpan=function(){return o(a.ContextAPI.getInstance().active())},t.setSpan=c,t.deleteSpan=function(e){return e.deleteValue(s)},t.setSpanContext=function(e,t){return c(e,new i.NonRecordingSpan(t))},t.getSpanContext=function(e){var t;return null===(t=o(e))||void 0===t?void 0:t.spanContext()}},325:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TraceStateImpl=void 0;let n=r(564);class i{constructor(e){this._internalState=new Map,e&&this._parse(e)}set(e,t){let r=this._clone();return r._internalState.has(e)&&r._internalState.delete(e),r._internalState.set(e,t),r}unset(e){let t=this._clone();return t._internalState.delete(e),t}get(e){return this._internalState.get(e)}serialize(){return this._keys().reduce((e,t)=>(e.push(t+"="+this.get(t)),e),[]).join(",")}_parse(e){!(e.length>512)&&(this._internalState=e.split(",").reverse().reduce((e,t)=>{let r=t.trim(),i=r.indexOf("=");if(-1!==i){let a=r.slice(0,i),s=r.slice(i+1,t.length);(0,n.validateKey)(a)&&(0,n.validateValue)(s)&&e.set(a,s)}return e},new Map),this._internalState.size>32&&(this._internalState=new Map(Array.from(this._internalState.entries()).reverse().slice(0,32))))}_keys(){return Array.from(this._internalState.keys()).reverse()}_clone(){let e=new i;return e._internalState=new Map(this._internalState),e}}t.TraceStateImpl=i},564:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.validateValue=t.validateKey=void 0;let r="[_0-9a-z-*/]",n=`[a-z]${r}{0,255}`,i=`[a-z0-9]${r}{0,240}@[a-z]${r}{0,13}`,a=RegExp(`^(?:${n}|${i})$`),s=/^[ -~]{0,255}[!-~]$/,o=/,|=/;t.validateKey=function(e){return a.test(e)},t.validateValue=function(e){return s.test(e)&&!o.test(e)}},98:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createTraceState=void 0;let n=r(325);t.createTraceState=function(e){return new n.TraceStateImpl(e)}},476:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.INVALID_SPAN_CONTEXT=t.INVALID_TRACEID=t.INVALID_SPANID=void 0;let n=r(475);t.INVALID_SPANID="0000000000000000",t.INVALID_TRACEID="00000000000000000000000000000000",t.INVALID_SPAN_CONTEXT={traceId:t.INVALID_TRACEID,spanId:t.INVALID_SPANID,traceFlags:n.TraceFlags.NONE}},357:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SpanKind=void 0,function(e){e[e.INTERNAL=0]="INTERNAL",e[e.SERVER=1]="SERVER",e[e.CLIENT=2]="CLIENT",e[e.PRODUCER=3]="PRODUCER",e[e.CONSUMER=4]="CONSUMER"}(t.SpanKind||(t.SpanKind={}))},139:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.wrapSpanContext=t.isSpanContextValid=t.isValidSpanId=t.isValidTraceId=void 0;let n=r(476),i=r(403),a=/^([0-9a-f]{32})$/i,s=/^[0-9a-f]{16}$/i;function o(e){return a.test(e)&&e!==n.INVALID_TRACEID}function c(e){return s.test(e)&&e!==n.INVALID_SPANID}t.isValidTraceId=o,t.isValidSpanId=c,t.isSpanContextValid=function(e){return o(e.traceId)&&c(e.spanId)},t.wrapSpanContext=function(e){return new i.NonRecordingSpan(e)}},847:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SpanStatusCode=void 0,function(e){e[e.UNSET=0]="UNSET",e[e.OK=1]="OK",e[e.ERROR=2]="ERROR"}(t.SpanStatusCode||(t.SpanStatusCode={}))},475:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TraceFlags=void 0,function(e){e[e.NONE=0]="NONE",e[e.SAMPLED=1]="SAMPLED"}(t.TraceFlags||(t.TraceFlags={}))},521:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.VERSION=void 0,t.VERSION="1.6.0"}},n={};function i(e){var r=n[e];if(void 0!==r)return r.exports;var a=n[e]={exports:{}},s=!0;try{t[e].call(a.exports,a,a.exports,i),s=!1}finally{s&&delete n[e]}return a.exports}i.ab="//";var a={};(()=>{Object.defineProperty(a,"__esModule",{value:!0}),a.trace=a.propagation=a.metrics=a.diag=a.context=a.INVALID_SPAN_CONTEXT=a.INVALID_TRACEID=a.INVALID_SPANID=a.isValidSpanId=a.isValidTraceId=a.isSpanContextValid=a.createTraceState=a.TraceFlags=a.SpanStatusCode=a.SpanKind=a.SamplingDecision=a.ProxyTracerProvider=a.ProxyTracer=a.defaultTextMapSetter=a.defaultTextMapGetter=a.ValueType=a.createNoopMeter=a.DiagLogLevel=a.DiagConsoleLogger=a.ROOT_CONTEXT=a.createContextKey=a.baggageEntryMetadataFromString=void 0;var e=i(369);Object.defineProperty(a,"baggageEntryMetadataFromString",{enumerable:!0,get:function(){return e.baggageEntryMetadataFromString}});var t=i(780);Object.defineProperty(a,"createContextKey",{enumerable:!0,get:function(){return t.createContextKey}}),Object.defineProperty(a,"ROOT_CONTEXT",{enumerable:!0,get:function(){return t.ROOT_CONTEXT}});var r=i(972);Object.defineProperty(a,"DiagConsoleLogger",{enumerable:!0,get:function(){return r.DiagConsoleLogger}});var n=i(957);Object.defineProperty(a,"DiagLogLevel",{enumerable:!0,get:function(){return n.DiagLogLevel}});var s=i(102);Object.defineProperty(a,"createNoopMeter",{enumerable:!0,get:function(){return s.createNoopMeter}});var o=i(901);Object.defineProperty(a,"ValueType",{enumerable:!0,get:function(){return o.ValueType}});var c=i(194);Object.defineProperty(a,"defaultTextMapGetter",{enumerable:!0,get:function(){return c.defaultTextMapGetter}}),Object.defineProperty(a,"defaultTextMapSetter",{enumerable:!0,get:function(){return c.defaultTextMapSetter}});var l=i(125);Object.defineProperty(a,"ProxyTracer",{enumerable:!0,get:function(){return l.ProxyTracer}});var u=i(846);Object.defineProperty(a,"ProxyTracerProvider",{enumerable:!0,get:function(){return u.ProxyTracerProvider}});var d=i(996);Object.defineProperty(a,"SamplingDecision",{enumerable:!0,get:function(){return d.SamplingDecision}});var f=i(357);Object.defineProperty(a,"SpanKind",{enumerable:!0,get:function(){return f.SpanKind}});var h=i(847);Object.defineProperty(a,"SpanStatusCode",{enumerable:!0,get:function(){return h.SpanStatusCode}});var p=i(475);Object.defineProperty(a,"TraceFlags",{enumerable:!0,get:function(){return p.TraceFlags}});var g=i(98);Object.defineProperty(a,"createTraceState",{enumerable:!0,get:function(){return g.createTraceState}});var m=i(139);Object.defineProperty(a,"isSpanContextValid",{enumerable:!0,get:function(){return m.isSpanContextValid}}),Object.defineProperty(a,"isValidTraceId",{enumerable:!0,get:function(){return m.isValidTraceId}}),Object.defineProperty(a,"isValidSpanId",{enumerable:!0,get:function(){return m.isValidSpanId}});var y=i(476);Object.defineProperty(a,"INVALID_SPANID",{enumerable:!0,get:function(){return y.INVALID_SPANID}}),Object.defineProperty(a,"INVALID_TRACEID",{enumerable:!0,get:function(){return y.INVALID_TRACEID}}),Object.defineProperty(a,"INVALID_SPAN_CONTEXT",{enumerable:!0,get:function(){return y.INVALID_SPAN_CONTEXT}});let b=i(67);Object.defineProperty(a,"context",{enumerable:!0,get:function(){return b.context}});let w=i(506);Object.defineProperty(a,"diag",{enumerable:!0,get:function(){return w.diag}});let v=i(886);Object.defineProperty(a,"metrics",{enumerable:!0,get:function(){return v.metrics}});let x=i(939);Object.defineProperty(a,"propagation",{enumerable:!0,get:function(){return x.propagation}});let _=i(845);Object.defineProperty(a,"trace",{enumerable:!0,get:function(){return _.trace}}),a.default={context:b.context,diag:w.diag,metrics:v.metrics,propagation:x.propagation,trace:_.trace}})(),e.exports=a})()},503:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab="//");var t={};(()=>{t.parse=function(t,r){if("string"!=typeof t)throw TypeError("argument str must be a string");for(var i={},a=t.split(n),s=(r||{}).decode||e,o=0;o<a.length;o++){var c=a[o],l=c.indexOf("=");if(!(l<0)){var u=c.substr(0,l).trim(),d=c.substr(++l,c.length).trim();'"'==d[0]&&(d=d.slice(1,-1)),void 0==i[u]&&(i[u]=function(e,t){try{return t(e)}catch(t){return e}}(d,s))}}return i},t.serialize=function(e,t,n){var a=n||{},s=a.encode||r;if("function"!=typeof s)throw TypeError("option encode is invalid");if(!i.test(e))throw TypeError("argument name is invalid");var o=s(t);if(o&&!i.test(o))throw TypeError("argument val is invalid");var c=e+"="+o;if(null!=a.maxAge){var l=a.maxAge-0;if(isNaN(l)||!isFinite(l))throw TypeError("option maxAge is invalid");c+="; Max-Age="+Math.floor(l)}if(a.domain){if(!i.test(a.domain))throw TypeError("option domain is invalid");c+="; Domain="+a.domain}if(a.path){if(!i.test(a.path))throw TypeError("option path is invalid");c+="; Path="+a.path}if(a.expires){if("function"!=typeof a.expires.toUTCString)throw TypeError("option expires is invalid");c+="; Expires="+a.expires.toUTCString()}if(a.httpOnly&&(c+="; HttpOnly"),a.secure&&(c+="; Secure"),a.sameSite)switch("string"==typeof a.sameSite?a.sameSite.toLowerCase():a.sameSite){case!0:case"strict":c+="; SameSite=Strict";break;case"lax":c+="; SameSite=Lax";break;case"none":c+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return c};var e=decodeURIComponent,r=encodeURIComponent,n=/; */,i=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),e.exports=t})()},541:e=>{(()=>{"use strict";var t={993:e=>{var t=Object.prototype.hasOwnProperty,r="~";function n(){}function i(e,t,r){this.fn=e,this.context=t,this.once=r||!1}function a(e,t,n,a,s){if("function"!=typeof n)throw TypeError("The listener must be a function");var o=new i(n,a||e,s),c=r?r+t:t;return e._events[c]?e._events[c].fn?e._events[c]=[e._events[c],o]:e._events[c].push(o):(e._events[c]=o,e._eventsCount++),e}function s(e,t){0==--e._eventsCount?e._events=new n:delete e._events[t]}function o(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),(new n).__proto__||(r=!1)),o.prototype.eventNames=function(){var e,n,i=[];if(0===this._eventsCount)return i;for(n in e=this._events)t.call(e,n)&&i.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?i.concat(Object.getOwnPropertySymbols(e)):i},o.prototype.listeners=function(e){var t=r?r+e:e,n=this._events[t];if(!n)return[];if(n.fn)return[n.fn];for(var i=0,a=n.length,s=Array(a);i<a;i++)s[i]=n[i].fn;return s},o.prototype.listenerCount=function(e){var t=r?r+e:e,n=this._events[t];return n?n.fn?1:n.length:0},o.prototype.emit=function(e,t,n,i,a,s){var o=r?r+e:e;if(!this._events[o])return!1;var c,l,u=this._events[o],d=arguments.length;if(u.fn){switch(u.once&&this.removeListener(e,u.fn,void 0,!0),d){case 1:return u.fn.call(u.context),!0;case 2:return u.fn.call(u.context,t),!0;case 3:return u.fn.call(u.context,t,n),!0;case 4:return u.fn.call(u.context,t,n,i),!0;case 5:return u.fn.call(u.context,t,n,i,a),!0;case 6:return u.fn.call(u.context,t,n,i,a,s),!0}for(l=1,c=Array(d-1);l<d;l++)c[l-1]=arguments[l];u.fn.apply(u.context,c)}else{var f,h=u.length;for(l=0;l<h;l++)switch(u[l].once&&this.removeListener(e,u[l].fn,void 0,!0),d){case 1:u[l].fn.call(u[l].context);break;case 2:u[l].fn.call(u[l].context,t);break;case 3:u[l].fn.call(u[l].context,t,n);break;case 4:u[l].fn.call(u[l].context,t,n,i);break;default:if(!c)for(f=1,c=Array(d-1);f<d;f++)c[f-1]=arguments[f];u[l].fn.apply(u[l].context,c)}}return!0},o.prototype.on=function(e,t,r){return a(this,e,t,r,!1)},o.prototype.once=function(e,t,r){return a(this,e,t,r,!0)},o.prototype.removeListener=function(e,t,n,i){var a=r?r+e:e;if(!this._events[a])return this;if(!t)return s(this,a),this;var o=this._events[a];if(o.fn)o.fn!==t||i&&!o.once||n&&o.context!==n||s(this,a);else{for(var c=0,l=[],u=o.length;c<u;c++)(o[c].fn!==t||i&&!o[c].once||n&&o[c].context!==n)&&l.push(o[c]);l.length?this._events[a]=1===l.length?l[0]:l:s(this,a)}return this},o.prototype.removeAllListeners=function(e){var t;return e?(t=r?r+e:e,this._events[t]&&s(this,t)):(this._events=new n,this._eventsCount=0),this},o.prototype.off=o.prototype.removeListener,o.prototype.addListener=o.prototype.on,o.prefixed=r,o.EventEmitter=o,e.exports=o},213:e=>{e.exports=(e,t)=>(t=t||(()=>{}),e.then(e=>new Promise(e=>{e(t())}).then(()=>e),e=>new Promise(e=>{e(t())}).then(()=>{throw e})))},574:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t,r){let n=0,i=e.length;for(;i>0;){let a=i/2|0,s=n+a;0>=r(e[s],t)?(n=++s,i-=a+1):i=a}return n}},821:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});let n=r(574);class i{constructor(){this._queue=[]}enqueue(e,t){let r={priority:(t=Object.assign({priority:0},t)).priority,run:e};if(this.size&&this._queue[this.size-1].priority>=t.priority){this._queue.push(r);return}let i=n.default(this._queue,r,(e,t)=>t.priority-e.priority);this._queue.splice(i,0,r)}dequeue(){let e=this._queue.shift();return null==e?void 0:e.run}filter(e){return this._queue.filter(t=>t.priority===e.priority).map(e=>e.run)}get size(){return this._queue.length}}t.default=i},816:(e,t,r)=>{let n=r(213);class i extends Error{constructor(e){super(e),this.name="TimeoutError"}}let a=(e,t,r)=>new Promise((a,s)=>{if("number"!=typeof t||t<0)throw TypeError("Expected `milliseconds` to be a positive number");if(t===1/0){a(e);return}let o=setTimeout(()=>{if("function"==typeof r){try{a(r())}catch(e){s(e)}return}let n="string"==typeof r?r:`Promise timed out after ${t} milliseconds`,o=r instanceof Error?r:new i(n);"function"==typeof e.cancel&&e.cancel(),s(o)},t);n(e.then(a,s),()=>{clearTimeout(o)})});e.exports=a,e.exports.default=a,e.exports.TimeoutError=i}},r={};function n(e){var i=r[e];if(void 0!==i)return i.exports;var a=r[e]={exports:{}},s=!0;try{t[e](a,a.exports,n),s=!1}finally{s&&delete r[e]}return a.exports}n.ab="//";var i={};(()=>{Object.defineProperty(i,"__esModule",{value:!0});let e=n(993),t=n(816),r=n(821),a=()=>{},s=new t.TimeoutError;class o extends e{constructor(e){var t,n,i,s;if(super(),this._intervalCount=0,this._intervalEnd=0,this._pendingCount=0,this._resolveEmpty=a,this._resolveIdle=a,!("number"==typeof(e=Object.assign({carryoverConcurrencyCount:!1,intervalCap:1/0,interval:0,concurrency:1/0,autoStart:!0,queueClass:r.default},e)).intervalCap&&e.intervalCap>=1))throw TypeError(`Expected \`intervalCap\` to be a number from 1 and up, got \`${null!==(n=null===(t=e.intervalCap)||void 0===t?void 0:t.toString())&&void 0!==n?n:""}\` (${typeof e.intervalCap})`);if(void 0===e.interval||!(Number.isFinite(e.interval)&&e.interval>=0))throw TypeError(`Expected \`interval\` to be a finite number >= 0, got \`${null!==(s=null===(i=e.interval)||void 0===i?void 0:i.toString())&&void 0!==s?s:""}\` (${typeof e.interval})`);this._carryoverConcurrencyCount=e.carryoverConcurrencyCount,this._isIntervalIgnored=e.intervalCap===1/0||0===e.interval,this._intervalCap=e.intervalCap,this._interval=e.interval,this._queue=new e.queueClass,this._queueClass=e.queueClass,this.concurrency=e.concurrency,this._timeout=e.timeout,this._throwOnTimeout=!0===e.throwOnTimeout,this._isPaused=!1===e.autoStart}get _doesIntervalAllowAnother(){return this._isIntervalIgnored||this._intervalCount<this._intervalCap}get _doesConcurrentAllowAnother(){return this._pendingCount<this._concurrency}_next(){this._pendingCount--,this._tryToStartAnother(),this.emit("next")}_resolvePromises(){this._resolveEmpty(),this._resolveEmpty=a,0===this._pendingCount&&(this._resolveIdle(),this._resolveIdle=a,this.emit("idle"))}_onResumeInterval(){this._onInterval(),this._initializeIntervalIfNeeded(),this._timeoutId=void 0}_isIntervalPaused(){let e=Date.now();if(void 0===this._intervalId){let t=this._intervalEnd-e;if(!(t<0))return void 0===this._timeoutId&&(this._timeoutId=setTimeout(()=>{this._onResumeInterval()},t)),!0;this._intervalCount=this._carryoverConcurrencyCount?this._pendingCount:0}return!1}_tryToStartAnother(){if(0===this._queue.size)return this._intervalId&&clearInterval(this._intervalId),this._intervalId=void 0,this._resolvePromises(),!1;if(!this._isPaused){let e=!this._isIntervalPaused();if(this._doesIntervalAllowAnother&&this._doesConcurrentAllowAnother){let t=this._queue.dequeue();return!!t&&(this.emit("active"),t(),e&&this._initializeIntervalIfNeeded(),!0)}}return!1}_initializeIntervalIfNeeded(){this._isIntervalIgnored||void 0!==this._intervalId||(this._intervalId=setInterval(()=>{this._onInterval()},this._interval),this._intervalEnd=Date.now()+this._interval)}_onInterval(){0===this._intervalCount&&0===this._pendingCount&&this._intervalId&&(clearInterval(this._intervalId),this._intervalId=void 0),this._intervalCount=this._carryoverConcurrencyCount?this._pendingCount:0,this._processQueue()}_processQueue(){for(;this._tryToStartAnother(););}get concurrency(){return this._concurrency}set concurrency(e){if(!("number"==typeof e&&e>=1))throw TypeError(`Expected \`concurrency\` to be a number from 1 and up, got \`${e}\` (${typeof e})`);this._concurrency=e,this._processQueue()}async add(e,r={}){return new Promise((n,i)=>{let a=async()=>{this._pendingCount++,this._intervalCount++;try{let a=void 0===this._timeout&&void 0===r.timeout?e():t.default(Promise.resolve(e()),void 0===r.timeout?this._timeout:r.timeout,()=>{(void 0===r.throwOnTimeout?this._throwOnTimeout:r.throwOnTimeout)&&i(s)});n(await a)}catch(e){i(e)}this._next()};this._queue.enqueue(a,r),this._tryToStartAnother(),this.emit("add")})}async addAll(e,t){return Promise.all(e.map(async e=>this.add(e,t)))}start(){return this._isPaused&&(this._isPaused=!1,this._processQueue()),this}pause(){this._isPaused=!0}clear(){this._queue=new this._queueClass}async onEmpty(){if(0!==this._queue.size)return new Promise(e=>{let t=this._resolveEmpty;this._resolveEmpty=()=>{t(),e()}})}async onIdle(){if(0!==this._pendingCount||0!==this._queue.size)return new Promise(e=>{let t=this._resolveIdle;this._resolveIdle=()=>{t(),e()}})}get size(){return this._queue.size}sizeBy(e){return this._queue.filter(e).length}get pending(){return this._pendingCount}get isPaused(){return this._isPaused}get timeout(){return this._timeout}set timeout(e){this._timeout=e}}i.default=o})(),e.exports=i})()},544:(e,t)=>{"use strict";var r={H:null,A:null};function n(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var r=2;r<arguments.length;r++)t+="&args[]="+encodeURIComponent(arguments[r])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var i=Array.isArray,a=Symbol.for("react.transitional.element"),s=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),c=Symbol.for("react.strict_mode"),l=Symbol.for("react.profiler"),u=Symbol.for("react.forward_ref"),d=Symbol.for("react.suspense"),f=Symbol.for("react.memo"),h=Symbol.for("react.lazy"),p=Symbol.iterator,g=Object.prototype.hasOwnProperty,m=Object.assign;function y(e,t,r,n,i,s){return{$$typeof:a,type:e,key:t,ref:void 0!==(r=s.ref)?r:null,props:s}}function b(e){return"object"==typeof e&&null!==e&&e.$$typeof===a}var w=/\/+/g;function v(e,t){var r,n;return"object"==typeof e&&null!==e&&null!=e.key?(r=""+e.key,n={"=":"=0",":":"=2"},"$"+r.replace(/[=:]/g,function(e){return n[e]})):t.toString(36)}function x(){}function _(e,t,r){if(null==e)return e;var o=[],c=0;return!function e(t,r,o,c,l){var u,d,f,g=typeof t;("undefined"===g||"boolean"===g)&&(t=null);var m=!1;if(null===t)m=!0;else switch(g){case"bigint":case"string":case"number":m=!0;break;case"object":switch(t.$$typeof){case a:case s:m=!0;break;case h:return e((m=t._init)(t._payload),r,o,c,l)}}if(m)return l=l(t),m=""===c?"."+v(t,0):c,i(l)?(o="",null!=m&&(o=m.replace(w,"$&/")+"/"),e(l,r,o,"",function(e){return e})):null!=l&&(b(l)&&(u=l,d=o+(null==l.key||t&&t.key===l.key?"":(""+l.key).replace(w,"$&/")+"/")+m,l=y(u.type,d,void 0,void 0,void 0,u.props)),r.push(l)),1;m=0;var _=""===c?".":c+":";if(i(t))for(var S=0;S<t.length;S++)g=_+v(c=t[S],S),m+=e(c,r,o,g,l);else if("function"==typeof(S=null===(f=t)||"object"!=typeof f?null:"function"==typeof(f=p&&f[p]||f["@@iterator"])?f:null))for(t=S.call(t),S=0;!(c=t.next()).done;)g=_+v(c=c.value,S++),m+=e(c,r,o,g,l);else if("object"===g){if("function"==typeof t.then)return e(function(e){switch(e.status){case"fulfilled":return e.value;case"rejected":throw e.reason;default:switch("string"==typeof e.status?e.then(x,x):(e.status="pending",e.then(function(t){"pending"===e.status&&(e.status="fulfilled",e.value=t)},function(t){"pending"===e.status&&(e.status="rejected",e.reason=t)})),e.status){case"fulfilled":return e.value;case"rejected":throw e.reason}}throw e}(t),r,o,c,l);throw Error(n(31,"[object Object]"===(r=String(t))?"object with keys {"+Object.keys(t).join(", ")+"}":r))}return m}(e,o,"","",function(e){return t.call(r,e,c++)}),o}function S(e){if(-1===e._status){var t=e._result;(t=t()).then(function(t){(0===e._status||-1===e._status)&&(e._status=1,e._result=t)},function(t){(0===e._status||-1===e._status)&&(e._status=2,e._result=t)}),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}function k(){return new WeakMap}function E(){return{s:0,v:void 0,o:null,p:null}}t.Children={map:_,forEach:function(e,t,r){_(e,function(){t.apply(this,arguments)},r)},count:function(e){var t=0;return _(e,function(){t++}),t},toArray:function(e){return _(e,function(e){return e})||[]},only:function(e){if(!b(e))throw Error(n(143));return e}},t.Fragment=o,t.Profiler=l,t.StrictMode=c,t.Suspense=d,t.__SERVER_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=r,t.cache=function(e){return function(){var t=r.A;if(!t)return e.apply(null,arguments);var n=t.getCacheForType(k);void 0===(t=n.get(e))&&(t=E(),n.set(e,t)),n=0;for(var i=arguments.length;n<i;n++){var a=arguments[n];if("function"==typeof a||"object"==typeof a&&null!==a){var s=t.o;null===s&&(t.o=s=new WeakMap),void 0===(t=s.get(a))&&(t=E(),s.set(a,t))}else null===(s=t.p)&&(t.p=s=new Map),void 0===(t=s.get(a))&&(t=E(),s.set(a,t))}if(1===t.s)return t.v;if(2===t.s)throw t.v;try{var o=e.apply(null,arguments);return(n=t).s=1,n.v=o}catch(e){throw(o=t).s=2,o.v=e,e}}},t.cloneElement=function(e,t,r){if(null==e)throw Error(n(267,e));var i=m({},e.props),a=e.key,s=void 0;if(null!=t)for(o in void 0!==t.ref&&(s=void 0),void 0!==t.key&&(a=""+t.key),t)g.call(t,o)&&"key"!==o&&"__self"!==o&&"__source"!==o&&("ref"!==o||void 0!==t.ref)&&(i[o]=t[o]);var o=arguments.length-2;if(1===o)i.children=r;else if(1<o){for(var c=Array(o),l=0;l<o;l++)c[l]=arguments[l+2];i.children=c}return y(e.type,a,void 0,void 0,s,i)},t.createElement=function(e,t,r){var n,i={},a=null;if(null!=t)for(n in void 0!==t.key&&(a=""+t.key),t)g.call(t,n)&&"key"!==n&&"__self"!==n&&"__source"!==n&&(i[n]=t[n]);var s=arguments.length-2;if(1===s)i.children=r;else if(1<s){for(var o=Array(s),c=0;c<s;c++)o[c]=arguments[c+2];i.children=o}if(e&&e.defaultProps)for(n in s=e.defaultProps)void 0===i[n]&&(i[n]=s[n]);return y(e,a,void 0,void 0,null,i)},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:u,render:e}},t.isValidElement=b,t.lazy=function(e){return{$$typeof:h,_payload:{_status:-1,_result:e},_init:S}},t.memo=function(e,t){return{$$typeof:f,type:e,compare:void 0===t?null:t}},t.use=function(e){return r.H.use(e)},t.useCallback=function(e,t){return r.H.useCallback(e,t)},t.useDebugValue=function(){},t.useId=function(){return r.H.useId()},t.useMemo=function(e,t){return r.H.useMemo(e,t)},t.version="19.0.0-rc-65e06cb7-20241218"},886:(e,t,r)=>{"use strict";e.exports=r(544)},113:(e,t,r)=>{var n;(()=>{var i={226:function(i,a){!function(s,o){"use strict";var c="function",l="undefined",u="object",d="string",f="major",h="model",p="name",g="type",m="vendor",y="version",b="architecture",w="console",v="mobile",x="tablet",_="smarttv",S="wearable",k="embedded",E="Amazon",A="Apple",T="ASUS",P="BlackBerry",C="Browser",O="Chrome",N="Firefox",R="Google",I="Huawei",$="Microsoft",j="Motorola",U="Opera",L="Samsung",D="Sharp",M="Sony",B="Xiaomi",H="Zebra",q="Facebook",z="Chromium OS",W="Mac OS",F=function(e,t){var r={};for(var n in e)t[n]&&t[n].length%2==0?r[n]=t[n].concat(e[n]):r[n]=e[n];return r},K=function(e){for(var t={},r=0;r<e.length;r++)t[e[r].toUpperCase()]=e[r];return t},V=function(e,t){return typeof e===d&&-1!==J(t).indexOf(J(e))},J=function(e){return e.toLowerCase()},Q=function(e,t){if(typeof e===d)return e=e.replace(/^\s\s*/,""),typeof t===l?e:e.substring(0,350)},G=function(e,t){for(var r,n,i,a,s,l,d=0;d<t.length&&!s;){var f=t[d],h=t[d+1];for(r=n=0;r<f.length&&!s&&f[r];)if(s=f[r++].exec(e))for(i=0;i<h.length;i++)l=s[++n],typeof(a=h[i])===u&&a.length>0?2===a.length?typeof a[1]==c?this[a[0]]=a[1].call(this,l):this[a[0]]=a[1]:3===a.length?typeof a[1]!==c||a[1].exec&&a[1].test?this[a[0]]=l?l.replace(a[1],a[2]):void 0:this[a[0]]=l?a[1].call(this,l,a[2]):void 0:4===a.length&&(this[a[0]]=l?a[3].call(this,l.replace(a[1],a[2])):void 0):this[a]=l||o;d+=2}},X=function(e,t){for(var r in t)if(typeof t[r]===u&&t[r].length>0){for(var n=0;n<t[r].length;n++)if(V(t[r][n],e))return"?"===r?o:r}else if(V(t[r],e))return"?"===r?o:r;return e},Y={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},Z={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[y,[p,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[y,[p,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[p,y],[/opios[\/ ]+([\w\.]+)/i],[y,[p,U+" Mini"]],[/\bopr\/([\w\.]+)/i],[y,[p,U]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/ ]?([\w\.]*)/i,/(avant |iemobile|slim)(?:browser)?[\/ ]?([\w\.]*)/i,/(ba?idubrowser)[\/ ]?([\w\.]+)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|qq|duckduckgo)\/([-\w\.]+)/i,/(heytap|ovi)browser\/([\d\.]+)/i,/(weibo)__([\d\.]+)/i],[p,y],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[y,[p,"UC"+C]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i],[y,[p,"WeChat(Win) Desktop"]],[/micromessenger\/([\w\.]+)/i],[y,[p,"WeChat"]],[/konqueror\/([\w\.]+)/i],[y,[p,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[y,[p,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[y,[p,"Yandex"]],[/(avast|avg)\/([\w\.]+)/i],[[p,/(.+)/,"$1 Secure "+C],y],[/\bfocus\/([\w\.]+)/i],[y,[p,N+" Focus"]],[/\bopt\/([\w\.]+)/i],[y,[p,U+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[y,[p,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[y,[p,"Dolphin"]],[/coast\/([\w\.]+)/i],[y,[p,U+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[y,[p,"MIUI "+C]],[/fxios\/([-\w\.]+)/i],[y,[p,N]],[/\bqihu|(qi?ho?o?|360)browser/i],[[p,"360 "+C]],[/(oculus|samsung|sailfish|huawei)browser\/([\w\.]+)/i],[[p,/(.+)/,"$1 "+C],y],[/(comodo_dragon)\/([\w\.]+)/i],[[p,/_/g," "],y],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|baiduboxapp|2345Explorer)[\/ ]?([\w\.]+)/i],[p,y],[/(metasr)[\/ ]?([\w\.]+)/i,/(lbbrowser)/i,/\[(linkedin)app\]/i],[p],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[p,q],y],[/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(chromium|instagram)[\/ ]([-\w\.]+)/i],[p,y],[/\bgsa\/([\w\.]+) .*safari\//i],[y,[p,"GSA"]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[y,[p,"TikTok"]],[/headlesschrome(?:\/([\w\.]+)| )/i],[y,[p,O+" Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[p,O+" WebView"],y],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[y,[p,"Android "+C]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[p,y],[/version\/([\w\.\,]+) .*mobile\/\w+ (safari)/i],[y,[p,"Mobile Safari"]],[/version\/([\w(\.|\,)]+) .*(mobile ?safari|safari)/i],[y,p],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[p,[y,X,{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}]],[/(webkit|khtml)\/([\w\.]+)/i],[p,y],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[p,"Netscape"],y],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[y,[p,N+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror|klar)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/(links) \(([\w\.]+)/i,/panasonic;(viera)/i],[p,y],[/(cobalt)\/([\w\.]+)/i],[p,[y,/master.|lts./,""]]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i],[[b,"amd64"]],[/(ia32(?=;))/i],[[b,J]],[/((?:i[346]|x)86)[;\)]/i],[[b,"ia32"]],[/\b(aarch64|arm(v?8e?l?|_?64))\b/i],[[b,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[b,"armhf"]],[/windows (ce|mobile); ppc;/i],[[b,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],[[b,/ower/,"",J]],[/(sun4\w)[;\)]/i],[[b,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[b,J]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[h,[m,L],[g,x]],[/\b((?:s[cgp]h|gt|sm)-\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]([-\w]+)/i,/sec-(sgh\w+)/i],[h,[m,L],[g,v]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[h,[m,A],[g,v]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[h,[m,A],[g,x]],[/(macintosh);/i],[h,[m,A]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[h,[m,D],[g,v]],[/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],[h,[m,I],[g,x]],[/(?:huawei|honor)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[h,[m,I],[g,v]],[/\b(poco[\w ]+)(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite)?)(?: bui|\))/i],[[h,/_/g," "],[m,B],[g,v]],[/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],[[h,/_/g," "],[m,B],[g,x]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[h,[m,"OPPO"],[g,v]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[h,[m,"Vivo"],[g,v]],[/\b(rmx[12]\d{3})(?: bui|;|\))/i],[h,[m,"Realme"],[g,v]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[h,[m,j],[g,v]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[h,[m,j],[g,x]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[h,[m,"LG"],[g,x]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,/\blg-?([\d\w]+) bui/i],[h,[m,"LG"],[g,v]],[/(ideatab[-\w ]+)/i,/lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],[h,[m,"Lenovo"],[g,x]],[/(?:maemo|nokia).*(n900|lumia \d+)/i,/nokia[-_ ]?([-\w\.]*)/i],[[h,/_/g," "],[m,"Nokia"],[g,v]],[/(pixel c)\b/i],[h,[m,R],[g,x]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[h,[m,R],[g,v]],[/droid.+ (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[h,[m,M],[g,v]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[h,"Xperia Tablet"],[m,M],[g,x]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[h,[m,"OnePlus"],[g,v]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[h,[m,E],[g,x]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[h,/(.+)/g,"Fire Phone $1"],[m,E],[g,v]],[/(playbook);[-\w\),; ]+(rim)/i],[h,m,[g,x]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[h,[m,P],[g,v]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[h,[m,T],[g,x]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[h,[m,T],[g,v]],[/(nexus 9)/i],[h,[m,"HTC"],[g,x]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[m,[h,/_/g," "],[g,v]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[h,[m,"Acer"],[g,x]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[h,[m,"Meizu"],[g,v]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron)[-_ ]?([-\w]*)/i,/(hp) ([\w ]+\w)/i,/(asus)-?(\w+)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w]+)/i,/(jolla)/i,/(oppo) ?([\w ]+) bui/i],[m,h,[g,v]],[/(kobo)\s(ereader|touch)/i,/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/(nook)[\w ]+build\/(\w+)/i,/(dell) (strea[kpr\d ]*[\dko])/i,/(le[- ]+pan)[- ]+(\w{1,9}) bui/i,/(trinity)[- ]*(t\d{3}) bui/i,/(gigaset)[- ]+(q\w{1,9}) bui/i,/(vodafone) ([\w ]+)(?:\)| bui)/i],[m,h,[g,x]],[/(surface duo)/i],[h,[m,$],[g,x]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[h,[m,"Fairphone"],[g,v]],[/(u304aa)/i],[h,[m,"AT&T"],[g,v]],[/\bsie-(\w*)/i],[h,[m,"Siemens"],[g,v]],[/\b(rct\w+) b/i],[h,[m,"RCA"],[g,x]],[/\b(venue[\d ]{2,7}) b/i],[h,[m,"Dell"],[g,x]],[/\b(q(?:mv|ta)\w+) b/i],[h,[m,"Verizon"],[g,x]],[/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i],[h,[m,"Barnes & Noble"],[g,x]],[/\b(tm\d{3}\w+) b/i],[h,[m,"NuVision"],[g,x]],[/\b(k88) b/i],[h,[m,"ZTE"],[g,x]],[/\b(nx\d{3}j) b/i],[h,[m,"ZTE"],[g,v]],[/\b(gen\d{3}) b.+49h/i],[h,[m,"Swiss"],[g,v]],[/\b(zur\d{3}) b/i],[h,[m,"Swiss"],[g,x]],[/\b((zeki)?tb.*\b) b/i],[h,[m,"Zeki"],[g,x]],[/\b([yr]\d{2}) b/i,/\b(dragon[- ]+touch |dt)(\w{5}) b/i],[[m,"Dragon Touch"],h,[g,x]],[/\b(ns-?\w{0,9}) b/i],[h,[m,"Insignia"],[g,x]],[/\b((nxa|next)-?\w{0,9}) b/i],[h,[m,"NextBook"],[g,x]],[/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[m,"Voice"],h,[g,v]],[/\b(lvtel\-)?(v1[12]) b/i],[[m,"LvTel"],h,[g,v]],[/\b(ph-1) /i],[h,[m,"Essential"],[g,v]],[/\b(v(100md|700na|7011|917g).*\b) b/i],[h,[m,"Envizen"],[g,x]],[/\b(trio[-\w\. ]+) b/i],[h,[m,"MachSpeed"],[g,x]],[/\btu_(1491) b/i],[h,[m,"Rotor"],[g,x]],[/(shield[\w ]+) b/i],[h,[m,"Nvidia"],[g,x]],[/(sprint) (\w+)/i],[m,h,[g,v]],[/(kin\.[onetw]{3})/i],[[h,/\./g," "],[m,$],[g,v]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[h,[m,H],[g,x]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[h,[m,H],[g,v]],[/smart-tv.+(samsung)/i],[m,[g,_]],[/hbbtv.+maple;(\d+)/i],[[h,/^/,"SmartTV"],[m,L],[g,_]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[m,"LG"],[g,_]],[/(apple) ?tv/i],[m,[h,A+" TV"],[g,_]],[/crkey/i],[[h,O+"cast"],[m,R],[g,_]],[/droid.+aft(\w)( bui|\))/i],[h,[m,E],[g,_]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[h,[m,D],[g,_]],[/(bravia[\w ]+)( bui|\))/i],[h,[m,M],[g,_]],[/(mitv-\w{5}) bui/i],[h,[m,B],[g,_]],[/Hbbtv.*(technisat) (.*);/i],[m,h,[g,_]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[m,Q],[h,Q],[g,_]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[g,_]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[m,h,[g,w]],[/droid.+; (shield) bui/i],[h,[m,"Nvidia"],[g,w]],[/(playstation [345portablevi]+)/i],[h,[m,M],[g,w]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[h,[m,$],[g,w]],[/((pebble))app/i],[m,h,[g,S]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[h,[m,A],[g,S]],[/droid.+; (glass) \d/i],[h,[m,R],[g,S]],[/droid.+; (wt63?0{2,3})\)/i],[h,[m,H],[g,S]],[/(quest( 2| pro)?)/i],[h,[m,q],[g,S]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[m,[g,k]],[/(aeobc)\b/i],[h,[m,E],[g,k]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+? mobile safari/i],[h,[g,v]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],[h,[g,x]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[g,x]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[g,v]],[/(android[-\w\. ]{0,9});.+buil/i],[h,[m,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[y,[p,"EdgeHTML"]],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[y,[p,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[p,y],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[y,p]],os:[[/microsoft (windows) (vista|xp)/i],[p,y],[/(windows) nt 6\.2; (arm)/i,/(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i,/(windows)[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i],[p,[y,X,Y]],[/(win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[p,"Windows"],[y,X,Y]],[/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,/ios;fbsv\/([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[y,/_/g,"."],[p,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[p,W],[y,/_/g,"."]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[y,p],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish)[-\/ ]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/ ]([\w\.]+)/i,/\((series40);/i],[p,y],[/\(bb(10);/i],[y,[p,P]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],[y,[p,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[y,[p,N+" OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[y,[p,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[y,[p,"watchOS"]],[/crkey\/([\d\.]+)/i],[y,[p,O+"cast"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[p,z],y],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\);]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux) ?([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[p,y],[/(sunos) ?([\w\.\d]*)/i],[[p,"Solaris"],y],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[p,y]]},ee=function(e,t){if(typeof e===u&&(t=e,e=o),!(this instanceof ee))return new ee(e,t).getResult();var r=typeof s!==l&&s.navigator?s.navigator:o,n=e||(r&&r.userAgent?r.userAgent:""),i=r&&r.userAgentData?r.userAgentData:o,a=t?F(Z,t):Z,w=r&&r.userAgent==n;return this.getBrowser=function(){var e,t={};return t[p]=o,t[y]=o,G.call(t,n,a.browser),t[f]=typeof(e=t[y])===d?e.replace(/[^\d\.]/g,"").split(".")[0]:o,w&&r&&r.brave&&typeof r.brave.isBrave==c&&(t[p]="Brave"),t},this.getCPU=function(){var e={};return e[b]=o,G.call(e,n,a.cpu),e},this.getDevice=function(){var e={};return e[m]=o,e[h]=o,e[g]=o,G.call(e,n,a.device),w&&!e[g]&&i&&i.mobile&&(e[g]=v),w&&"Macintosh"==e[h]&&r&&typeof r.standalone!==l&&r.maxTouchPoints&&r.maxTouchPoints>2&&(e[h]="iPad",e[g]=x),e},this.getEngine=function(){var e={};return e[p]=o,e[y]=o,G.call(e,n,a.engine),e},this.getOS=function(){var e={};return e[p]=o,e[y]=o,G.call(e,n,a.os),w&&!e[p]&&i&&"Unknown"!=i.platform&&(e[p]=i.platform.replace(/chrome os/i,z).replace(/macos/i,W)),e},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return n},this.setUA=function(e){return n=typeof e===d&&e.length>350?Q(e,350):e,this},this.setUA(n),this};ee.VERSION="1.0.35",ee.BROWSER=K([p,y,f]),ee.CPU=K([b]),ee.DEVICE=K([h,m,g,w,v,_,x,S,k]),ee.ENGINE=ee.OS=K([p,y]),typeof a!==l?(i.exports&&(a=i.exports=ee),a.UAParser=ee):r.amdO?void 0!==(n=(function(){return ee}).call(t,r,t,e))&&(e.exports=n):typeof s!==l&&(s.UAParser=ee);var et=typeof s!==l&&(s.jQuery||s.Zepto);if(et&&!et.ua){var er=new ee;et.ua=er.getResult(),et.ua.get=function(){return er.getUA()},et.ua.set=function(e){er.setUA(e);var t=er.getResult();for(var r in t)et.ua[r]=t[r]}}}("object"==typeof window?window:this)}},a={};function s(e){var t=a[e];if(void 0!==t)return t.exports;var r=a[e]={exports:{}},n=!0;try{i[e].call(r.exports,r,r.exports,s),n=!1}finally{n&&delete a[e]}return r.exports}s.ab="//";var o=s(226);e.exports=o})()},152:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getTestReqInfo:function(){return s},withRequest:function(){return a}});let n=new(r(521)).AsyncLocalStorage;function i(e,t){let r=t.header(e,"next-test-proxy-port");if(r)return{url:t.url(e),proxyPort:Number(r),testData:t.header(e,"next-test-data")||""}}function a(e,t,r){let a=i(e,t);return a?n.run(a,r):r()}function s(e,t){return n.getStore()||(e&&t?i(e,t):void 0)}},53:(e,t,r)=>{"use strict";var n=r(356).Buffer;Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{handleFetch:function(){return o},interceptFetch:function(){return c},reader:function(){return a}});let i=r(152),a={url:e=>e.url,header:(e,t)=>e.headers.get(t)};async function s(e,t){let{url:r,method:i,headers:a,body:s,cache:o,credentials:c,integrity:l,mode:u,redirect:d,referrer:f,referrerPolicy:h}=t;return{testData:e,api:"fetch",request:{url:r,method:i,headers:[...Array.from(a),["next-test-stack",function(){let e=(Error().stack??"").split("\n");for(let t=1;t<e.length;t++)if(e[t].length>0){e=e.slice(t);break}return(e=(e=(e=e.filter(e=>!e.includes("/next/dist/"))).slice(0,5)).map(e=>e.replace("webpack-internal:///(rsc)/","").trim())).join("    ")}()]],body:s?n.from(await t.arrayBuffer()).toString("base64"):null,cache:o,credentials:c,integrity:l,mode:u,redirect:d,referrer:f,referrerPolicy:h}}}async function o(e,t){let r=(0,i.getTestReqInfo)(t,a);if(!r)return e(t);let{testData:o,proxyPort:c}=r,l=await s(o,t),u=await e(`http://localhost:${c}`,{method:"POST",body:JSON.stringify(l),next:{internal:!0}});if(!u.ok)throw Error(`Proxy request failed: ${u.status}`);let d=await u.json(),{api:f}=d;switch(f){case"continue":return e(t);case"abort":case"unhandled":throw Error(`Proxy request aborted [${t.method} ${t.url}]`)}return function(e){let{status:t,headers:r,body:i}=e.response;return new Response(i?n.from(i,"base64"):null,{status:t,headers:new Headers(r)})}(d)}function c(e){return r.g.fetch=function(t,r){var n;return(null==r?void 0:null==(n=r.next)?void 0:n.internal)?e(t,r):o(e,new Request(t,r))},()=>{r.g.fetch=e}}},384:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{interceptTestApis:function(){return a},wrapRequestHandler:function(){return s}});let n=r(152),i=r(53);function a(){return(0,i.interceptFetch)(r.g.fetch)}function s(e){return(t,r)=>(0,n.withRequest)(t,i.reader,()=>e(t,r))}},43:(e,t,r)=>{"use strict";let n,i,a,s,o,c,l;r.r(t),r.d(t,{default:()=>ph});var u={};r.r(u),r.d(u,{q:()=>nC,l:()=>nR});var d={};r.r(d),r.d(d,{accounts:()=>hL,aiFeedback:()=>hq,candidates:()=>hB,sessions:()=>hD,testResults:()=>hH,users:()=>hU,verificationTokens:()=>hM});var f={};async function h(){return"_ENTRIES"in globalThis&&_ENTRIES.middleware_instrumentation&&await _ENTRIES.middleware_instrumentation}r.r(f),r.d(f,{config:()=>pl,default:()=>pc});let p=null;async function g(){if("phase-production-build"===process.env.NEXT_PHASE)return;p||(p=h());let e=await p;if(null==e?void 0:e.register)try{await e.register()}catch(e){throw e.message=`An error occurred while loading instrumentation hook: ${e.message}`,e}}async function m(...e){let t=await h();try{var r;await (null==t?void 0:null==(r=t.onRequestError)?void 0:r.call(t,...e))}catch(e){console.error("Error in instrumentation.onRequestError:",e)}}let y=null;function b(){return y||(y=g()),y}function w(e){return`The edge runtime does not support Node.js '${e}' module.
Learn More: https://nextjs.org/docs/messages/node-module-in-edge-runtime`}process!==r.g.process&&(process.env=r.g.process.env,r.g.process=process),Object.defineProperty(globalThis,"__import_unsupported",{value:function(e){let t=new Proxy(function(){},{get(t,r){if("then"===r)return{};throw Error(w(e))},construct(){throw Error(w(e))},apply(r,n,i){if("function"==typeof i[0])return i[0](t);throw Error(w(e))}});return new Proxy({},{get:()=>t})},enumerable:!1,configurable:!1}),b();class v extends Error{constructor({page:e}){super(`The middleware "${e}" accepts an async API directly with the form:
  
  export function middleware(request, event) {
    return NextResponse.redirect('/new-location')
  }
  
  Read more: https://nextjs.org/docs/messages/middleware-new-signature
  `)}}class x extends Error{constructor(){super(`The request.page has been deprecated in favour of \`URLPattern\`.
  Read more: https://nextjs.org/docs/messages/middleware-request-page
  `)}}class _ extends Error{constructor(){super(`The request.ua has been removed in favour of \`userAgent\` function.
  Read more: https://nextjs.org/docs/messages/middleware-parse-user-agent
  `)}}let S={shared:"shared",reactServerComponents:"rsc",serverSideRendering:"ssr",actionBrowser:"action-browser",api:"api",middleware:"middleware",instrument:"instrument",edgeAsset:"edge-asset",appPagesBrowser:"app-pages-browser"};function k(e){var t,r,n,i,a,s=[],o=0;function c(){for(;o<e.length&&/\s/.test(e.charAt(o));)o+=1;return o<e.length}for(;o<e.length;){for(t=o,a=!1;c();)if(","===(r=e.charAt(o))){for(n=o,o+=1,c(),i=o;o<e.length&&"="!==(r=e.charAt(o))&&";"!==r&&","!==r;)o+=1;o<e.length&&"="===e.charAt(o)?(a=!0,o=i,s.push(e.substring(t,n)),t=o):o=n+1}else o+=1;(!a||o>=e.length)&&s.push(e.substring(t,e.length))}return s}function E(e){let t={},r=[];if(e)for(let[n,i]of e.entries())"set-cookie"===n.toLowerCase()?(r.push(...k(i)),t[n]=1===r.length?r[0]:r):t[n]=i;return t}function A(e){try{return String(new URL(String(e)))}catch(t){throw Error(`URL is malformed "${String(e)}". Please use only absolute URLs - https://nextjs.org/docs/messages/middleware-relative-urls`,{cause:t})}}({...S,GROUP:{builtinReact:[S.reactServerComponents,S.actionBrowser],serverOnly:[S.reactServerComponents,S.actionBrowser,S.instrument,S.middleware],neutralTarget:[S.api],clientOnly:[S.serverSideRendering,S.appPagesBrowser],bundled:[S.reactServerComponents,S.actionBrowser,S.serverSideRendering,S.appPagesBrowser,S.shared,S.instrument],appPages:[S.reactServerComponents,S.serverSideRendering,S.appPagesBrowser,S.actionBrowser]}});let T=Symbol("response"),P=Symbol("passThrough"),C=Symbol("waitUntil");class O{constructor(e,t){this[P]=!1,this[C]=t?{kind:"external",function:t}:{kind:"internal",promises:[]}}respondWith(e){this[T]||(this[T]=Promise.resolve(e))}passThroughOnException(){this[P]=!0}waitUntil(e){if("external"===this[C].kind)return(0,this[C].function)(e);this[C].promises.push(e)}}class N extends O{constructor(e){var t;super(e.request,null==(t=e.context)?void 0:t.waitUntil),this.sourcePage=e.page}get request(){throw new v({page:this.sourcePage})}respondWith(){throw new v({page:this.sourcePage})}}function R(e){return e.replace(/\/$/,"")||"/"}function I(e){let t=e.indexOf("#"),r=e.indexOf("?"),n=r>-1&&(t<0||r<t);return n||t>-1?{pathname:e.substring(0,n?r:t),query:n?e.substring(r,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}function $(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:n,hash:i}=I(e);return""+t+r+n+i}function j(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:n,hash:i}=I(e);return""+r+t+n+i}function U(e,t){if("string"!=typeof e)return!1;let{pathname:r}=I(e);return r===t||r.startsWith(t+"/")}function L(e,t){let r;let n=e.split("/");return(t||[]).some(t=>!!n[1]&&n[1].toLowerCase()===t.toLowerCase()&&(r=t,n.splice(1,1),e=n.join("/")||"/",!0)),{pathname:e,detectedLocale:r}}let D=/(?!^https?:\/\/)(127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}|\[::1\]|localhost)/;function M(e,t){return new URL(String(e).replace(D,"localhost"),t&&String(t).replace(D,"localhost"))}let B=Symbol("NextURLInternal");class H{constructor(e,t,r){let n,i;"object"==typeof t&&"pathname"in t||"string"==typeof t?(n=t,i=r||{}):i=r||t||{},this[B]={url:M(e,n??i.base),options:i,basePath:""},this.analyze()}analyze(){var e,t,r,n,i;let a=function(e,t){var r,n;let{basePath:i,i18n:a,trailingSlash:s}=null!=(r=t.nextConfig)?r:{},o={pathname:e,trailingSlash:"/"!==e?e.endsWith("/"):s};i&&U(o.pathname,i)&&(o.pathname=function(e,t){if(!U(e,t))return e;let r=e.slice(t.length);return r.startsWith("/")?r:"/"+r}(o.pathname,i),o.basePath=i);let c=o.pathname;if(o.pathname.startsWith("/_next/data/")&&o.pathname.endsWith(".json")){let e=o.pathname.replace(/^\/_next\/data\//,"").replace(/\.json$/,"").split("/"),r=e[0];o.buildId=r,c="index"!==e[1]?"/"+e.slice(1).join("/"):"/",!0===t.parseData&&(o.pathname=c)}if(a){let e=t.i18nProvider?t.i18nProvider.analyze(o.pathname):L(o.pathname,a.locales);o.locale=e.detectedLocale,o.pathname=null!=(n=e.pathname)?n:o.pathname,!e.detectedLocale&&o.buildId&&(e=t.i18nProvider?t.i18nProvider.analyze(c):L(c,a.locales)).detectedLocale&&(o.locale=e.detectedLocale)}return o}(this[B].url.pathname,{nextConfig:this[B].options.nextConfig,parseData:!0,i18nProvider:this[B].options.i18nProvider}),s=function(e,t){let r;if((null==t?void 0:t.host)&&!Array.isArray(t.host))r=t.host.toString().split(":",1)[0];else{if(!e.hostname)return;r=e.hostname}return r.toLowerCase()}(this[B].url,this[B].options.headers);this[B].domainLocale=this[B].options.i18nProvider?this[B].options.i18nProvider.detectDomainLocale(s):function(e,t,r){if(e)for(let a of(r&&(r=r.toLowerCase()),e)){var n,i;if(t===(null==(n=a.domain)?void 0:n.split(":",1)[0].toLowerCase())||r===a.defaultLocale.toLowerCase()||(null==(i=a.locales)?void 0:i.some(e=>e.toLowerCase()===r)))return a}}(null==(t=this[B].options.nextConfig)?void 0:null==(e=t.i18n)?void 0:e.domains,s);let o=(null==(r=this[B].domainLocale)?void 0:r.defaultLocale)||(null==(i=this[B].options.nextConfig)?void 0:null==(n=i.i18n)?void 0:n.defaultLocale);this[B].url.pathname=a.pathname,this[B].defaultLocale=o,this[B].basePath=a.basePath??"",this[B].buildId=a.buildId,this[B].locale=a.locale??o,this[B].trailingSlash=a.trailingSlash}formatPathname(){var e;let t;return t=function(e,t,r,n){if(!t||t===r)return e;let i=e.toLowerCase();return!n&&(U(i,"/api")||U(i,"/"+t.toLowerCase()))?e:$(e,"/"+t)}((e={basePath:this[B].basePath,buildId:this[B].buildId,defaultLocale:this[B].options.forceLocale?void 0:this[B].defaultLocale,locale:this[B].locale,pathname:this[B].url.pathname,trailingSlash:this[B].trailingSlash}).pathname,e.locale,e.buildId?void 0:e.defaultLocale,e.ignorePrefix),(e.buildId||!e.trailingSlash)&&(t=R(t)),e.buildId&&(t=j($(t,"/_next/data/"+e.buildId),"/"===e.pathname?"index.json":".json")),t=$(t,e.basePath),!e.buildId&&e.trailingSlash?t.endsWith("/")?t:j(t,"/"):R(t)}formatSearch(){return this[B].url.search}get buildId(){return this[B].buildId}set buildId(e){this[B].buildId=e}get locale(){return this[B].locale??""}set locale(e){var t,r;if(!this[B].locale||!(null==(r=this[B].options.nextConfig)?void 0:null==(t=r.i18n)?void 0:t.locales.includes(e)))throw TypeError(`The NextURL configuration includes no locale "${e}"`);this[B].locale=e}get defaultLocale(){return this[B].defaultLocale}get domainLocale(){return this[B].domainLocale}get searchParams(){return this[B].url.searchParams}get host(){return this[B].url.host}set host(e){this[B].url.host=e}get hostname(){return this[B].url.hostname}set hostname(e){this[B].url.hostname=e}get port(){return this[B].url.port}set port(e){this[B].url.port=e}get protocol(){return this[B].url.protocol}set protocol(e){this[B].url.protocol=e}get href(){let e=this.formatPathname(),t=this.formatSearch();return`${this.protocol}//${this.host}${e}${t}${this.hash}`}set href(e){this[B].url=M(e),this.analyze()}get origin(){return this[B].url.origin}get pathname(){return this[B].url.pathname}set pathname(e){this[B].url.pathname=e}get hash(){return this[B].url.hash}set hash(e){this[B].url.hash=e}get search(){return this[B].url.search}set search(e){this[B].url.search=e}get password(){return this[B].url.password}set password(e){this[B].url.password=e}get username(){return this[B].url.username}set username(e){this[B].url.username=e}get basePath(){return this[B].basePath}set basePath(e){this[B].basePath=e.startsWith("/")?e:`/${e}`}toString(){return this.href}toJSON(){return this.href}[Symbol.for("edge-runtime.inspect.custom")](){return{href:this.href,origin:this.origin,protocol:this.protocol,username:this.username,password:this.password,host:this.host,hostname:this.hostname,port:this.port,pathname:this.pathname,search:this.search,searchParams:this.searchParams,hash:this.hash}}clone(){return new H(String(this),this[B].options)}}var q=r(555);let z=Symbol("internal request");class W extends Request{constructor(e,t={}){let r="string"!=typeof e&&"url"in e?e.url:String(e);A(r),e instanceof Request?super(e,t):super(r,t);let n=new H(r,{headers:E(this.headers),nextConfig:t.nextConfig});this[z]={cookies:new q.RequestCookies(this.headers),nextUrl:n,url:n.toString()}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,nextUrl:this.nextUrl,url:this.url,bodyUsed:this.bodyUsed,cache:this.cache,credentials:this.credentials,destination:this.destination,headers:Object.fromEntries(this.headers),integrity:this.integrity,keepalive:this.keepalive,method:this.method,mode:this.mode,redirect:this.redirect,referrer:this.referrer,referrerPolicy:this.referrerPolicy,signal:this.signal}}get cookies(){return this[z].cookies}get nextUrl(){return this[z].nextUrl}get page(){throw new x}get ua(){throw new _}get url(){return this[z].url}}class F{static get(e,t,r){let n=Reflect.get(e,t,r);return"function"==typeof n?n.bind(e):n}static set(e,t,r,n){return Reflect.set(e,t,r,n)}static has(e,t){return Reflect.has(e,t)}static deleteProperty(e,t){return Reflect.deleteProperty(e,t)}}let K=Symbol("internal response"),V=new Set([301,302,303,307,308]);function J(e,t){var r;if(null==e?void 0:null==(r=e.request)?void 0:r.headers){if(!(e.request.headers instanceof Headers))throw Error("request.headers must be an instance of Headers");let r=[];for(let[n,i]of e.request.headers)t.set("x-middleware-request-"+n,i),r.push(n);t.set("x-middleware-override-headers",r.join(","))}}class Q extends Response{constructor(e,t={}){super(e,t);let r=this.headers,n=new Proxy(new q.ResponseCookies(r),{get(e,n,i){switch(n){case"delete":case"set":return(...i)=>{let a=Reflect.apply(e[n],e,i),s=new Headers(r);return a instanceof q.ResponseCookies&&r.set("x-middleware-set-cookie",a.getAll().map(e=>(0,q.stringifyCookie)(e)).join(",")),J(t,s),a};default:return F.get(e,n,i)}}});this[K]={cookies:n,url:t.url?new H(t.url,{headers:E(r),nextConfig:t.nextConfig}):void 0}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,url:this.url,body:this.body,bodyUsed:this.bodyUsed,headers:Object.fromEntries(this.headers),ok:this.ok,redirected:this.redirected,status:this.status,statusText:this.statusText,type:this.type}}get cookies(){return this[K].cookies}static json(e,t){let r=Response.json(e,t);return new Q(r.body,r)}static redirect(e,t){let r="number"==typeof t?t:(null==t?void 0:t.status)??307;if(!V.has(r))throw RangeError('Failed to execute "redirect" on "response": Invalid status code');let n="object"==typeof t?t:{},i=new Headers(null==n?void 0:n.headers);return i.set("Location",A(e)),new Q(null,{...n,headers:i,status:r})}static rewrite(e,t){let r=new Headers(null==t?void 0:t.headers);return r.set("x-middleware-rewrite",A(e)),J(t,r),new Q(null,{...t,headers:r})}static next(e){let t=new Headers(null==e?void 0:e.headers);return t.set("x-middleware-next","1"),J(e,t),new Q(null,{...e,headers:t})}}function G(e,t){let r="string"==typeof t?new URL(t):t,n=new URL(e,t),i=r.protocol+"//"+r.host;return n.protocol+"//"+n.host===i?n.toString().replace(i,""):n.toString()}let X="Next-Router-Prefetch",Y=["RSC","Next-Router-State-Tree",X,"Next-HMR-Refresh","Next-Router-Segment-Prefetch"],Z=["__nextFallback","__nextLocale","__nextInferredLocaleFromDefault","__nextDefaultLocale","__nextIsNotFound","_rsc"],ee=["__nextDataReq"];class et extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new et}}class er extends Headers{constructor(e){super(),this.headers=new Proxy(e,{get(t,r,n){if("symbol"==typeof r)return F.get(t,r,n);let i=r.toLowerCase(),a=Object.keys(e).find(e=>e.toLowerCase()===i);if(void 0!==a)return F.get(t,a,n)},set(t,r,n,i){if("symbol"==typeof r)return F.set(t,r,n,i);let a=r.toLowerCase(),s=Object.keys(e).find(e=>e.toLowerCase()===a);return F.set(t,s??r,n,i)},has(t,r){if("symbol"==typeof r)return F.has(t,r);let n=r.toLowerCase(),i=Object.keys(e).find(e=>e.toLowerCase()===n);return void 0!==i&&F.has(t,i)},deleteProperty(t,r){if("symbol"==typeof r)return F.deleteProperty(t,r);let n=r.toLowerCase(),i=Object.keys(e).find(e=>e.toLowerCase()===n);return void 0===i||F.deleteProperty(t,i)}})}static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"append":case"delete":case"set":return et.callable;default:return F.get(e,t,r)}}})}merge(e){return Array.isArray(e)?e.join(", "):e}static from(e){return e instanceof Headers?e:new er(e)}append(e,t){let r=this.headers[e];"string"==typeof r?this.headers[e]=[r,t]:Array.isArray(r)?r.push(t):this.headers[e]=t}delete(e){delete this.headers[e]}get(e){let t=this.headers[e];return void 0!==t?this.merge(t):null}has(e){return void 0!==this.headers[e]}set(e,t){this.headers[e]=t}forEach(e,t){for(let[r,n]of this.entries())e.call(t,n,r,this)}*entries(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase(),r=this.get(t);yield[t,r]}}*keys(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase();yield t}}*values(){for(let e of Object.keys(this.headers)){let t=this.get(e);yield t}}[Symbol.iterator](){return this.entries()}}let en=Error("Invariant: AsyncLocalStorage accessed in runtime where it is not available");class ei{disable(){throw en}getStore(){}run(){throw en}exit(){throw en}enterWith(){throw en}static bind(e){return e}}let ea="undefined"!=typeof globalThis&&globalThis.AsyncLocalStorage;function es(){return ea?new ea:new ei}let eo=es(),ec=es();function el(e){let t=ec.getStore();if(t){if("request"===t.type)return t;if("prerender"===t.type||"prerender-ppr"===t.type||"prerender-legacy"===t.type)throw Error(`\`${e}\` cannot be called inside a prerender. This is a bug in Next.js.`);if("cache"===t.type)throw Error(`\`${e}\` cannot be called inside "use cache". Call it outside and pass an argument instead. Read more: https://nextjs.org/docs/messages/next-request-in-use-cache`);if("unstable-cache"===t.type)throw Error(`\`${e}\` cannot be called inside unstable_cache. Call it outside and pass an argument instead. Read more: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`)}throw Error(`\`${e}\` was called outside a request scope. Read more: https://nextjs.org/docs/messages/next-dynamic-api-wrong-context`)}class eu extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#options")}static callable(){throw new eu}}class ed{static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"clear":case"delete":case"set":return eu.callable;default:return F.get(e,t,r)}}})}}let ef=Symbol.for("next.mutated.cookies");class eh{static wrap(e,t){let r=new q.ResponseCookies(new Headers);for(let t of e.getAll())r.set(t);let n=[],i=new Set,a=()=>{let e=eo.getStore();if(e&&(e.pathWasRevalidated=!0),n=r.getAll().filter(e=>i.has(e.name)),t){let e=[];for(let t of n){let r=new q.ResponseCookies(new Headers);r.set(t),e.push(r.toString())}t(e)}},s=new Proxy(r,{get(e,t,r){switch(t){case ef:return n;case"delete":return function(...t){i.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.delete(...t),s}finally{a()}};case"set":return function(...t){i.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.set(...t),s}finally{a()}};default:return F.get(e,t,r)}}});return s}}function ep(e){return"action"===e.phase}function eg(e){if(!ep(el(e)))throw new eu}var em=function(e){return e.handleRequest="BaseServer.handleRequest",e.run="BaseServer.run",e.pipe="BaseServer.pipe",e.getStaticHTML="BaseServer.getStaticHTML",e.render="BaseServer.render",e.renderToResponseWithComponents="BaseServer.renderToResponseWithComponents",e.renderToResponse="BaseServer.renderToResponse",e.renderToHTML="BaseServer.renderToHTML",e.renderError="BaseServer.renderError",e.renderErrorToResponse="BaseServer.renderErrorToResponse",e.renderErrorToHTML="BaseServer.renderErrorToHTML",e.render404="BaseServer.render404",e}(em||{}),ey=function(e){return e.loadDefaultErrorComponents="LoadComponents.loadDefaultErrorComponents",e.loadComponents="LoadComponents.loadComponents",e}(ey||{}),eb=function(e){return e.getRequestHandler="NextServer.getRequestHandler",e.getServer="NextServer.getServer",e.getServerRequestHandler="NextServer.getServerRequestHandler",e.createServer="createServer.createServer",e}(eb||{}),ew=function(e){return e.compression="NextNodeServer.compression",e.getBuildId="NextNodeServer.getBuildId",e.createComponentTree="NextNodeServer.createComponentTree",e.clientComponentLoading="NextNodeServer.clientComponentLoading",e.getLayoutOrPageModule="NextNodeServer.getLayoutOrPageModule",e.generateStaticRoutes="NextNodeServer.generateStaticRoutes",e.generateFsStaticRoutes="NextNodeServer.generateFsStaticRoutes",e.generatePublicRoutes="NextNodeServer.generatePublicRoutes",e.generateImageRoutes="NextNodeServer.generateImageRoutes.route",e.sendRenderResult="NextNodeServer.sendRenderResult",e.proxyRequest="NextNodeServer.proxyRequest",e.runApi="NextNodeServer.runApi",e.render="NextNodeServer.render",e.renderHTML="NextNodeServer.renderHTML",e.imageOptimizer="NextNodeServer.imageOptimizer",e.getPagePath="NextNodeServer.getPagePath",e.getRoutesManifest="NextNodeServer.getRoutesManifest",e.findPageComponents="NextNodeServer.findPageComponents",e.getFontManifest="NextNodeServer.getFontManifest",e.getServerComponentManifest="NextNodeServer.getServerComponentManifest",e.getRequestHandler="NextNodeServer.getRequestHandler",e.renderToHTML="NextNodeServer.renderToHTML",e.renderError="NextNodeServer.renderError",e.renderErrorToHTML="NextNodeServer.renderErrorToHTML",e.render404="NextNodeServer.render404",e.startResponse="NextNodeServer.startResponse",e.route="route",e.onProxyReq="onProxyReq",e.apiResolver="apiResolver",e.internalFetch="internalFetch",e}(ew||{}),ev=function(e){return e.startServer="startServer.startServer",e}(ev||{}),ex=function(e){return e.getServerSideProps="Render.getServerSideProps",e.getStaticProps="Render.getStaticProps",e.renderToString="Render.renderToString",e.renderDocument="Render.renderDocument",e.createBodyResult="Render.createBodyResult",e}(ex||{}),e_=function(e){return e.renderToString="AppRender.renderToString",e.renderToReadableStream="AppRender.renderToReadableStream",e.getBodyResult="AppRender.getBodyResult",e.fetch="AppRender.fetch",e}(e_||{}),eS=function(e){return e.executeRoute="Router.executeRoute",e}(eS||{}),ek=function(e){return e.runHandler="Node.runHandler",e}(ek||{}),eE=function(e){return e.runHandler="AppRouteRouteHandlers.runHandler",e}(eE||{}),eA=function(e){return e.generateMetadata="ResolveMetadata.generateMetadata",e.generateViewport="ResolveMetadata.generateViewport",e}(eA||{}),eT=function(e){return e.execute="Middleware.execute",e}(eT||{});let eP=["Middleware.execute","BaseServer.handleRequest","Render.getServerSideProps","Render.getStaticProps","AppRender.fetch","AppRender.getBodyResult","Render.renderDocument","Node.runHandler","AppRouteRouteHandlers.runHandler","ResolveMetadata.generateMetadata","ResolveMetadata.generateViewport","NextNodeServer.createComponentTree","NextNodeServer.findPageComponents","NextNodeServer.getLayoutOrPageModule","NextNodeServer.startResponse","NextNodeServer.clientComponentLoading"],eC=["NextNodeServer.findPageComponents","NextNodeServer.createComponentTree","NextNodeServer.clientComponentLoading"];function eO(e){return null!==e&&"object"==typeof e&&"then"in e&&"function"==typeof e.then}let{context:eN,propagation:eR,trace:eI,SpanStatusCode:e$,SpanKind:ej,ROOT_CONTEXT:eU}=n=r(777);class eL extends Error{constructor(e,t){super(),this.bubble=e,this.result=t}}let eD=(e,t)=>{(function(e){return"object"==typeof e&&null!==e&&e instanceof eL})(t)&&t.bubble?e.setAttribute("next.bubble",!0):(t&&e.recordException(t),e.setStatus({code:e$.ERROR,message:null==t?void 0:t.message})),e.end()},eM=new Map,eB=n.createContextKey("next.rootSpanId"),eH=0,eq=()=>eH++,ez={set(e,t,r){e.push({key:t,value:r})}};class eW{getTracerInstance(){return eI.getTracer("next.js","0.0.1")}getContext(){return eN}getTracePropagationData(){let e=eN.active(),t=[];return eR.inject(e,t,ez),t}getActiveScopeSpan(){return eI.getSpan(null==eN?void 0:eN.active())}withPropagatedContext(e,t,r){let n=eN.active();if(eI.getSpanContext(n))return t();let i=eR.extract(n,e,r);return eN.with(i,t)}trace(...e){var t;let[r,n,i]=e,{fn:a,options:s}="function"==typeof n?{fn:n,options:{}}:{fn:i,options:{...n}},o=s.spanName??r;if(!eP.includes(r)&&"1"!==process.env.NEXT_OTEL_VERBOSE||s.hideSpan)return a();let c=this.getSpanContext((null==s?void 0:s.parentSpan)??this.getActiveScopeSpan()),l=!1;c?(null==(t=eI.getSpanContext(c))?void 0:t.isRemote)&&(l=!0):(c=(null==eN?void 0:eN.active())??eU,l=!0);let u=eq();return s.attributes={"next.span_name":o,"next.span_type":r,...s.attributes},eN.with(c.setValue(eB,u),()=>this.getTracerInstance().startActiveSpan(o,s,e=>{let t="performance"in globalThis&&"measure"in performance?globalThis.performance.now():void 0,n=()=>{eM.delete(u),t&&process.env.NEXT_OTEL_PERFORMANCE_PREFIX&&eC.includes(r||"")&&performance.measure(`${process.env.NEXT_OTEL_PERFORMANCE_PREFIX}:next-${(r.split(".").pop()||"").replace(/[A-Z]/g,e=>"-"+e.toLowerCase())}`,{start:t,end:performance.now()})};l&&eM.set(u,new Map(Object.entries(s.attributes??{})));try{if(a.length>1)return a(e,t=>eD(e,t));let t=a(e);if(eO(t))return t.then(t=>(e.end(),t)).catch(t=>{throw eD(e,t),t}).finally(n);return e.end(),n(),t}catch(t){throw eD(e,t),n(),t}}))}wrap(...e){let t=this,[r,n,i]=3===e.length?e:[e[0],{},e[1]];return eP.includes(r)||"1"===process.env.NEXT_OTEL_VERBOSE?function(){let e=n;"function"==typeof e&&"function"==typeof i&&(e=e.apply(this,arguments));let a=arguments.length-1,s=arguments[a];if("function"!=typeof s)return t.trace(r,e,()=>i.apply(this,arguments));{let n=t.getContext().bind(eN.active(),s);return t.trace(r,e,(e,t)=>(arguments[a]=function(e){return null==t||t(e),n.apply(this,arguments)},i.apply(this,arguments)))}}:i}startSpan(...e){let[t,r]=e,n=this.getSpanContext((null==r?void 0:r.parentSpan)??this.getActiveScopeSpan());return this.getTracerInstance().startSpan(t,r,n)}getSpanContext(e){return e?eI.setSpan(eN.active(),e):void 0}getRootSpanAttributes(){let e=eN.active().getValue(eB);return eM.get(e)}setRootSpanAttribute(e,t){let r=eN.active().getValue(eB),n=eM.get(r);n&&n.set(e,t)}}let eF=(()=>{let e=new eW;return()=>e})(),eK="__prerender_bypass";Symbol("__next_preview_data"),Symbol(eK);class eV{constructor(e,t,r,n){var i;let a=e&&function(e,t){let r=er.from(e.headers);return{isOnDemandRevalidate:r.get("x-prerender-revalidate")===t.previewModeId,revalidateOnlyGenerated:r.has("x-prerender-revalidate-if-generated")}}(t,e).isOnDemandRevalidate,s=null==(i=r.get(eK))?void 0:i.value;this.isEnabled=!!(!a&&s&&e&&s===e.previewModeId),this._previewModeId=null==e?void 0:e.previewModeId,this._mutableCookies=n}enable(){if(!this._previewModeId)throw Error("Invariant: previewProps missing previewModeId this should never happen");this._mutableCookies.set({name:eK,value:this._previewModeId,httpOnly:!0,sameSite:"none",secure:!0,path:"/"})}disable(){this._mutableCookies.set({name:eK,value:"",httpOnly:!0,sameSite:"none",secure:!0,path:"/",expires:new Date(0)})}}function eJ(e,t){if("x-middleware-set-cookie"in e.headers&&"string"==typeof e.headers["x-middleware-set-cookie"]){let r=e.headers["x-middleware-set-cookie"],n=new Headers;for(let e of k(r))n.append("set-cookie",e);for(let e of new q.ResponseCookies(n).getAll())t.set(e)}}var eQ=r(541),eG=r.n(eQ);class eX extends Error{constructor(e,t){super("Invariant: "+(e.endsWith(".")?e:e+".")+" This is a bug in Next.js.",t),this.name="InvariantError"}}async function eY(e,t){if(!e)return t();let r=eZ(e);try{return await t()}finally{let t=function(e,t){let r=new Set(e.revalidatedTags),n=new Set(e.pendingRevalidateWrites);return{revalidatedTags:t.revalidatedTags.filter(e=>!r.has(e)),pendingRevalidates:Object.fromEntries(Object.entries(t.pendingRevalidates).filter(([t])=>!(t in e.pendingRevalidates))),pendingRevalidateWrites:t.pendingRevalidateWrites.filter(e=>!n.has(e))}}(r,eZ(e));await e0(e,t)}}function eZ(e){return{revalidatedTags:e.revalidatedTags?[...e.revalidatedTags]:[],pendingRevalidates:{...e.pendingRevalidates},pendingRevalidateWrites:e.pendingRevalidateWrites?[...e.pendingRevalidateWrites]:[]}}async function e0(e,{revalidatedTags:t,pendingRevalidates:r,pendingRevalidateWrites:n}){var i;return Promise.all([null==(i=e.incrementalCache)?void 0:i.revalidateTag(t),...Object.values(r),...n])}let e1=Error("Invariant: AsyncLocalStorage accessed in runtime where it is not available");class e2{disable(){throw e1}getStore(){}run(){throw e1}exit(){throw e1}enterWith(){throw e1}static bind(e){return e}}let e5="undefined"!=typeof globalThis&&globalThis.AsyncLocalStorage,e6=e5?new e5:new e2;class e3{constructor({waitUntil:e,onClose:t,onTaskError:r}){this.workUnitStores=new Set,this.waitUntil=e,this.onClose=t,this.onTaskError=r,this.callbackQueue=new(eG()),this.callbackQueue.pause()}after(e){if(eO(e))this.waitUntil||e8(),this.waitUntil(e.catch(e=>this.reportTaskError("promise",e)));else if("function"==typeof e)this.addCallback(e);else throw Error("`after()`: Argument must be a promise or a function")}addCallback(e){var t;this.waitUntil||e8();let r=ec.getStore();r&&this.workUnitStores.add(r);let n=e6.getStore(),i=n?n.rootTaskSpawnPhase:null==r?void 0:r.phase;this.runCallbacksOnClosePromise||(this.runCallbacksOnClosePromise=this.runCallbacksOnClose(),this.waitUntil(this.runCallbacksOnClosePromise));let a=(t=async()=>{try{await e6.run({rootTaskSpawnPhase:i},()=>e())}catch(e){this.reportTaskError("function",e)}},e5?e5.bind(t):e2.bind(t));this.callbackQueue.add(a)}async runCallbacksOnClose(){return await new Promise(e=>this.onClose(e)),this.runCallbacks()}async runCallbacks(){if(0===this.callbackQueue.size)return;for(let e of this.workUnitStores)e.phase="after";let e=eo.getStore();if(!e)throw new eX("Missing workStore in AfterContext.runCallbacks");return eY(e,()=>(this.callbackQueue.start(),this.callbackQueue.onIdle()))}reportTaskError(e,t){if(console.error("promise"===e?"A promise passed to `after()` rejected:":"An error occurred in a function passed to `after()`:",t),this.onTaskError)try{null==this.onTaskError||this.onTaskError.call(this,t)}catch(e){console.error(new eX("`onTaskError` threw while handling an error thrown from an `after` task",{cause:e}))}}}function e8(){throw Error("`after()` will not work correctly, because `waitUntil` is not available in the current environment.")}class e4{onClose(e){if(this.isClosed)throw Error("Cannot subscribe to a closed CloseController");this.target.addEventListener("close",e),this.listeners++}dispatchClose(){if(this.isClosed)throw Error("Cannot close a CloseController multiple times");this.listeners>0&&this.target.dispatchEvent(new Event("close")),this.isClosed=!0}constructor(){this.target=new EventTarget,this.listeners=0,this.isClosed=!1}}function e9(){return{previewModeId:process.env.__NEXT_PREVIEW_MODE_ID,previewModeSigningKey:process.env.__NEXT_PREVIEW_MODE_SIGNING_KEY||"",previewModeEncryptionKey:process.env.__NEXT_PREVIEW_MODE_ENCRYPTION_KEY||""}}let e7=Symbol.for("@next/request-context");class te extends W{constructor(e){super(e.input,e.init),this.sourcePage=e.page}get request(){throw new v({page:this.sourcePage})}respondWith(){throw new v({page:this.sourcePage})}waitUntil(){throw new v({page:this.sourcePage})}}let tt={keys:e=>Array.from(e.keys()),get:(e,t)=>e.get(t)??void 0},tr=(e,t)=>eF().withPropagatedContext(e.headers,t,tt),tn=!1;async function ti(e){var t;let n,i;!function(){if(!tn&&(tn=!0,"true"===process.env.NEXT_PRIVATE_TEST_PROXY)){let{interceptTestApis:e,wrapRequestHandler:t}=r(384);e(),tr=t(tr)}}(),await b();let a=void 0!==self.__BUILD_MANIFEST;e.request.url=e.request.url.replace(/\.rsc($|\?)/,"$1");let s=new H(e.request.url,{headers:e.request.headers,nextConfig:e.request.nextConfig});for(let e of[...s.searchParams.keys()]){let t=s.searchParams.getAll(e);!function(e,t){for(let r of["nxtP","nxtI"])e!==r&&e.startsWith(r)&&t(e.substring(r.length))}(e,r=>{for(let e of(s.searchParams.delete(r),t))s.searchParams.append(r,e);s.searchParams.delete(e)})}let o=s.buildId;s.buildId="";let c=e.request.headers["x-nextjs-data"];c&&"/index"===s.pathname&&(s.pathname="/");let l=function(e){let t=new Headers;for(let[r,n]of Object.entries(e))for(let e of Array.isArray(n)?n:[n])void 0!==e&&("number"==typeof e&&(e=e.toString()),t.append(r,e));return t}(e.request.headers),u=new Map;if(!a)for(let e of Y){let t=e.toLowerCase(),r=l.get(t);r&&(u.set(t,r),l.delete(t))}let d=new te({page:e.page,input:(function(e,t){let r="string"==typeof e,n=r?new URL(e):e;for(let e of Z)n.searchParams.delete(e);if(t)for(let e of ee)n.searchParams.delete(e);return r?n.toString():n})(s,!0).toString(),init:{body:e.request.body,headers:l,method:e.request.method,nextConfig:e.request.nextConfig,signal:e.request.signal}});c&&Object.defineProperty(d,"__isData",{enumerable:!1,value:!0}),!globalThis.__incrementalCache&&e.IncrementalCache&&(globalThis.__incrementalCache=new e.IncrementalCache({appDir:!0,fetchCache:!0,minimalMode:!0,fetchCacheKeyPrefix:"",dev:!1,requestHeaders:e.request.headers,requestProtocol:"https",getPrerenderManifest:()=>({version:-1,routes:{},dynamicRoutes:{},notFoundRoutes:[],preview:e9()})}));let f=e.request.waitUntil??(null==(t=function(){let e=globalThis[e7];return null==e?void 0:e.get()}())?void 0:t.waitUntil),h=new N({request:d,page:e.page,context:f?{waitUntil:f}:void 0});if((n=await tr(d,()=>{if("/middleware"===e.page||"/src/middleware"===e.page){let t=h.waitUntil.bind(h),r=new e4;return eF().trace(eT.execute,{spanName:`middleware ${d.method} ${d.nextUrl.pathname}`,attributes:{"http.target":d.nextUrl.pathname,"http.method":d.method}},async()=>{try{var n,a,s,c,l,u,f;let p=e9(),g=(l=d.nextUrl,u=void 0,f=e=>{i=e},function(e,t,r,n,i,a,s,o,c,l){function u(e){r&&r.setHeader("Set-Cookie",e)}let d={};return{type:"request",phase:e,implicitTags:i??[],url:{pathname:n.pathname,search:n.search??""},get headers(){return d.headers||(d.headers=function(e){let t=er.from(e);for(let e of Y)t.delete(e.toLowerCase());return er.seal(t)}(t.headers)),d.headers},get cookies(){if(!d.cookies){let e=new q.RequestCookies(er.from(t.headers));eJ(t,e),d.cookies=ed.seal(e)}return d.cookies},set cookies(value){d.cookies=value},get mutableCookies(){if(!d.mutableCookies){let e=function(e,t){let r=new q.RequestCookies(er.from(e));return eh.wrap(r,t)}(t.headers,a||(r?u:void 0));eJ(t,e),d.mutableCookies=e}return d.mutableCookies},get userspaceMutableCookies(){if(!d.userspaceMutableCookies){let e=function(e){let t=new Proxy(e,{get(e,r,n){switch(r){case"delete":return function(...r){return eg("cookies().delete"),e.delete(...r),t};case"set":return function(...r){return eg("cookies().set"),e.set(...r),t};default:return F.get(e,r,n)}}});return t}(this.mutableCookies);d.userspaceMutableCookies=e}return d.userspaceMutableCookies},get draftMode(){return d.draftMode||(d.draftMode=new eV(o,t,this.cookies,this.mutableCookies)),d.draftMode},renderResumeDataCache:s??null,isHmrRefresh:c,serverComponentsHmrCache:l||globalThis.__serverComponentsHmrCache}}("action",d,void 0,l,u,f,void 0,p,!1,void 0)),m=function({page:e,fallbackRouteParams:t,renderOpts:r,requestEndedState:n,isPrefetchRequest:i}){var a;let s={isStaticGeneration:!r.supportsDynamicResponse&&!r.isDraftMode&&!r.isServerAction,page:e,fallbackRouteParams:t,route:(a=e.split("/").reduce((e,t,r,n)=>t?"("===t[0]&&t.endsWith(")")||"@"===t[0]||("page"===t||"route"===t)&&r===n.length-1?e:e+"/"+t:e,"")).startsWith("/")?a:"/"+a,incrementalCache:r.incrementalCache||globalThis.__incrementalCache,cacheLifeProfiles:r.cacheLifeProfiles,isRevalidate:r.isRevalidate,isPrerendering:r.nextExport,fetchCache:r.fetchCache,isOnDemandRevalidate:r.isOnDemandRevalidate,isDraftMode:r.isDraftMode,requestEndedState:n,isPrefetchRequest:i,buildId:r.buildId,reactLoadableManifest:(null==r?void 0:r.reactLoadableManifest)||{},assetPrefix:(null==r?void 0:r.assetPrefix)||"",afterContext:function(e){let{waitUntil:t,onClose:r,onAfterTaskError:n}=e;return new e3({waitUntil:t,onClose:r,onTaskError:n})}(r)};return r.store=s,s}({page:"/",fallbackRouteParams:null,renderOpts:{cacheLifeProfiles:null==(a=e.request.nextConfig)?void 0:null==(n=a.experimental)?void 0:n.cacheLife,experimental:{isRoutePPREnabled:!1,dynamicIO:!1,authInterrupts:!!(null==(c=e.request.nextConfig)?void 0:null==(s=c.experimental)?void 0:s.authInterrupts)},buildId:o??"",supportsDynamicResponse:!0,waitUntil:t,onClose:r.onClose.bind(r),onAfterTaskError:void 0},requestEndedState:{ended:!1},isPrefetchRequest:d.headers.has(X)});return await eo.run(m,()=>ec.run(g,e.handler,d,h))}finally{setTimeout(()=>{r.dispatchClose()},0)}})}return e.handler(d,h)}))&&!(n instanceof Response))throw TypeError("Expected an instance of Response to be returned");n&&i&&n.headers.set("set-cookie",i);let p=null==n?void 0:n.headers.get("x-middleware-rewrite");if(n&&p&&!a){let t=new H(p,{forceLocale:!0,headers:e.request.headers,nextConfig:e.request.nextConfig});t.host===d.nextUrl.host&&(t.buildId=o||t.buildId,n.headers.set("x-middleware-rewrite",String(t)));let r=G(String(t),String(s));c&&n.headers.set("x-nextjs-rewrite",r)}let g=null==n?void 0:n.headers.get("Location");if(n&&g&&!a){let t=new H(g,{forceLocale:!1,headers:e.request.headers,nextConfig:e.request.nextConfig});n=new Response(n.body,n),t.host===d.nextUrl.host&&(t.buildId=o||t.buildId,n.headers.set("Location",String(t))),c&&(n.headers.delete("Location"),n.headers.set("x-nextjs-redirect",G(String(t),String(s))))}let m=n||Q.next(),y=m.headers.get("x-middleware-override-headers"),w=[];if(y){for(let[e,t]of u)m.headers.set(`x-middleware-request-${e}`,t),w.push(e);w.length>0&&m.headers.set("x-middleware-override-headers",y+","+w.join(","))}return{response:m,waitUntil:("internal"===h[C].kind?Promise.all(h[C].promises).then(()=>{}):void 0)??Promise.resolve(),fetchMetrics:d.fetchMetrics}}var ta=function(e,t,r,n,i){if("m"===n)throw TypeError("Private method is not writable");if("a"===n&&!i)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!i:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?i.call(e,r):i?i.value=r:t.set(e,r),r},ts=function(e,t,r,n){if("a"===r&&!n)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!n:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===r?n:"a"===r?n.call(e):n?n.value:t.get(e)};function to(e){let t=e?"__Secure-":"";return{sessionToken:{name:`${t}authjs.session-token`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},callbackUrl:{name:`${t}authjs.callback-url`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},csrfToken:{name:`${e?"__Host-":""}authjs.csrf-token`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},pkceCodeVerifier:{name:`${t}authjs.pkce.code_verifier`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e,maxAge:900}},state:{name:`${t}authjs.state`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e,maxAge:900}},nonce:{name:`${t}authjs.nonce`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},webauthnChallenge:{name:`${t}authjs.challenge`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e,maxAge:900}}}}class tc{constructor(e,t,r){if(ii.add(this),ia.set(this,{}),is.set(this,void 0),io.set(this,void 0),ta(this,io,r,"f"),ta(this,is,e,"f"),!t)return;let{name:n}=e;for(let[e,r]of Object.entries(t))e.startsWith(n)&&r&&(ts(this,ia,"f")[e]=r)}get value(){return Object.keys(ts(this,ia,"f")).sort((e,t)=>parseInt(e.split(".").pop()||"0")-parseInt(t.split(".").pop()||"0")).map(e=>ts(this,ia,"f")[e]).join("")}chunk(e,t){let r=ts(this,ii,"m",il).call(this);for(let n of ts(this,ii,"m",ic).call(this,{name:ts(this,is,"f").name,value:e,options:{...ts(this,is,"f").options,...t}}))r[n.name]=n;return Object.values(r)}clean(){return Object.values(ts(this,ii,"m",il).call(this))}}ia=new WeakMap,is=new WeakMap,io=new WeakMap,ii=new WeakSet,ic=function(e){let t=Math.ceil(e.value.length/3936);if(1===t)return ts(this,ia,"f")[e.name]=e.value,[e];let r=[];for(let n=0;n<t;n++){let t=`${e.name}.${n}`,i=e.value.substr(3936*n,3936);r.push({...e,name:t,value:i}),ts(this,ia,"f")[t]=i}return ts(this,io,"f").debug("CHUNKING_SESSION_COOKIE",{message:"Session cookie exceeds allowed 4096 bytes.",emptyCookieSize:160,valueSize:e.value.length,chunks:r.map(e=>e.value.length+160)}),r},il=function(){let e={};for(let t in ts(this,ia,"f"))delete ts(this,ia,"f")?.[t],e[t]={name:t,value:"",options:{...ts(this,is,"f").options,maxAge:0}};return e};class tl extends Error{constructor(e,t){e instanceof Error?super(void 0,{cause:{err:e,...e.cause,...t}}):"string"==typeof e?(t instanceof Error&&(t={err:t,...t.cause}),super(e,t)):super(void 0,e),this.name=this.constructor.name,this.type=this.constructor.type??"AuthError",this.kind=this.constructor.kind??"error",Error.captureStackTrace?.(this,this.constructor);let r=`https://errors.authjs.dev#${this.type.toLowerCase()}`;this.message+=`${this.message?". ":""}Read more at ${r}`}}class tu extends tl{}tu.kind="signIn";class td extends tl{}td.type="AdapterError";class tf extends tl{}tf.type="AccessDenied";class th extends tl{}th.type="CallbackRouteError";class tp extends tl{}tp.type="ErrorPageLoop";class tg extends tl{}tg.type="EventError";class tm extends tl{}tm.type="InvalidCallbackUrl";class ty extends tu{constructor(){super(...arguments),this.code="credentials"}}ty.type="CredentialsSignin";class tb extends tl{}tb.type="InvalidEndpoints";class tw extends tl{}tw.type="InvalidCheck";class tv extends tl{}tv.type="JWTSessionError";class tx extends tl{}tx.type="MissingAdapter";class t_ extends tl{}t_.type="MissingAdapterMethods";class tS extends tl{}tS.type="MissingAuthorize";class tk extends tl{}tk.type="MissingSecret";class tE extends tu{}tE.type="OAuthAccountNotLinked";class tA extends tu{}tA.type="OAuthCallbackError";class tT extends tl{}tT.type="OAuthProfileParseError";class tP extends tl{}tP.type="SessionTokenError";class tC extends tu{}tC.type="OAuthSignInError";class tO extends tu{}tO.type="EmailSignInError";class tN extends tl{}tN.type="SignOutError";class tR extends tl{}tR.type="UnknownAction";class tI extends tl{}tI.type="UnsupportedStrategy";class t$ extends tl{}t$.type="InvalidProvider";class tj extends tl{}tj.type="UntrustedHost";class tU extends tl{}tU.type="Verification";class tL extends tu{}tL.type="MissingCSRF";let tD=new Set(["CredentialsSignin","OAuthAccountNotLinked","OAuthCallbackError","AccessDenied","Verification","MissingCSRF","AccountNotLinked","WebAuthnVerificationError"]);class tM extends tl{}tM.type="DuplicateConditionalUI";class tB extends tl{}tB.type="MissingWebAuthnAutocomplete";class tH extends tl{}tH.type="WebAuthnVerificationError";class tq extends tu{}tq.type="AccountNotLinked";class tz extends tl{}tz.type="ExperimentalFeatureNotEnabled";let tW=!1;function tF(e,t){try{return/^https?:/.test(new URL(e,e.startsWith("/")?t:void 0).protocol)}catch{return!1}}let tK=!1,tV=!1,tJ=!1,tQ=["createVerificationToken","useVerificationToken","getUserByEmail"],tG=["createUser","getUser","getUserByEmail","getUserByAccount","updateUser","linkAccount","createSession","getSessionAndUser","updateSession","deleteSession"],tX=["createUser","getUser","linkAccount","getAccount","getAuthenticator","createAuthenticator","listAuthenticatorsByUserId","updateAuthenticatorCounter"],tY=()=>{if("undefined"!=typeof globalThis)return globalThis;if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;throw Error("unable to locate global object")},tZ=async(e,t,r,n,i)=>{let{crypto:{subtle:a}}=tY();return new Uint8Array(await a.deriveBits({name:"HKDF",hash:`SHA-${e.substr(3)}`,salt:r,info:n},await a.importKey("raw",t,"HKDF",!1,["deriveBits"]),i<<3))};function t0(e,t){if("string"==typeof e)return new TextEncoder().encode(e);if(!(e instanceof Uint8Array))throw TypeError(`"${t}"" must be an instance of Uint8Array or a string`);return e}async function t1(e,t,r,n,i){return tZ(function(e){switch(e){case"sha256":case"sha384":case"sha512":case"sha1":return e;default:throw TypeError('unsupported "digest" value')}}(e),function(e){let t=t0(e,"ikm");if(!t.byteLength)throw TypeError('"ikm" must be at least one byte in length');return t}(t),t0(r,"salt"),function(e){let t=t0(e,"info");if(t.byteLength>1024)throw TypeError('"info" must not contain more than 1024 bytes');return t}(n),function(e,t){if("number"!=typeof e||!Number.isInteger(e)||e<1)throw TypeError('"keylen" must be a positive integer');if(e>255*(parseInt(t.substr(3),10)>>3||20))throw TypeError('"keylen" too large');return e}(i,e))}let t2=async(e,t)=>{let r=`SHA-${e.slice(-3)}`;return new Uint8Array(await crypto.subtle.digest(r,t))},t5=new TextEncoder,t6=new TextDecoder;function t3(...e){let t=new Uint8Array(e.reduce((e,{length:t})=>e+t,0)),r=0;for(let n of e)t.set(n,r),r+=n.length;return t}function t8(e,t,r){if(t<0||t>=0x100000000)throw RangeError(`value must be >= 0 and <= ${0x100000000-1}. Received ${t}`);e.set([t>>>24,t>>>16,t>>>8,255&t],r)}function t4(e){let t=Math.floor(e/0x100000000),r=new Uint8Array(8);return t8(r,t,0),t8(r,e%0x100000000,4),r}function t9(e){let t=new Uint8Array(4);return t8(t,e),t}function t7(e){if(Uint8Array.fromBase64)return Uint8Array.fromBase64("string"==typeof e?e:t6.decode(e),{alphabet:"base64url"});let t=e;t instanceof Uint8Array&&(t=t6.decode(t)),t=t.replace(/-/g,"+").replace(/_/g,"/").replace(/\s/g,"");try{return function(e){if(Uint8Array.fromBase64)return Uint8Array.fromBase64(e);let t=atob(e),r=new Uint8Array(t.length);for(let e=0;e<t.length;e++)r[e]=t.charCodeAt(e);return r}(t)}catch{throw TypeError("The input to be decoded is not correctly encoded.")}}function re(e){let t=e;return("string"==typeof t&&(t=t5.encode(t)),Uint8Array.prototype.toBase64)?t.toBase64({alphabet:"base64url",omitPadding:!0}):(function(e){if(Uint8Array.prototype.toBase64)return e.toBase64();let t=[];for(let r=0;r<e.length;r+=32768)t.push(String.fromCharCode.apply(null,e.subarray(r,r+32768)));return btoa(t.join(""))})(t).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}class rt extends Error{static code="ERR_JOSE_GENERIC";code="ERR_JOSE_GENERIC";constructor(e,t){super(e,t),this.name=this.constructor.name,Error.captureStackTrace?.(this,this.constructor)}}class rr extends rt{static code="ERR_JWT_CLAIM_VALIDATION_FAILED";code="ERR_JWT_CLAIM_VALIDATION_FAILED";claim;reason;payload;constructor(e,t,r="unspecified",n="unspecified"){super(e,{cause:{claim:r,reason:n,payload:t}}),this.claim=r,this.reason=n,this.payload=t}}class rn extends rt{static code="ERR_JWT_EXPIRED";code="ERR_JWT_EXPIRED";claim;reason;payload;constructor(e,t,r="unspecified",n="unspecified"){super(e,{cause:{claim:r,reason:n,payload:t}}),this.claim=r,this.reason=n,this.payload=t}}class ri extends rt{static code="ERR_JOSE_ALG_NOT_ALLOWED";code="ERR_JOSE_ALG_NOT_ALLOWED"}class ra extends rt{static code="ERR_JOSE_NOT_SUPPORTED";code="ERR_JOSE_NOT_SUPPORTED"}class rs extends rt{static code="ERR_JWE_DECRYPTION_FAILED";code="ERR_JWE_DECRYPTION_FAILED";constructor(e="decryption operation failed",t){super(e,t)}}class ro extends rt{static code="ERR_JWE_INVALID";code="ERR_JWE_INVALID"}class rc extends rt{static code="ERR_JWT_INVALID";code="ERR_JWT_INVALID"}class rl extends rt{static code="ERR_JWK_INVALID";code="ERR_JWK_INVALID"}class ru extends rt{[Symbol.asyncIterator];static code="ERR_JWKS_MULTIPLE_MATCHING_KEYS";code="ERR_JWKS_MULTIPLE_MATCHING_KEYS";constructor(e="multiple matching keys found in the JSON Web Key Set",t){super(e,t)}}function rd(e){if(!rf(e))throw Error("CryptoKey instance expected")}function rf(e){return e?.[Symbol.toStringTag]==="CryptoKey"}function rh(e){return e?.[Symbol.toStringTag]==="KeyObject"}let rp=e=>rf(e)||rh(e),rg=e=>{if(!function(e){return"object"==typeof e&&null!==e}(e)||"[object Object]"!==Object.prototype.toString.call(e))return!1;if(null===Object.getPrototypeOf(e))return!0;let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t};function rm(e){return rg(e)&&"string"==typeof e.kty}function ry(e,t,...r){if((r=r.filter(Boolean)).length>2){let t=r.pop();e+=`one of type ${r.join(", ")}, or ${t}.`}else 2===r.length?e+=`one of type ${r[0]} or ${r[1]}.`:e+=`of type ${r[0]}.`;return null==t?e+=` Received ${t}`:"function"==typeof t&&t.name?e+=` Received function ${t.name}`:"object"==typeof t&&null!=t&&t.constructor?.name&&(e+=` Received an instance of ${t.constructor.name}`),e}let rb=(e,...t)=>ry("Key must be ",e,...t);function rw(e,t,...r){return ry(`Key for the ${e} algorithm must be `,t,...r)}async function rv(e){if(rh(e)){if("secret"!==e.type)return e.export({format:"jwk"});e=e.export()}if(e instanceof Uint8Array)return{kty:"oct",k:re(e)};if(!rf(e))throw TypeError(rb(e,"CryptoKey","KeyObject","Uint8Array"));if(!e.extractable)throw TypeError("non-extractable CryptoKey cannot be exported as a JWK");let{ext:t,key_ops:r,alg:n,use:i,...a}=await crypto.subtle.exportKey("jwk",e);return a}async function rx(e){return rv(e)}let r_=(e,t)=>{if("string"!=typeof e||!e)throw new rl(`${t} missing or invalid`)};async function rS(e,t){let r,n;if(rm(e))r=e;else if(rp(e))r=await rx(e);else throw TypeError(rb(e,"CryptoKey","KeyObject","JSON Web Key"));if("sha256"!==(t??="sha256")&&"sha384"!==t&&"sha512"!==t)throw TypeError('digestAlgorithm must one of "sha256", "sha384", or "sha512"');switch(r.kty){case"EC":r_(r.crv,'"crv" (Curve) Parameter'),r_(r.x,'"x" (X Coordinate) Parameter'),r_(r.y,'"y" (Y Coordinate) Parameter'),n={crv:r.crv,kty:r.kty,x:r.x,y:r.y};break;case"OKP":r_(r.crv,'"crv" (Subtype of Key Pair) Parameter'),r_(r.x,'"x" (Public Key) Parameter'),n={crv:r.crv,kty:r.kty,x:r.x};break;case"RSA":r_(r.e,'"e" (Exponent) Parameter'),r_(r.n,'"n" (Modulus) Parameter'),n={e:r.e,kty:r.kty,n:r.n};break;case"oct":r_(r.k,'"k" (Key Value) Parameter'),n={k:r.k,kty:r.kty};break;default:throw new ra('"kty" (Key Type) Parameter missing or unsupported')}let i=t5.encode(JSON.stringify(n));return re(await t2(t,i))}let rk=Symbol();function rE(e){switch(e){case"A128GCM":case"A128GCMKW":case"A192GCM":case"A192GCMKW":case"A256GCM":case"A256GCMKW":return 96;case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return 128;default:throw new ra(`Unsupported JWE Algorithm: ${e}`)}}let rA=e=>crypto.getRandomValues(new Uint8Array(rE(e)>>3)),rT=(e,t)=>{if(t.length<<3!==rE(e))throw new ro("Invalid Initialization Vector length")},rP=(e,t)=>{let r=e.byteLength<<3;if(r!==t)throw new ro(`Invalid Content Encryption Key length. Expected ${t} bits, got ${r} bits`)};function rC(e,t="algorithm.name"){return TypeError(`CryptoKey does not support this operation, its ${t} must be ${e}`)}function rO(e,t){return e.name===t}function rN(e,t,r){switch(t){case"A128GCM":case"A192GCM":case"A256GCM":{if(!rO(e.algorithm,"AES-GCM"))throw rC("AES-GCM");let r=parseInt(t.slice(1,4),10);if(e.algorithm.length!==r)throw rC(r,"algorithm.length");break}case"A128KW":case"A192KW":case"A256KW":{if(!rO(e.algorithm,"AES-KW"))throw rC("AES-KW");let r=parseInt(t.slice(1,4),10);if(e.algorithm.length!==r)throw rC(r,"algorithm.length");break}case"ECDH":switch(e.algorithm.name){case"ECDH":case"X25519":break;default:throw rC("ECDH or X25519")}break;case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":if(!rO(e.algorithm,"PBKDF2"))throw rC("PBKDF2");break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":{if(!rO(e.algorithm,"RSA-OAEP"))throw rC("RSA-OAEP");let r=parseInt(t.slice(9),10)||1;if(parseInt(e.algorithm.hash.name.slice(4),10)!==r)throw rC(`SHA-${r}`,"algorithm.hash");break}default:throw TypeError("CryptoKey does not support this operation")}!function(e,t){if(t&&!e.usages.includes(t))throw TypeError(`CryptoKey does not support this operation, its usages must include ${t}.`)}(e,r)}async function rR(e,t,r,n,i){if(!(r instanceof Uint8Array))throw TypeError(rb(r,"Uint8Array"));let a=parseInt(e.slice(1,4),10),s=await crypto.subtle.importKey("raw",r.subarray(a>>3),"AES-CBC",!1,["encrypt"]),o=await crypto.subtle.importKey("raw",r.subarray(0,a>>3),{hash:`SHA-${a<<1}`,name:"HMAC"},!1,["sign"]),c=new Uint8Array(await crypto.subtle.encrypt({iv:n,name:"AES-CBC"},s,t)),l=t3(i,n,c,t4(i.length<<3));return{ciphertext:c,tag:new Uint8Array((await crypto.subtle.sign("HMAC",o,l)).slice(0,a>>3)),iv:n}}async function rI(e,t,r,n,i){let a;r instanceof Uint8Array?a=await crypto.subtle.importKey("raw",r,"AES-GCM",!1,["encrypt"]):(rN(r,e,"encrypt"),a=r);let s=new Uint8Array(await crypto.subtle.encrypt({additionalData:i,iv:n,name:"AES-GCM",tagLength:128},a,t)),o=s.slice(-16);return{ciphertext:s.slice(0,-16),tag:o,iv:n}}let r$=async(e,t,r,n,i)=>{if(!rf(r)&&!(r instanceof Uint8Array))throw TypeError(rb(r,"CryptoKey","KeyObject","Uint8Array","JSON Web Key"));switch(n?rT(e,n):n=rA(e),e){case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return r instanceof Uint8Array&&rP(r,parseInt(e.slice(-3),10)),rR(e,t,r,n,i);case"A128GCM":case"A192GCM":case"A256GCM":return r instanceof Uint8Array&&rP(r,parseInt(e.slice(1,4),10)),rI(e,t,r,n,i);default:throw new ra("Unsupported JWE Content Encryption Algorithm")}};function rj(e,t){if(e.algorithm.length!==parseInt(t.slice(1,4),10))throw TypeError(`Invalid key size for alg: ${t}`)}function rU(e,t,r){return e instanceof Uint8Array?crypto.subtle.importKey("raw",e,"AES-KW",!0,[r]):(rN(e,t,r),e)}async function rL(e,t,r){let n=await rU(t,e,"wrapKey");rj(n,e);let i=await crypto.subtle.importKey("raw",r,{hash:"SHA-256",name:"HMAC"},!0,["sign"]);return new Uint8Array(await crypto.subtle.wrapKey("raw",i,n,"AES-KW"))}async function rD(e,t,r){let n=await rU(t,e,"unwrapKey");rj(n,e);let i=await crypto.subtle.unwrapKey("raw",r,n,"AES-KW",{hash:"SHA-256",name:"HMAC"},!0,["sign"]);return new Uint8Array(await crypto.subtle.exportKey("raw",i))}function rM(e){return t3(t9(e.length),e)}async function rB(e,t,r){let n=Math.ceil((t>>3)/32),i=new Uint8Array(32*n);for(let t=0;t<n;t++){let n=new Uint8Array(4+e.length+r.length);n.set(t9(t+1)),n.set(e,4),n.set(r,4+e.length),i.set(await t2("sha256",n),32*t)}return i.slice(0,t>>3)}async function rH(e,t,r,n,i=new Uint8Array(0),a=new Uint8Array(0)){let s;rN(e,"ECDH"),rN(t,"ECDH","deriveBits");let o=t3(rM(t5.encode(r)),rM(i),rM(a),t9(n));return s="X25519"===e.algorithm.name?256:Math.ceil(parseInt(e.algorithm.namedCurve.slice(-3),10)/8)<<3,rB(new Uint8Array(await crypto.subtle.deriveBits({name:e.algorithm.name,public:e},t,s)),n,o)}function rq(e){switch(e.algorithm.namedCurve){case"P-256":case"P-384":case"P-521":return!0;default:return"X25519"===e.algorithm.name}}let rz=(e,t)=>t3(t5.encode(e),new Uint8Array([0]),t);async function rW(e,t,r,n){if(!(e instanceof Uint8Array)||e.length<8)throw new ro("PBES2 Salt Input must be 8 or more octets");let i=rz(t,e),a=parseInt(t.slice(13,16),10),s={hash:`SHA-${t.slice(8,11)}`,iterations:r,name:"PBKDF2",salt:i},o=await (n instanceof Uint8Array?crypto.subtle.importKey("raw",n,"PBKDF2",!1,["deriveBits"]):(rN(n,t,"deriveBits"),n));return new Uint8Array(await crypto.subtle.deriveBits(s,o,a))}async function rF(e,t,r,n=2048,i=crypto.getRandomValues(new Uint8Array(16))){let a=await rW(i,e,n,t);return{encryptedKey:await rL(e.slice(-6),a,r),p2c:n,p2s:re(i)}}async function rK(e,t,r,n,i){let a=await rW(i,e,n,t);return rD(e.slice(-6),a,r)}let rV=(e,t)=>{if(e.startsWith("RS")||e.startsWith("PS")){let{modulusLength:r}=t.algorithm;if("number"!=typeof r||r<2048)throw TypeError(`${e} requires key modulusLength to be 2048 bits or larger`)}},rJ=e=>{switch(e){case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":return"RSA-OAEP";default:throw new ra(`alg ${e} is not supported either by JOSE or your javascript runtime`)}};async function rQ(e,t,r){return rN(t,e,"encrypt"),rV(e,t),new Uint8Array(await crypto.subtle.encrypt(rJ(e),t,r))}async function rG(e,t,r){return rN(t,e,"decrypt"),rV(e,t),new Uint8Array(await crypto.subtle.decrypt(rJ(e),t,r))}let rX=async e=>{if(!e.alg)throw TypeError('"alg" argument is required when "jwk.alg" is not present');let{algorithm:t,keyUsages:r}=function(e){let t,r;switch(e.kty){case"RSA":switch(e.alg){case"PS256":case"PS384":case"PS512":t={name:"RSA-PSS",hash:`SHA-${e.alg.slice(-3)}`},r=e.d?["sign"]:["verify"];break;case"RS256":case"RS384":case"RS512":t={name:"RSASSA-PKCS1-v1_5",hash:`SHA-${e.alg.slice(-3)}`},r=e.d?["sign"]:["verify"];break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":t={name:"RSA-OAEP",hash:`SHA-${parseInt(e.alg.slice(-3),10)||1}`},r=e.d?["decrypt","unwrapKey"]:["encrypt","wrapKey"];break;default:throw new ra('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;case"EC":switch(e.alg){case"ES256":t={name:"ECDSA",namedCurve:"P-256"},r=e.d?["sign"]:["verify"];break;case"ES384":t={name:"ECDSA",namedCurve:"P-384"},r=e.d?["sign"]:["verify"];break;case"ES512":t={name:"ECDSA",namedCurve:"P-521"},r=e.d?["sign"]:["verify"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":t={name:"ECDH",namedCurve:e.crv},r=e.d?["deriveBits"]:[];break;default:throw new ra('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;case"OKP":switch(e.alg){case"Ed25519":case"EdDSA":t={name:"Ed25519"},r=e.d?["sign"]:["verify"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":t={name:e.crv},r=e.d?["deriveBits"]:[];break;default:throw new ra('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;default:throw new ra('Invalid or unsupported JWK "kty" (Key Type) Parameter value')}return{algorithm:t,keyUsages:r}}(e),n={...e};return delete n.alg,delete n.use,crypto.subtle.importKey("jwk",n,t,e.ext??!e.d,e.key_ops??r)},rY=async(e,t,r,n=!1)=>{let a=(i||=new WeakMap).get(e);if(a?.[r])return a[r];let s=await rX({...t,alg:r});return n&&Object.freeze(e),a?a[r]=s:i.set(e,{[r]:s}),s},rZ=(e,t)=>{let r;let n=(i||=new WeakMap).get(e);if(n?.[t])return n[t];let a="public"===e.type,s=!!a;if("x25519"===e.asymmetricKeyType){switch(t){case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":break;default:throw TypeError("given KeyObject instance cannot be used for this algorithm")}r=e.toCryptoKey(e.asymmetricKeyType,s,a?[]:["deriveBits"])}if("ed25519"===e.asymmetricKeyType){if("EdDSA"!==t&&"Ed25519"!==t)throw TypeError("given KeyObject instance cannot be used for this algorithm");r=e.toCryptoKey(e.asymmetricKeyType,s,[a?"verify":"sign"])}if("rsa"===e.asymmetricKeyType){let n;switch(t){case"RSA-OAEP":n="SHA-1";break;case"RS256":case"PS256":case"RSA-OAEP-256":n="SHA-256";break;case"RS384":case"PS384":case"RSA-OAEP-384":n="SHA-384";break;case"RS512":case"PS512":case"RSA-OAEP-512":n="SHA-512";break;default:throw TypeError("given KeyObject instance cannot be used for this algorithm")}if(t.startsWith("RSA-OAEP"))return e.toCryptoKey({name:"RSA-OAEP",hash:n},s,a?["encrypt"]:["decrypt"]);r=e.toCryptoKey({name:t.startsWith("PS")?"RSA-PSS":"RSASSA-PKCS1-v1_5",hash:n},s,[a?"verify":"sign"])}if("ec"===e.asymmetricKeyType){let n=new Map([["prime256v1","P-256"],["secp384r1","P-384"],["secp521r1","P-521"]]).get(e.asymmetricKeyDetails?.namedCurve);if(!n)throw TypeError("given KeyObject instance cannot be used for this algorithm");"ES256"===t&&"P-256"===n&&(r=e.toCryptoKey({name:"ECDSA",namedCurve:n},s,[a?"verify":"sign"])),"ES384"===t&&"P-384"===n&&(r=e.toCryptoKey({name:"ECDSA",namedCurve:n},s,[a?"verify":"sign"])),"ES512"===t&&"P-521"===n&&(r=e.toCryptoKey({name:"ECDSA",namedCurve:n},s,[a?"verify":"sign"])),t.startsWith("ECDH-ES")&&(r=e.toCryptoKey({name:"ECDH",namedCurve:n},s,a?[]:["deriveBits"]))}if(!r)throw TypeError("given KeyObject instance cannot be used for this algorithm");return n?n[t]=r:i.set(e,{[t]:r}),r},r0=async(e,t)=>{if(e instanceof Uint8Array||rf(e))return e;if(rh(e)){if("secret"===e.type)return e.export();if("toCryptoKey"in e&&"function"==typeof e.toCryptoKey)try{return rZ(e,t)}catch(e){if(e instanceof TypeError)throw e}let r=e.export({format:"jwk"});return rY(e,r,t)}if(rm(e))return e.k?t7(e.k):rY(e,e,t,!0);throw Error("unreachable")};function r1(e){switch(e){case"A128GCM":return 128;case"A192GCM":return 192;case"A256GCM":case"A128CBC-HS256":return 256;case"A192CBC-HS384":return 384;case"A256CBC-HS512":return 512;default:throw new ra(`Unsupported JWE Algorithm: ${e}`)}}let r2=e=>crypto.getRandomValues(new Uint8Array(r1(e)>>3));async function r5(e,t){if(!(e instanceof Uint8Array))throw TypeError("First argument must be a buffer");if(!(t instanceof Uint8Array))throw TypeError("Second argument must be a buffer");let r={name:"HMAC",hash:"SHA-256"},n=await crypto.subtle.generateKey(r,!1,["sign"]),i=new Uint8Array(await crypto.subtle.sign(r,n,e)),a=new Uint8Array(await crypto.subtle.sign(r,n,t)),s=0,o=-1;for(;++o<32;)s|=i[o]^a[o];return 0===s}async function r6(e,t,r,n,i,a){let s,o;if(!(t instanceof Uint8Array))throw TypeError(rb(t,"Uint8Array"));let c=parseInt(e.slice(1,4),10),l=await crypto.subtle.importKey("raw",t.subarray(c>>3),"AES-CBC",!1,["decrypt"]),u=await crypto.subtle.importKey("raw",t.subarray(0,c>>3),{hash:`SHA-${c<<1}`,name:"HMAC"},!1,["sign"]),d=t3(a,n,r,t4(a.length<<3)),f=new Uint8Array((await crypto.subtle.sign("HMAC",u,d)).slice(0,c>>3));try{s=await r5(i,f)}catch{}if(!s)throw new rs;try{o=new Uint8Array(await crypto.subtle.decrypt({iv:n,name:"AES-CBC"},l,r))}catch{}if(!o)throw new rs;return o}async function r3(e,t,r,n,i,a){let s;t instanceof Uint8Array?s=await crypto.subtle.importKey("raw",t,"AES-GCM",!1,["decrypt"]):(rN(t,e,"decrypt"),s=t);try{return new Uint8Array(await crypto.subtle.decrypt({additionalData:a,iv:n,name:"AES-GCM",tagLength:128},s,t3(r,i)))}catch{throw new rs}}let r8=async(e,t,r,n,i,a)=>{if(!rf(t)&&!(t instanceof Uint8Array))throw TypeError(rb(t,"CryptoKey","KeyObject","Uint8Array","JSON Web Key"));if(!n)throw new ro("JWE Initialization Vector missing");if(!i)throw new ro("JWE Authentication Tag missing");switch(rT(e,n),e){case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return t instanceof Uint8Array&&rP(t,parseInt(e.slice(-3),10)),r6(e,t,r,n,i,a);case"A128GCM":case"A192GCM":case"A256GCM":return t instanceof Uint8Array&&rP(t,parseInt(e.slice(1,4),10)),r3(e,t,r,n,i,a);default:throw new ra("Unsupported JWE Content Encryption Algorithm")}};async function r4(e,t,r,n){let i=e.slice(0,7),a=await r$(i,r,t,n,new Uint8Array(0));return{encryptedKey:a.ciphertext,iv:re(a.iv),tag:re(a.tag)}}async function r9(e,t,r,n,i){return r8(e.slice(0,7),t,r,n,i,new Uint8Array(0))}let r7=async(e,t,r,n,i={})=>{let a,s,o;switch(e){case"dir":o=r;break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":{let c;if(rd(r),!rq(r))throw new ra("ECDH with the provided key is not allowed or not supported by your javascript runtime");let{apu:l,apv:u}=i;c=i.epk?await r0(i.epk,e):(await crypto.subtle.generateKey(r.algorithm,!0,["deriveBits"])).privateKey;let{x:d,y:f,crv:h,kty:p}=await rx(c),g=await rH(r,c,"ECDH-ES"===e?t:e,"ECDH-ES"===e?r1(t):parseInt(e.slice(-5,-2),10),l,u);if(s={epk:{x:d,crv:h,kty:p}},"EC"===p&&(s.epk.y=f),l&&(s.apu=re(l)),u&&(s.apv=re(u)),"ECDH-ES"===e){o=g;break}o=n||r2(t);let m=e.slice(-6);a=await rL(m,g,o);break}case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":o=n||r2(t),rd(r),a=await rQ(e,r,o);break;case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":{o=n||r2(t);let{p2c:c,p2s:l}=i;({encryptedKey:a,...s}=await rF(e,r,o,c,l));break}case"A128KW":case"A192KW":case"A256KW":o=n||r2(t),a=await rL(e,r,o);break;case"A128GCMKW":case"A192GCMKW":case"A256GCMKW":{o=n||r2(t);let{iv:c}=i;({encryptedKey:a,...s}=await r4(e,r,o,c));break}default:throw new ra('Invalid or unsupported "alg" (JWE Algorithm) header value')}return{cek:o,encryptedKey:a,parameters:s}},ne=(...e)=>{let t;let r=e.filter(Boolean);if(0===r.length||1===r.length)return!0;for(let e of r){let r=Object.keys(e);if(!t||0===t.size){t=new Set(r);continue}for(let e of r){if(t.has(e))return!1;t.add(e)}}return!0},nt=(e,t,r,n,i)=>{let a;if(void 0!==i.crit&&n?.crit===void 0)throw new e('"crit" (Critical) Header Parameter MUST be integrity protected');if(!n||void 0===n.crit)return new Set;if(!Array.isArray(n.crit)||0===n.crit.length||n.crit.some(e=>"string"!=typeof e||0===e.length))throw new e('"crit" (Critical) Header Parameter MUST be an array of non-empty strings when present');for(let s of(a=void 0!==r?new Map([...Object.entries(r),...t.entries()]):t,n.crit)){if(!a.has(s))throw new ra(`Extension Header Parameter "${s}" is not recognized`);if(void 0===i[s])throw new e(`Extension Header Parameter "${s}" is missing`);if(a.get(s)&&void 0===n[s])throw new e(`Extension Header Parameter "${s}" MUST be integrity protected`)}return new Set(n.crit)},nr=e=>e?.[Symbol.toStringTag],nn=(e,t,r)=>{if(void 0!==t.use){let e;switch(r){case"sign":case"verify":e="sig";break;case"encrypt":case"decrypt":e="enc"}if(t.use!==e)throw TypeError(`Invalid key for this operation, its "use" must be "${e}" when present`)}if(void 0!==t.alg&&t.alg!==e)throw TypeError(`Invalid key for this operation, its "alg" must be "${e}" when present`);if(Array.isArray(t.key_ops)){let n;switch(!0){case"sign"===r||"verify"===r:case"dir"===e:case e.includes("CBC-HS"):n=r;break;case e.startsWith("PBES2"):n="deriveBits";break;case/^A\d{3}(?:GCM)?(?:KW)?$/.test(e):n=!e.includes("GCM")&&e.endsWith("KW")?"encrypt"===r?"wrapKey":"unwrapKey":r;break;case"encrypt"===r&&e.startsWith("RSA"):n="wrapKey";break;case"decrypt"===r:n=e.startsWith("RSA")?"unwrapKey":"deriveBits"}if(n&&t.key_ops?.includes?.(n)===!1)throw TypeError(`Invalid key for this operation, its "key_ops" must include "${n}" when present`)}return!0},ni=(e,t,r)=>{if(!(t instanceof Uint8Array)){if(rm(t)){if(function(e){return"oct"===e.kty&&"string"==typeof e.k}(t)&&nn(e,t,r))return;throw TypeError('JSON Web Key for symmetric algorithms must have JWK "kty" (Key Type) equal to "oct" and the JWK "k" (Key Value) present')}if(!rp(t))throw TypeError(rw(e,t,"CryptoKey","KeyObject","JSON Web Key","Uint8Array"));if("secret"!==t.type)throw TypeError(`${nr(t)} instances for symmetric algorithms must be of type "secret"`)}},na=(e,t,r)=>{if(rm(t))switch(r){case"decrypt":case"sign":if(function(e){return"oct"!==e.kty&&"string"==typeof e.d}(t)&&nn(e,t,r))return;throw TypeError("JSON Web Key for this operation be a private JWK");case"encrypt":case"verify":if(function(e){return"oct"!==e.kty&&void 0===e.d}(t)&&nn(e,t,r))return;throw TypeError("JSON Web Key for this operation be a public JWK")}if(!rp(t))throw TypeError(rw(e,t,"CryptoKey","KeyObject","JSON Web Key"));if("secret"===t.type)throw TypeError(`${nr(t)} instances for asymmetric algorithms must not be of type "secret"`);if("public"===t.type)switch(r){case"sign":throw TypeError(`${nr(t)} instances for asymmetric algorithm signing must be of type "private"`);case"decrypt":throw TypeError(`${nr(t)} instances for asymmetric algorithm decryption must be of type "private"`)}if("private"===t.type)switch(r){case"verify":throw TypeError(`${nr(t)} instances for asymmetric algorithm verifying must be of type "public"`);case"encrypt":throw TypeError(`${nr(t)} instances for asymmetric algorithm encryption must be of type "public"`)}},ns=(e,t,r)=>{e.startsWith("HS")||"dir"===e||e.startsWith("PBES2")||/^A(?:128|192|256)(?:GCM)?(?:KW)?$/.test(e)||/^A(?:128|192|256)CBC-HS(?:256|384|512)$/.test(e)?ni(e,t,r):na(e,t,r)};class no{#e;#t;#r;#n;#i;#a;#s;#o;constructor(e){if(!(e instanceof Uint8Array))throw TypeError("plaintext must be an instance of Uint8Array");this.#e=e}setKeyManagementParameters(e){if(this.#o)throw TypeError("setKeyManagementParameters can only be called once");return this.#o=e,this}setProtectedHeader(e){if(this.#t)throw TypeError("setProtectedHeader can only be called once");return this.#t=e,this}setSharedUnprotectedHeader(e){if(this.#r)throw TypeError("setSharedUnprotectedHeader can only be called once");return this.#r=e,this}setUnprotectedHeader(e){if(this.#n)throw TypeError("setUnprotectedHeader can only be called once");return this.#n=e,this}setAdditionalAuthenticatedData(e){return this.#i=e,this}setContentEncryptionKey(e){if(this.#a)throw TypeError("setContentEncryptionKey can only be called once");return this.#a=e,this}setInitializationVector(e){if(this.#s)throw TypeError("setInitializationVector can only be called once");return this.#s=e,this}async encrypt(e,t){let r,n,i,a,s;if(!this.#t&&!this.#n&&!this.#r)throw new ro("either setProtectedHeader, setUnprotectedHeader, or sharedUnprotectedHeader must be called before #encrypt()");if(!ne(this.#t,this.#n,this.#r))throw new ro("JWE Protected, JWE Shared Unprotected and JWE Per-Recipient Header Parameter names must be disjoint");let o={...this.#t,...this.#n,...this.#r};if(nt(ro,new Map,t?.crit,this.#t,o),void 0!==o.zip)throw new ra('JWE "zip" (Compression Algorithm) Header Parameter is not supported.');let{alg:c,enc:l}=o;if("string"!=typeof c||!c)throw new ro('JWE "alg" (Algorithm) Header Parameter missing or invalid');if("string"!=typeof l||!l)throw new ro('JWE "enc" (Encryption Algorithm) Header Parameter missing or invalid');if(this.#a&&("dir"===c||"ECDH-ES"===c))throw TypeError(`setContentEncryptionKey cannot be called with JWE "alg" (Algorithm) Header ${c}`);ns("dir"===c?l:c,e,"encrypt");{let i;let a=await r0(e,c);({cek:n,encryptedKey:r,parameters:i}=await r7(c,l,a,this.#a,this.#o)),i&&(t&&rk in t?this.#n?this.#n={...this.#n,...i}:this.setUnprotectedHeader(i):this.#t?this.#t={...this.#t,...i}:this.setProtectedHeader(i))}a=this.#t?t5.encode(re(JSON.stringify(this.#t))):t5.encode(""),this.#i?(s=re(this.#i),i=t3(a,t5.encode("."),t5.encode(s))):i=a;let{ciphertext:u,tag:d,iv:f}=await r$(l,this.#e,n,this.#s,i),h={ciphertext:re(u)};return f&&(h.iv=re(f)),d&&(h.tag=re(d)),r&&(h.encrypted_key=re(r)),s&&(h.aad=s),this.#t&&(h.protected=t6.decode(a)),this.#r&&(h.unprotected=this.#r),this.#n&&(h.header=this.#n),h}}class nc{#c;constructor(e){this.#c=new no(e)}setContentEncryptionKey(e){return this.#c.setContentEncryptionKey(e),this}setInitializationVector(e){return this.#c.setInitializationVector(e),this}setProtectedHeader(e){return this.#c.setProtectedHeader(e),this}setKeyManagementParameters(e){return this.#c.setKeyManagementParameters(e),this}async encrypt(e,t){let r=await this.#c.encrypt(e,t);return[r.protected,r.encrypted_key,r.iv,r.ciphertext,r.tag].join(".")}}let nl=e=>Math.floor(e.getTime()/1e3),nu=/^(\+|\-)? ?(\d+|\d+\.\d+) ?(seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)(?: (ago|from now))?$/i,nd=e=>{let t;let r=nu.exec(e);if(!r||r[4]&&r[1])throw TypeError("Invalid time period format");let n=parseFloat(r[2]);switch(r[3].toLowerCase()){case"sec":case"secs":case"second":case"seconds":case"s":t=Math.round(n);break;case"minute":case"minutes":case"min":case"mins":case"m":t=Math.round(60*n);break;case"hour":case"hours":case"hr":case"hrs":case"h":t=Math.round(3600*n);break;case"day":case"days":case"d":t=Math.round(86400*n);break;case"week":case"weeks":case"w":t=Math.round(604800*n);break;default:t=Math.round(0x1e187e0*n)}return"-"===r[1]||"ago"===r[4]?-t:t};function nf(e,t){if(!Number.isFinite(t))throw TypeError(`Invalid ${e} input`);return t}let nh=e=>e.includes("/")?e.toLowerCase():`application/${e.toLowerCase()}`,np=(e,t)=>"string"==typeof e?t.includes(e):!!Array.isArray(e)&&t.some(Set.prototype.has.bind(new Set(e)));class ng{#l;constructor(e){if(!rg(e))throw TypeError("JWT Claims Set MUST be an object");this.#l=structuredClone(e)}data(){return t5.encode(JSON.stringify(this.#l))}get iss(){return this.#l.iss}set iss(e){this.#l.iss=e}get sub(){return this.#l.sub}set sub(e){this.#l.sub=e}get aud(){return this.#l.aud}set aud(e){this.#l.aud=e}set jti(e){this.#l.jti=e}set nbf(e){"number"==typeof e?this.#l.nbf=nf("setNotBefore",e):e instanceof Date?this.#l.nbf=nf("setNotBefore",nl(e)):this.#l.nbf=nl(new Date)+nd(e)}set exp(e){"number"==typeof e?this.#l.exp=nf("setExpirationTime",e):e instanceof Date?this.#l.exp=nf("setExpirationTime",nl(e)):this.#l.exp=nl(new Date)+nd(e)}set iat(e){void 0===e?this.#l.iat=nl(new Date):e instanceof Date?this.#l.iat=nf("setIssuedAt",nl(e)):"string"==typeof e?this.#l.iat=nf("setIssuedAt",nl(new Date)+nd(e)):this.#l.iat=nf("setIssuedAt",e)}}class nm{#a;#s;#o;#t;#u;#d;#f;#h;constructor(e={}){this.#h=new ng(e)}setIssuer(e){return this.#h.iss=e,this}setSubject(e){return this.#h.sub=e,this}setAudience(e){return this.#h.aud=e,this}setJti(e){return this.#h.jti=e,this}setNotBefore(e){return this.#h.nbf=e,this}setExpirationTime(e){return this.#h.exp=e,this}setIssuedAt(e){return this.#h.iat=e,this}setProtectedHeader(e){if(this.#t)throw TypeError("setProtectedHeader can only be called once");return this.#t=e,this}setKeyManagementParameters(e){if(this.#o)throw TypeError("setKeyManagementParameters can only be called once");return this.#o=e,this}setContentEncryptionKey(e){if(this.#a)throw TypeError("setContentEncryptionKey can only be called once");return this.#a=e,this}setInitializationVector(e){if(this.#s)throw TypeError("setInitializationVector can only be called once");return this.#s=e,this}replicateIssuerAsHeader(){return this.#u=!0,this}replicateSubjectAsHeader(){return this.#d=!0,this}replicateAudienceAsHeader(){return this.#f=!0,this}async encrypt(e,t){let r=new nc(this.#h.data());return this.#t&&(this.#u||this.#d||this.#f)&&(this.#t={...this.#t,iss:this.#u?this.#h.iss:void 0,sub:this.#d?this.#h.sub:void 0,aud:this.#f?this.#h.aud:void 0}),r.setProtectedHeader(this.#t),this.#s&&r.setInitializationVector(this.#s),this.#a&&r.setContentEncryptionKey(this.#a),this.#o&&r.setKeyManagementParameters(this.#o),r.encrypt(e,t)}}async function ny(e,t,r){let n;if(!rg(e))throw TypeError("JWK must be an object");switch(t??=e.alg,n??=r?.extractable??e.ext,e.kty){case"oct":if("string"!=typeof e.k||!e.k)throw TypeError('missing "k" (Key Value) Parameter value');return t7(e.k);case"RSA":if("oth"in e&&void 0!==e.oth)throw new ra('RSA JWK "oth" (Other Primes Info) Parameter value is not supported');case"EC":case"OKP":return rX({...e,alg:t,ext:n});default:throw new ra('Unsupported "kty" (Key Type) Parameter value')}}let nb=async(e,t,r,n,i)=>{switch(e){case"dir":if(void 0!==r)throw new ro("Encountered unexpected JWE Encrypted Key");return t;case"ECDH-ES":if(void 0!==r)throw new ro("Encountered unexpected JWE Encrypted Key");case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":{let i,a;if(!rg(n.epk))throw new ro('JOSE Header "epk" (Ephemeral Public Key) missing or invalid');if(rd(t),!rq(t))throw new ra("ECDH with the provided key is not allowed or not supported by your javascript runtime");let s=await ny(n.epk,e);if(rd(s),void 0!==n.apu){if("string"!=typeof n.apu)throw new ro('JOSE Header "apu" (Agreement PartyUInfo) invalid');try{i=t7(n.apu)}catch{throw new ro("Failed to base64url decode the apu")}}if(void 0!==n.apv){if("string"!=typeof n.apv)throw new ro('JOSE Header "apv" (Agreement PartyVInfo) invalid');try{a=t7(n.apv)}catch{throw new ro("Failed to base64url decode the apv")}}let o=await rH(s,t,"ECDH-ES"===e?n.enc:e,"ECDH-ES"===e?r1(n.enc):parseInt(e.slice(-5,-2),10),i,a);if("ECDH-ES"===e)return o;if(void 0===r)throw new ro("JWE Encrypted Key missing");return rD(e.slice(-6),o,r)}case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":if(void 0===r)throw new ro("JWE Encrypted Key missing");return rd(t),rG(e,t,r);case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":{let a;if(void 0===r)throw new ro("JWE Encrypted Key missing");if("number"!=typeof n.p2c)throw new ro('JOSE Header "p2c" (PBES2 Count) missing or invalid');let s=i?.maxPBES2Count||1e4;if(n.p2c>s)throw new ro('JOSE Header "p2c" (PBES2 Count) out is of acceptable bounds');if("string"!=typeof n.p2s)throw new ro('JOSE Header "p2s" (PBES2 Salt) missing or invalid');try{a=t7(n.p2s)}catch{throw new ro("Failed to base64url decode the p2s")}return rK(e,t,r,n.p2c,a)}case"A128KW":case"A192KW":case"A256KW":if(void 0===r)throw new ro("JWE Encrypted Key missing");return rD(e,t,r);case"A128GCMKW":case"A192GCMKW":case"A256GCMKW":{let i,a;if(void 0===r)throw new ro("JWE Encrypted Key missing");if("string"!=typeof n.iv)throw new ro('JOSE Header "iv" (Initialization Vector) missing or invalid');if("string"!=typeof n.tag)throw new ro('JOSE Header "tag" (Authentication Tag) missing or invalid');try{i=t7(n.iv)}catch{throw new ro("Failed to base64url decode the iv")}try{a=t7(n.tag)}catch{throw new ro("Failed to base64url decode the tag")}return r9(e,t,r,i,a)}default:throw new ra('Invalid or unsupported "alg" (JWE Algorithm) header value')}},nw=(e,t)=>{if(void 0!==t&&(!Array.isArray(t)||t.some(e=>"string"!=typeof e)))throw TypeError(`"${e}" option must be an array of strings`);if(t)return new Set(t)};async function nv(e,t,r){let n,i,a,s,o,c,l;if(!rg(e))throw new ro("Flattened JWE must be an object");if(void 0===e.protected&&void 0===e.header&&void 0===e.unprotected)throw new ro("JOSE Header missing");if(void 0!==e.iv&&"string"!=typeof e.iv)throw new ro("JWE Initialization Vector incorrect type");if("string"!=typeof e.ciphertext)throw new ro("JWE Ciphertext missing or incorrect type");if(void 0!==e.tag&&"string"!=typeof e.tag)throw new ro("JWE Authentication Tag incorrect type");if(void 0!==e.protected&&"string"!=typeof e.protected)throw new ro("JWE Protected Header incorrect type");if(void 0!==e.encrypted_key&&"string"!=typeof e.encrypted_key)throw new ro("JWE Encrypted Key incorrect type");if(void 0!==e.aad&&"string"!=typeof e.aad)throw new ro("JWE AAD incorrect type");if(void 0!==e.header&&!rg(e.header))throw new ro("JWE Shared Unprotected Header incorrect type");if(void 0!==e.unprotected&&!rg(e.unprotected))throw new ro("JWE Per-Recipient Unprotected Header incorrect type");if(e.protected)try{let t=t7(e.protected);n=JSON.parse(t6.decode(t))}catch{throw new ro("JWE Protected Header is invalid")}if(!ne(n,e.header,e.unprotected))throw new ro("JWE Protected, JWE Unprotected Header, and JWE Per-Recipient Unprotected Header Parameter names must be disjoint");let u={...n,...e.header,...e.unprotected};if(nt(ro,new Map,r?.crit,n,u),void 0!==u.zip)throw new ra('JWE "zip" (Compression Algorithm) Header Parameter is not supported.');let{alg:d,enc:f}=u;if("string"!=typeof d||!d)throw new ro("missing JWE Algorithm (alg) in JWE Header");if("string"!=typeof f||!f)throw new ro("missing JWE Encryption Algorithm (enc) in JWE Header");let h=r&&nw("keyManagementAlgorithms",r.keyManagementAlgorithms),p=r&&nw("contentEncryptionAlgorithms",r.contentEncryptionAlgorithms);if(h&&!h.has(d)||!h&&d.startsWith("PBES2"))throw new ri('"alg" (Algorithm) Header Parameter value not allowed');if(p&&!p.has(f))throw new ri('"enc" (Encryption Algorithm) Header Parameter value not allowed');if(void 0!==e.encrypted_key)try{i=t7(e.encrypted_key)}catch{throw new ro("Failed to base64url decode the encrypted_key")}let g=!1;"function"==typeof t&&(t=await t(n,e),g=!0),ns("dir"===d?f:d,t,"decrypt");let m=await r0(t,d);try{a=await nb(d,m,i,u,r)}catch(e){if(e instanceof TypeError||e instanceof ro||e instanceof ra)throw e;a=r2(f)}if(void 0!==e.iv)try{s=t7(e.iv)}catch{throw new ro("Failed to base64url decode the iv")}if(void 0!==e.tag)try{o=t7(e.tag)}catch{throw new ro("Failed to base64url decode the tag")}let y=t5.encode(e.protected??"");c=void 0!==e.aad?t3(y,t5.encode("."),t5.encode(e.aad)):y;try{l=t7(e.ciphertext)}catch{throw new ro("Failed to base64url decode the ciphertext")}let b={plaintext:await r8(f,a,l,s,o,c)};if(void 0!==e.protected&&(b.protectedHeader=n),void 0!==e.aad)try{b.additionalAuthenticatedData=t7(e.aad)}catch{throw new ro("Failed to base64url decode the aad")}return(void 0!==e.unprotected&&(b.sharedUnprotectedHeader=e.unprotected),void 0!==e.header&&(b.unprotectedHeader=e.header),g)?{...b,key:m}:b}async function nx(e,t,r){if(e instanceof Uint8Array&&(e=t6.decode(e)),"string"!=typeof e)throw new ro("Compact JWE must be a string or Uint8Array");let{0:n,1:i,2:a,3:s,4:o,length:c}=e.split(".");if(5!==c)throw new ro("Invalid Compact JWE");let l=await nv({ciphertext:s,iv:a||void 0,protected:n,tag:o||void 0,encrypted_key:i||void 0},t,r),u={plaintext:l.plaintext,protectedHeader:l.protectedHeader};return"function"==typeof t?{...u,key:l.key}:u}async function n_(e,t,r){let n=await nx(e,t,r),i=function(e,t,r={}){let n,i;try{n=JSON.parse(t6.decode(t))}catch{}if(!rg(n))throw new rc("JWT Claims Set must be a top-level JSON object");let{typ:a}=r;if(a&&("string"!=typeof e.typ||nh(e.typ)!==nh(a)))throw new rr('unexpected "typ" JWT header value',n,"typ","check_failed");let{requiredClaims:s=[],issuer:o,subject:c,audience:l,maxTokenAge:u}=r,d=[...s];for(let e of(void 0!==u&&d.push("iat"),void 0!==l&&d.push("aud"),void 0!==c&&d.push("sub"),void 0!==o&&d.push("iss"),new Set(d.reverse())))if(!(e in n))throw new rr(`missing required "${e}" claim`,n,e,"missing");if(o&&!(Array.isArray(o)?o:[o]).includes(n.iss))throw new rr('unexpected "iss" claim value',n,"iss","check_failed");if(c&&n.sub!==c)throw new rr('unexpected "sub" claim value',n,"sub","check_failed");if(l&&!np(n.aud,"string"==typeof l?[l]:l))throw new rr('unexpected "aud" claim value',n,"aud","check_failed");switch(typeof r.clockTolerance){case"string":i=nd(r.clockTolerance);break;case"number":i=r.clockTolerance;break;case"undefined":i=0;break;default:throw TypeError("Invalid clockTolerance option type")}let{currentDate:f}=r,h=nl(f||new Date);if((void 0!==n.iat||u)&&"number"!=typeof n.iat)throw new rr('"iat" claim must be a number',n,"iat","invalid");if(void 0!==n.nbf){if("number"!=typeof n.nbf)throw new rr('"nbf" claim must be a number',n,"nbf","invalid");if(n.nbf>h+i)throw new rr('"nbf" claim timestamp check failed',n,"nbf","check_failed")}if(void 0!==n.exp){if("number"!=typeof n.exp)throw new rr('"exp" claim must be a number',n,"exp","invalid");if(n.exp<=h-i)throw new rn('"exp" claim timestamp check failed',n,"exp","check_failed")}if(u){let e=h-n.iat;if(e-i>("number"==typeof u?u:nd(u)))throw new rn('"iat" claim timestamp check failed (too far in the past)',n,"iat","check_failed");if(e<0-i)throw new rr('"iat" claim timestamp check failed (it should be in the past)',n,"iat","check_failed")}return n}(n.protectedHeader,n.plaintext,r),{protectedHeader:a}=n;if(void 0!==a.iss&&a.iss!==i.iss)throw new rr('replicated "iss" claim header parameter mismatch',i,"iss","mismatch");if(void 0!==a.sub&&a.sub!==i.sub)throw new rr('replicated "sub" claim header parameter mismatch',i,"sub","mismatch");if(void 0!==a.aud&&JSON.stringify(a.aud)!==JSON.stringify(i.aud))throw new rr('replicated "aud" claim header parameter mismatch',i,"aud","mismatch");let s={payload:i,protectedHeader:a};return"function"==typeof t?{...s,key:n.key}:s}let nS=/^[!#$%&'*+\-.^_`|~0-9A-Za-z]+$/,nk=/^("?)[\u0021\u0023-\u002B\u002D-\u003A\u003C-\u005B\u005D-\u007E]*\1$/,nE=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,nA=/^[\u0020-\u003A\u003D-\u007E]*$/,nT=Object.prototype.toString,nP=(()=>{let e=function(){};return e.prototype=Object.create(null),e})();function nC(e,t){let r=new nP,n=e.length;if(n<2)return r;let i=t?.decode||nI,a=0;do{let t=e.indexOf("=",a);if(-1===t)break;let s=e.indexOf(";",a),o=-1===s?n:s;if(t>o){a=e.lastIndexOf(";",t-1)+1;continue}let c=nO(e,a,t),l=nN(e,t,c),u=e.slice(c,l);if(void 0===r[u]){let n=nO(e,t+1,o),a=nN(e,o,n),s=i(e.slice(n,a));r[u]=s}a=o+1}while(a<n);return r}function nO(e,t,r){do{let r=e.charCodeAt(t);if(32!==r&&9!==r)return t}while(++t<r);return r}function nN(e,t,r){for(;t>r;){let r=e.charCodeAt(--t);if(32!==r&&9!==r)return t+1}return r}function nR(e,t,r){let n=r?.encode||encodeURIComponent;if(!nS.test(e))throw TypeError(`argument name is invalid: ${e}`);let i=n(t);if(!nk.test(i))throw TypeError(`argument val is invalid: ${t}`);let a=e+"="+i;if(!r)return a;if(void 0!==r.maxAge){if(!Number.isInteger(r.maxAge))throw TypeError(`option maxAge is invalid: ${r.maxAge}`);a+="; Max-Age="+r.maxAge}if(r.domain){if(!nE.test(r.domain))throw TypeError(`option domain is invalid: ${r.domain}`);a+="; Domain="+r.domain}if(r.path){if(!nA.test(r.path))throw TypeError(`option path is invalid: ${r.path}`);a+="; Path="+r.path}if(r.expires){var s;if(s=r.expires,"[object Date]"!==nT.call(s)||!Number.isFinite(r.expires.valueOf()))throw TypeError(`option expires is invalid: ${r.expires}`);a+="; Expires="+r.expires.toUTCString()}if(r.httpOnly&&(a+="; HttpOnly"),r.secure&&(a+="; Secure"),r.partitioned&&(a+="; Partitioned"),r.priority)switch("string"==typeof r.priority?r.priority.toLowerCase():void 0){case"low":a+="; Priority=Low";break;case"medium":a+="; Priority=Medium";break;case"high":a+="; Priority=High";break;default:throw TypeError(`option priority is invalid: ${r.priority}`)}if(r.sameSite)switch("string"==typeof r.sameSite?r.sameSite.toLowerCase():r.sameSite){case!0:case"strict":a+="; SameSite=Strict";break;case"lax":a+="; SameSite=Lax";break;case"none":a+="; SameSite=None";break;default:throw TypeError(`option sameSite is invalid: ${r.sameSite}`)}return a}function nI(e){if(-1===e.indexOf("%"))return e;try{return decodeURIComponent(e)}catch(t){return e}}let{q:n$}=u,nj=()=>Date.now()/1e3|0,nU="A256CBC-HS512";async function nL(e){let{token:t={},secret:r,maxAge:n=2592e3,salt:i}=e,a=Array.isArray(r)?r:[r],s=await nM(nU,a[0],i),o=await rS({kty:"oct",k:re(s)},`sha${s.byteLength<<3}`);return await new nm(t).setProtectedHeader({alg:"dir",enc:nU,kid:o}).setIssuedAt().setExpirationTime(nj()+n).setJti(crypto.randomUUID()).encrypt(s)}async function nD(e){let{token:t,secret:r,salt:n}=e,i=Array.isArray(r)?r:[r];if(!t)return null;let{payload:a}=await n_(t,async({kid:e,enc:t})=>{for(let r of i){let i=await nM(t,r,n);if(void 0===e||e===await rS({kty:"oct",k:re(i)},`sha${i.byteLength<<3}`))return i}throw Error("no matching decryption secret")},{clockTolerance:15,keyManagementAlgorithms:["dir"],contentEncryptionAlgorithms:[nU,"A256GCM"]});return a}async function nM(e,t,r){let n;switch(e){case"A256CBC-HS512":n=64;break;case"A256GCM":n=32;break;default:throw Error("Unsupported JWT Content Encryption Algorithm")}return await t1("sha256",t,r,`Auth.js Generated Encryption Key (${r})`,n)}async function nB({options:e,paramValue:t,cookieValue:r}){let{url:n,callbacks:i}=e,a=n.origin;return t?a=await i.redirect({url:t,baseUrl:n.origin}):r&&(a=await i.redirect({url:r,baseUrl:n.origin})),{callbackUrl:a,callbackUrlCookie:a!==r?a:void 0}}let nH="\x1b[31m",nq="\x1b[0m",nz={error(e){let t=e instanceof tl?e.type:e.name;if(console.error(`${nH}[auth][error]${nq} ${t}: ${e.message}`),e.cause&&"object"==typeof e.cause&&"err"in e.cause&&e.cause.err instanceof Error){let{err:t,...r}=e.cause;console.error(`${nH}[auth][cause]${nq}:`,t.stack),r&&console.error(`${nH}[auth][details]${nq}:`,JSON.stringify(r,null,2))}else e.stack&&console.error(e.stack.replace(/.*/,"").substring(1))},warn(e){console.warn(`\x1b[33m[auth][warn][${e}]${nq}`,"Read more: https://warnings.authjs.dev")},debug(e,t){console.log(`\x1b[90m[auth][debug]:${nq} ${e}`,JSON.stringify(t,null,2))}};function nW(e){let t={...nz};return e.debug||(t.debug=()=>{}),e.logger?.error&&(t.error=e.logger.error),e.logger?.warn&&(t.warn=e.logger.warn),e.logger?.debug&&(t.debug=e.logger.debug),e.logger??(e.logger=t),t}let nF=["providers","session","csrf","signin","signout","callback","verify-request","error","webauthn-options"],{q:nK,l:nV}=u;async function nJ(e){if(!("body"in e)||!e.body||"POST"!==e.method)return;let t=e.headers.get("content-type");return t?.includes("application/json")?await e.json():t?.includes("application/x-www-form-urlencoded")?Object.fromEntries(new URLSearchParams(await e.text())):void 0}async function nQ(e,t){try{if("GET"!==e.method&&"POST"!==e.method)throw new tR("Only GET and POST requests are supported");t.basePath??(t.basePath="/auth");let r=new URL(e.url),{action:n,providerId:i}=function(e,t){let r=e.match(RegExp(`^${t}(.+)`));if(null===r)throw new tR(`Cannot parse action at ${e}`);let n=r.at(-1).replace(/^\//,"").split("/").filter(Boolean);if(1!==n.length&&2!==n.length)throw new tR(`Cannot parse action at ${e}`);let[i,a]=n;if(!nF.includes(i)||a&&!["signin","callback","webauthn-options"].includes(i))throw new tR(`Cannot parse action at ${e}`);return{action:i,providerId:a}}(r.pathname,t.basePath);return{url:r,action:n,providerId:i,method:e.method,headers:Object.fromEntries(e.headers),body:e.body?await nJ(e):void 0,cookies:nK(e.headers.get("cookie")??"")??{},error:r.searchParams.get("error")??void 0,query:Object.fromEntries(r.searchParams)}}catch(n){let r=nW(t);r.error(n),r.debug("request",e)}}function nG(e){let t=new Headers(e.headers);e.cookies?.forEach(e=>{let{name:r,value:n,options:i}=e,a=nV(r,n,i);t.has("Set-Cookie")?t.append("Set-Cookie",a):t.set("Set-Cookie",a)});let r=e.body;"application/json"===t.get("content-type")?r=JSON.stringify(e.body):"application/x-www-form-urlencoded"===t.get("content-type")&&(r=new URLSearchParams(e.body).toString());let n=new Response(r,{headers:t,status:e.redirect?302:e.status??200});return e.redirect&&n.headers.set("Location",e.redirect),n}async function nX(e){let t=new TextEncoder().encode(e);return Array.from(new Uint8Array(await crypto.subtle.digest("SHA-256",t))).map(e=>e.toString(16).padStart(2,"0")).join("").toString()}function nY(e){let t=e=>("0"+e.toString(16)).slice(-2);return Array.from(crypto.getRandomValues(new Uint8Array(e))).reduce((e,r)=>e+t(r),"")}async function nZ({options:e,cookieValue:t,isPost:r,bodyValue:n}){if(t){let[i,a]=t.split("|");if(a===await nX(`${i}${e.secret}`))return{csrfTokenVerified:r&&i===n,csrfToken:i}}let i=nY(32),a=await nX(`${i}${e.secret}`);return{cookie:`${i}|${a}`,csrfToken:i}}function n0(e,t){if(!t)throw new tL(`CSRF token was missing during an action ${e}`)}function n1(e){return null!==e&&"object"==typeof e}function n2(e,...t){if(!t.length)return e;let r=t.shift();if(n1(e)&&n1(r))for(let t in r)n1(r[t])?(n1(e[t])||(e[t]=Array.isArray(r[t])?[]:{}),n2(e[t],r[t])):void 0!==r[t]&&(e[t]=r[t]);return n2(e,...t)}let n5=Symbol("skip-csrf-check"),n6=Symbol("return-type-raw"),n3=Symbol("custom-fetch"),n8=Symbol("conform-internal"),n4=e=>n7({id:e.sub??e.id??crypto.randomUUID(),name:e.name??e.nickname??e.preferred_username,email:e.email,image:e.picture}),n9=e=>n7({access_token:e.access_token,id_token:e.id_token,refresh_token:e.refresh_token,expires_at:e.expires_at,scope:e.scope,token_type:e.token_type,session_state:e.session_state});function n7(e){let t={};for(let[r,n]of Object.entries(e))void 0!==n&&(t[r]=n);return t}function ie(e,t){if(!e&&t)return;if("string"==typeof e)return{url:new URL(e)};let r=new URL(e?.url??"https://authjs.dev");if(e?.params!=null)for(let[t,n]of Object.entries(e.params))"claims"===t&&(n=JSON.stringify(n)),r.searchParams.set(t,String(n));return{url:r,request:e?.request,conform:e?.conform,...e?.clientPrivateKey?{clientPrivateKey:e?.clientPrivateKey}:null}}let it={signIn:()=>!0,redirect:({url:e,baseUrl:t})=>e.startsWith("/")?`${t}${e}`:new URL(e).origin===t?e:t,session:({session:e})=>({user:{name:e.user?.name,email:e.user?.email,image:e.user?.image},expires:e.expires?.toISOString?.()??e.expires}),jwt:({token:e})=>e};async function ir({authOptions:e,providerId:t,action:r,url:n,cookies:i,callbackUrl:a,csrfToken:s,csrfDisabled:o,isPost:c}){var l;let u=nW(e),{providers:d,provider:f}=function(e){let{providerId:t,config:r}=e,n=new URL(r.basePath??"/auth",e.url.origin),i=r.providers.map(e=>{let t="function"==typeof e?e():e,{options:i,...a}=t,s=i?.id??a.id,o=n2(a,i,{signinUrl:`${n}/signin/${s}`,callbackUrl:`${n}/callback/${s}`});if("oauth"===t.type||"oidc"===t.type){o.redirectProxyUrl??(o.redirectProxyUrl=i?.redirectProxyUrl??r.redirectProxyUrl);let e=function(e){e.issuer&&(e.wellKnown??(e.wellKnown=`${e.issuer}/.well-known/openid-configuration`));let t=ie(e.authorization,e.issuer);t&&!t.url?.searchParams.has("scope")&&t.url.searchParams.set("scope","openid profile email");let r=ie(e.token,e.issuer),n=ie(e.userinfo,e.issuer),i=e.checks??["pkce"];return e.redirectProxyUrl&&(i.includes("state")||i.push("state"),e.redirectProxyUrl=`${e.redirectProxyUrl}/callback/${e.id}`),{...e,authorization:t,token:r,checks:i,userinfo:n,profile:e.profile??n4,account:e.account??n9}}(o);return e.authorization?.url.searchParams.get("response_mode")==="form_post"&&delete e.redirectProxyUrl,e[n3]??(e[n3]=i?.[n3]),e}return o}),a=i.find(({id:e})=>e===t);if(t&&!a){let e=i.map(e=>e.id).join(", ");throw Error(`Provider with id "${t}" not found. Available providers: [${e}].`)}return{providers:i,provider:a}}({url:n,providerId:t,config:e}),h=!1;if((f?.type==="oauth"||f?.type==="oidc")&&f.redirectProxyUrl)try{h=new URL(f.redirectProxyUrl).origin===n.origin}catch{throw TypeError(`redirectProxyUrl must be a valid URL. Received: ${f.redirectProxyUrl}`)}let p={debug:!1,pages:{},theme:{colorScheme:"auto",logo:"",brandColor:"",buttonText:""},...e,url:n,action:r,provider:f,cookies:n2(to(e.useSecureCookies??"https:"===n.protocol),e.cookies),providers:d,session:{strategy:e.adapter?"database":"jwt",maxAge:2592e3,updateAge:86400,generateSessionToken:()=>crypto.randomUUID(),...e.session},jwt:{secret:e.secret,maxAge:e.session?.maxAge??2592e3,encode:nL,decode:nD,...e.jwt},events:Object.keys(l=e.events??{}).reduce((e,t)=>(e[t]=async(...e)=>{try{let r=l[t];return await r(...e)}catch(e){u.error(new tg(e))}},e),{}),adapter:function(e,t){if(e)return Object.keys(e).reduce((r,n)=>(r[n]=async(...r)=>{try{t.debug(`adapter_${n}`,{args:r});let i=e[n];return await i(...r)}catch(r){let e=new td(r);throw t.error(e),e}},r),{})}(e.adapter,u),callbacks:{...it,...e.callbacks},logger:u,callbackUrl:n.origin,isOnRedirectProxy:h,experimental:{...e.experimental}},g=[];if(o)p.csrfTokenVerified=!0;else{let{csrfToken:e,cookie:t,csrfTokenVerified:r}=await nZ({options:p,cookieValue:i?.[p.cookies.csrfToken.name],isPost:c,bodyValue:s});p.csrfToken=e,p.csrfTokenVerified=r,t&&g.push({name:p.cookies.csrfToken.name,value:t,options:p.cookies.csrfToken.options})}let{callbackUrl:m,callbackUrlCookie:y}=await nB({options:p,cookieValue:i?.[p.cookies.callbackUrl.name],paramValue:a});return p.callbackUrl=m,y&&g.push({name:p.cookies.callbackUrl.name,value:y,options:p.cookies.callbackUrl.options}),{options:p,cookies:g}}var ii,ia,is,io,ic,il,iu,id,ih,ip,ig,im,iy,ib,iw,iv,ix={},i_=[],iS=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,ik=Array.isArray;function iE(e,t){for(var r in t)e[r]=t[r];return e}function iA(e){e&&e.parentNode&&e.parentNode.removeChild(e)}function iT(e,t,r){var n,i,a,s={};for(a in t)"key"==a?n=t[a]:"ref"==a?i=t[a]:s[a]=t[a];if(arguments.length>2&&(s.children=arguments.length>3?iu.call(arguments,2):r),"function"==typeof e&&null!=e.defaultProps)for(a in e.defaultProps)void 0===s[a]&&(s[a]=e.defaultProps[a]);return iP(e,s,n,i,null)}function iP(e,t,r,n,i){var a={type:e,props:t,key:r,ref:n,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,constructor:void 0,__v:null==i?++ih:i,__i:-1,__u:0};return null==i&&null!=id.vnode&&id.vnode(a),a}function iC(e){return e.children}function iO(e,t){this.props=e,this.context=t}function iN(e,t){if(null==t)return e.__?iN(e.__,e.__i+1):null;for(var r;t<e.__k.length;t++)if(null!=(r=e.__k[t])&&null!=r.__e)return r.__e;return"function"==typeof e.type?iN(e):null}function iR(e){(!e.__d&&(e.__d=!0)&&ip.push(e)&&!iI.__r++||ig!==id.debounceRendering)&&((ig=id.debounceRendering)||im)(iI)}function iI(){var e,t,r,n,i,a,s,o;for(ip.sort(iy);e=ip.shift();)e.__d&&(t=ip.length,n=void 0,a=(i=(r=e).__v).__e,s=[],o=[],r.__P&&((n=iE({},i)).__v=i.__v+1,id.vnode&&id.vnode(n),iD(r.__P,n,i,r.__n,r.__P.namespaceURI,32&i.__u?[a]:null,s,null==a?iN(i):a,!!(32&i.__u),o),n.__v=i.__v,n.__.__k[n.__i]=n,iM(s,n,o),n.__e!=a&&function e(t){var r,n;if(null!=(t=t.__)&&null!=t.__c){for(t.__e=t.__c.base=null,r=0;r<t.__k.length;r++)if(null!=(n=t.__k[r])&&null!=n.__e){t.__e=t.__c.base=n.__e;break}return e(t)}}(n)),ip.length>t&&ip.sort(iy));iI.__r=0}function i$(e,t,r,n,i,a,s,o,c,l,u){var d,f,h,p,g,m=n&&n.__k||i_,y=t.length;for(r.__d=c,function(e,t,r){var n,i,a,s,o,c=t.length,l=r.length,u=l,d=0;for(e.__k=[],n=0;n<c;n++)null!=(i=t[n])&&"boolean"!=typeof i&&"function"!=typeof i?(s=n+d,(i=e.__k[n]="string"==typeof i||"number"==typeof i||"bigint"==typeof i||i.constructor==String?iP(null,i,null,null,null):ik(i)?iP(iC,{children:i},null,null,null):void 0===i.constructor&&i.__b>0?iP(i.type,i.props,i.key,i.ref?i.ref:null,i.__v):i).__=e,i.__b=e.__b+1,a=null,-1!==(o=i.__i=function(e,t,r,n){var i=e.key,a=e.type,s=r-1,o=r+1,c=t[r];if(null===c||c&&i==c.key&&a===c.type&&0==(131072&c.__u))return r;if(n>(null!=c&&0==(131072&c.__u)?1:0))for(;s>=0||o<t.length;){if(s>=0){if((c=t[s])&&0==(131072&c.__u)&&i==c.key&&a===c.type)return s;s--}if(o<t.length){if((c=t[o])&&0==(131072&c.__u)&&i==c.key&&a===c.type)return o;o++}}return -1}(i,r,s,u))&&(u--,(a=r[o])&&(a.__u|=131072)),null==a||null===a.__v?(-1==o&&d--,"function"!=typeof i.type&&(i.__u|=65536)):o!==s&&(o==s-1?d--:o==s+1?d++:(o>s?d--:d++,i.__u|=65536))):i=e.__k[n]=null;if(u)for(n=0;n<l;n++)null!=(a=r[n])&&0==(131072&a.__u)&&(a.__e==e.__d&&(e.__d=iN(a)),function e(t,r,n){var i,a;if(id.unmount&&id.unmount(t),(i=t.ref)&&(i.current&&i.current!==t.__e||iB(i,null,r)),null!=(i=t.__c)){if(i.componentWillUnmount)try{i.componentWillUnmount()}catch(e){id.__e(e,r)}i.base=i.__P=null}if(i=t.__k)for(a=0;a<i.length;a++)i[a]&&e(i[a],r,n||"function"!=typeof t.type);n||iA(t.__e),t.__c=t.__=t.__e=t.__d=void 0}(a,a))}(r,t,m),c=r.__d,d=0;d<y;d++)null!=(h=r.__k[d])&&(f=-1===h.__i?ix:m[h.__i]||ix,h.__i=d,iD(e,h,f,i,a,s,o,c,l,u),p=h.__e,h.ref&&f.ref!=h.ref&&(f.ref&&iB(f.ref,null,h),u.push(h.ref,h.__c||p,h)),null==g&&null!=p&&(g=p),65536&h.__u||f.__k===h.__k?c=function e(t,r,n){var i,a;if("function"==typeof t.type){for(i=t.__k,a=0;i&&a<i.length;a++)i[a]&&(i[a].__=t,r=e(i[a],r,n));return r}t.__e!=r&&(r&&t.type&&!n.contains(r)&&(r=iN(t)),n.insertBefore(t.__e,r||null),r=t.__e);do r=r&&r.nextSibling;while(null!=r&&8===r.nodeType);return r}(h,c,e):"function"==typeof h.type&&void 0!==h.__d?c=h.__d:p&&(c=p.nextSibling),h.__d=void 0,h.__u&=-196609);r.__d=c,r.__e=g}function ij(e,t,r){"-"===t[0]?e.setProperty(t,null==r?"":r):e[t]=null==r?"":"number"!=typeof r||iS.test(t)?r:r+"px"}function iU(e,t,r,n,i){var a;e:if("style"===t){if("string"==typeof r)e.style.cssText=r;else{if("string"==typeof n&&(e.style.cssText=n=""),n)for(t in n)r&&t in r||ij(e.style,t,"");if(r)for(t in r)n&&r[t]===n[t]||ij(e.style,t,r[t])}}else if("o"===t[0]&&"n"===t[1])a=t!==(t=t.replace(/(PointerCapture)$|Capture$/i,"$1")),t=t.toLowerCase()in e||"onFocusOut"===t||"onFocusIn"===t?t.toLowerCase().slice(2):t.slice(2),e.l||(e.l={}),e.l[t+a]=r,r?n?r.u=n.u:(r.u=ib,e.addEventListener(t,a?iv:iw,a)):e.removeEventListener(t,a?iv:iw,a);else{if("http://www.w3.org/2000/svg"==i)t=t.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if("width"!=t&&"height"!=t&&"href"!=t&&"list"!=t&&"form"!=t&&"tabIndex"!=t&&"download"!=t&&"rowSpan"!=t&&"colSpan"!=t&&"role"!=t&&"popover"!=t&&t in e)try{e[t]=null==r?"":r;break e}catch(e){}"function"==typeof r||(null==r||!1===r&&"-"!==t[4]?e.removeAttribute(t):e.setAttribute(t,"popover"==t&&1==r?"":r))}}function iL(e){return function(t){if(this.l){var r=this.l[t.type+e];if(null==t.t)t.t=ib++;else if(t.t<r.u)return;return r(id.event?id.event(t):t)}}}function iD(e,t,r,n,i,a,s,o,c,l){var u,d,f,h,p,g,m,y,b,w,v,x,_,S,k,E,A=t.type;if(void 0!==t.constructor)return null;128&r.__u&&(c=!!(32&r.__u),a=[o=t.__e=r.__e]),(u=id.__b)&&u(t);e:if("function"==typeof A)try{if(y=t.props,b="prototype"in A&&A.prototype.render,w=(u=A.contextType)&&n[u.__c],v=u?w?w.props.value:u.__:n,r.__c?m=(d=t.__c=r.__c).__=d.__E:(b?t.__c=d=new A(y,v):(t.__c=d=new iO(y,v),d.constructor=A,d.render=iH),w&&w.sub(d),d.props=y,d.state||(d.state={}),d.context=v,d.__n=n,f=d.__d=!0,d.__h=[],d._sb=[]),b&&null==d.__s&&(d.__s=d.state),b&&null!=A.getDerivedStateFromProps&&(d.__s==d.state&&(d.__s=iE({},d.__s)),iE(d.__s,A.getDerivedStateFromProps(y,d.__s))),h=d.props,p=d.state,d.__v=t,f)b&&null==A.getDerivedStateFromProps&&null!=d.componentWillMount&&d.componentWillMount(),b&&null!=d.componentDidMount&&d.__h.push(d.componentDidMount);else{if(b&&null==A.getDerivedStateFromProps&&y!==h&&null!=d.componentWillReceiveProps&&d.componentWillReceiveProps(y,v),!d.__e&&(null!=d.shouldComponentUpdate&&!1===d.shouldComponentUpdate(y,d.__s,v)||t.__v===r.__v)){for(t.__v!==r.__v&&(d.props=y,d.state=d.__s,d.__d=!1),t.__e=r.__e,t.__k=r.__k,t.__k.some(function(e){e&&(e.__=t)}),x=0;x<d._sb.length;x++)d.__h.push(d._sb[x]);d._sb=[],d.__h.length&&s.push(d);break e}null!=d.componentWillUpdate&&d.componentWillUpdate(y,d.__s,v),b&&null!=d.componentDidUpdate&&d.__h.push(function(){d.componentDidUpdate(h,p,g)})}if(d.context=v,d.props=y,d.__P=e,d.__e=!1,_=id.__r,S=0,b){for(d.state=d.__s,d.__d=!1,_&&_(t),u=d.render(d.props,d.state,d.context),k=0;k<d._sb.length;k++)d.__h.push(d._sb[k]);d._sb=[]}else do d.__d=!1,_&&_(t),u=d.render(d.props,d.state,d.context),d.state=d.__s;while(d.__d&&++S<25);d.state=d.__s,null!=d.getChildContext&&(n=iE(iE({},n),d.getChildContext())),b&&!f&&null!=d.getSnapshotBeforeUpdate&&(g=d.getSnapshotBeforeUpdate(h,p)),i$(e,ik(E=null!=u&&u.type===iC&&null==u.key?u.props.children:u)?E:[E],t,r,n,i,a,s,o,c,l),d.base=t.__e,t.__u&=-161,d.__h.length&&s.push(d),m&&(d.__E=d.__=null)}catch(e){if(t.__v=null,c||null!=a){for(t.__u|=c?160:128;o&&8===o.nodeType&&o.nextSibling;)o=o.nextSibling;a[a.indexOf(o)]=null,t.__e=o}else t.__e=r.__e,t.__k=r.__k;id.__e(e,t,r)}else null==a&&t.__v===r.__v?(t.__k=r.__k,t.__e=r.__e):t.__e=function(e,t,r,n,i,a,s,o,c){var l,u,d,f,h,p,g,m=r.props,y=t.props,b=t.type;if("svg"===b?i="http://www.w3.org/2000/svg":"math"===b?i="http://www.w3.org/1998/Math/MathML":i||(i="http://www.w3.org/1999/xhtml"),null!=a){for(l=0;l<a.length;l++)if((h=a[l])&&"setAttribute"in h==!!b&&(b?h.localName===b:3===h.nodeType)){e=h,a[l]=null;break}}if(null==e){if(null===b)return document.createTextNode(y);e=document.createElementNS(i,b,y.is&&y),o&&(id.__m&&id.__m(t,a),o=!1),a=null}if(null===b)m===y||o&&e.data===y||(e.data=y);else{if(a=a&&iu.call(e.childNodes),m=r.props||ix,!o&&null!=a)for(m={},l=0;l<e.attributes.length;l++)m[(h=e.attributes[l]).name]=h.value;for(l in m)if(h=m[l],"children"==l);else if("dangerouslySetInnerHTML"==l)d=h;else if(!(l in y)){if("value"==l&&"defaultValue"in y||"checked"==l&&"defaultChecked"in y)continue;iU(e,l,null,h,i)}for(l in y)h=y[l],"children"==l?f=h:"dangerouslySetInnerHTML"==l?u=h:"value"==l?p=h:"checked"==l?g=h:o&&"function"!=typeof h||m[l]===h||iU(e,l,h,m[l],i);if(u)o||d&&(u.__html===d.__html||u.__html===e.innerHTML)||(e.innerHTML=u.__html),t.__k=[];else if(d&&(e.innerHTML=""),i$(e,ik(f)?f:[f],t,r,n,"foreignObject"===b?"http://www.w3.org/1999/xhtml":i,a,s,a?a[0]:r.__k&&iN(r,0),o,c),null!=a)for(l=a.length;l--;)iA(a[l]);o||(l="value","progress"===b&&null==p?e.removeAttribute("value"):void 0===p||p===e[l]&&("progress"!==b||p)&&("option"!==b||p===m[l])||iU(e,l,p,m[l],i),l="checked",void 0!==g&&g!==e[l]&&iU(e,l,g,m[l],i))}return e}(r.__e,t,r,n,i,a,s,c,l);(u=id.diffed)&&u(t)}function iM(e,t,r){t.__d=void 0;for(var n=0;n<r.length;n++)iB(r[n],r[++n],r[++n]);id.__c&&id.__c(t,e),e.some(function(t){try{e=t.__h,t.__h=[],e.some(function(e){e.call(t)})}catch(e){id.__e(e,t.__v)}})}function iB(e,t,r){try{if("function"==typeof e){var n="function"==typeof e.__u;n&&e.__u(),n&&null==t||(e.__u=e(t))}else e.current=t}catch(e){id.__e(e,r)}}function iH(e,t,r){return this.constructor(e,r)}function iq(e,t){var r,n,i,a,s;r=e,id.__&&id.__(r,t),i=(n="function"==typeof iq)?null:iq&&iq.__k||t.__k,a=[],s=[],iD(t,r=(!n&&iq||t).__k=iT(iC,null,[r]),i||ix,ix,t.namespaceURI,!n&&iq?[iq]:i?null:t.firstChild?iu.call(t.childNodes):null,a,!n&&iq?iq:i?i.__e:t.firstChild,n,s),iM(a,r,s)}iu=i_.slice,id={__e:function(e,t,r,n){for(var i,a,s;t=t.__;)if((i=t.__c)&&!i.__)try{if((a=i.constructor)&&null!=a.getDerivedStateFromError&&(i.setState(a.getDerivedStateFromError(e)),s=i.__d),null!=i.componentDidCatch&&(i.componentDidCatch(e,n||{}),s=i.__d),s)return i.__E=i}catch(t){e=t}throw e}},ih=0,iO.prototype.setState=function(e,t){var r;r=null!=this.__s&&this.__s!==this.state?this.__s:this.__s=iE({},this.state),"function"==typeof e&&(e=e(iE({},r),this.props)),e&&iE(r,e),null!=e&&this.__v&&(t&&this._sb.push(t),iR(this))},iO.prototype.forceUpdate=function(e){this.__v&&(this.__e=!0,e&&this.__h.push(e),iR(this))},iO.prototype.render=iC,ip=[],im="function"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,iy=function(e,t){return e.__v.__b-t.__v.__b},iI.__r=0,ib=0,iw=iL(!1),iv=iL(!0);var iz=/[\s\n\\/='"\0<>]/,iW=/^(xlink|xmlns|xml)([A-Z])/,iF=/^accessK|^auto[A-Z]|^cell|^ch|^col|cont|cross|dateT|encT|form[A-Z]|frame|hrefL|inputM|maxL|minL|noV|playsI|popoverT|readO|rowS|src[A-Z]|tabI|useM|item[A-Z]/,iK=/^ac|^ali|arabic|basel|cap|clipPath$|clipRule$|color|dominant|enable|fill|flood|font|glyph[^R]|horiz|image|letter|lighting|marker[^WUH]|overline|panose|pointe|paint|rendering|shape|stop|strikethrough|stroke|text[^L]|transform|underline|unicode|units|^v[^i]|^w|^xH/,iV=new Set(["draggable","spellcheck"]),iJ=/["&<]/;function iQ(e){if(0===e.length||!1===iJ.test(e))return e;for(var t=0,r=0,n="",i="";r<e.length;r++){switch(e.charCodeAt(r)){case 34:i="&quot;";break;case 38:i="&amp;";break;case 60:i="&lt;";break;default:continue}r!==t&&(n+=e.slice(t,r)),n+=i,t=r+1}return r!==t&&(n+=e.slice(t,r)),n}var iG={},iX=new Set(["animation-iteration-count","border-image-outset","border-image-slice","border-image-width","box-flex","box-flex-group","box-ordinal-group","column-count","fill-opacity","flex","flex-grow","flex-negative","flex-order","flex-positive","flex-shrink","flood-opacity","font-weight","grid-column","grid-row","line-clamp","line-height","opacity","order","orphans","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-miterlimit","stroke-opacity","stroke-width","tab-size","widows","z-index","zoom"]),iY=/[A-Z]/g;function iZ(){this.__d=!0}var i0,i1,i2,i5,i6={},i3=[],i8=Array.isArray,i4=Object.assign;function i9(e,t){var r,n=e.type,i=!0;return e.__c?(i=!1,(r=e.__c).state=r.__s):r=new n(e.props,t),e.__c=r,r.__v=e,r.props=e.props,r.context=t,r.__d=!0,null==r.state&&(r.state=i6),null==r.__s&&(r.__s=r.state),n.getDerivedStateFromProps?r.state=i4({},r.state,n.getDerivedStateFromProps(r.props,r.state)):i&&r.componentWillMount?(r.componentWillMount(),r.state=r.__s!==r.state?r.__s:r.state):!i&&r.componentWillUpdate&&r.componentWillUpdate(),i2&&i2(e),r.render(r.props,r.state,t)}var i7=new Set(["area","base","br","col","command","embed","hr","img","input","keygen","link","meta","param","source","track","wbr"]),ae=0;function at(e,t,r,n,i,a){t||(t={});var s,o,c=t;"ref"in t&&(s=t.ref,delete t.ref);var l={type:e,props:c,key:r,ref:s,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,constructor:void 0,__v:--ae,__i:-1,__u:0,__source:i,__self:a};if("function"==typeof e&&(s=e.defaultProps))for(o in s)void 0===c[o]&&(c[o]=s[o]);return id.vnode&&id.vnode(l),l}async function ar(e,t){let r=window.SimpleWebAuthnBrowser;async function n(r){let n=new URL(`${e}/webauthn-options/${t}`);r&&n.searchParams.append("action",r),a().forEach(e=>{n.searchParams.append(e.name,e.value)});let i=await fetch(n);if(!i.ok){console.error("Failed to fetch options",i);return}return i.json()}function i(){let e=`#${t}-form`,r=document.querySelector(e);if(!r)throw Error(`Form '${e}' not found`);return r}function a(){return Array.from(i().querySelectorAll("input[data-form-field]"))}async function s(e,t){let r=i();if(e){let t=document.createElement("input");t.type="hidden",t.name="action",t.value=e,r.appendChild(t)}if(t){let e=document.createElement("input");e.type="hidden",e.name="data",e.value=JSON.stringify(t),r.appendChild(e)}return r.submit()}async function o(e,t){let n=await r.startAuthentication(e,t);return await s("authenticate",n)}async function c(e){a().forEach(e=>{if(e.required&&!e.value)throw Error(`Missing required field: ${e.name}`)});let t=await r.startRegistration(e);return await s("register",t)}async function l(){if(!r.browserSupportsWebAuthnAutofill())return;let e=await n("authenticate");if(!e){console.error("Failed to fetch option for autofill authentication");return}try{await o(e.options,!0)}catch(e){console.error(e)}}(async function(){let e=i();if(!r.browserSupportsWebAuthn()){e.style.display="none";return}e&&e.addEventListener("submit",async e=>{e.preventDefault();let t=await n(void 0);if(!t){console.error("Failed to fetch options for form submission");return}if("authenticate"===t.action)try{await o(t.options,!1)}catch(e){console.error(e)}else if("register"===t.action)try{await c(t.options)}catch(e){console.error(e)}})})(),l()}let an={default:"Unable to sign in.",Signin:"Try signing in with a different account.",OAuthSignin:"Try signing in with a different account.",OAuthCallbackError:"Try signing in with a different account.",OAuthCreateAccount:"Try signing in with a different account.",EmailCreateAccount:"Try signing in with a different account.",Callback:"Try signing in with a different account.",OAuthAccountNotLinked:"To confirm your identity, sign in with the same account you used originally.",EmailSignin:"The e-mail could not be sent.",CredentialsSignin:"Sign in failed. Check the details you provided are correct.",SessionRequired:"Please sign in to access this page."},ai=`:root {
  --border-width: 1px;
  --border-radius: 0.5rem;
  --color-error: #c94b4b;
  --color-info: #157efb;
  --color-info-hover: #0f6ddb;
  --color-info-text: #fff;
}

.__next-auth-theme-auto,
.__next-auth-theme-light {
  --color-background: #ececec;
  --color-background-hover: rgba(236, 236, 236, 0.8);
  --color-background-card: #fff;
  --color-text: #000;
  --color-primary: #444;
  --color-control-border: #bbb;
  --color-button-active-background: #f9f9f9;
  --color-button-active-border: #aaa;
  --color-separator: #ccc;
  --provider-bg: #fff;
  --provider-bg-hover: color-mix(
    in srgb,
    var(--provider-brand-color) 30%,
    #fff
  );
}

.__next-auth-theme-dark {
  --color-background: #161b22;
  --color-background-hover: rgba(22, 27, 34, 0.8);
  --color-background-card: #0d1117;
  --color-text: #fff;
  --color-primary: #ccc;
  --color-control-border: #555;
  --color-button-active-background: #060606;
  --color-button-active-border: #666;
  --color-separator: #444;
  --provider-bg: #161b22;
  --provider-bg-hover: color-mix(
    in srgb,
    var(--provider-brand-color) 30%,
    #000
  );
}

.__next-auth-theme-dark img[src$="42-school.svg"],
  .__next-auth-theme-dark img[src$="apple.svg"],
  .__next-auth-theme-dark img[src$="boxyhq-saml.svg"],
  .__next-auth-theme-dark img[src$="eveonline.svg"],
  .__next-auth-theme-dark img[src$="github.svg"],
  .__next-auth-theme-dark img[src$="mailchimp.svg"],
  .__next-auth-theme-dark img[src$="medium.svg"],
  .__next-auth-theme-dark img[src$="okta.svg"],
  .__next-auth-theme-dark img[src$="patreon.svg"],
  .__next-auth-theme-dark img[src$="ping-id.svg"],
  .__next-auth-theme-dark img[src$="roblox.svg"],
  .__next-auth-theme-dark img[src$="threads.svg"],
  .__next-auth-theme-dark img[src$="wikimedia.svg"] {
    filter: invert(1);
  }

.__next-auth-theme-dark #submitButton {
    background-color: var(--provider-bg, var(--color-info));
  }

@media (prefers-color-scheme: dark) {
  .__next-auth-theme-auto {
    --color-background: #161b22;
    --color-background-hover: rgba(22, 27, 34, 0.8);
    --color-background-card: #0d1117;
    --color-text: #fff;
    --color-primary: #ccc;
    --color-control-border: #555;
    --color-button-active-background: #060606;
    --color-button-active-border: #666;
    --color-separator: #444;
    --provider-bg: #161b22;
    --provider-bg-hover: color-mix(
      in srgb,
      var(--provider-brand-color) 30%,
      #000
    );
  }
    .__next-auth-theme-auto img[src$="42-school.svg"],
    .__next-auth-theme-auto img[src$="apple.svg"],
    .__next-auth-theme-auto img[src$="boxyhq-saml.svg"],
    .__next-auth-theme-auto img[src$="eveonline.svg"],
    .__next-auth-theme-auto img[src$="github.svg"],
    .__next-auth-theme-auto img[src$="mailchimp.svg"],
    .__next-auth-theme-auto img[src$="medium.svg"],
    .__next-auth-theme-auto img[src$="okta.svg"],
    .__next-auth-theme-auto img[src$="patreon.svg"],
    .__next-auth-theme-auto img[src$="ping-id.svg"],
    .__next-auth-theme-auto img[src$="roblox.svg"],
    .__next-auth-theme-auto img[src$="threads.svg"],
    .__next-auth-theme-auto img[src$="wikimedia.svg"] {
      filter: invert(1);
    }
    .__next-auth-theme-auto #submitButton {
      background-color: var(--provider-bg, var(--color-info));
    }
}

html {
  box-sizing: border-box;
}

*,
*:before,
*:after {
  box-sizing: inherit;
  margin: 0;
  padding: 0;
}

body {
  background-color: var(--color-background);
  margin: 0;
  padding: 0;
  font-family:
    ui-sans-serif,
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    "Segoe UI",
    Roboto,
    "Helvetica Neue",
    Arial,
    "Noto Sans",
    sans-serif,
    "Apple Color Emoji",
    "Segoe UI Emoji",
    "Segoe UI Symbol",
    "Noto Color Emoji";
}

h1 {
  margin-bottom: 1.5rem;
  padding: 0 1rem;
  font-weight: 400;
  color: var(--color-text);
}

p {
  margin-bottom: 1.5rem;
  padding: 0 1rem;
  color: var(--color-text);
}

form {
  margin: 0;
  padding: 0;
}

label {
  font-weight: 500;
  text-align: left;
  margin-bottom: 0.25rem;
  display: block;
  color: var(--color-text);
}

input[type] {
  box-sizing: border-box;
  display: block;
  width: 100%;
  padding: 0.5rem 1rem;
  border: var(--border-width) solid var(--color-control-border);
  background: var(--color-background-card);
  font-size: 1rem;
  border-radius: var(--border-radius);
  color: var(--color-text);
}

p {
  font-size: 1.1rem;
  line-height: 2rem;
}

a.button {
  text-decoration: none;
  line-height: 1rem;
}

a.button:link,
  a.button:visited {
    background-color: var(--color-background);
    color: var(--color-primary);
  }

button,
a.button {
  padding: 0.75rem 1rem;
  color: var(--provider-color, var(--color-primary));
  background-color: var(--provider-bg, var(--color-background));
  border: 1px solid #00000031;
  font-size: 0.9rem;
  height: 50px;
  border-radius: var(--border-radius);
  transition: background-color 250ms ease-in-out;
  font-weight: 300;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

:is(button,a.button):hover {
    background-color: var(--provider-bg-hover, var(--color-background-hover));
    cursor: pointer;
  }

:is(button,a.button):active {
    cursor: pointer;
  }

:is(button,a.button) span {
    color: var(--provider-bg);
  }

#submitButton {
  color: var(--button-text-color, var(--color-info-text));
  background-color: var(--brand-color, var(--color-info));
  width: 100%;
}

#submitButton:hover {
    background-color: var(
      --button-hover-bg,
      var(--color-info-hover)
    ) !important;
  }

a.site {
  color: var(--color-primary);
  text-decoration: none;
  font-size: 1rem;
  line-height: 2rem;
}

a.site:hover {
    text-decoration: underline;
  }

.page {
  position: absolute;
  width: 100%;
  height: 100%;
  display: grid;
  place-items: center;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.page > div {
    text-align: center;
  }

.error a.button {
    padding-left: 2rem;
    padding-right: 2rem;
    margin-top: 0.5rem;
  }

.error .message {
    margin-bottom: 1.5rem;
  }

.signin input[type="text"] {
    margin-left: auto;
    margin-right: auto;
    display: block;
  }

.signin hr {
    display: block;
    border: 0;
    border-top: 1px solid var(--color-separator);
    margin: 2rem auto 1rem auto;
    overflow: visible;
  }

.signin hr::before {
      content: "or";
      background: var(--color-background-card);
      color: #888;
      padding: 0 0.4rem;
      position: relative;
      top: -0.7rem;
    }

.signin .error {
    background: #f5f5f5;
    font-weight: 500;
    border-radius: 0.3rem;
    background: var(--color-error);
  }

.signin .error p {
      text-align: left;
      padding: 0.5rem 1rem;
      font-size: 0.9rem;
      line-height: 1.2rem;
      color: var(--color-info-text);
    }

.signin > div,
  .signin form {
    display: block;
  }

.signin > div input[type], .signin form input[type] {
      margin-bottom: 0.5rem;
    }

.signin > div button, .signin form button {
      width: 100%;
    }

.signin .provider + .provider {
    margin-top: 1rem;
  }

.logo {
  display: inline-block;
  max-width: 150px;
  margin: 1.25rem 0;
  max-height: 70px;
}

.card {
  background-color: var(--color-background-card);
  border-radius: 1rem;
  padding: 1.25rem 2rem;
}

.card .header {
    color: var(--color-primary);
  }

.card input[type]::-moz-placeholder {
    color: color-mix(
      in srgb,
      var(--color-text) 20%,
      var(--color-button-active-background)
    );
  }

.card input[type]::placeholder {
    color: color-mix(
      in srgb,
      var(--color-text) 20%,
      var(--color-button-active-background)
    );
  }

.card input[type] {
    background: color-mix(in srgb, var(--color-background-card) 95%, black);
  }

.section-header {
  color: var(--color-text);
}

@media screen and (min-width: 450px) {
  .card {
    margin: 2rem 0;
    width: 368px;
  }
}

@media screen and (max-width: 450px) {
  .card {
    margin: 1rem 0;
    width: 343px;
  }
}
`;function aa({html:e,title:t,status:r,cookies:n,theme:i,headTags:a}){return{cookies:n,status:r,headers:{"Content-Type":"text/html"},body:`<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width, initial-scale=1.0"><style>${ai}</style><title>${t}</title>${a??""}</head><body class="__next-auth-theme-${i?.colorScheme??"auto"}"><div class="page">${function(e,t,r){var n=id.__s;id.__s=!0,i0=id.__b,i1=id.diffed,i2=id.__r,i5=id.unmount;var i=iT(iC,null);i.__k=[e];try{var a=function e(t,r,n,i,a,s,o){if(null==t||!0===t||!1===t||""===t)return"";var c=typeof t;if("object"!=c)return"function"==c?"":"string"==c?iQ(t):t+"";if(i8(t)){var l,u="";a.__k=t;for(var d=0;d<t.length;d++){var f=t[d];if(null!=f&&"boolean"!=typeof f){var h,p=e(f,r,n,i,a,s,o);"string"==typeof p?u+=p:(l||(l=[]),u&&l.push(u),u="",i8(p)?(h=l).push.apply(h,p):l.push(p))}}return l?(u&&l.push(u),l):u}if(void 0!==t.constructor)return"";t.__=a,i0&&i0(t);var g=t.type,m=t.props;if("function"==typeof g){var y,b,w,v=r;if(g===iC){if("tpl"in m){for(var x="",_=0;_<m.tpl.length;_++)if(x+=m.tpl[_],m.exprs&&_<m.exprs.length){var S=m.exprs[_];if(null==S)continue;"object"==typeof S&&(void 0===S.constructor||i8(S))?x+=e(S,r,n,i,t,s,o):x+=S}return x}if("UNSTABLE_comment"in m)return"\x3c!--"+iQ(m.UNSTABLE_comment)+"--\x3e";b=m.children}else{if(null!=(y=g.contextType)){var k=r[y.__c];v=k?k.props.value:y.__}var E=g.prototype&&"function"==typeof g.prototype.render;if(E)b=i9(t,v),w=t.__c;else{t.__c=w={__v:t,context:v,props:t.props,setState:iZ,forceUpdate:iZ,__d:!0,__h:[]};for(var A=0;w.__d&&A++<25;)w.__d=!1,i2&&i2(t),b=g.call(w,m,v);w.__d=!0}if(null!=w.getChildContext&&(r=i4({},r,w.getChildContext())),E&&id.errorBoundaries&&(g.getDerivedStateFromError||w.componentDidCatch)){b=null!=b&&b.type===iC&&null==b.key&&null==b.props.tpl?b.props.children:b;try{return e(b,r,n,i,t,s,o)}catch(a){return g.getDerivedStateFromError&&(w.__s=g.getDerivedStateFromError(a)),w.componentDidCatch&&w.componentDidCatch(a,i6),w.__d?(b=i9(t,r),null!=(w=t.__c).getChildContext&&(r=i4({},r,w.getChildContext())),e(b=null!=b&&b.type===iC&&null==b.key&&null==b.props.tpl?b.props.children:b,r,n,i,t,s,o)):""}finally{i1&&i1(t),t.__=null,i5&&i5(t)}}}b=null!=b&&b.type===iC&&null==b.key&&null==b.props.tpl?b.props.children:b;try{var T=e(b,r,n,i,t,s,o);return i1&&i1(t),t.__=null,id.unmount&&id.unmount(t),T}catch(a){if(!s&&o&&o.onError){var P=o.onError(a,t,function(a){return e(a,r,n,i,t,s,o)});if(void 0!==P)return P;var C=id.__e;return C&&C(a,t),""}if(!s||!a||"function"!=typeof a.then)throw a;return a.then(function a(){try{return e(b,r,n,i,t,s,o)}catch(c){if(!c||"function"!=typeof c.then)throw c;return c.then(function(){return e(b,r,n,i,t,s,o)},a)}})}}var O,N="<"+g,R="";for(var I in m){var $=m[I];if("function"!=typeof $||"class"===I||"className"===I){switch(I){case"children":O=$;continue;case"key":case"ref":case"__self":case"__source":continue;case"htmlFor":if("for"in m)continue;I="for";break;case"className":if("class"in m)continue;I="class";break;case"defaultChecked":I="checked";break;case"defaultSelected":I="selected";break;case"defaultValue":case"value":switch(I="value",g){case"textarea":O=$;continue;case"select":i=$;continue;case"option":i!=$||"selected"in m||(N+=" selected")}break;case"dangerouslySetInnerHTML":R=$&&$.__html;continue;case"style":"object"==typeof $&&($=function(e){var t="";for(var r in e){var n=e[r];if(null!=n&&""!==n){var i="-"==r[0]?r:iG[r]||(iG[r]=r.replace(iY,"-$&").toLowerCase()),a=";";"number"!=typeof n||i.startsWith("--")||iX.has(i)||(a="px;"),t=t+i+":"+n+a}}return t||void 0}($));break;case"acceptCharset":I="accept-charset";break;case"httpEquiv":I="http-equiv";break;default:if(iW.test(I))I=I.replace(iW,"$1:$2").toLowerCase();else{if(iz.test(I))continue;("-"===I[4]||iV.has(I))&&null!=$?$+="":n?iK.test(I)&&(I="panose1"===I?"panose-1":I.replace(/([A-Z])/g,"-$1").toLowerCase()):iF.test(I)&&(I=I.toLowerCase())}}null!=$&&!1!==$&&(N=!0===$||""===$?N+" "+I:N+" "+I+'="'+("string"==typeof $?iQ($):$+"")+'"')}}if(iz.test(g))throw Error(g+" is not a valid HTML tag name in "+N+">");if(R||("string"==typeof O?R=iQ(O):null!=O&&!1!==O&&!0!==O&&(R=e(O,r,"svg"===g||"foreignObject"!==g&&n,i,t,s,o))),i1&&i1(t),t.__=null,i5&&i5(t),!R&&i7.has(g))return N+"/>";var j="</"+g+">",U=N+">";return i8(R)?[U].concat(R,[j]):"string"!=typeof R?[U,R,j]:U+R+j}(e,i6,!1,void 0,i,!1,void 0);return i8(a)?a.join(""):a}catch(e){if(e.then)throw Error('Use "renderToStringAsync" for suspenseful rendering.');throw e}finally{id.__c&&id.__c(e,i3),id.__s=n,i3.length=0}}(e)}</div></body></html>`}}function as(e){let{url:t,theme:r,query:n,cookies:i,pages:a,providers:s}=e;return{csrf:(e,t,r)=>e?(t.logger.warn("csrf-disabled"),r.push({name:t.cookies.csrfToken.name,value:"",options:{...t.cookies.csrfToken.options,maxAge:0}}),{status:404,cookies:r}):{headers:{"Content-Type":"application/json","Cache-Control":"private, no-cache, no-store",Expires:"0",Pragma:"no-cache"},body:{csrfToken:t.csrfToken},cookies:r},providers:e=>({headers:{"Content-Type":"application/json"},body:e.reduce((e,{id:t,name:r,type:n,signinUrl:i,callbackUrl:a})=>(e[t]={id:t,name:r,type:n,signinUrl:i,callbackUrl:a},e),{})}),signin(t,o){if(t)throw new tR("Unsupported action");if(a?.signIn){let t=`${a.signIn}${a.signIn.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:e.callbackUrl??"/"})}`;return o&&(t=`${t}&${new URLSearchParams({error:o})}`),{redirect:t,cookies:i}}let c=s?.find(e=>"webauthn"===e.type&&e.enableConditionalUI&&!!e.simpleWebAuthnBrowserVersion),l="";if(c){let{simpleWebAuthnBrowserVersion:e}=c;l=`<script src="https://unpkg.com/@simplewebauthn/browser@${e}/dist/bundle/index.umd.min.js" crossorigin="anonymous"></script>`}return aa({cookies:i,theme:r,html:function(e){let{csrfToken:t,providers:r=[],callbackUrl:n,theme:i,email:a,error:s}=e;"undefined"!=typeof document&&i?.brandColor&&document.documentElement.style.setProperty("--brand-color",i.brandColor),"undefined"!=typeof document&&i?.buttonText&&document.documentElement.style.setProperty("--button-text-color",i.buttonText);let o=s&&(an[s]??an.default),c=r.find(e=>"webauthn"===e.type&&e.enableConditionalUI)?.id;return at("div",{className:"signin",children:[i?.brandColor&&at("style",{dangerouslySetInnerHTML:{__html:`:root {--brand-color: ${i.brandColor}}`}}),i?.buttonText&&at("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --button-text-color: ${i.buttonText}
        }
      `}}),at("div",{className:"card",children:[o&&at("div",{className:"error",children:at("p",{children:o})}),i?.logo&&at("img",{src:i.logo,alt:"Logo",className:"logo"}),r.map((e,i)=>{let s,o,c;("oauth"===e.type||"oidc"===e.type)&&({bg:s="#fff",brandColor:o,logo:c=`https://authjs.dev/img/providers/${e.id}.svg`}=e.style??{});let l=o??s??"#fff";return at("div",{className:"provider",children:["oauth"===e.type||"oidc"===e.type?at("form",{action:e.signinUrl,method:"POST",children:[at("input",{type:"hidden",name:"csrfToken",value:t}),n&&at("input",{type:"hidden",name:"callbackUrl",value:n}),at("button",{type:"submit",className:"button",style:{"--provider-brand-color":l},tabIndex:0,children:[at("span",{style:{filter:"invert(1) grayscale(1) brightness(1.3) contrast(9000)","mix-blend-mode":"luminosity",opacity:.95},children:["Sign in with ",e.name]}),c&&at("img",{loading:"lazy",height:24,src:c})]})]}):null,("email"===e.type||"credentials"===e.type||"webauthn"===e.type)&&i>0&&"email"!==r[i-1].type&&"credentials"!==r[i-1].type&&"webauthn"!==r[i-1].type&&at("hr",{}),"email"===e.type&&at("form",{action:e.signinUrl,method:"POST",children:[at("input",{type:"hidden",name:"csrfToken",value:t}),at("label",{className:"section-header",htmlFor:`input-email-for-${e.id}-provider`,children:"Email"}),at("input",{id:`input-email-for-${e.id}-provider`,autoFocus:!0,type:"email",name:"email",value:a,placeholder:"<EMAIL>",required:!0}),at("button",{id:"submitButton",type:"submit",tabIndex:0,children:["Sign in with ",e.name]})]}),"credentials"===e.type&&at("form",{action:e.callbackUrl,method:"POST",children:[at("input",{type:"hidden",name:"csrfToken",value:t}),Object.keys(e.credentials).map(t=>at("div",{children:[at("label",{className:"section-header",htmlFor:`input-${t}-for-${e.id}-provider`,children:e.credentials[t].label??t}),at("input",{name:t,id:`input-${t}-for-${e.id}-provider`,type:e.credentials[t].type??"text",placeholder:e.credentials[t].placeholder??"",...e.credentials[t]})]},`input-group-${e.id}`)),at("button",{id:"submitButton",type:"submit",tabIndex:0,children:["Sign in with ",e.name]})]}),"webauthn"===e.type&&at("form",{action:e.callbackUrl,method:"POST",id:`${e.id}-form`,children:[at("input",{type:"hidden",name:"csrfToken",value:t}),Object.keys(e.formFields).map(t=>at("div",{children:[at("label",{className:"section-header",htmlFor:`input-${t}-for-${e.id}-provider`,children:e.formFields[t].label??t}),at("input",{name:t,"data-form-field":!0,id:`input-${t}-for-${e.id}-provider`,type:e.formFields[t].type??"text",placeholder:e.formFields[t].placeholder??"",...e.formFields[t]})]},`input-group-${e.id}`)),at("button",{id:`submitButton-${e.id}`,type:"submit",tabIndex:0,children:["Sign in with ",e.name]})]}),("email"===e.type||"credentials"===e.type||"webauthn"===e.type)&&i+1<r.length&&at("hr",{})]},e.id)})]}),c&&at(iC,{children:at("script",{dangerouslySetInnerHTML:{__html:`
const currentURL = window.location.href;
const authURL = currentURL.substring(0, currentURL.lastIndexOf('/'));
(${ar})(authURL, "${c}");
`}})})]})}({csrfToken:e.csrfToken,providers:e.providers?.filter(e=>["email","oauth","oidc"].includes(e.type)||"credentials"===e.type&&e.credentials||"webauthn"===e.type&&e.formFields||!1),callbackUrl:e.callbackUrl,theme:e.theme,error:o,...n}),title:"Sign In",headTags:l})},signout:()=>a?.signOut?{redirect:a.signOut,cookies:i}:aa({cookies:i,theme:r,html:function(e){let{url:t,csrfToken:r,theme:n}=e;return at("div",{className:"signout",children:[n?.brandColor&&at("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${n.brandColor}
        }
      `}}),n?.buttonText&&at("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --button-text-color: ${n.buttonText}
        }
      `}}),at("div",{className:"card",children:[n?.logo&&at("img",{src:n.logo,alt:"Logo",className:"logo"}),at("h1",{children:"Signout"}),at("p",{children:"Are you sure you want to sign out?"}),at("form",{action:t?.toString(),method:"POST",children:[at("input",{type:"hidden",name:"csrfToken",value:r}),at("button",{id:"submitButton",type:"submit",children:"Sign out"})]})]})]})}({csrfToken:e.csrfToken,url:t,theme:r}),title:"Sign Out"}),verifyRequest:e=>a?.verifyRequest?{redirect:`${a.verifyRequest}${t?.search??""}`,cookies:i}:aa({cookies:i,theme:r,html:function(e){let{url:t,theme:r}=e;return at("div",{className:"verify-request",children:[r.brandColor&&at("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${r.brandColor}
        }
      `}}),at("div",{className:"card",children:[r.logo&&at("img",{src:r.logo,alt:"Logo",className:"logo"}),at("h1",{children:"Check your email"}),at("p",{children:"A sign in link has been sent to your email address."}),at("p",{children:at("a",{className:"site",href:t.origin,children:t.host})})]})]})}({url:t,theme:r,...e}),title:"Verify Request"}),error:e=>a?.error?{redirect:`${a.error}${a.error.includes("?")?"&":"?"}error=${e}`,cookies:i}:aa({cookies:i,theme:r,...function(e){let{url:t,error:r="default",theme:n}=e,i=`${t}/signin`,a={default:{status:200,heading:"Error",message:at("p",{children:at("a",{className:"site",href:t?.origin,children:t?.host})})},Configuration:{status:500,heading:"Server error",message:at("div",{children:[at("p",{children:"There is a problem with the server configuration."}),at("p",{children:"Check the server logs for more information."})]})},AccessDenied:{status:403,heading:"Access Denied",message:at("div",{children:[at("p",{children:"You do not have permission to sign in."}),at("p",{children:at("a",{className:"button",href:i,children:"Sign in"})})]})},Verification:{status:403,heading:"Unable to sign in",message:at("div",{children:[at("p",{children:"The sign in link is no longer valid."}),at("p",{children:"It may have been used already or it may have expired."})]}),signin:at("a",{className:"button",href:i,children:"Sign in"})}},{status:s,heading:o,message:c,signin:l}=a[r]??a.default;return{status:s,html:at("div",{className:"error",children:[n?.brandColor&&at("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${n?.brandColor}
        }
      `}}),at("div",{className:"card",children:[n?.logo&&at("img",{src:n?.logo,alt:"Logo",className:"logo"}),at("h1",{children:o}),at("div",{className:"message",children:c}),l]})]})}}({url:t,theme:r,error:e}),title:"Error"})}}function ao(e,t=Date.now()){return new Date(t+1e3*e)}async function ac(e,t,r,n){if(!r?.providerAccountId||!r.type)throw Error("Missing or invalid provider account");if(!["email","oauth","oidc","webauthn"].includes(r.type))throw Error("Provider not supported");let{adapter:i,jwt:a,events:s,session:{strategy:o,generateSessionToken:c}}=n;if(!i)return{user:t,account:r};let l=r,{createUser:u,updateUser:d,getUser:f,getUserByAccount:h,getUserByEmail:p,linkAccount:g,createSession:m,getSessionAndUser:y,deleteSession:b}=i,w=null,v=null,x=!1,_="jwt"===o;if(e){if(_)try{let t=n.cookies.sessionToken.name;(w=await a.decode({...a,token:e,salt:t}))&&"sub"in w&&w.sub&&(v=await f(w.sub))}catch{}else{let t=await y(e);t&&(w=t.session,v=t.user)}}if("email"===l.type){let r=await p(t.email);return r?(v?.id!==r.id&&!_&&e&&await b(e),v=await d({id:r.id,emailVerified:new Date}),await s.updateUser?.({user:v})):(v=await u({...t,emailVerified:new Date}),await s.createUser?.({user:v}),x=!0),{session:w=_?{}:await m({sessionToken:c(),userId:v.id,expires:ao(n.session.maxAge)}),user:v,isNewUser:x}}if("webauthn"===l.type){let e=await h({providerAccountId:l.providerAccountId,provider:l.provider});if(e){if(v){if(e.id===v.id){let e={...l,userId:v.id};return{session:w,user:v,isNewUser:x,account:e}}throw new tq("The account is already associated with another user",{provider:l.provider})}w=_?{}:await m({sessionToken:c(),userId:e.id,expires:ao(n.session.maxAge)});let t={...l,userId:e.id};return{session:w,user:e,isNewUser:x,account:t}}{if(v){await g({...l,userId:v.id}),await s.linkAccount?.({user:v,account:l,profile:t});let e={...l,userId:v.id};return{session:w,user:v,isNewUser:x,account:e}}if(t.email?await p(t.email):null)throw new tq("Another account already exists with the same e-mail address",{provider:l.provider});v=await u({...t}),await s.createUser?.({user:v}),await g({...l,userId:v.id}),await s.linkAccount?.({user:v,account:l,profile:t}),w=_?{}:await m({sessionToken:c(),userId:v.id,expires:ao(n.session.maxAge)});let e={...l,userId:v.id};return{session:w,user:v,isNewUser:!0,account:e}}}let S=await h({providerAccountId:l.providerAccountId,provider:l.provider});if(S){if(v){if(S.id===v.id)return{session:w,user:v,isNewUser:x};throw new tE("The account is already associated with another user",{provider:l.provider})}return{session:w=_?{}:await m({sessionToken:c(),userId:S.id,expires:ao(n.session.maxAge)}),user:S,isNewUser:x}}{let{provider:e}=n,{type:r,provider:i,providerAccountId:a,userId:o,...d}=l;if(l=Object.assign(e.account(d)??{},{providerAccountId:a,provider:i,type:r,userId:o}),v)return await g({...l,userId:v.id}),await s.linkAccount?.({user:v,account:l,profile:t}),{session:w,user:v,isNewUser:x};let f=t.email?await p(t.email):null;if(f){let e=n.provider;if(e?.allowDangerousEmailAccountLinking)v=f,x=!1;else throw new tE("Another account already exists with the same e-mail address",{provider:l.provider})}else v=await u({...t,emailVerified:null}),x=!0;return await s.createUser?.({user:v}),await g({...l,userId:v.id}),await s.linkAccount?.({user:v,account:l,profile:t}),{session:w=_?{}:await m({sessionToken:c(),userId:v.id,expires:ao(n.session.maxAge)}),user:v,isNewUser:x}}}function al(e,t){if(null==e)return!1;try{return e instanceof t||Object.getPrototypeOf(e)[Symbol.toStringTag]===t.prototype[Symbol.toStringTag]}catch{return!1}}"undefined"!=typeof navigator&&navigator.userAgent?.startsWith?.("Mozilla/5.0 ")||(a="oauth4webapi/v3.5.1");let au="ERR_INVALID_ARG_VALUE",ad="ERR_INVALID_ARG_TYPE";function af(e,t,r){let n=TypeError(e,{cause:r});return Object.assign(n,{code:t}),n}let ah=Symbol(),ap=Symbol(),ag=Symbol(),am=Symbol(),ay=Symbol(),ab=Symbol(),aw=Symbol(),av=new TextEncoder,ax=new TextDecoder;function a_(e){return"string"==typeof e?av.encode(e):ax.decode(e)}function aS(e){return"string"==typeof e?function(e){try{let t=atob(e.replace(/-/g,"+").replace(/_/g,"/").replace(/\s/g,"")),r=new Uint8Array(t.length);for(let e=0;e<t.length;e++)r[e]=t.charCodeAt(e);return r}catch(e){throw af("The input to be decoded is not correctly encoded.",au,e)}}(e):function(e){e instanceof ArrayBuffer&&(e=new Uint8Array(e));let t=[];for(let r=0;r<e.byteLength;r+=32768)t.push(String.fromCharCode.apply(null,e.subarray(r,r+32768)));return btoa(t.join("")).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}(e)}class ak extends Error{code;constructor(e,t){super(e,t),this.name=this.constructor.name,this.code=sP,Error.captureStackTrace?.(this,this.constructor)}}class aE extends Error{code;constructor(e,t){super(e,t),this.name=this.constructor.name,t?.code&&(this.code=t?.code),Error.captureStackTrace?.(this,this.constructor)}}function aA(e,t,r){return new aE(e,{code:t,cause:r})}function aT(e){return!(null===e||"object"!=typeof e||Array.isArray(e))}function aP(e){al(e,Headers)&&(e=Object.fromEntries(e.entries()));let t=new Headers(e);if(a&&!t.has("user-agent")&&t.set("user-agent",a),t.has("authorization"))throw af('"options.headers" must not include the "authorization" header name',au);return t}function aC(e){if("function"==typeof e&&(e=e()),!(e instanceof AbortSignal))throw af('"options.signal" must return or be an instance of AbortSignal',ad);return e}function aO(e){return e.includes("//")?e.replace("//","/"):e}async function aN(e,t,r,n){if(!(e instanceof URL))throw af(`"${t}" must be an instance of URL`,ad);aQ(e,n?.[ah]!==!0);let i=r(new URL(e.href)),a=aP(n?.headers);return a.set("accept","application/json"),(n?.[am]||fetch)(i.href,{body:void 0,headers:Object.fromEntries(a.entries()),method:"GET",redirect:"manual",signal:n?.signal?aC(n.signal):void 0})}async function aR(e,t){return aN(e,"issuerIdentifier",e=>{switch(t?.algorithm){case void 0:case"oidc":e.pathname=aO(`${e.pathname}/.well-known/openid-configuration`);break;case"oauth2":var r;r=".well-known/oauth-authorization-server","/"===e.pathname?e.pathname=r:e.pathname=aO(`${r}/${e.pathname}`);break;default:throw af('"options.algorithm" must be "oidc" (default), or "oauth2"',au)}return e},t)}function aI(e,t,r,n,i){try{if("number"!=typeof e||!Number.isFinite(e))throw af(`${r} must be a number`,ad,i);if(e>0)return;if(t){if(0!==e)throw af(`${r} must be a non-negative number`,au,i);return}throw af(`${r} must be a positive number`,au,i)}catch(e){if(n)throw aA(e.message,n,i);throw e}}function a$(e,t,r,n){try{if("string"!=typeof e)throw af(`${t} must be a string`,ad,n);if(0===e.length)throw af(`${t} must not be empty`,au,n)}catch(e){if(r)throw aA(e.message,r,n);throw e}}async function aj(e,t){if(!(e instanceof URL)&&e!==s6)throw af('"expectedIssuerIdentifier" must be an instance of URL',ad);if(!al(t,Response))throw af('"response" must be an instance of Response',ad);if(200!==t.status)throw aA('"response" is not a conform Authorization Server Metadata response (unexpected HTTP status code)',s$,t);sz(t);let r=await s2(t);if(a$(r.issuer,'"response" body "issuer" property',sR,{body:r}),e!==s6&&new URL(r.issuer).href!==e.href)throw aA('"response" body "issuer" property does not match the expected value',sM,{expected:e.href,body:r,attribute:"issuer"});return r}function aU(e){!function(e,t){if(si(e)!==t)throw aL(e,t)}(e,"application/json")}function aL(e,...t){let r='"response" content-type must be ';if(t.length>2){let e=t.pop();r+=`${t.join(", ")}, or ${e}`}else 2===t.length?r+=`${t[0]} or ${t[1]}`:r+=t[0];return aA(r,sI,e)}function aD(){return aS(crypto.getRandomValues(new Uint8Array(32)))}async function aM(e){return a$(e,"codeVerifier"),aS(await crypto.subtle.digest("SHA-256",a_(e)))}function aB(e){let t=e?.[ap];return"number"==typeof t&&Number.isFinite(t)?t:0}function aH(e){let t=e?.[ag];return"number"==typeof t&&Number.isFinite(t)&&-1!==Math.sign(t)?t:30}function aq(){return Math.floor(Date.now()/1e3)}function az(e){if("object"!=typeof e||null===e)throw af('"as" must be an object',ad);a$(e.issuer,'"as.issuer"')}function aW(e){if("object"!=typeof e||null===e)throw af('"client" must be an object',ad);a$(e.client_id,'"client.client_id"')}function aF(e,t){let r=aq()+aB(t);return{jti:aD(),aud:e.issuer,exp:r+60,iat:r,nbf:r,iss:t.client_id,sub:t.client_id}}async function aK(e,t,r){if(!r.usages.includes("sign"))throw af('CryptoKey instances used for signing assertions must include "sign" in their "usages"',au);let n=`${aS(a_(JSON.stringify(e)))}.${aS(a_(JSON.stringify(t)))}`,i=aS(await crypto.subtle.sign(sV(r),r,a_(n)));return`${n}.${i}`}async function aV(e){let{kty:t,e:r,n,x:i,y:a,crv:o}=await crypto.subtle.exportKey("jwk",e),c={kty:t,e:r,n,x:i,y:a,crv:o};return s.set(e,c),c}let aJ=URL.parse?(e,t)=>URL.parse(e,t):(e,t)=>{try{return new URL(e,t)}catch{return null}};function aQ(e,t){if(t&&"https:"!==e.protocol)throw aA("only requests to HTTPS are allowed",sj,e);if("https:"!==e.protocol&&"http:"!==e.protocol)throw aA("only HTTP and HTTPS requests are allowed",sU,e)}function aG(e,t,r,n){let i;if("string"!=typeof e||!(i=aJ(e)))throw aA(`authorization server metadata does not contain a valid ${r?`"as.mtls_endpoint_aliases.${t}"`:`"as.${t}"`}`,void 0===e?sH:sq,{attribute:r?`mtls_endpoint_aliases.${t}`:t});return aQ(i,n),i}function aX(e,t,r,n){return r&&e.mtls_endpoint_aliases&&t in e.mtls_endpoint_aliases?aG(e.mtls_endpoint_aliases[t],t,r,n):aG(e[t],t,r,n)}class aY extends Error{cause;code;error;status;error_description;response;constructor(e,t){super(e,t),this.name=this.constructor.name,this.code=sT,this.cause=t.cause,this.error=t.cause.error,this.status=t.response.status,this.error_description=t.cause.error_description,Object.defineProperty(this,"response",{enumerable:!1,value:t.response}),Error.captureStackTrace?.(this,this.constructor)}}class aZ extends Error{cause;code;error;error_description;constructor(e,t){super(e,t),this.name=this.constructor.name,this.code=sC,this.cause=t.cause,this.error=t.cause.get("error"),this.error_description=t.cause.get("error_description")??void 0,Error.captureStackTrace?.(this,this.constructor)}}class a0 extends Error{cause;code;response;status;constructor(e,t){super(e,t),this.name=this.constructor.name,this.code=sA,this.cause=t.cause,this.status=t.response.status,this.response=t.response,Object.defineProperty(this,"response",{enumerable:!1}),Error.captureStackTrace?.(this,this.constructor)}}let a1="[a-zA-Z0-9!#$%&\\'\\*\\+\\-\\.\\^_`\\|~]+",a2=RegExp("^[,\\s]*("+a1+")\\s(.*)"),a5=RegExp("^[,\\s]*("+a1+')\\s*=\\s*"((?:[^"\\\\]|\\\\.)*)"[,\\s]*(.*)'),a6=RegExp("^[,\\s]*("+a1+")\\s*=\\s*("+a1+")[,\\s]*(.*)"),a3=RegExp("^([a-zA-Z0-9\\-\\._\\~\\+\\/]+[=]{0,2})(?:$|[,\\s])(.*)");async function a8(e){if(e.status>399&&e.status<500){sz(e),aU(e);try{let t=await e.clone().json();if(aT(t)&&"string"==typeof t.error&&t.error.length)return t}catch{}}}async function a4(e,t,r){if(e.status!==t){let t;if(t=await a8(e))throw await e.body?.cancel(),new aY("server responded with an error in the response body",{cause:t,response:e});throw aA(`"response" is not a conform ${r} response (unexpected HTTP status code)`,s$,e)}}function a9(e){if(!sy.has(e))throw af('"options.DPoP" is not a valid DPoPHandle',au)}async function a7(e,t,r,n,i,a){if(a$(e,'"accessToken"'),!(r instanceof URL))throw af('"url" must be an instance of URL',ad);aQ(r,a?.[ah]!==!0),n=aP(n),a?.DPoP&&(a9(a.DPoP),await a.DPoP.addProof(r,n,t.toUpperCase(),e)),n.set("authorization",`${n.has("dpop")?"DPoP":"Bearer"} ${e}`);let s=await (a?.[am]||fetch)(r.href,{body:i,headers:Object.fromEntries(n.entries()),method:t,redirect:"manual",signal:a?.signal?aC(a.signal):void 0});return a?.DPoP?.cacheNonce(s),s}async function se(e,t,r,n){az(e),aW(t);let i=aX(e,"userinfo_endpoint",t.use_mtls_endpoint_aliases,n?.[ah]!==!0),a=aP(n?.headers);return t.userinfo_signed_response_alg?a.set("accept","application/jwt"):(a.set("accept","application/json"),a.append("accept","application/jwt")),a7(r,"GET",i,a,null,{...n,[ap]:aB(t)})}function st(e,t,r,n){(o||=new WeakMap).set(e,{jwks:t,uat:r,get age(){return aq()-this.uat}}),n&&Object.assign(n,{jwks:structuredClone(t),uat:r})}function sr(e,t){o?.delete(e),delete t?.jwks,delete t?.uat}let sn=Symbol();function si(e){return e.headers.get("content-type")?.split(";")[0]}async function sa(e,t,r,n,i){let a;if(az(e),aW(t),!al(n,Response))throw af('"response" must be an instance of Response',ad);if(sf(n),200!==n.status)throw aA('"response" is not a conform UserInfo Endpoint response (unexpected HTTP status code)',s$,n);if(sz(n),"application/jwt"===si(n)){let{claims:r,jwt:s}=await sJ(await n.text(),sX.bind(void 0,t.userinfo_signed_response_alg,e.userinfo_signing_alg_values_supported,void 0),aB(t),aH(t),i?.[ab]).then(sh.bind(void 0,t.client_id)).then(sg.bind(void 0,e));sl.set(n,s),a=r}else{if(t.userinfo_signed_response_alg)throw aA("JWT UserInfo Response expected",sO,n);a=await s2(n)}if(a$(a.sub,'"response" body "sub" property',sR,{body:a}),r===sn);else if(a$(r,'"expectedSubject"'),a.sub!==r)throw aA('unexpected "response" body "sub" property value',sM,{expected:r,body:a,attribute:"sub"});return a}async function ss(e,t,r,n,i,a,s){return await r(e,t,i,a),a.set("content-type","application/x-www-form-urlencoded;charset=UTF-8"),(s?.[am]||fetch)(n.href,{body:i,headers:Object.fromEntries(a.entries()),method:"POST",redirect:"manual",signal:s?.signal?aC(s.signal):void 0})}async function so(e,t,r,n,i,a){let s=aX(e,"token_endpoint",t.use_mtls_endpoint_aliases,a?.[ah]!==!0);i.set("grant_type",n);let o=aP(a?.headers);o.set("accept","application/json"),a?.DPoP!==void 0&&(a9(a.DPoP),await a.DPoP.addProof(s,o,"POST"));let c=await ss(e,t,r,s,i,o,a);return a?.DPoP?.cacheNonce(c),c}let sc=new WeakMap,sl=new WeakMap;function su(e){if(!e.id_token)return;let t=sc.get(e);if(!t)throw af('"ref" was already garbage collected or did not resolve from the proper sources',au);return t}async function sd(e,t,r,n,i){if(az(e),aW(t),!al(r,Response))throw af('"response" must be an instance of Response',ad);sf(r),await a4(r,200,"Token Endpoint"),sz(r);let a=await s2(r);if(a$(a.access_token,'"response" body "access_token" property',sR,{body:a}),a$(a.token_type,'"response" body "token_type" property',sR,{body:a}),a.token_type=a.token_type.toLowerCase(),"dpop"!==a.token_type&&"bearer"!==a.token_type)throw new ak("unsupported `token_type` value",{cause:{body:a}});if(void 0!==a.expires_in){let e="number"!=typeof a.expires_in?parseFloat(a.expires_in):a.expires_in;aI(e,!1,'"response" body "expires_in" property',sR,{body:a}),a.expires_in=e}if(void 0!==a.refresh_token&&a$(a.refresh_token,'"response" body "refresh_token" property',sR,{body:a}),void 0!==a.scope&&"string"!=typeof a.scope)throw aA('"response" body "scope" property must be a string',sR,{body:a});if(void 0!==a.id_token){a$(a.id_token,'"response" body "id_token" property',sR,{body:a});let s=["aud","exp","iat","iss","sub"];!0===t.require_auth_time&&s.push("auth_time"),void 0!==t.default_max_age&&(aI(t.default_max_age,!1,'"client.default_max_age"'),s.push("auth_time")),n?.length&&s.push(...n);let{claims:o,jwt:c}=await sJ(a.id_token,sX.bind(void 0,t.id_token_signed_response_alg,e.id_token_signing_alg_values_supported,"RS256"),aB(t),aH(t),i?.[ab]).then(sv.bind(void 0,s)).then(sm.bind(void 0,e)).then(sp.bind(void 0,t.client_id));if(Array.isArray(o.aud)&&1!==o.aud.length){if(void 0===o.azp)throw aA('ID Token "aud" (audience) claim includes additional untrusted audiences',sD,{claims:o,claim:"aud"});if(o.azp!==t.client_id)throw aA('unexpected ID Token "azp" (authorized party) claim value',sD,{expected:t.client_id,claims:o,claim:"azp"})}void 0!==o.auth_time&&aI(o.auth_time,!1,'ID Token "auth_time" (authentication time)',sR,{claims:o}),sl.set(r,c),sc.set(a,o)}return a}function sf(e){let t;if(t=function(e){if(!al(e,Response))throw af('"response" must be an instance of Response',ad);let t=e.headers.get("www-authenticate");if(null===t)return;let r=[],n=t;for(;n;){let e,t=n.match(a2),i=t?.["1"].toLowerCase();if(n=t?.["2"],!i)return;let a={};for(;n;){let r,i;if(t=n.match(a5)){if([,r,i,n]=t,i.includes("\\"))try{i=JSON.parse(`"${i}"`)}catch{}a[r.toLowerCase()]=i;continue}if(t=n.match(a6)){[,r,i,n]=t,a[r.toLowerCase()]=i;continue}if(t=n.match(a3)){if(Object.keys(a).length)break;[,e,n]=t;break}return}let s={scheme:i,parameters:a};e&&(s.token68=e),r.push(s)}if(r.length)return r}(e))throw new a0("server responded with a challenge in the WWW-Authenticate HTTP Header",{cause:t,response:e})}function sh(e,t){return void 0!==t.claims.aud?sp(e,t):t}function sp(e,t){if(Array.isArray(t.claims.aud)){if(!t.claims.aud.includes(e))throw aA('unexpected JWT "aud" (audience) claim value',sD,{expected:e,claims:t.claims,claim:"aud"})}else if(t.claims.aud!==e)throw aA('unexpected JWT "aud" (audience) claim value',sD,{expected:e,claims:t.claims,claim:"aud"});return t}function sg(e,t){return void 0!==t.claims.iss?sm(e,t):t}function sm(e,t){let r=e[s3]?.(t)??e.issuer;if(t.claims.iss!==r)throw aA('unexpected JWT "iss" (issuer) claim value',sD,{expected:r,claims:t.claims,claim:"iss"});return t}let sy=new WeakSet;async function sb(e,t,r,n,i,a,s){if(az(e),aW(t),!sy.has(n))throw af('"callbackParameters" must be an instance of URLSearchParams obtained from "validateAuthResponse()", or "validateJwtAuthResponse()',au);a$(i,'"redirectUri"');let o=sY(n,"code");if(!o)throw aA('no authorization code in "callbackParameters"',sR);let c=new URLSearchParams(s?.additionalParameters);return c.set("redirect_uri",i),c.set("code",o),a!==s5&&(a$(a,'"codeVerifier"'),c.set("code_verifier",a)),so(e,t,r,"authorization_code",c,s)}let sw={aud:"audience",c_hash:"code hash",client_id:"client id",exp:"expiration time",iat:"issued at",iss:"issuer",jti:"jwt id",nonce:"nonce",s_hash:"state hash",sub:"subject",ath:"access token hash",htm:"http method",htu:"http uri",cnf:"confirmation",auth_time:"authentication time"};function sv(e,t){for(let r of e)if(void 0===t.claims[r])throw aA(`JWT "${r}" (${sw[r]}) claim missing`,sR,{claims:t.claims});return t}let sx=Symbol(),s_=Symbol();async function sS(e,t,r,n){return"string"==typeof n?.expectedNonce||"number"==typeof n?.maxAge||n?.requireIdToken?sk(e,t,r,n.expectedNonce,n.maxAge,{[ab]:n[ab]}):sE(e,t,r,n)}async function sk(e,t,r,n,i,a){let s=[];switch(n){case void 0:n=sx;break;case sx:break;default:a$(n,'"expectedNonce" argument'),s.push("nonce")}switch(i??=t.default_max_age){case void 0:i=s_;break;case s_:break;default:aI(i,!1,'"maxAge" argument'),s.push("auth_time")}let o=await sd(e,t,r,s,a);a$(o.id_token,'"response" body "id_token" property',sR,{body:o});let c=su(o);if(i!==s_){let e=aq()+aB(t),r=aH(t);if(c.auth_time+i<e-r)throw aA("too much time has elapsed since the last End-User authentication",sL,{claims:c,now:e,tolerance:r,claim:"auth_time"})}if(n===sx){if(void 0!==c.nonce)throw aA('unexpected ID Token "nonce" claim value',sD,{expected:void 0,claims:c,claim:"nonce"})}else if(c.nonce!==n)throw aA('unexpected ID Token "nonce" claim value',sD,{expected:n,claims:c,claim:"nonce"});return o}async function sE(e,t,r,n){let i=await sd(e,t,r,void 0,n),a=su(i);if(a){if(void 0!==t.default_max_age){aI(t.default_max_age,!1,'"client.default_max_age"');let e=aq()+aB(t),r=aH(t);if(a.auth_time+t.default_max_age<e-r)throw aA("too much time has elapsed since the last End-User authentication",sL,{claims:a,now:e,tolerance:r,claim:"auth_time"})}if(void 0!==a.nonce)throw aA('unexpected ID Token "nonce" claim value',sD,{expected:void 0,claims:a,claim:"nonce"})}return i}let sA="OAUTH_WWW_AUTHENTICATE_CHALLENGE",sT="OAUTH_RESPONSE_BODY_ERROR",sP="OAUTH_UNSUPPORTED_OPERATION",sC="OAUTH_AUTHORIZATION_RESPONSE_ERROR",sO="OAUTH_JWT_USERINFO_EXPECTED",sN="OAUTH_PARSE_ERROR",sR="OAUTH_INVALID_RESPONSE",sI="OAUTH_RESPONSE_IS_NOT_JSON",s$="OAUTH_RESPONSE_IS_NOT_CONFORM",sj="OAUTH_HTTP_REQUEST_FORBIDDEN",sU="OAUTH_REQUEST_PROTOCOL_FORBIDDEN",sL="OAUTH_JWT_TIMESTAMP_CHECK_FAILED",sD="OAUTH_JWT_CLAIM_COMPARISON_FAILED",sM="OAUTH_JSON_ATTRIBUTE_COMPARISON_FAILED",sB="OAUTH_KEY_SELECTION_FAILED",sH="OAUTH_MISSING_SERVER_METADATA",sq="OAUTH_INVALID_SERVER_METADATA";function sz(e){if(e.bodyUsed)throw af('"response" body has been used already',au)}async function sW(e,t){az(e);let r=aX(e,"jwks_uri",!1,t?.[ah]!==!0),n=aP(t?.headers);return n.set("accept","application/json"),n.append("accept","application/jwk-set+json"),(t?.[am]||fetch)(r.href,{body:void 0,headers:Object.fromEntries(n.entries()),method:"GET",redirect:"manual",signal:t?.signal?aC(t.signal):void 0})}async function sF(e){if(!al(e,Response))throw af('"response" must be an instance of Response',ad);if(200!==e.status)throw aA('"response" is not a conform JSON Web Key Set response (unexpected HTTP status code)',s$,e);sz(e);let t=await s2(e,e=>(function(e,...t){if(!t.includes(si(e)))throw aL(e,...t)})(e,"application/json","application/jwk-set+json"));if(!Array.isArray(t.keys))throw aA('"response" body "keys" property must be an array',sR,{body:t});if(!Array.prototype.every.call(t.keys,aT))throw aA('"response" body "keys" property members must be JWK formatted objects',sR,{body:t});return t}function sK(e){let{algorithm:t}=e;if("number"!=typeof t.modulusLength||t.modulusLength<2048)throw new ak(`unsupported ${t.name} modulusLength`,{cause:e})}function sV(e){switch(e.algorithm.name){case"ECDSA":return{name:e.algorithm.name,hash:function(e){let{algorithm:t}=e;switch(t.namedCurve){case"P-256":return"SHA-256";case"P-384":return"SHA-384";case"P-521":return"SHA-512";default:throw new ak("unsupported ECDSA namedCurve",{cause:e})}}(e)};case"RSA-PSS":switch(sK(e),e.algorithm.hash.name){case"SHA-256":case"SHA-384":case"SHA-512":return{name:e.algorithm.name,saltLength:parseInt(e.algorithm.hash.name.slice(-3),10)>>3};default:throw new ak("unsupported RSA-PSS hash name",{cause:e})}case"RSASSA-PKCS1-v1_5":return sK(e),e.algorithm.name;case"Ed25519":return e.algorithm.name}throw new ak("unsupported CryptoKey algorithm name",{cause:e})}async function sJ(e,t,r,n,i){let a,s,{0:o,1:c,length:l}=e.split(".");if(5===l){if(void 0!==i)e=await i(e),{0:o,1:c,length:l}=e.split(".");else throw new ak("JWE decryption is not configured",{cause:e})}if(3!==l)throw aA("Invalid JWT",sR,e);try{a=JSON.parse(a_(aS(o)))}catch(e){throw aA("failed to parse JWT Header body as base64url encoded JSON",sN,e)}if(!aT(a))throw aA("JWT Header must be a top level object",sR,e);if(t(a),void 0!==a.crit)throw new ak('no JWT "crit" header parameter extensions are supported',{cause:{header:a}});try{s=JSON.parse(a_(aS(c)))}catch(e){throw aA("failed to parse JWT Payload body as base64url encoded JSON",sN,e)}if(!aT(s))throw aA("JWT Payload must be a top level object",sR,e);let u=aq()+r;if(void 0!==s.exp){if("number"!=typeof s.exp)throw aA('unexpected JWT "exp" (expiration time) claim type',sR,{claims:s});if(s.exp<=u-n)throw aA('unexpected JWT "exp" (expiration time) claim value, expiration is past current timestamp',sL,{claims:s,now:u,tolerance:n,claim:"exp"})}if(void 0!==s.iat&&"number"!=typeof s.iat)throw aA('unexpected JWT "iat" (issued at) claim type',sR,{claims:s});if(void 0!==s.iss&&"string"!=typeof s.iss)throw aA('unexpected JWT "iss" (issuer) claim type',sR,{claims:s});if(void 0!==s.nbf){if("number"!=typeof s.nbf)throw aA('unexpected JWT "nbf" (not before) claim type',sR,{claims:s});if(s.nbf>u+n)throw aA('unexpected JWT "nbf" (not before) claim value',sL,{claims:s,now:u,tolerance:n,claim:"nbf"})}if(void 0!==s.aud&&"string"!=typeof s.aud&&!Array.isArray(s.aud))throw aA('unexpected JWT "aud" (audience) claim type',sR,{claims:s});return{header:a,claims:s,jwt:e}}async function sQ(e,t,r){let n;switch(t.alg){case"RS256":case"PS256":case"ES256":n="SHA-256";break;case"RS384":case"PS384":case"ES384":n="SHA-384";break;case"RS512":case"PS512":case"ES512":case"Ed25519":case"EdDSA":n="SHA-512";break;default:throw new ak(`unsupported JWS algorithm for ${r} calculation`,{cause:{alg:t.alg}})}let i=await crypto.subtle.digest(n,a_(e));return aS(i.slice(0,i.byteLength/2))}async function sG(e){if(e.bodyUsed)throw af("form_post Request instances must contain a readable body",au,{cause:e});return e.text()}function sX(e,t,r,n){if(void 0!==e){if("string"==typeof e?n.alg!==e:!e.includes(n.alg))throw aA('unexpected JWT "alg" header parameter',sR,{header:n,expected:e,reason:"client configuration"});return}if(Array.isArray(t)){if(!t.includes(n.alg))throw aA('unexpected JWT "alg" header parameter',sR,{header:n,expected:t,reason:"authorization server metadata"});return}if(void 0!==r){if("string"==typeof r?n.alg!==r:"function"==typeof r?!r(n.alg):!r.includes(n.alg))throw aA('unexpected JWT "alg" header parameter',sR,{header:n,expected:r,reason:"default value"});return}throw aA('missing client or server configuration to verify used JWT "alg" header parameter',void 0,{client:e,issuer:t,fallback:r})}function sY(e,t){let{0:r,length:n}=e.getAll(t);if(n>1)throw aA(`"${t}" parameter must be provided only once`,sR);return r}let sZ=Symbol(),s0=Symbol();async function s1(e,t){let{ext:r,key_ops:n,use:i,...a}=t;return crypto.subtle.importKey("jwk",a,function(e){switch(e){case"PS256":case"PS384":case"PS512":return{name:"RSA-PSS",hash:`SHA-${e.slice(-3)}`};case"RS256":case"RS384":case"RS512":return{name:"RSASSA-PKCS1-v1_5",hash:`SHA-${e.slice(-3)}`};case"ES256":case"ES384":return{name:"ECDSA",namedCurve:`P-${e.slice(-3)}`};case"ES512":return{name:"ECDSA",namedCurve:"P-521"};case"Ed25519":case"EdDSA":return"Ed25519";default:throw new ak("unsupported JWS algorithm",{cause:{alg:e}})}}(e),!0,["verify"])}async function s2(e,t=aU){let r;try{r=await e.json()}catch(r){throw t(e),aA('failed to parse "response" body as JSON',sN,r)}if(!aT(r))throw aA('"response" body must be a top level object',sR,{body:r});return r}let s5=Symbol(),s6=Symbol(),s3=Symbol();async function s8(e,t,r){let{cookies:n,logger:i}=r,a=n[e],s=new Date;s.setTime(s.getTime()+9e5),i.debug(`CREATE_${e.toUpperCase()}`,{name:a.name,payload:t,COOKIE_TTL:900,expires:s});let o=await nL({...r.jwt,maxAge:900,token:{value:t},salt:a.name}),c={...a.options,expires:s};return{name:a.name,value:o,options:c}}async function s4(e,t,r){try{let{logger:n,cookies:i,jwt:a}=r;if(n.debug(`PARSE_${e.toUpperCase()}`,{cookie:t}),!t)throw new tw(`${e} cookie was missing`);let s=await nD({...a,token:t,salt:i[e].name});if(s?.value)return s.value;throw Error("Invalid cookie")}catch(t){throw new tw(`${e} value could not be parsed`,{cause:t})}}function s9(e,t,r){let{logger:n,cookies:i}=t,a=i[e];n.debug(`CLEAR_${e.toUpperCase()}`,{cookie:a}),r.push({name:a.name,value:"",options:{...i[e].options,maxAge:0}})}function s7(e,t){return async function(r,n,i){let{provider:a,logger:s}=i;if(!a?.checks?.includes(e))return;let o=r?.[i.cookies[t].name];s.debug(`USE_${t.toUpperCase()}`,{value:o});let c=await s4(t,o,i);return s9(t,i,n),c}}let oe={async create(e){let t=aD(),r=await aM(t);return{cookie:await s8("pkceCodeVerifier",t,e),value:r}},use:s7("pkce","pkceCodeVerifier")},ot="encodedState",or={async create(e,t){let{provider:r}=e;if(!r.checks.includes("state")){if(t)throw new tw("State data was provided but the provider is not configured to use state");return}let n={origin:t,random:aD()},i=await nL({secret:e.jwt.secret,token:n,salt:ot,maxAge:900});return{cookie:await s8("state",i,e),value:i}},use:s7("state","state"),async decode(e,t){try{t.logger.debug("DECODE_STATE",{state:e});let r=await nD({secret:t.jwt.secret,token:e,salt:ot});if(r)return r;throw Error("Invalid state")}catch(e){throw new tw("State could not be decoded",{cause:e})}}},on={async create(e){if(!e.provider.checks.includes("nonce"))return;let t=aD();return{cookie:await s8("nonce",t,e),value:t}},use:s7("nonce","nonce")},oi="encodedWebauthnChallenge",oa={create:async(e,t,r)=>({cookie:await s8("webauthnChallenge",await nL({secret:e.jwt.secret,token:{challenge:t,registerData:r},salt:oi,maxAge:900}),e)}),async use(e,t,r){let n=t?.[e.cookies.webauthnChallenge.name],i=await s4("webauthnChallenge",n,e),a=await nD({secret:e.jwt.secret,token:i,salt:oi});if(s9("webauthnChallenge",e,r),!a)throw new tw("WebAuthn challenge was missing");return a}};function os(e){return encodeURIComponent(e).replace(/%20/g,"+")}async function oo(e,t,r){let n,i,a;let{logger:s,provider:o}=r,{token:c,userinfo:l}=o;if(c?.url&&"authjs.dev"!==c.url.host||l?.url&&"authjs.dev"!==l.url.host)n={issuer:o.issuer??"https://authjs.dev",token_endpoint:c?.url.toString(),userinfo_endpoint:l?.url.toString()};else{let e=new URL(o.issuer),t=await aR(e,{[ah]:!0,[am]:o[n3]});if(!(n=await aj(e,t)).token_endpoint)throw TypeError("TODO: Authorization server did not provide a token endpoint.");if(!n.userinfo_endpoint)throw TypeError("TODO: Authorization server did not provide a userinfo endpoint.")}let u={client_id:o.clientId,...o.client};switch(u.token_endpoint_auth_method){case void 0:case"client_secret_basic":i=(e,t,r,n)=>{n.set("authorization",function(e,t){let r=os(e),n=os(t),i=btoa(`${r}:${n}`);return`Basic ${i}`}(o.clientId,o.clientSecret))};break;case"client_secret_post":var d;a$(d=o.clientSecret,'"clientSecret"'),i=(e,t,r,n)=>{r.set("client_id",t.client_id),r.set("client_secret",d)};break;case"client_secret_jwt":i=function(e,t){let r;a$(e,'"clientSecret"');let n=void 0;return async(t,i,a,s)=>{r||=await crypto.subtle.importKey("raw",a_(e),{hash:"SHA-256",name:"HMAC"},!1,["sign"]);let o={alg:"HS256"},c=aF(t,i);n?.(o,c);let l=`${aS(a_(JSON.stringify(o)))}.${aS(a_(JSON.stringify(c)))}`,u=await crypto.subtle.sign(r.algorithm,r,a_(l));a.set("client_id",i.client_id),a.set("client_assertion_type","urn:ietf:params:oauth:client-assertion-type:jwt-bearer"),a.set("client_assertion",`${l}.${aS(new Uint8Array(u))}`)}}(o.clientSecret);break;case"private_key_jwt":i=function(e,t){let{key:r,kid:n}=e instanceof CryptoKey?{key:e}:e?.key instanceof CryptoKey?(void 0!==e.kid&&a$(e.kid,'"kid"'),{key:e.key,kid:e.kid}):{};return function(e,t){if(function(e,t){if(!(e instanceof CryptoKey))throw af(`${t} must be a CryptoKey`,ad)}(e,t),"private"!==e.type)throw af(`${t} must be a private CryptoKey`,au)}(r,'"clientPrivateKey.key"'),async(e,i,a,s)=>{let o={alg:function(e){switch(e.algorithm.name){case"RSA-PSS":return function(e){switch(e.algorithm.hash.name){case"SHA-256":return"PS256";case"SHA-384":return"PS384";case"SHA-512":return"PS512";default:throw new ak("unsupported RsaHashedKeyAlgorithm hash name",{cause:e})}}(e);case"RSASSA-PKCS1-v1_5":return function(e){switch(e.algorithm.hash.name){case"SHA-256":return"RS256";case"SHA-384":return"RS384";case"SHA-512":return"RS512";default:throw new ak("unsupported RsaHashedKeyAlgorithm hash name",{cause:e})}}(e);case"ECDSA":return function(e){switch(e.algorithm.namedCurve){case"P-256":return"ES256";case"P-384":return"ES384";case"P-521":return"ES512";default:throw new ak("unsupported EcKeyAlgorithm namedCurve",{cause:e})}}(e);case"Ed25519":case"EdDSA":return"Ed25519";default:throw new ak("unsupported CryptoKey algorithm name",{cause:e})}}(r),kid:n},c=aF(e,i);t?.[ay]?.(o,c),a.set("client_id",i.client_id),a.set("client_assertion_type","urn:ietf:params:oauth:client-assertion-type:jwt-bearer"),a.set("client_assertion",await aK(o,c,r))}}(o.token.clientPrivateKey,{[ay](e,t){t.aud=[n.issuer,n.token_endpoint]}});break;case"none":i=(e,t,r,n)=>{r.set("client_id",t.client_id)};break;default:throw Error("unsupported client authentication method")}let f=[],h=await or.use(t,f,r);try{a=function(e,t,r,n){var i;if(az(e),aW(t),r instanceof URL&&(r=r.searchParams),!(r instanceof URLSearchParams))throw af('"parameters" must be an instance of URLSearchParams, or URL',ad);if(sY(r,"response"))throw aA('"parameters" contains a JARM response, use validateJwtAuthResponse() instead of validateAuthResponse()',sR,{parameters:r});let a=sY(r,"iss"),s=sY(r,"state");if(!a&&e.authorization_response_iss_parameter_supported)throw aA('response parameter "iss" (issuer) missing',sR,{parameters:r});if(a&&a!==e.issuer)throw aA('unexpected "iss" (issuer) response parameter value',sR,{expected:e.issuer,parameters:r});switch(n){case void 0:case s0:if(void 0!==s)throw aA('unexpected "state" response parameter encountered',sR,{expected:void 0,parameters:r});break;case sZ:break;default:if(a$(n,'"expectedState" argument'),s!==n)throw aA(void 0===s?'response parameter "state" missing':'unexpected "state" response parameter value',sR,{expected:n,parameters:r})}if(sY(r,"error"))throw new aZ("authorization response from the server is an error",{cause:r});let o=sY(r,"id_token"),c=sY(r,"token");if(void 0!==o||void 0!==c)throw new ak("implicit and hybrid flows are not supported");return i=new URLSearchParams(r),sy.add(i),i}(n,u,new URLSearchParams(e),o.checks.includes("state")?h:sZ)}catch(e){if(e instanceof aZ){let t={providerId:o.id,...Object.fromEntries(e.cause.entries())};throw s.debug("OAuthCallbackError",t),new tA("OAuth Provider returned an error",t)}throw e}let p=await oe.use(t,f,r),g=o.callbackUrl;!r.isOnRedirectProxy&&o.redirectProxyUrl&&(g=o.redirectProxyUrl);let m=await sb(n,u,i,a,g,p??"decoy",{[ah]:!0,[am]:(...e)=>(o.checks.includes("pkce")||e[1].body.delete("code_verifier"),(o[n3]??fetch)(...e))});o.token?.conform&&(m=await o.token.conform(m.clone())??m);let y={},b="oidc"===o.type;if(o[n8])switch(o.id){case"microsoft-entra-id":case"azure-ad":{let e=await m.clone().json();if(e.error){let t={providerId:o.id,...e};throw new tA(`OAuth Provider returned an error: ${e.error}`,t)}let{tid:t}=function(e){let t,r;if("string"!=typeof e)throw new rc("JWTs must use Compact JWS serialization, JWT must be a string");let{1:n,length:i}=e.split(".");if(5===i)throw new rc("Only JWTs using Compact JWS serialization can be decoded");if(3!==i)throw new rc("Invalid JWT");if(!n)throw new rc("JWTs must contain a payload");try{t=t7(n)}catch{throw new rc("Failed to base64url decode the payload")}try{r=JSON.parse(t6.decode(t))}catch{throw new rc("Failed to parse the decoded payload as JSON")}if(!rg(r))throw new rc("Invalid JWT Claims Set");return r}(e.id_token);if("string"==typeof t){let e=n.issuer?.match(/microsoftonline\.com\/(\w+)\/v2\.0/)?.[1]??"common",r=new URL(n.issuer.replace(e,t)),i=await aR(r,{[am]:o[n3]});n=await aj(r,i)}}}let w=await sS(n,u,m,{expectedNonce:await on.use(t,f,r),requireIdToken:b});if(b){let t=su(w);if(y=t,o[n8]&&"apple"===o.id)try{y.user=JSON.parse(e?.user)}catch{}if(!1===o.idToken){let e=await se(n,u,w.access_token,{[am]:o[n3],[ah]:!0});y=await sa(n,u,t.sub,e)}}else if(l?.request){let e=await l.request({tokens:w,provider:o});e instanceof Object&&(y=e)}else if(l?.url){let e=await se(n,u,w.access_token,{[am]:o[n3],[ah]:!0});y=await e.json()}else throw TypeError("No userinfo endpoint configured");return w.expires_in&&(w.expires_at=Math.floor(Date.now()/1e3)+Number(w.expires_in)),{...await oc(y,o,w,s),profile:y,cookies:f}}async function oc(e,t,r,n){try{let n=await t.profile(e,r);return{user:{...n,id:crypto.randomUUID(),email:n.email?.toLowerCase()},account:{...r,provider:t.id,type:t.type,providerAccountId:n.id??crypto.randomUUID()}}}catch(r){n.debug("getProfile error details",e),n.error(new tT(r,{provider:t.id}))}}var ol=r(356).Buffer;async function ou(e,t,r,n){let i=await og(e,t,r),{cookie:a}=await oa.create(e,i.challenge,r);return{status:200,cookies:[...n??[],a],body:{action:"register",options:i},headers:{"Content-Type":"application/json"}}}async function od(e,t,r,n){let i=await op(e,t,r),{cookie:a}=await oa.create(e,i.challenge);return{status:200,cookies:[...n??[],a],body:{action:"authenticate",options:i},headers:{"Content-Type":"application/json"}}}async function of(e,t,r){let n;let{adapter:i,provider:a}=e,s=t.body&&"string"==typeof t.body.data?JSON.parse(t.body.data):void 0;if(!s||"object"!=typeof s||!("id"in s)||"string"!=typeof s.id)throw new tl("Invalid WebAuthn Authentication response");let o=ob(oy(s.id)),c=await i.getAuthenticator(o);if(!c)throw new tl(`WebAuthn authenticator not found in database: ${JSON.stringify({credentialID:o})}`);let{challenge:l}=await oa.use(e,t.cookies,r);try{let r=a.getRelayingParty(e,t);n=await a.simpleWebAuthn.verifyAuthenticationResponse({...a.verifyAuthenticationOptions,expectedChallenge:l,response:s,authenticator:{...c,credentialDeviceType:c.credentialDeviceType,transports:ow(c.transports),credentialID:oy(c.credentialID),credentialPublicKey:oy(c.credentialPublicKey)},expectedOrigin:r.origin,expectedRPID:r.id})}catch(e){throw new tH(e)}let{verified:u,authenticationInfo:d}=n;if(!u)throw new tH("WebAuthn authentication response could not be verified");try{let{newCounter:e}=d;await i.updateAuthenticatorCounter(c.credentialID,e)}catch(e){throw new td(`Failed to update authenticator counter. This may cause future authentication attempts to fail. ${JSON.stringify({credentialID:o,oldCounter:c.counter,newCounter:d.newCounter})}`,e)}let f=await i.getAccount(c.providerAccountId,a.id);if(!f)throw new tl(`WebAuthn account not found in database: ${JSON.stringify({credentialID:o,providerAccountId:c.providerAccountId})}`);let h=await i.getUser(f.userId);if(!h)throw new tl(`WebAuthn user not found in database: ${JSON.stringify({credentialID:o,providerAccountId:c.providerAccountId,userID:f.userId})}`);return{account:f,user:h}}async function oh(e,t,r){var n;let i;let{provider:a}=e,s=t.body&&"string"==typeof t.body.data?JSON.parse(t.body.data):void 0;if(!s||"object"!=typeof s||!("id"in s)||"string"!=typeof s.id)throw new tl("Invalid WebAuthn Registration response");let{challenge:o,registerData:c}=await oa.use(e,t.cookies,r);if(!c)throw new tl("Missing user registration data in WebAuthn challenge cookie");try{let r=a.getRelayingParty(e,t);i=await a.simpleWebAuthn.verifyRegistrationResponse({...a.verifyRegistrationOptions,expectedChallenge:o,response:s,expectedOrigin:r.origin,expectedRPID:r.id})}catch(e){throw new tH(e)}if(!i.verified||!i.registrationInfo)throw new tH("WebAuthn registration response could not be verified");let l={providerAccountId:ob(i.registrationInfo.credentialID),provider:e.provider.id,type:a.type},u={providerAccountId:l.providerAccountId,counter:i.registrationInfo.counter,credentialID:ob(i.registrationInfo.credentialID),credentialPublicKey:ob(i.registrationInfo.credentialPublicKey),credentialBackedUp:i.registrationInfo.credentialBackedUp,credentialDeviceType:i.registrationInfo.credentialDeviceType,transports:(n=s.response.transports,n?.join(","))};return{user:c,account:l,authenticator:u}}async function op(e,t,r){let{provider:n,adapter:i}=e,a=r&&r.id?await i.listAuthenticatorsByUserId(r.id):null,s=n.getRelayingParty(e,t);return await n.simpleWebAuthn.generateAuthenticationOptions({...n.authenticationOptions,rpID:s.id,allowCredentials:a?.map(e=>({id:oy(e.credentialID),type:"public-key",transports:ow(e.transports)}))})}async function og(e,t,r){let{provider:n,adapter:i}=e,a=r.id?await i.listAuthenticatorsByUserId(r.id):null,s=nY(32),o=n.getRelayingParty(e,t);return await n.simpleWebAuthn.generateRegistrationOptions({...n.registrationOptions,userID:s,userName:r.email,userDisplayName:r.name??void 0,rpID:o.id,rpName:o.name,excludeCredentials:a?.map(e=>({id:oy(e.credentialID),type:"public-key",transports:ow(e.transports)}))})}function om(e){let{provider:t,adapter:r}=e;if(!r)throw new tx("An adapter is required for the WebAuthn provider");if(!t||"webauthn"!==t.type)throw new t$("Provider must be WebAuthn");return{...e,provider:t,adapter:r}}function oy(e){return new Uint8Array(ol.from(e,"base64"))}function ob(e){return ol.from(e).toString("base64")}function ow(e){return e?e.split(","):void 0}async function ov(e,t,r,n){if(!t.provider)throw new t$("Callback route called without provider");let{query:i,body:a,method:s,headers:o}=e,{provider:c,adapter:l,url:u,callbackUrl:d,pages:f,jwt:h,events:p,callbacks:g,session:{strategy:m,maxAge:y},logger:b}=t,w="jwt"===m;try{if("oauth"===c.type||"oidc"===c.type){let s;let o=c.authorization?.url.searchParams.get("response_mode")==="form_post"?a:i;if(t.isOnRedirectProxy&&o?.state){let e=await or.decode(o.state,t);if(e?.origin&&new URL(e.origin).origin!==t.url.origin){let t=`${e.origin}?${new URLSearchParams(o)}`;return b.debug("Proxy redirecting to",t),{redirect:t,cookies:n}}}let m=await oo(o,e.cookies,t);m.cookies.length&&n.push(...m.cookies),b.debug("authorization result",m);let{user:v,account:x,profile:_}=m;if(!v||!x||!_)return{redirect:`${u}/signin`,cookies:n};if(l){let{getUserByAccount:e}=l;s=await e({providerAccountId:x.providerAccountId,provider:c.id})}let S=await ox({user:s??v,account:x,profile:_},t);if(S)return{redirect:S,cookies:n};let{user:k,session:E,isNewUser:A}=await ac(r.value,v,x,t);if(w){let e={name:k.name,email:k.email,picture:k.image,sub:k.id?.toString()},i=await g.jwt({token:e,user:k,account:x,profile:_,isNewUser:A,trigger:A?"signUp":"signIn"});if(null===i)n.push(...r.clean());else{let e=t.cookies.sessionToken.name,a=await h.encode({...h,token:i,salt:e}),s=new Date;s.setTime(s.getTime()+1e3*y);let o=r.chunk(a,{expires:s});n.push(...o)}}else n.push({name:t.cookies.sessionToken.name,value:E.sessionToken,options:{...t.cookies.sessionToken.options,expires:E.expires}});if(await p.signIn?.({user:k,account:x,profile:_,isNewUser:A}),A&&f.newUser)return{redirect:`${f.newUser}${f.newUser.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:d})}`,cookies:n};return{redirect:d,cookies:n}}if("email"===c.type){let e=i?.token,a=i?.email;if(!e){let t=TypeError("Missing token. The sign-in URL was manually opened without token or the link was not sent correctly in the email.",{cause:{hasToken:!!e}});throw t.name="Configuration",t}let s=c.secret??t.secret,o=await l.useVerificationToken({identifier:a,token:await nX(`${e}${s}`)}),u=!!o,m=u&&o.expires.valueOf()<Date.now();if(!u||m||a&&o.identifier!==a)throw new tU({hasInvite:u,expired:m});let{identifier:b}=o,v=await l.getUserByEmail(b)??{id:crypto.randomUUID(),email:b,emailVerified:null},x={providerAccountId:v.email,userId:v.id,type:"email",provider:c.id},_=await ox({user:v,account:x},t);if(_)return{redirect:_,cookies:n};let{user:S,session:k,isNewUser:E}=await ac(r.value,v,x,t);if(w){let e={name:S.name,email:S.email,picture:S.image,sub:S.id?.toString()},i=await g.jwt({token:e,user:S,account:x,isNewUser:E,trigger:E?"signUp":"signIn"});if(null===i)n.push(...r.clean());else{let e=t.cookies.sessionToken.name,a=await h.encode({...h,token:i,salt:e}),s=new Date;s.setTime(s.getTime()+1e3*y);let o=r.chunk(a,{expires:s});n.push(...o)}}else n.push({name:t.cookies.sessionToken.name,value:k.sessionToken,options:{...t.cookies.sessionToken.options,expires:k.expires}});if(await p.signIn?.({user:S,account:x,isNewUser:E}),E&&f.newUser)return{redirect:`${f.newUser}${f.newUser.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:d})}`,cookies:n};return{redirect:d,cookies:n}}if("credentials"===c.type&&"POST"===s){let e=a??{};Object.entries(i??{}).forEach(([e,t])=>u.searchParams.set(e,t));let l=await c.authorize(e,new Request(u,{headers:o,method:s,body:JSON.stringify(a)}));if(l)l.id=l.id?.toString()??crypto.randomUUID();else throw new ty;let f={providerAccountId:l.id,type:"credentials",provider:c.id},m=await ox({user:l,account:f,credentials:e},t);if(m)return{redirect:m,cookies:n};let b={name:l.name,email:l.email,picture:l.image,sub:l.id},w=await g.jwt({token:b,user:l,account:f,isNewUser:!1,trigger:"signIn"});if(null===w)n.push(...r.clean());else{let e=t.cookies.sessionToken.name,i=await h.encode({...h,token:w,salt:e}),a=new Date;a.setTime(a.getTime()+1e3*y);let s=r.chunk(i,{expires:a});n.push(...s)}return await p.signIn?.({user:l,account:f}),{redirect:d,cookies:n}}if("webauthn"===c.type&&"POST"===s){let i,a,s;let o=e.body?.action;if("string"!=typeof o||"authenticate"!==o&&"register"!==o)throw new tl("Invalid action parameter");let c=om(t);switch(o){case"authenticate":{let t=await of(c,e,n);i=t.user,a=t.account;break}case"register":{let r=await oh(t,e,n);i=r.user,a=r.account,s=r.authenticator}}await ox({user:i,account:a},t);let{user:l,isNewUser:u,session:m,account:b}=await ac(r.value,i,a,t);if(!b)throw new tl("Error creating or finding account");if(s&&l.id&&await c.adapter.createAuthenticator({...s,userId:l.id}),w){let e={name:l.name,email:l.email,picture:l.image,sub:l.id?.toString()},i=await g.jwt({token:e,user:l,account:b,isNewUser:u,trigger:u?"signUp":"signIn"});if(null===i)n.push(...r.clean());else{let e=t.cookies.sessionToken.name,a=await h.encode({...h,token:i,salt:e}),s=new Date;s.setTime(s.getTime()+1e3*y);let o=r.chunk(a,{expires:s});n.push(...o)}}else n.push({name:t.cookies.sessionToken.name,value:m.sessionToken,options:{...t.cookies.sessionToken.options,expires:m.expires}});if(await p.signIn?.({user:l,account:b,isNewUser:u}),u&&f.newUser)return{redirect:`${f.newUser}${f.newUser.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:d})}`,cookies:n};return{redirect:d,cookies:n}}throw new t$(`Callback for provider type (${c.type}) is not supported`)}catch(t){if(t instanceof tl)throw t;let e=new th(t,{provider:c.id});throw b.debug("callback route error details",{method:s,query:i,body:a}),e}}async function ox(e,t){let r;let{signIn:n,redirect:i}=t.callbacks;try{r=await n(e)}catch(e){if(e instanceof tl)throw e;throw new tf(e)}if(!r)throw new tf("AccessDenied");if("string"==typeof r)return await i({url:r,baseUrl:t.url.origin})}async function o_(e,t,r,n,i){let{adapter:a,jwt:s,events:o,callbacks:c,logger:l,session:{strategy:u,maxAge:d}}=e,f={body:null,headers:{"Content-Type":"application/json",...!n&&{"Cache-Control":"private, no-cache, no-store",Expires:"0",Pragma:"no-cache"}},cookies:r},h=t.value;if(!h)return f;if("jwt"===u){try{let r=e.cookies.sessionToken.name,a=await s.decode({...s,token:h,salt:r});if(!a)throw Error("Invalid JWT");let l=await c.jwt({token:a,...n&&{trigger:"update"},session:i}),u=ao(d);if(null!==l){let e={user:{name:l.name,email:l.email,image:l.picture},expires:u.toISOString()},n=await c.session({session:e,token:l});f.body=n;let i=await s.encode({...s,token:l,salt:r}),a=t.chunk(i,{expires:u});f.cookies?.push(...a),await o.session?.({session:n,token:l})}else f.cookies?.push(...t.clean())}catch(e){l.error(new tv(e)),f.cookies?.push(...t.clean())}return f}try{let{getSessionAndUser:r,deleteSession:s,updateSession:l}=a,u=await r(h);if(u&&u.session.expires.valueOf()<Date.now()&&(await s(h),u=null),u){let{user:t,session:r}=u,a=e.session.updateAge,s=r.expires.valueOf()-1e3*d+1e3*a,p=ao(d);s<=Date.now()&&await l({sessionToken:h,expires:p});let g=await c.session({session:{...r,user:t},user:t,newSession:i,...n?{trigger:"update"}:{}});f.body=g,f.cookies?.push({name:e.cookies.sessionToken.name,value:h,options:{...e.cookies.sessionToken.options,expires:p}}),await o.session?.({session:g})}else h&&f.cookies?.push(...t.clean())}catch(e){l.error(new tP(e))}return f}async function oS(e,t){let r,n;let{logger:i,provider:a}=t,s=a.authorization?.url;if(!s||"authjs.dev"===s.host){let e=new URL(a.issuer),t=await aR(e,{[am]:a[n3],[ah]:!0}),r=await aj(e,t).catch(t=>{if(!(t instanceof TypeError)||"Invalid URL"!==t.message)throw t;throw TypeError(`Discovery request responded with an invalid issuer. expected: ${e}`)});if(!r.authorization_endpoint)throw TypeError("Authorization server did not provide an authorization endpoint.");s=new URL(r.authorization_endpoint)}let o=s.searchParams,c=a.callbackUrl;!t.isOnRedirectProxy&&a.redirectProxyUrl&&(c=a.redirectProxyUrl,n=a.callbackUrl,i.debug("using redirect proxy",{redirect_uri:c,data:n}));let l=Object.assign({response_type:"code",client_id:a.clientId,redirect_uri:c,...a.authorization?.params},Object.fromEntries(a.authorization?.url.searchParams??[]),e);for(let e in l)o.set(e,l[e]);let u=[];a.authorization?.url.searchParams.get("response_mode")==="form_post"&&(t.cookies.state.options.sameSite="none",t.cookies.state.options.secure=!0,t.cookies.nonce.options.sameSite="none",t.cookies.nonce.options.secure=!0);let d=await or.create(t,n);if(d&&(o.set("state",d.value),u.push(d.cookie)),a.checks?.includes("pkce")){if(r&&!r.code_challenge_methods_supported?.includes("S256"))"oidc"===a.type&&(a.checks=["nonce"]);else{let{value:e,cookie:r}=await oe.create(t);o.set("code_challenge",e),o.set("code_challenge_method","S256"),u.push(r)}}let f=await on.create(t);return f&&(o.set("nonce",f.value),u.push(f.cookie)),"oidc"!==a.type||s.searchParams.has("scope")||s.searchParams.set("scope","openid profile email"),i.debug("authorization url is ready",{url:s,cookies:u,provider:a}),{redirect:s.toString(),cookies:u}}async function ok(e,t){let r;let{body:n}=e,{provider:i,callbacks:a,adapter:s}=t,o=(i.normalizeIdentifier??function(e){if(!e)throw Error("Missing email from request body.");let[t,r]=e.toLowerCase().trim().split("@");return r=r.split(",")[0],`${t}@${r}`})(n?.email),c={id:crypto.randomUUID(),email:o,emailVerified:null},l=await s.getUserByEmail(o)??c,u={providerAccountId:o,userId:l.id,type:"email",provider:i.id};try{r=await a.signIn({user:l,account:u,email:{verificationRequest:!0}})}catch(e){throw new tf(e)}if(!r)throw new tf("AccessDenied");if("string"==typeof r)return{redirect:await a.redirect({url:r,baseUrl:t.url.origin})};let{callbackUrl:d,theme:f}=t,h=await i.generateVerificationToken?.()??nY(32),p=new Date(Date.now()+(i.maxAge??86400)*1e3),g=i.secret??t.secret,m=new URL(t.basePath,t.url.origin),y=i.sendVerificationRequest({identifier:o,token:h,expires:p,url:`${m}/callback/${i.id}?${new URLSearchParams({callbackUrl:d,token:h,email:o})}`,provider:i,theme:f,request:new Request(e.url,{headers:e.headers,method:e.method,body:"POST"===e.method?JSON.stringify(e.body??{}):void 0})}),b=s.createVerificationToken?.({identifier:o,token:await nX(`${h}${g}`),expires:p});return await Promise.all([y,b]),{redirect:`${m}/verify-request?${new URLSearchParams({provider:i.id,type:i.type})}`}}async function oE(e,t,r){let n=`${r.url.origin}${r.basePath}/signin`;if(!r.provider)return{redirect:n,cookies:t};switch(r.provider.type){case"oauth":case"oidc":{let{redirect:n,cookies:i}=await oS(e.query,r);return i&&t.push(...i),{redirect:n,cookies:t}}case"email":return{...await ok(e,r),cookies:t};default:return{redirect:n,cookies:t}}}async function oA(e,t,r){let{jwt:n,events:i,callbackUrl:a,logger:s,session:o}=r,c=t.value;if(!c)return{redirect:a,cookies:e};try{if("jwt"===o.strategy){let e=r.cookies.sessionToken.name,t=await n.decode({...n,token:c,salt:e});await i.signOut?.({token:t})}else{let e=await r.adapter?.deleteSession(c);await i.signOut?.({session:e})}}catch(e){s.error(new tN(e))}return e.push(...t.clean()),{redirect:a,cookies:e}}async function oT(e,t){let{adapter:r,jwt:n,session:{strategy:i}}=e,a=t.value;if(!a)return null;if("jwt"===i){let t=e.cookies.sessionToken.name,r=await n.decode({...n,token:a,salt:t});if(r&&r.sub)return{id:r.sub,name:r.name,email:r.email,image:r.picture}}else{let e=await r?.getSessionAndUser(a);if(e)return e.user}return null}async function oP(e,t,r,n){let i=om(t),{provider:a}=i,{action:s}=e.query??{};if("register"!==s&&"authenticate"!==s&&void 0!==s)return{status:400,body:{error:"Invalid action"},cookies:n,headers:{"Content-Type":"application/json"}};let o=await oT(t,r),c=o?{user:o,exists:!0}:await a.getUserInfo(t,e),l=c?.user;switch(function(e,t,r){let{user:n,exists:i=!1}=r??{};switch(e){case"authenticate":return"authenticate";case"register":if(n&&t===i)return"register";break;case void 0:if(!t){if(!n||i)return"authenticate";return"register"}}return null}(s,!!o,c)){case"authenticate":return od(i,e,l,n);case"register":if("string"==typeof l?.email)return ou(i,e,l,n);break;default:return{status:400,body:{error:"Invalid request"},cookies:n,headers:{"Content-Type":"application/json"}}}}async function oC(e,t){let{action:r,providerId:n,error:i,method:a}=e,s=t.skipCSRFCheck===n5,{options:o,cookies:c}=await ir({authOptions:t,action:r,providerId:n,url:e.url,callbackUrl:e.body?.callbackUrl??e.query?.callbackUrl,csrfToken:e.body?.csrfToken,cookies:e.cookies,isPost:"POST"===a,csrfDisabled:s}),l=new tc(o.cookies.sessionToken,e.cookies,o.logger);if("GET"===a){let t=as({...o,query:e.query,cookies:c});switch(r){case"callback":return await ov(e,o,l,c);case"csrf":return t.csrf(s,o,c);case"error":return t.error(i);case"providers":return t.providers(o.providers);case"session":return await o_(o,l,c);case"signin":return t.signin(n,i);case"signout":return t.signout();case"verify-request":return t.verifyRequest();case"webauthn-options":return await oP(e,o,l,c)}}else{let{csrfTokenVerified:t}=o;switch(r){case"callback":return"credentials"===o.provider.type&&n0(r,t),await ov(e,o,l,c);case"session":return n0(r,t),await o_(o,l,c,!0,e.body?.data);case"signin":return n0(r,t),await oE(e,c,o);case"signout":return n0(r,t),await oA(c,l,o)}}throw new tR(`Cannot handle action: ${r}`)}function oO(e,t,r,n,i){let a;let s=i?.basePath,o=n.AUTH_URL??n.NEXTAUTH_URL;if(o)a=new URL(o),s&&"/"!==s&&"/"!==a.pathname&&(a.pathname!==s&&nW(i).warn("env-url-basepath-mismatch"),a.pathname="/");else{let e=r.get("x-forwarded-host")??r.get("host"),n=r.get("x-forwarded-proto")??t??"https",i=n.endsWith(":")?n:n+":";a=new URL(`${i}//${e}`)}let c=a.toString().replace(/\/$/,"");if(s){let t=s?.replace(/(^\/|\/$)/g,"")??"";return new URL(`${c}/${t}/${e}`)}return new URL(`${c}/${e}`)}async function oN(e,t){let r=nW(t),n=await nQ(e,t);if(!n)return Response.json("Bad request.",{status:400});let i=function(e,t){let{url:r}=e,n=[];if(!tW&&t.debug&&n.push("debug-enabled"),!t.trustHost)return new tj(`Host must be trusted. URL was: ${e.url}`);if(!t.secret?.length)return new tk("Please define a `secret`");let i=e.query?.callbackUrl;if(i&&!tF(i,r.origin))return new tm(`Invalid callback URL. Received: ${i}`);let{callbackUrl:a}=to(t.useSecureCookies??"https:"===r.protocol),s=e.cookies?.[t.cookies?.callbackUrl?.name??a.name];if(s&&!tF(s,r.origin))return new tm(`Invalid callback URL. Received: ${s}`);let o=!1;for(let e of t.providers){let t="function"==typeof e?e():e;if(("oauth"===t.type||"oidc"===t.type)&&!(t.issuer??t.options?.issuer)){let e;let{authorization:r,token:n,userinfo:i}=t;if("string"==typeof r||r?.url?"string"==typeof n||n?.url?"string"==typeof i||i?.url||(e="userinfo"):e="token":e="authorization",e)return new tb(`Provider "${t.id}" is missing both \`issuer\` and \`${e}\` endpoint config. At least one of them is required`)}if("credentials"===t.type)tK=!0;else if("email"===t.type)tV=!0;else if("webauthn"===t.type){var c;if(tJ=!0,t.simpleWebAuthnBrowserVersion&&(c=t.simpleWebAuthnBrowserVersion,!/^v\d+(?:\.\d+){0,2}$/.test(c)))return new tl(`Invalid provider config for "${t.id}": simpleWebAuthnBrowserVersion "${t.simpleWebAuthnBrowserVersion}" must be a valid semver string.`);if(t.enableConditionalUI){if(o)return new tM("Multiple webauthn providers have 'enableConditionalUI' set to True. Only one provider can have this option enabled at a time");if(o=!0,!Object.values(t.formFields).some(e=>e.autocomplete&&e.autocomplete.toString().indexOf("webauthn")>-1))return new tB(`Provider "${t.id}" has 'enableConditionalUI' set to True, but none of its formFields have 'webauthn' in their autocomplete param`)}}}if(tK){let e=t.session?.strategy==="database",r=!t.providers.some(e=>"credentials"!==("function"==typeof e?e():e).type);if(e&&r)return new tI("Signing in with credentials only supported if JWT strategy is enabled");if(t.providers.some(e=>{let t="function"==typeof e?e():e;return"credentials"===t.type&&!t.authorize}))return new tS("Must define an authorize() handler to use credentials authentication provider")}let{adapter:l,session:u}=t,d=[];if(tV||u?.strategy==="database"||!u?.strategy&&l){if(tV){if(!l)return new tx("Email login requires an adapter");d.push(...tQ)}else{if(!l)return new tx("Database session requires an adapter");d.push(...tG)}}if(tJ){if(!t.experimental?.enableWebAuthn)return new tz("WebAuthn is an experimental feature. To enable it, set `experimental.enableWebAuthn` to `true` in your config");if(n.push("experimental-webauthn"),!l)return new tx("WebAuthn requires an adapter");d.push(...tX)}if(l){let e=d.filter(e=>!(e in l));if(e.length)return new t_(`Required adapter methods were missing: ${e.join(", ")}`)}return tW||(tW=!0),n}(n,t);if(Array.isArray(i))i.forEach(r.warn);else if(i){if(r.error(i),!new Set(["signin","signout","error","verify-request"]).has(n.action)||"GET"!==n.method)return Response.json({message:"There was a problem with the server configuration. Check the server logs for more information."},{status:500});let{pages:e,theme:a}=t,s=e?.error&&n.url.searchParams.get("callbackUrl")?.startsWith(e.error);if(!e?.error||s)return s&&r.error(new tp(`The error page ${e?.error} should not require authentication`)),nG(as({theme:a}).error("Configuration"));let o=`${n.url.origin}${e.error}?error=Configuration`;return Response.redirect(o)}let a=e.headers?.has("X-Auth-Return-Redirect"),s=t.raw===n6;try{let e=await oC(n,t);if(s)return e;let r=nG(e),i=r.headers.get("Location");if(!a||!i)return r;return Response.json({url:i},{headers:r.headers})}catch(d){r.error(d);let i=d instanceof tl;if(i&&s&&!a)throw d;if("POST"===e.method&&"session"===n.action)return Response.json(null,{status:400});let o=new URLSearchParams({error:d instanceof tl&&tD.has(d.type)?d.type:"Configuration"});d instanceof ty&&o.set("code",d.code);let c=i&&d.kind||"error",l=t.pages?.[c]??`${t.basePath}/${c.toLowerCase()}`,u=`${n.url.origin}${l}?${o}`;if(a)return Response.json({url:u});return Response.redirect(u)}}r(113),"undefined"==typeof URLPattern||URLPattern;var oR=r(886);class oI extends Error{constructor(e){super("Dynamic server usage: "+e),this.description=e,this.digest="DYNAMIC_SERVER_USAGE"}}class o$ extends Error{constructor(...e){super(...e),this.code="NEXT_STATIC_GEN_BAILOUT"}}let oj="function"==typeof oR.unstable_postpone;function oU(e,t,r){let n=new oI(`Route ${t.route} couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`);throw r.revalidate=0,t.dynamicUsageDescription=e,t.dynamicUsageStack=n.stack,n}function oL(e,t){t&&"cache"!==t.type&&"unstable-cache"!==t.type&&("prerender"===t.type||"prerender-legacy"===t.type)&&(t.revalidate=0)}function oD(e,t,r,n){let i=n.dynamicTracking;throw i&&null===i.syncDynamicErrorWithStack&&(i.syncDynamicExpression=t,i.syncDynamicErrorWithStack=r,!0===n.validating&&(i.syncDynamicLogged=!0)),function(e,t,r){let n=oH(`Route ${e} needs to bail out of prerendering at this point because it used ${t}.`);r.controller.abort(n);let i=r.dynamicTracking;i&&i.dynamicAccesses.push({stack:i.isDebugDynamicAccesses?Error().stack:void 0,expression:t})}(e,t,n),oH(`Route ${e} needs to bail out of prerendering at this point because it used ${t}.`)}function oM(e,t,r){(function(){if(!oj)throw Error("Invariant: React.unstable_postpone is not defined. This suggests the wrong version of React was loaded. This is a bug in Next.js")})(),r&&r.dynamicAccesses.push({stack:r.isDebugDynamicAccesses?Error().stack:void 0,expression:t}),oR.unstable_postpone(oB(e,t))}function oB(e,t){return`Route ${e} needs to bail out of prerendering at this point because it used ${t}. React throws this special object to indicate where. It should not be caught by your own try/catch. Learn more: https://nextjs.org/docs/messages/ppr-caught-error`}if(!1===function(e){return e.includes("needs to bail out of prerendering at this point because it used")&&e.includes("Learn more: https://nextjs.org/docs/messages/ppr-caught-error")}(oB("%%%","^^^")))throw Error("Invariant: isDynamicPostpone misidentified a postpone reason. This is a bug in Next.js");function oH(e){let t=Error(e);return t.digest="NEXT_PRERENDER_INTERRUPTED",t}function oq(){let e=e6.getStore();return(null==e?void 0:e.rootTaskSpawnPhase)==="action"}function oz(e){let t=process.env.AUTH_URL??process.env.NEXTAUTH_URL;if(!t)return e;let{origin:r}=new URL(t),{href:n,origin:i}=e.nextUrl;return new W(n.replace(i,r),e)}function oW(e){try{e.secret??(e.secret=process.env.AUTH_SECRET??process.env.NEXTAUTH_SECRET);let t=process.env.AUTH_URL??process.env.NEXTAUTH_URL;if(!t)return;let{pathname:r}=new URL(t);if("/"===r)return;e.basePath||(e.basePath=r)}catch{}finally{e.basePath||(e.basePath="/api/auth"),function(e,t,r=!1){try{let n=e.AUTH_URL;n&&(t.basePath?r||nW(t).warn("env-url-basepath-redundant"):t.basePath=new URL(n).pathname)}catch{}finally{t.basePath??(t.basePath="/auth")}if(!t.secret?.length){t.secret=[];let r=e.AUTH_SECRET;for(let n of(r&&t.secret.push(r),[1,2,3])){let r=e[`AUTH_SECRET_${n}`];r&&t.secret.unshift(r)}}t.redirectProxyUrl??(t.redirectProxyUrl=e.AUTH_REDIRECT_PROXY_URL),t.trustHost??(t.trustHost=!!(e.AUTH_URL??e.AUTH_TRUST_HOST??e.VERCEL??e.CF_PAGES??"production"!==e.NODE_ENV)),t.providers=t.providers.map(t=>{let{id:r}="function"==typeof t?t({}):t,n=r.toUpperCase().replace(/-/g,"_"),i=e[`AUTH_${n}_ID`],a=e[`AUTH_${n}_SECRET`],s=e[`AUTH_${n}_ISSUER`],o=e[`AUTH_${n}_KEY`],c="function"==typeof t?t({clientId:i,clientSecret:a,issuer:s,apiKey:o}):t;return"oauth"===c.type||"oidc"===c.type?(c.clientId??(c.clientId=i),c.clientSecret??(c.clientSecret=a),c.issuer??(c.issuer=s)):"email"===c.type&&(c.apiKey??(c.apiKey=o)),c})}(process.env,e,!0)}}function oF(e,t){let r=new Promise((r,n)=>{e.addEventListener("abort",()=>{n(Error(`During prerendering, ${t} rejects when the prerender is complete. Typically these errors are handled by React but if you move ${t} to a different context by using \`setTimeout\`, \`after\`, or similar functions you may observe this error and you should handle it in that context.`))},{once:!0})});return r.catch(oK),r}function oK(){}RegExp(`\\n\\s+at __next_metadata_boundary__[\\n\\s]`),RegExp(`\\n\\s+at __next_viewport_boundary__[\\n\\s]`),RegExp(`\\n\\s+at __next_outlet_boundary__[\\n\\s]`);let oV={current:null},oJ="function"==typeof oR.cache?oR.cache:e=>e,oQ=console.warn;function oG(e){return function(...t){oQ(e(...t))}}function oX(){let e="cookies",t=eo.getStore(),r=ec.getStore();if(t){if(r&&"after"===r.phase&&!oq())throw Error(`Route ${t.route} used "cookies" inside "after(...)". This is not supported. If you need this data inside an "after" callback, use "cookies" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`);if(t.forceStatic)return oZ(ed.seal(new q.RequestCookies(new Headers({}))));if(r){if("cache"===r.type)throw Error(`Route ${t.route} used "cookies" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "cookies" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`);if("unstable-cache"===r.type)throw Error(`Route ${t.route} used "cookies" inside a function cached with "unstable_cache(...)". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "cookies" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`)}if(t.dynamicShouldError)throw new o$(`Route ${t.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`cookies\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`);if(r){if("prerender"===r.type)return function(e,t){let r=oY.get(t);if(r)return r;let n=oF(t.renderSignal,"`cookies()`");return oY.set(t,n),Object.defineProperties(n,{[Symbol.iterator]:{value:function(){let r="`cookies()[Symbol.iterator]()`",n=o1(e,r);oD(e,r,n,t)}},size:{get(){let r="`cookies().size`",n=o1(e,r);oD(e,r,n,t)}},get:{value:function(){let r;r=0==arguments.length?"`cookies().get()`":`\`cookies().get(${o0(arguments[0])})\``;let n=o1(e,r);oD(e,r,n,t)}},getAll:{value:function(){let r;r=0==arguments.length?"`cookies().getAll()`":`\`cookies().getAll(${o0(arguments[0])})\``;let n=o1(e,r);oD(e,r,n,t)}},has:{value:function(){let r;r=0==arguments.length?"`cookies().has()`":`\`cookies().has(${o0(arguments[0])})\``;let n=o1(e,r);oD(e,r,n,t)}},set:{value:function(){let r;if(0==arguments.length)r="`cookies().set()`";else{let e=arguments[0];r=e?`\`cookies().set(${o0(e)}, ...)\``:"`cookies().set(...)`"}let n=o1(e,r);oD(e,r,n,t)}},delete:{value:function(){let r;r=0==arguments.length?"`cookies().delete()`":1==arguments.length?`\`cookies().delete(${o0(arguments[0])})\``:`\`cookies().delete(${o0(arguments[0])}, ...)\``;let n=o1(e,r);oD(e,r,n,t)}},clear:{value:function(){let r="`cookies().clear()`",n=o1(e,r);oD(e,r,n,t)}},toString:{value:function(){let r="`cookies().toString()`",n=o1(e,r);oD(e,r,n,t)}}}),n}(t.route,r);"prerender-ppr"===r.type?oM(t.route,e,r.dynamicTracking):"prerender-legacy"===r.type&&oU(e,t,r)}oL(t,r)}let n=el(e);return oZ(ep(n)?n.userspaceMutableCookies:n.cookies)}oJ(e=>{try{oQ(oV.current)}finally{oV.current=null}});let oY=new WeakMap;function oZ(e){let t=oY.get(e);if(t)return t;let r=Promise.resolve(e);return oY.set(e,r),Object.defineProperties(r,{[Symbol.iterator]:{value:e[Symbol.iterator]?e[Symbol.iterator].bind(e):o2.bind(e)},size:{get:()=>e.size},get:{value:e.get.bind(e)},getAll:{value:e.getAll.bind(e)},has:{value:e.has.bind(e)},set:{value:e.set.bind(e)},delete:{value:e.delete.bind(e)},clear:{value:"function"==typeof e.clear?e.clear.bind(e):o5.bind(e,r)},toString:{value:e.toString.bind(e)}}),r}function o0(e){return"object"==typeof e&&null!==e&&"string"==typeof e.name?`'${e.name}'`:"string"==typeof e?`'${e}'`:"..."}function o1(e,t){let r=e?`Route "${e}" `:"This route ";return Error(`${r}used ${t}. \`cookies()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`)}function o2(){return this.getAll().map(e=>[e.name,e]).values()}function o5(e){for(let e of this.getAll())this.delete(e.name);return e}function o6(){let e=eo.getStore(),t=ec.getStore();if(e){if(t&&"after"===t.phase&&!oq())throw Error(`Route ${e.route} used "headers" inside "after(...)". This is not supported. If you need this data inside an "after" callback, use "headers" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`);if(e.forceStatic)return o8(er.seal(new Headers({})));if(t){if("cache"===t.type)throw Error(`Route ${e.route} used "headers" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`);if("unstable-cache"===t.type)throw Error(`Route ${e.route} used "headers" inside a function cached with "unstable_cache(...)". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`)}if(e.dynamicShouldError)throw new o$(`Route ${e.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`headers\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`);if(t){if("prerender"===t.type)return function(e,t){let r=o3.get(t);if(r)return r;let n=oF(t.renderSignal,"`headers()`");return o3.set(t,n),Object.defineProperties(n,{append:{value:function(){let r=`\`headers().append(${o4(arguments[0])}, ...)\``,n=o9(e,r);oD(e,r,n,t)}},delete:{value:function(){let r=`\`headers().delete(${o4(arguments[0])})\``,n=o9(e,r);oD(e,r,n,t)}},get:{value:function(){let r=`\`headers().get(${o4(arguments[0])})\``,n=o9(e,r);oD(e,r,n,t)}},has:{value:function(){let r=`\`headers().has(${o4(arguments[0])})\``,n=o9(e,r);oD(e,r,n,t)}},set:{value:function(){let r=`\`headers().set(${o4(arguments[0])}, ...)\``,n=o9(e,r);oD(e,r,n,t)}},getSetCookie:{value:function(){let r="`headers().getSetCookie()`",n=o9(e,r);oD(e,r,n,t)}},forEach:{value:function(){let r="`headers().forEach(...)`",n=o9(e,r);oD(e,r,n,t)}},keys:{value:function(){let r="`headers().keys()`",n=o9(e,r);oD(e,r,n,t)}},values:{value:function(){let r="`headers().values()`",n=o9(e,r);oD(e,r,n,t)}},entries:{value:function(){let r="`headers().entries()`",n=o9(e,r);oD(e,r,n,t)}},[Symbol.iterator]:{value:function(){let r="`headers()[Symbol.iterator]()`",n=o9(e,r);oD(e,r,n,t)}}}),n}(e.route,t);"prerender-ppr"===t.type?oM(e.route,"headers",t.dynamicTracking):"prerender-legacy"===t.type&&oU("headers",e,t)}oL(e,t)}return o8(el("headers").headers)}oG(o1);let o3=new WeakMap;function o8(e){let t=o3.get(e);if(t)return t;let r=Promise.resolve(e);return o3.set(e,r),Object.defineProperties(r,{append:{value:e.append.bind(e)},delete:{value:e.delete.bind(e)},get:{value:e.get.bind(e)},has:{value:e.has.bind(e)},set:{value:e.set.bind(e)},getSetCookie:{value:e.getSetCookie.bind(e)},forEach:{value:e.forEach.bind(e)},keys:{value:e.keys.bind(e)},values:{value:e.values.bind(e)},entries:{value:e.entries.bind(e)},[Symbol.iterator]:{value:e[Symbol.iterator].bind(e)}}),r}function o4(e){return"string"==typeof e?`'${e}'`:"..."}function o9(e,t){let r=e?`Route "${e}" `:"This route ";return Error(`${r}used ${t}. \`headers()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`)}oG(o9),new WeakMap;function o7(e){let t=workAsyncStorage.getStore(),r=workUnitAsyncStorage.getStore();if(t){if(r){if("cache"===r.type)throw Error(`Route ${t.route} used "${e}" inside "use cache". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`);if("unstable-cache"===r.type)throw Error(`Route ${t.route} used "${e}" inside a function cached with "unstable_cache(...)". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`);if("after"===r.phase)throw Error(`Route ${t.route} used "${e}" inside \`after\`. The enabled status of draftMode can be read inside \`after\` but you cannot enable or disable draftMode. See more info here: https://nextjs.org/docs/app/api-reference/functions/after`)}if(t.dynamicShouldError)throw new StaticGenBailoutError(`Route ${t.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`);if(r){if("prerender"===r.type){let n=Error(`Route ${t.route} used ${e} without first calling \`await connection()\`. See more info here: https://nextjs.org/docs/messages/next-prerender-sync-headers`);abortAndThrowOnSynchronousRequestDataAccess(t.route,e,n,r)}else if("prerender-ppr"===r.type)postponeWithTracking(t.route,e,r.dynamicTracking);else if("prerender-legacy"===r.type){r.revalidate=0;let n=new DynamicServerError(`Route ${t.route} couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`);throw t.dynamicUsageDescription=e,t.dynamicUsageStack=n.stack,n}}}}async function ce(e,t){return oN(new Request(oO("session",e.get("x-forwarded-proto"),e,process.env,t),{headers:{cookie:e.get("cookie")??""}}),{...t,callbacks:{...t.callbacks,async session(...e){let r=await t.callbacks?.session?.(...e)??{...e[0].session,expires:e[0].session.expires?.toISOString?.()??e[0].session.expires};return{user:e[0].user??e[0].token,...r}}}})}function ct(e){return"function"==typeof e}function cr(e,t){return"function"==typeof e?async(...r)=>{if(!r.length){let r=await o6(),n=await e(void 0);return t?.(n),ce(r,n).then(e=>e.json())}if(r[0]instanceof Request){let n=r[0],i=r[1],a=await e(n);return t?.(a),cn([n,i],a)}if(ct(r[0])){let n=r[0];return async(...r)=>{let i=await e(r[0]);return t?.(i),cn(r,i,n)}}let n="req"in r[0]?r[0].req:r[0],i="res"in r[0]?r[0].res:r[1],a=await e(n);return t?.(a),ce(new Headers(n.headers),a).then(async e=>{let t=await e.json();for(let t of e.headers.getSetCookie())"headers"in i?i.headers.append("set-cookie",t):i.appendHeader("set-cookie",t);return t})}:(...t)=>{if(!t.length)return Promise.resolve(o6()).then(t=>ce(t,e).then(e=>e.json()));if(t[0]instanceof Request)return cn([t[0],t[1]],e);if(ct(t[0])){let r=t[0];return async(...t)=>cn(t,e,r).then(e=>e)}let r="req"in t[0]?t[0].req:t[0],n="res"in t[0]?t[0].res:t[1];return ce(new Headers(r.headers),e).then(async e=>{let t=await e.json();for(let t of e.headers.getSetCookie())"headers"in n?n.headers.append("set-cookie",t):n.appendHeader("set-cookie",t);return t})}}async function cn(e,t,r){let n=oz(e[0]),i=await ce(n.headers,t),a=await i.json(),s=!0;t.callbacks?.authorized&&(s=await t.callbacks.authorized({request:n,auth:a}));let o=Q.next?.();if(s instanceof Response){o=s;let e=s.headers.get("Location"),{pathname:r}=n.nextUrl;e&&function(e,t,r){let n=t.replace(`${e}/`,""),i=Object.values(r.pages??{});return(ci.has(n)||i.includes(t))&&t===e}(r,new URL(e).pathname,t)&&(s=!0)}else if(r)n.auth=a,o=await r(n,e[1])??Q.next();else if(!s){let e=t.pages?.signIn??`${t.basePath}/signin`;if(n.nextUrl.pathname!==e){let t=n.nextUrl.clone();t.pathname=e,t.searchParams.set("callbackUrl",n.nextUrl.href),o=Q.redirect(t)}}let c=new Response(o?.body,o);for(let e of i.headers.getSetCookie())c.headers.append("set-cookie",e);return c}oG(function(e,t){let r=e?`Route "${e}" `:"This route ";return Error(`${r}used ${t}. \`draftMode()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`)});let ci=new Set(["providers","session","csrf","signin","signout","callback","verify-request","error"]),ca=es();var cs=function(e){return e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect",e}({});let co="NEXT_REDIRECT";var cc=function(e){return e.push="push",e.replace="replace",e}({});function cl(e,t){let r=ca.getStore();throw function(e,t,r){void 0===r&&(r=cs.TemporaryRedirect);let n=Error(co);return n.digest=co+";"+t+";"+e+";"+r+";",n}(e,t||((null==r?void 0:r.isAction)?cc.push:cc.replace),cs.TemporaryRedirect)}async function cu(e,t={},r,n){let i=new Headers(await o6()),{redirect:a=!0,redirectTo:s,...o}=t instanceof FormData?Object.fromEntries(t):t,c=s?.toString()??i.get("Referer")??"/",l=oO("signin",i.get("x-forwarded-proto"),i,process.env,n);if(!e)return l.searchParams.append("callbackUrl",c),a&&cl(l.toString()),l.toString();let u=`${l}/${e}?${new URLSearchParams(r)}`,d={};for(let t of n.providers){let{options:r,...n}="function"==typeof t?t():t,i=r?.id??n.id;if(i===e){d={id:i,type:r?.type??n.type};break}}if(!d.id){let e=`${l}?${new URLSearchParams({callbackUrl:c})}`;return a&&cl(e),e}"credentials"===d.type&&(u=u.replace("signin","callback")),i.set("Content-Type","application/x-www-form-urlencoded");let f=new Request(u,{method:"POST",headers:i,body:new URLSearchParams({...o,callbackUrl:c})}),h=await oN(f,{...n,raw:n6,skipCSRFCheck:n5}),p=await oX();for(let e of h?.cookies??[])p.set(e.name,e.value,e.options);let g=(h instanceof Response?h.headers.get("Location"):h.redirect)??u;return a?cl(g):g}async function cd(e,t){let r=new Headers(await o6());r.set("Content-Type","application/x-www-form-urlencoded");let n=oO("signout",r.get("x-forwarded-proto"),r,process.env,t),i=new URLSearchParams({callbackUrl:e?.redirectTo??r.get("Referer")??"/"}),a=new Request(n,{method:"POST",headers:r,body:i}),s=await oN(a,{...t,raw:n6,skipCSRFCheck:n5}),o=await oX();for(let e of s?.cookies??[])o.set(e.name,e.value,e.options);return e?.redirect??!0?cl(s.redirect):s}async function cf(e,t){let r=new Headers(await o6());r.set("Content-Type","application/json");let n=new Request(oO("session",r.get("x-forwarded-proto"),r,process.env,t),{method:"POST",headers:r,body:JSON.stringify({data:e})}),i=await oN(n,{...t,raw:n6,skipCSRFCheck:n5}),a=await oX();for(let e of i?.cookies??[])a.set(e.name,e.value,e.options);return i.body}Object.values({NOT_FOUND:404,FORBIDDEN:403,UNAUTHORIZED:401}),Symbol.for("react.postpone");let ch=globalThis.__import_unsupported("os"),cp=globalThis.__import_unsupported("fs"),cg=new Map,cm=new Map,cy=Symbol("OriginError"),cb={};class cw extends Promise{constructor(e,t,r,n,i={}){let a,s;super((e,t)=>{a=e,s=t}),this.tagged=Array.isArray(e.raw),this.strings=e,this.args=t,this.handler=r,this.canceller=n,this.options=i,this.state=null,this.statement=null,this.resolve=e=>(this.active=!1,a(e)),this.reject=e=>(this.active=!1,s(e)),this.active=!1,this.cancelled=null,this.executed=!1,this.signature="",this[cy]=this.handler.debug?Error():this.tagged&&function(e){if(cg.has(e))return cg.get(e);let t=Error.stackTraceLimit;return Error.stackTraceLimit=4,cg.set(e,Error()),Error.stackTraceLimit=t,cg.get(e)}(this.strings)}get origin(){return(this.handler.debug?this[cy].stack:this.tagged&&cm.has(this.strings)?cm.get(this.strings):cm.set(this.strings,this[cy].stack).get(this.strings))||""}static get[Symbol.species](){return Promise}cancel(){return this.canceller&&(this.canceller(this),this.canceller=null)}simple(){return this.options.simple=!0,this.options.prepare=!1,this}async readable(){return this.simple(),this.streaming=!0,this}async writable(){return this.simple(),this.streaming=!0,this}cursor(e=1,t){let r;return(this.options.simple=!1,"function"==typeof e&&(t=e,e=1),this.cursorRows=e,"function"==typeof t)?(this.cursorFn=t,this):{[Symbol.asyncIterator]:()=>({next:()=>{if(this.executed&&!this.active)return{done:!0};r&&r();let e=new Promise((e,t)=>{this.cursorFn=t=>(e({value:t,done:!1}),new Promise(e=>r=e)),this.resolve=()=>(this.active=!1,e({done:!0})),this.reject=e=>(this.active=!1,t(e))});return this.execute(),e},return:()=>(r&&r(cb),{done:!0})})}}describe(){return this.options.simple=!1,this.onlyDescribe=this.options.prepare=!0,this}stream(){throw Error(".stream has been renamed to .forEach")}forEach(e){return this.forEachFn=e,this.handle(),this}raw(){return this.isRaw=!0,this}values(){return this.isRaw="values",this}async handle(){!this.executed&&(this.executed=!0)&&await 1&&this.handler(this)}execute(){return this.handle(),this}then(){return this.handle(),super.then.apply(this,arguments)}catch(){return this.handle(),super.catch.apply(this,arguments)}finally(){return this.handle(),super.finally.apply(this,arguments)}}class cv extends Error{constructor(e){super(e.message),this.name=this.constructor.name,Object.assign(this,e)}}let cx={connection:function e(t,r,n){let{host:i,port:a}=n||r,s=Object.assign(Error("write "+t+" "+(r.path||i+":"+a)),{code:t,errno:t,address:r.path||i},r.path?{}:{port:a});return Error.captureStackTrace(s,e),s},postgres:function e(t){let r=new cv(t);return Error.captureStackTrace(r,e),r},generic:function e(t,r){let n=Object.assign(Error(t+": "+r),{code:t});return Error.captureStackTrace(n,e),n},notSupported:function e(t){let r=Object.assign(Error(t+" (B) is not supported"),{code:"MESSAGE_NOT_SUPPORTED",name:t});return Error.captureStackTrace(r,e),r}};var c_=r(356).Buffer;class cS{then(){cU()}catch(){cU()}finally(){cU()}}class ck extends cS{constructor(e){super(),this.value=cq(e)}}class cE extends cS{constructor(e,t,r){super(),this.value=e,this.type=t,this.array=r}}class cA extends cS{constructor(e,t){super(),this.first=e,this.rest=t}build(e,t,r,n){let i=cj.map(([t,r])=>({fn:r,i:e.search(t)})).sort((e,t)=>e.i-t.i).pop();return -1===i.i?cH(this.first,n):i.fn(this.first,this.rest,t,r,n)}}function cT(e,t,r,n){let i=e instanceof cE?e.value:e;if(void 0===i&&(e instanceof cE?e.value=n.transform.undefined:i=e=n.transform.undefined,void 0===i))throw cx.generic("UNDEFINED_VALUE","Undefined values are not allowed");return"$"+r.push(e instanceof cE?(t.push(e.value),e.array?e.array[e.type||cz(e.value)]||e.type||function e(t){return Array.isArray(t)?e(t[0]):"string"==typeof t?1009:0}(e.value):e.type):(t.push(e),cz(e)))}let cP=cB({string:{to:25,from:null,serialize:e=>""+e},number:{to:0,from:[21,23,26,700,701],serialize:e=>""+e,parse:e=>+e},json:{to:114,from:[114,3802],serialize:e=>JSON.stringify(e),parse:e=>JSON.parse(e)},boolean:{to:16,from:16,serialize:e=>!0===e?"t":"f",parse:e=>"t"===e},date:{to:1184,from:[1082,1114,1184],serialize:e=>(e instanceof Date?e:new Date(e)).toISOString(),parse:e=>new Date(e)},bytea:{to:17,from:17,serialize:e=>"\\x"+c_.from(e).toString("hex"),parse:e=>c_.from(e.slice(2),"hex")}});function cC(e,t,r,n,i,a){for(let s=1;s<e.strings.length;s++)t+=cO(t,r,n,i,a)+e.strings[s],r=e.args[s];return t}function cO(e,t,r,n,i){return t instanceof cA?t.build(e,r,n,i):t instanceof cw?cN(t,r,n,i):t instanceof ck?t.value:t&&t[0]instanceof cw?t.reduce((e,t)=>e+" "+cN(t,r,n,i),""):cT(t,r,n,i)}function cN(e,t,r,n){return e.fragment=!0,cC(e,e.strings[0],e.args[0],t,r,n)}function cR(e,t,r,n,i){return e.map(e=>"("+n.map(n=>cO("values",e[n],t,r,i)).join(",")+")").join(",")}function cI(e,t,r,n,i){let a=Array.isArray(e[0]),s=t.length?t.flat():Object.keys(a?e[0]:e);return cR(a?e:[e],r,n,s,i)}function c$(e,t,r,n,i){let a;return("string"==typeof e&&(e=[e].concat(t)),Array.isArray(e))?cH(e,i):(t.length?t.flat():Object.keys(e)).map(t=>((a=e[t])instanceof cw?cN(a,r,n,i):a instanceof ck?a.value:cT(a,r,n,i))+" as "+cq(i.transform.column.to?i.transform.column.to(t):t)).join(",")}let cj=Object.entries({values:cI,in:(...e)=>{let t=cI(...e);return"()"===t?"(null)":t},select:c$,as:c$,returning:c$,"\\(":c$,update:(e,t,r,n,i)=>(t.length?t.flat():Object.keys(e)).map(t=>cq(i.transform.column.to?i.transform.column.to(t):t)+"="+cO("values",e[t],r,n,i)),insert(e,t,r,n,i){let a=t.length?t.flat():Object.keys(Array.isArray(e)?e[0]:e);return"("+cH(a,i)+")values"+cR(Array.isArray(e)?e:[e],r,n,a,i)}}).map(([e,t])=>[RegExp("((?:^|[\\s(])"+e+"(?:$|[\\s(]))(?![\\s\\S]*\\1)","i"),t]);function cU(){throw cx.generic("NOT_TAGGED_CALL","Query not called as a tagged template literal")}let cL=cP.serializers,cD=cP.parsers,cM=function(e){let t=cB(e||{});return{serializers:Object.assign({},cL,t.serializers),parsers:Object.assign({},cD,t.parsers)}};function cB(e){return Object.keys(e).reduce((t,r)=>(e[r].from&&[].concat(e[r].from).forEach(n=>t.parsers[n]=e[r].parse),e[r].serialize&&(t.serializers[e[r].to]=e[r].serialize,e[r].from&&[].concat(e[r].from).forEach(n=>t.serializers[n]=e[r].serialize)),t),{parsers:{},serializers:{}})}function cH(e,{transform:{column:t}}){return e.map(e=>cq(t.to?t.to(e):e)).join(",")}let cq=function(e){return'"'+e.replace(/"/g,'""').replace(/\./g,'"."')+'"'},cz=function e(t){return t instanceof cE?t.type:t instanceof Date?1184:t instanceof Uint8Array?17:!0===t||!1===t?16:"bigint"==typeof t?20:Array.isArray(t)?e(t[0]):0},cW=/\\/g,cF=/"/g,cK=function e(t,r,n,i){if(!1===Array.isArray(t))return t;if(!t.length)return"{}";let a=t[0],s=1020===i?";":",";return Array.isArray(a)&&!a.type?"{"+t.map(t=>e(t,r,n,i)).join(s)+"}":"{"+t.map(e=>{if(void 0===e&&void 0===(e=n.transform.undefined))throw cx.generic("UNDEFINED_VALUE","Undefined values are not allowed");return null===e?"null":'"'+(r?r(e.type?e.value:e):""+e).replace(cW,"\\\\").replace(cF,'\\"')+'"'}).join(s)+"}"},cV={i:0,char:null,str:"",quoted:!1,last:0},cJ=e=>{let t=e[0];for(let r=1;r<e.length;r++)t+="_"===e[r]?e[++r].toUpperCase():e[r];return t},cQ=e=>{let t=e[0].toUpperCase();for(let r=1;r<e.length;r++)t+="_"===e[r]?e[++r].toUpperCase():e[r];return t},cG=e=>e.replace(/_/g,"-"),cX=e=>e.replace(/([A-Z])/g,"_$1").toLowerCase(),cY=e=>(e.slice(0,1)+e.slice(1).replace(/([A-Z])/g,"_$1")).toLowerCase(),cZ=e=>e.replace(/-/g,"_");function c0(e){return function t(r,n){return"object"==typeof r&&null!==r&&(114===n.type||3802===n.type)?Array.isArray(r)?r.map(e=>t(e,n)):Object.entries(r).reduce((r,[i,a])=>Object.assign(r,{[e(i)]:t(a,n)}),{}):r}}cJ.column={from:cJ},cJ.value={from:c0(cJ)},cX.column={to:cX};let c1={...cJ};c1.column.to=cX,cQ.column={from:cQ},cQ.value={from:c0(cQ)},cY.column={to:cY};let c2={...cQ};c2.column.to=cY,cG.column={from:cG},cG.value={from:c0(cG)},cZ.column={to:cZ};let c5={...cG};c5.column.to=cZ;let c6=globalThis.__import_unsupported("net"),c3=globalThis.__import_unsupported("tls"),c8=globalThis.__import_unsupported("crypto"),c4=globalThis.__import_unsupported("stream"),c9=globalThis.__import_unsupported("perf_hooks");class c7 extends Array{constructor(){super(),Object.defineProperties(this,{count:{value:null,writable:!0},state:{value:null,writable:!0},command:{value:null,writable:!0},columns:{value:null,writable:!0},statement:{value:null,writable:!0}})}static get[Symbol.species](){return Array}}let le=function(e=[]){let t=e.slice(),r=0;return{get length(){return t.length-r},remove:e=>{let r=t.indexOf(e);return -1===r?null:(t.splice(r,1),e)},push:e=>(t.push(e),e),shift:()=>{let e=t[r++];return r===t.length?(r=0,t=[]):t[r-1]=void 0,e}}};var lt=r(356).Buffer;let lr=lt.allocUnsafe(256),ln=Object.assign(function(){return ln.i=0,ln},"BCcDdEFfHPpQSX".split("").reduce((e,t)=>{let r=t.charCodeAt(0);return e[t]=()=>(lr[0]=r,ln.i=5,ln),e},{}),{N:"\0",i:0,inc:e=>(ln.i+=e,ln),str(e){let t=lt.byteLength(e);return li(t),ln.i+=lr.write(e,ln.i,t,"utf8"),ln},i16:e=>(li(2),lr.writeUInt16BE(e,ln.i),ln.i+=2,ln),i32:(e,t)=>(t||0===t?lr.writeUInt32BE(e,t):(li(4),lr.writeUInt32BE(e,ln.i),ln.i+=4),ln),z:e=>(li(e),lr.fill(0,ln.i,ln.i+e),ln.i+=e,ln),raw:e=>(lr=lt.concat([lr.subarray(0,ln.i),e]),ln.i=lr.length,ln),end(e=1){lr.writeUInt32BE(ln.i-e,e);let t=lr.subarray(0,ln.i);return ln.i=0,lr=lt.allocUnsafe(256),t}});function li(e){if(lr.length-ln.i<e){let t=lr,r=t.length;lr=lt.allocUnsafe(r+(r>>1)+e),t.copy(lr)}}var la=r(356).Buffer;let ls=function e(t,r={},{onopen:n=lh,onend:i=lh,onclose:a=lh}={}){let{ssl:s,max:o,user:c,host:l,port:u,database:d,parsers:f,transform:h,onnotice:p,onnotify:g,onparameter:m,max_pipeline:y,keep_alive:b,backoff:w,target_session_attrs:v}=t,x=le(),_=lo++,S={pid:null,secret:null},k=lw(ew,t.idle_timeout),E=lw(ew,t.max_lifetime),A=lw(function(){ey(cx.connection("CONNECT_TIMEOUT",t,T)),T.destroy()},t.connect_timeout),T=null,P,C=new c7,O=la.alloc(0),N=t.fetch_types,R={},I={},$=Math.random().toString(36).slice(2),j=1,U=0,L=0,D=0,M=0,B=0,H=0,q=0,z=null,W=null,F=!1,K=null,V=null,J=null,Q=null,G=null,X=null,Y=null,Z=null,ee=null,et=null,er={queue:r.closed,idleTimer:k,connect(e){J=e,ep()},terminate:ev,execute:ea,cancel:ei,end:ew,count:0,id:_};return r.closed&&r.closed.push(er),er;async function en(){let e;try{e=t.socket?await Promise.resolve(t.socket(t)):new c6.Socket}catch(e){em(e);return}return e.on("error",em),e.on("close",ex),e.on("drain",ed),e}async function ei({pid:e,secret:t},r,n){try{P=ln().i32(16).i32(0x4d2162e).i32(e).i32(t).end(16),await eh(),T.once("error",n),T.once("close",r)}catch(e){n(e)}}function ea(e){if(F)return eb(e,cx.connection("CONNECTION_DESTROYED",t));if(!e.cancelled)try{return e.state=S,ee?x.push(e):(ee=e).active=!0,function(e){let r=[],n=[],i=cC(e,e.strings[0],e.args[0],r,n,t);e.tagged||e.args.forEach(e=>cT(e,r,n,t)),e.prepare=t.prepare&&(!("prepare"in e.options)||e.options.prepare),e.string=i,e.signature=e.prepare&&n+i,e.onlyDescribe&&delete I[e.signature],e.parameters=e.parameters||r,e.prepared=e.prepare&&e.signature in I,e.describeFirst=e.onlyDescribe||r.length&&!e.prepared,e.statement=e.prepared?I[e.signature]:{string:i,types:n,name:e.prepare?$+j++:""},"function"==typeof t.debug&&t.debug(_,i,r,n)}(e),ec(function(e){if(e.parameters.length>=65534)throw cx.generic("MAX_PARAMETERS_EXCEEDED","Max number of parameters (65534) exceeded");return e.options.simple?ln().Q().str(e.statement.string+ln.N).end():e.describeFirst?la.concat([es(e),ll]):e.prepare?e.prepared?eo(e):la.concat([es(e),eo(e)]):la.concat([eN(e.statement.string,e.parameters,e.statement.types),lf,eo(e)])}(e))&&!e.describeFirst&&!e.cursorFn&&x.length<y&&(!e.options.onexecute||e.options.onexecute(er))}catch(e){return 0===x.length&&ec(lc),ey(e),!0}}function es(e){return la.concat([eN(e.statement.string,e.parameters,e.statement.types,e.statement.name),function(e,t=""){return ln().D().str("S").str(t+ln.N).end()}(0,e.statement.name)])}function eo(e){return la.concat([function(e,r,n="",i=""){let a,s;return ln().B().str(i+ln.N).str(n+ln.N).i16(0).i16(e.length),e.forEach((n,i)=>{if(null===n)return ln.i32(0xffffffff);s=r[i],e[i]=n=s in t.serializers?t.serializers[s](n):""+n,a=ln.i,ln.inc(4).str(n).i32(ln.i-a-4,a)}),ln.i16(0),ln.end()}(e.parameters,e.statement.types,e.statement.name,e.cursorName),e.cursorFn?eR("",e.cursorRows):ld])}function ec(e,t){return(X=X?la.concat([X,e]):la.from(e),t||X.length>=1024)?el(t):(null===W&&(W=setImmediate(el)),!0)}function el(e){let t=T.write(X,e);return null!==W&&clearImmediate(W),X=W=null,t}async function eu(){if(ec(lu),!await new Promise(e=>T.once("data",t=>e(83===t[0])))&&"prefer"===s)return eg();T.removeAllListeners(),(T=c3.connect({socket:T,servername:c6.isIP(T.host)?void 0:T.host,..."require"===s||"allow"===s||"prefer"===s?{rejectUnauthorized:!1}:"verify-full"===s?{}:"object"==typeof s?s:{}})).on("secureConnect",eg),T.on("error",em),T.on("close",ex),T.on("drain",ed)}function ed(){ee||n(er)}function ef(r){if(!K||(K.push(r),!((L-=r.length)>0)))for(O=K?la.concat(K,B-L):0===O.length?r:la.concat([O,r],O.length+r.length);O.length>4;){if((B=O.readUInt32BE(1))>=O.length){L=B-O.length,K=[O];break}try{!function(r,i=r[0]){(68===i?function(e){let t,r,n,i=7,a=ee.isRaw?Array(ee.statement.columns.length):{};for(let s=0;s<ee.statement.columns.length;s++)r=ee.statement.columns[s],t=e.readInt32BE(i),i+=4,n=-1===t?null:!0===ee.isRaw?e.subarray(i,i+=t):void 0===r.parser?e.toString("utf8",i,i+=t):!0===r.parser.array?r.parser(e.toString("utf8",i+1,i+=t)):r.parser(e.toString("utf8",i,i+=t)),ee.isRaw?a[s]=!0===ee.isRaw?n:h.value.from?h.value.from(n,r):n:a[r.name]=h.value.from?h.value.from(n,r):n;ee.forEachFn?ee.forEachFn(h.row.from?h.row.from(a):a,C):C[q++]=h.row.from?h.row.from(a):a}:100===i?function(e){G&&(G.push(e.subarray(5))||T.pause())}:65===i?function(e){if(!g)return;let t=9;for(;0!==e[t++];);g(e.toString("utf8",9,t-1),e.toString("utf8",t,e.length-1))}:83===i?function(e){let[r,n]=e.toString("utf8",5,e.length-1).split(ln.N);R[r]=n,t.parameters[r]!==n&&(t.parameters[r]=n,m&&m(r,n))}:90===i?function(r){if(ee&&ee.options.simple&&ee.resolve(V||C),ee=V=null,C=new c7,A.cancel(),J){if(v){var i;if(!R.in_hot_standby||!R.default_transaction_read_only)return function(){let e=new cw([`
      show transaction_read_only;
      select pg_catalog.pg_is_in_recovery()
    `],[],ea,null,{simple:!0});e.resolve=([[e],[t]])=>{R.default_transaction_read_only=e.transaction_read_only,R.in_hot_standby=t.pg_is_in_recovery?"on":"off"},e.execute()}();if(i=R,"read-write"===v&&"on"===i.default_transaction_read_only||"read-only"===v&&"off"===i.default_transaction_read_only||"primary"===v&&"on"===i.in_hot_standby||"standby"===v&&"off"===i.in_hot_standby||"prefer-standby"===v&&"off"===i.in_hot_standby&&t.host[M])return ev()}return N?(J.reserve&&(J=null),eC()):(J&&!J.reserve&&ea(J),t.shared.retries=M=0,void(J=null))}for(;x.length&&(ee=x.shift())&&(ee.active=!0,ee.cancelled);)e(t).cancel(ee.state,ee.cancelled.resolve,ee.cancelled.reject);ee||(er.reserved?er.reserved.release||73!==r[5]?er.reserved():Q?ev():(er.reserved=null,n(er)):Q?ev():n(er))}:67===i?function(e){q=0;for(let t=e.length-1;t>0;t--)if(32===e[t]&&e[t+1]<58&&null===C.count&&(C.count=+e.toString("utf8",t+1,e.length-1)),e[t-1]>=65){C.command=e.toString("utf8",5,t),C.state=S;break}return(et&&(et(),et=null),"BEGIN"!==C.command||1===o||er.reserved)?ee.options.simple?e_():void(ee.cursorFn&&(C.count&&ee.cursorFn(C),ec(lc)),ee.resolve(C)):ey(cx.generic("UNSAFE_TRANSACTION","Only use sql.begin, sql.reserved or max: 1"))}:50===i?e_:49===i?function(){ee.parsing=!1}:116===i?function(e){let t=e.readUInt16BE(5);for(let r=0;r<t;++r)ee.statement.types[r]||(ee.statement.types[r]=e.readUInt32BE(7+4*r));ee.prepare&&(I[ee.signature]=ee.statement),ee.describeFirst&&!ee.onlyDescribe&&(ec(eo(ee)),ee.describeFirst=!1)}:84===i?function(e){let t;C.command&&((V=V||[C]).push(C=new c7),C.count=null,ee.statement.columns=null);let r=e.readUInt16BE(5),n=7;ee.statement.columns=Array(r);for(let i=0;i<r;++i){for(t=n;0!==e[n++];);let r=e.readUInt32BE(n),a=e.readUInt16BE(n+4),s=e.readUInt32BE(n+6);ee.statement.columns[i]={name:h.column.from?h.column.from(e.toString("utf8",t,n-1)):e.toString("utf8",t,n-1),parser:f[s],table:r,number:a,type:s},n+=18}if(C.statement=ee.statement,ee.onlyDescribe)return ee.resolve(ee.statement),ec(lc)}:82===i?eS:110===i?function(){if(C.statement=ee.statement,C.statement.columns=[],ee.onlyDescribe)return ee.resolve(ee.statement),ec(lc)}:75===i?function(e){S.pid=e.readUInt32BE(5),S.secret=e.readUInt32BE(9)}:69===i?function(e){var t;ee&&(ee.cursorFn||ee.describeFirst)&&ec(lc);let r=cx.postgres(lm(e));ee&&ee.retried?ey(ee.retried):ee&&ee.prepared&&lp.has(r.routine)?(t=ee,delete I[t.signature],t.retried=r,ea(t)):ey(r)}:115===i?eO:51===i?function(){C.count&&ee.cursorFn(C),ee.resolve(C)}:71===i?function(){G=new c4.Writable({autoDestroy:!0,write(e,t,r){T.write(ln().d().raw(e).end(),r)},destroy(e,t){t(e),T.write(ln().f().str(e+ln.N).end()),G=null},final(e){T.write(ln().c().end()),et=e}}),ee.resolve(G)}:78===i?function(e){p?p(lm(e)):console.log(lm(e))}:72===i?function(){G=new c4.Readable({read(){T.resume()}}),ee.resolve(G)}:99===i?function(){G&&G.push(null),G=null}:73===i?function(){}:86===i?function(){ey(cx.notSupported("FunctionCallResponse"))}:118===i?function(){ey(cx.notSupported("NegotiateProtocolVersion"))}:87===i?function(){G=new c4.Duplex({autoDestroy:!0,read(){T.resume()},write(e,t,r){T.write(ln().d().raw(e).end(),r)},destroy(e,t){t(e),T.write(ln().f().str(e+ln.N).end()),G=null},final(e){T.write(ln().c().end()),et=e}}),ee.resolve(G)}:function(e){console.error("Postgres.js : Unknown Message:",e[0])})(r)}(O.subarray(0,B+1))}catch(e){ee&&(ee.cursorFn||ee.describeFirst)&&ec(lc),ey(e)}O=O.subarray(B+1),L=0,K=null}}async function eh(){if(F=!1,R={},T||(T=await en()),T){if(A.start(),t.socket)return s?eu():eg();if(T.on("connect",s?eu:eg),t.path)return T.connect(t.path);T.ssl=s,T.connect(u[D],l[D]),T.host=l[D],T.port=u[D],D=(D+1)%u.length}}function ep(){setTimeout(eh,U?U+H-c9.performance.now():0)}function eg(){try{I={},N=t.fetch_types,$=Math.random().toString(36).slice(2),j=1,E.start(),T.on("data",ef),b&&T.setKeepAlive&&T.setKeepAlive(!0,1e3*b);let e=P||ln().inc(4).i16(3).z(2).str(Object.entries(Object.assign({user:c,database:d,client_encoding:"UTF8"},t.connection)).filter(([,e])=>e).map(([e,t])=>e+ln.N+t).join(ln.N)).z(2).end(0);ec(e)}catch(e){em(e)}}function em(e){if(er.queue!==r.connecting||!t.host[M+1])for(ey(e);x.length;)eb(x.shift(),e)}function ey(e){G&&(G.destroy(e),G=null),ee&&eb(ee,e),J&&(eb(J,e),J=null)}function eb(e,r){if(e.reserve)return e.reject(r);r&&"object"==typeof r||(r=Error(r)),"query"in r||"parameters"in r||Object.defineProperties(r,{stack:{value:r.stack+e.origin.replace(/.*\n/,"\n"),enumerable:t.debug},query:{value:e.string,enumerable:t.debug},parameters:{value:e.parameters,enumerable:t.debug},args:{value:e.args,enumerable:t.debug},types:{value:e.statement&&e.statement.types,enumerable:t.debug}}),e.reject(r)}function ew(){return Q||(er.reserved||i(er),er.reserved||J||ee||0!==x.length?Q=new Promise(e=>Y=e):(ev(),new Promise(e=>T&&"closed"!==T.readyState?T.once("close",e):e())))}function ev(){F=!0,(G||ee||J||x.length)&&em(cx.connection("CONNECTION_DESTROYED",t)),clearImmediate(W),T&&(T.removeListener("data",ef),T.removeListener("connect",eg),"open"===T.readyState&&T.end(ln().X().end())),Y&&(Y(),Q=Y=null)}async function ex(e){if(O=la.alloc(0),L=0,K=null,clearImmediate(W),T.removeListener("data",ef),T.removeListener("connect",eg),k.cancel(),E.cancel(),A.cancel(),T.removeAllListeners(),T=null,J)return ep();!e&&(ee||x.length)&&em(cx.connection("CONNECTION_CLOSED",t,T)),U=c9.performance.now(),e&&t.shared.retries++,H=("function"==typeof w?w(t.shared.retries):w)*1e3,a(er,cx.connection("CONNECTION_CLOSED",t,T))}function e_(){C.statement||(C.statement=ee.statement),C.columns=ee.statement.columns}async function eS(e,t=e.readUInt32BE(5)){(3===t?ek:5===t?eE:10===t?eA:11===t?eT:12===t?function(e){e.toString("utf8",9).split(ln.N,1)[0].slice(2)!==z&&(ey(cx.generic("SASL_SIGNATURE_MISMATCH","The server did not return the correct signature")),T.destroy())}:0!==t?function(e,t){console.error("Postgres.js : Unknown Auth:",t)}:lh)(e,t)}async function ek(){let e=await eP();ec(ln().p().str(e).z(1).end())}async function eE(e){let t="md5"+await ly(la.concat([la.from(await ly(await eP()+c)),e.subarray(9)]));ec(ln().p().str(t).z(1).end())}async function eA(){Z=(await c8.randomBytes(18)).toString("base64"),ln().p().str("SCRAM-SHA-256"+ln.N);let e=ln.i;ec(ln.inc(4).str("n,,n=*,r="+Z).i32(ln.i-e-4,e).end())}async function eT(e){let t=e.toString("utf8",9).split(",").reduce((e,t)=>(e[t[0]]=t.slice(2),e),{}),r=await c8.pbkdf2Sync(await eP(),la.from(t.s,"base64"),parseInt(t.i),32,"sha256"),n=await lb(r,"Client Key"),i="n=*,r="+Z+",r="+t.r+",s="+t.s+",i="+t.i+",c=biws,r="+t.r;z=(await lb(await lb(r,"Server Key"),i)).toString("base64");let a="c=biws,r="+t.r+",p="+(function(e,t){let r=Math.max(e.length,t.length),n=la.allocUnsafe(r);for(let i=0;i<r;i++)n[i]=e[i]^t[i];return n})(n,la.from(await lb(await c8.createHash("sha256").update(n).digest(),i))).toString("base64");ec(ln().p().str(a).end())}function eP(){return Promise.resolve("function"==typeof t.pass?t.pass():t.pass)}async function eC(){N=!1,(await new cw([`
      select b.oid, b.typarray
      from pg_catalog.pg_type a
      left join pg_catalog.pg_type b on b.oid = a.typelem
      where a.typcategory = 'A'
      group by b.oid, b.typarray
      order by b.oid
    `],[],ea)).forEach(({oid:e,typarray:r})=>(function(e,r){if(t.parsers[r]&&t.serializers[r])return;let n=t.parsers[e];t.shared.typeArrayMap[e]=r,t.parsers[r]=e=>(cV.i=cV.last=0,function e(t,r,n,i){let a=[],s=1020===i?";":",";for(;t.i<r.length;t.i++){if(t.char=r[t.i],t.quoted)"\\"===t.char?t.str+=r[++t.i]:'"'===t.char?(a.push(n?n(t.str):t.str),t.str="",t.quoted='"'===r[t.i+1],t.last=t.i+2):t.str+=t.char;else if('"'===t.char)t.quoted=!0;else if("{"===t.char)t.last=++t.i,a.push(e(t,r,n,i));else if("}"===t.char){t.quoted=!1,t.last<t.i&&a.push(n?n(r.slice(t.last,t.i)):r.slice(t.last,t.i)),t.last=t.i+1;break}else t.char===s&&"}"!==t.p&&'"'!==t.p&&(a.push(n?n(r.slice(t.last,t.i)):r.slice(t.last,t.i)),t.last=t.i+1);t.p=t.char}return t.last<t.i&&a.push(n?n(r.slice(t.last,t.i+1)):r.slice(t.last,t.i+1)),a}(cV,e,n,r)),t.parsers[r].array=!0,t.serializers[r]=n=>cK(n,t.serializers[e],t,r)})(e,r))}async function eO(){try{let e=await Promise.resolve(ee.cursorFn(C));q=0,e===cb?ec(function(e=""){return la.concat([ln().C().str("P").str(e+ln.N).end(),ln().S().end()])}(ee.portal)):(C=new c7,ec(eR("",ee.cursorRows)))}catch(e){ec(lc),ee.reject(e)}}function eN(e,t,r,n=""){return ln().P().str(n+ln.N).str(e+ln.N).i16(t.length),t.forEach((e,t)=>ln.i32(r[t]||0)),ln.end()}function eR(e="",t=0){return la.concat([ln().E().str(e+ln.N).i32(t).end(),ll])}},lo=1,lc=ln().S().end(),ll=ln().H().end(),lu=ln().i32(8).i32(0x4d2162f).end(8),ld=la.concat([ln().E().str(ln.N).i32(0).end(),lc]),lf=ln().D().str("S").str(ln.N).end(),lh=()=>{},lp=new Set(["FetchPreparedStatement","RevalidateCachedQuery","transformAssignedExpr"]),lg={83:"severity_local",86:"severity",67:"code",77:"message",68:"detail",72:"hint",80:"position",112:"internal_position",113:"internal_query",87:"where",115:"schema_name",116:"table_name",99:"column_name",100:"data type_name",110:"constraint_name",70:"file",76:"line",82:"routine"};function lm(e){let t={},r=5;for(let n=5;n<e.length-1;n++)0===e[n]&&(t[lg[e[r]]]=e.toString("utf8",r+1,n),r=n+1);return t}function ly(e){return c8.createHash("md5").update(e).digest("hex")}function lb(e,t){return c8.createHmac("sha256",e).update(t).digest()}function lw(e,t){let r;if(!(t="function"==typeof t?t():t))return{cancel:lh,start:lh};return{cancel(){r&&(clearTimeout(r),r=null)},start(){r&&clearTimeout(r),r=setTimeout(n,1e3*t,arguments)}};function n(t){e.apply(null,t),r=null}}var lv=r(356).Buffer;let lx=()=>{};function l_(e,t,r,n){let i,a,s;let o=n.raw?Array(t.length):{};for(let c=0;c<t.length;c++)i=e[r++],a=t[c],s=110===i?null:117===i?void 0:void 0===a.parser?e.toString("utf8",r+4,r+=4+e.readUInt32BE(r)):!0===a.parser.array?a.parser(e.toString("utf8",r+5,r+=4+e.readUInt32BE(r))):a.parser(e.toString("utf8",r+4,r+=4+e.readUInt32BE(r))),n.raw?o[c]=!0===n.raw?s:n.value.from?n.value.from(s,a):s:o[a.name]=n.value.from?n.value.from(s,a):s;return{i:r,row:n.row.from?n.row.from(o):o}}function lS(e,t,r=393216){return new Promise(async(n,i)=>{await e.begin(async e=>{let i;t||([{oid:t}]=await e`select lo_creat(-1) as oid`);let[{fd:a}]=await e`select lo_open(${t}, ${r}) as fd`,s={writable:c,readable:o,close:()=>e`select lo_close(${a})`.then(i),tell:()=>e`select lo_tell64(${a})`,read:t=>e`select loread(${a}, ${t}) as data`,write:t=>e`select lowrite(${a}, ${t})`,truncate:t=>e`select lo_truncate64(${a}, ${t})`,seek:(t,r=0)=>e`select lo_lseek64(${a}, ${t}, ${r})`,size:()=>e`
          select
            lo_lseek64(${a}, location, 0) as position,
            seek.size
          from (
            select
              lo_lseek64($1, 0, 2) as size,
              tell.location
            from (select lo_tell64($1) as location) tell
          ) seek
        `};return n(s),new Promise(async e=>i=e);async function o({highWaterMark:e=16384,start:t=0,end:r=1/0}={}){let n=r-t;return t&&await s.seek(t),new c4.Readable({highWaterMark:e,async read(e){let t=e>n?e-n:e;n-=e;let[{data:r}]=await s.read(t);this.push(r),r.length<e&&this.push(null)}})}async function c({highWaterMark:e=16384,start:t=0}={}){return t&&await s.seek(t),new c4.Writable({highWaterMark:e,write(e,t,r){s.write(e).then(()=>r(),r)}})}}).catch(i)})}Object.assign(lE,{PostgresError:cv,toPascal:cQ,pascal:c2,toCamel:cJ,camel:c1,toKebab:cG,kebab:c5,fromPascal:cY,fromCamel:cX,fromKebab:cZ,BigInt:{to:20,from:[20],parse:e=>BigInt(e),serialize:e=>e.toString()}});let lk=lE;function lE(e,t){let r=function(e,t){var r;if(e&&e.shared)return e;let n=process.env,i=(e&&"string"!=typeof e?e:t)||{},{url:a,multihost:s}=function(e){if(!e||"string"!=typeof e)return{url:{searchParams:new Map}};let t=e;t=decodeURIComponent((t=t.slice(t.indexOf("://")+3).split(/[?/]/)[0]).slice(t.indexOf("@")+1));let r=new URL(e.replace(t,t.split(",")[0]));return{url:{username:decodeURIComponent(r.username),password:decodeURIComponent(r.password),host:r.host,hostname:r.hostname,port:r.port,pathname:r.pathname,searchParams:r.searchParams},multihost:t.indexOf(",")>-1&&t}}(e),o=[...a.searchParams].reduce((e,[t,r])=>(e[t]=r,e),{}),c=i.hostname||i.host||s||a.hostname||n.PGHOST||"localhost",l=i.port||a.port||n.PGPORT||5432,u=i.user||i.username||a.username||n.PGUSERNAME||n.PGUSER||function(){try{return ch.userInfo().username}catch(e){return process.env.USERNAME||process.env.USER||process.env.LOGNAME}}();i.no_prepare&&(i.prepare=!1),o.sslmode&&(o.ssl=o.sslmode,delete o.sslmode),"timeout"in i&&(console.log("The timeout option is deprecated, use idle_timeout instead"),i.idle_timeout=i.timeout),"system"===o.sslrootcert&&(o.ssl="verify-full");let d=["idle_timeout","connect_timeout","max_lifetime","max_pipeline","backoff","keep_alive"],f={max:10,ssl:!1,idle_timeout:null,connect_timeout:30,max_lifetime:lT,max_pipeline:100,backoff:lA,keep_alive:60,prepare:!0,debug:!1,fetch_types:!0,publications:"alltables",target_session_attrs:null};return{host:Array.isArray(c)?c:c.split(",").map(e=>e.split(":")[0]),port:Array.isArray(l)?l:c.split(",").map(e=>parseInt(e.split(":")[1]||l)),path:i.path||c.indexOf("/")>-1&&c+"/.s.PGSQL."+l,database:i.database||i.db||(a.pathname||"").slice(1)||n.PGDATABASE||u,user:u,pass:i.pass||i.password||a.password||n.PGPASSWORD||"",...Object.entries(f).reduce((e,[t,r])=>{let a=t in i?i[t]:t in o?"disable"!==o[t]&&"false"!==o[t]&&o[t]:n["PG"+t.toUpperCase()]||r;return e[t]="string"==typeof a&&d.includes(t)?+a:a,e},{}),connection:{application_name:n.PGAPPNAME||"postgres.js",...i.connection,...Object.entries(o).reduce((e,[t,r])=>(t in f||(e[t]=r),e),{})},types:i.types||{},target_session_attrs:function(e,t,r){let n=e.target_session_attrs||t.searchParams.get("target_session_attrs")||r.PGTARGETSESSIONATTRS;if(!n||["read-write","read-only","primary","standby","prefer-standby"].includes(n))return n;throw Error("target_session_attrs "+n+" is not supported")}(i,a,n),onnotice:i.onnotice,onnotify:i.onnotify,onclose:i.onclose,onparameter:i.onparameter,socket:i.socket,transform:{undefined:(r=i.transform||{undefined:void 0}).undefined,column:{from:"function"==typeof r.column?r.column:r.column&&r.column.from,to:r.column&&r.column.to},value:{from:"function"==typeof r.value?r.value:r.value&&r.value.from,to:r.value&&r.value.to},row:{from:"function"==typeof r.row?r.row:r.row&&r.row.from,to:r.row&&r.row.to}},parameters:{},shared:{retries:0,typeArrayMap:{}},...cM(i.types)}}(e,t),n=r.no_subscribe||function(e,t){let r=new Map,n="postgresjs_"+Math.random().toString(36).slice(2),i={},a,s,o=!1,c=d.sql=e({...t,transform:{column:{},value:{},row:{}},max:1,fetch_types:!1,idle_timeout:null,max_lifetime:null,connection:{...t.connection,replication:"database"},onclose:async function(){o||(s=null,i.pid=i.secret=void 0,f(await h(c,n,t.publications)),r.forEach(e=>e.forEach(({onsubscribe:e})=>e())))},no_subscribe:!0}),l=c.end,u=c.close;return c.end=async()=>(o=!0,s&&await new Promise(e=>(s.once("close",e),s.end())),l()),c.close=async()=>(s&&await new Promise(e=>(s.once("close",e),s.end())),u()),d;async function d(e,o,l=lx,u=lx){e=function(e){let t=e.match(/^(\*|insert|update|delete)?:?([^.]+?\.?[^=]+)?=?(.+)?/i)||[];if(!t)throw Error("Malformed subscribe pattern: "+e);let[,r,n,i]=t;return(r||"*")+(n?":"+(-1===n.indexOf(".")?"public."+n:n):"")+(i?"="+i:"")}(e),a||(a=h(c,n,t.publications));let p={fn:o,onsubscribe:l},g=r.has(e)?r.get(e).add(p):r.set(e,new Set([p])).get(e),m=()=>{g.delete(p),0===g.size&&r.delete(e)};return a.then(e=>(f(e),l(),s&&s.on("error",u),{unsubscribe:m,state:i,sql:c}))}function f(e){s=e.stream,i.pid=e.state.pid,i.secret=e.state.secret}async function h(e,r,n){if(!n)throw Error("Missing publication names");let i=await e.unsafe(`CREATE_REPLICATION_SLOT ${r} TEMPORARY LOGICAL pgoutput NOEXPORT_SNAPSHOT`),[a]=i,s=await e.unsafe(`START_REPLICATION SLOT ${r} LOGICAL ${a.consistent_point} (proto_version '1', publication_names '${n}')`).writable(),o={lsn:lv.concat(a.consistent_point.split("/").map(e=>lv.from(("00000000"+e).slice(-8),"hex")))};return s.on("data",function(r){119===r[0]?function(e,t,r,n,i){Object.entries({R:e=>{let n=1,a=t[e.readUInt32BE(n)]={schema:e.toString("utf8",n+=4,n=e.indexOf(0,n))||"pg_catalog",table:e.toString("utf8",n+1,n=e.indexOf(0,n+1)),columns:Array(e.readUInt16BE(n+=2)),keys:[]};n+=2;let s=0,o;for(;n<e.length;)(o=a.columns[s++]={key:e[n++],name:i.column.from?i.column.from(e.toString("utf8",n,n=e.indexOf(0,n))):e.toString("utf8",n,n=e.indexOf(0,n)),type:e.readUInt32BE(n+=1),parser:r[e.readUInt32BE(n)],atttypmod:e.readUInt32BE(n+=4)}).key&&a.keys.push(o),n+=4},Y:()=>{},O:()=>{},B:e=>{var r;t.date=(r=e.readBigInt64BE(9),new Date(Date.UTC(2e3,0,1)+Number(r/BigInt(1e3)))),t.lsn=e.subarray(1,9)},I:e=>{let r=1,a=t[e.readUInt32BE(r)],{row:s}=l_(e,a.columns,r+=7,i);n(s,{command:"insert",relation:a})},D:e=>{let r=1,a=t[e.readUInt32BE(r)],s=75===e[r+=4];n(s||79===e[r]?l_(e,a.columns,r+=3,i).row:null,{command:"delete",relation:a,key:s})},U:e=>{let r=1,a=t[e.readUInt32BE(r)],s=75===e[r+=4],o=s||79===e[r]?l_(e,a.columns,r+=3,i):null;o&&(r=o.i);let{row:c}=l_(e,a.columns,r+3,i);n(c,{command:"update",relation:a,key:s,old:o&&o.row})},T:()=>{},C:()=>{}}).reduce((e,[t,r])=>(e[t.charCodeAt(0)]=r,e),{})[e[0]](e)}(r.subarray(25),o,e.options.parsers,c,t.transform):107===r[0]&&r[17]&&(o.lsn=r.subarray(1,9),function(){let e=lv.alloc(34);e[0]=114,e.fill(o.lsn,1),e.writeBigInt64BE(BigInt(Date.now()-Date.UTC(2e3,0,1))*BigInt(1e3),25),s.write(e)}())}),s.on("error",function(e){console.error("Unexpected error during logical streaming - reconnecting",e)}),s.on("close",e.close),{stream:s,state:i.state};function c(e,t){let r=t.relation.schema+"."+t.relation.table;p("*",e,t),p("*:"+r,e,t),t.relation.keys.length&&p("*:"+r+"="+t.relation.keys.map(t=>e[t.name]),e,t),p(t.command,e,t),p(t.command+":"+r,e,t),t.relation.keys.length&&p(t.command+":"+r+"="+t.relation.keys.map(t=>e[t.name]),e,t)}}function p(e,t,n){r.has(e)&&r.get(e).forEach(({fn:r})=>r(t,n,e))}}(lE,{...r}),i=!1,a=le(),s=le(),o=le(),c=le(),l=le(),u=le(),d=le(),f=le(),h={connecting:s,reserved:o,closed:c,ended:l,open:u,busy:d,full:f},p=[...Array(r.max)].map(()=>ls(r,h,{onopen:O,onend:C,onclose:N})),g=m(function(e){return i?e.reject(cx.connection("CONNECTION_ENDED",r,r)):u.length?S(u.shift(),e):c.length?P(c.shift(),e):void(d.length?S(d.shift(),e):a.push(e))});return Object.assign(g,{get parameters(){return r.parameters},largeObject:lS.bind(null,g),subscribe:n,CLOSE:cb,END:cb,PostgresError:cv,options:r,reserve:w,listen:y,begin:v,close:A,end:E}),g;function m(e){return e.debug=r.debug,Object.entries(r.types).reduce((e,[t,r])=>(e[t]=e=>new cE(e,r.to),e),t),Object.assign(n,{types:t,typed:t,unsafe:function(t,r=[],n={}){return 2!=arguments.length||Array.isArray(r)||(n=r,r=[]),new cw([t],r,e,k,{prepare:!1,...n,simple:"simple"in n?n.simple:0===r.length})},notify:b,array:function e(t,n){return Array.isArray(t)?new cE(t,n||(t.length?cz(t)||25:0),r.shared.typeArrayMap):e(Array.from(arguments))},json:_,file:function(t,r=[],n={}){return 2!=arguments.length||Array.isArray(r)||(n=r,r=[]),new cw([],r,r=>{cp.readFile(t,"utf8",(t,n)=>{if(t)return r.reject(t);r.strings=[n],e(r)})},k,{...n,simple:"simple"in n?n.simple:0===r.length})}}),n;function t(e,t){return new cE(e,t)}function n(t,...i){return t&&Array.isArray(t.raw)?new cw(t,i,e,k):"string"!=typeof t||i.length?new cA(t,i):new ck(r.transform.column.to?r.transform.column.to(t):t)}}async function y(e,t,n){let i={fn:t,onlisten:n},a=y.sql||(y.sql=lE({...r,max:1,idle_timeout:null,max_lifetime:null,fetch_types:!1,onclose(){Object.entries(y.channels).forEach(([e,{listeners:t}])=>{delete y.channels[e],Promise.all(t.map(t=>y(e,t.fn,t.onlisten).catch(()=>{})))})},onnotify(e,t){e in y.channels&&y.channels[e].listeners.forEach(e=>e.fn(t))}})),s=y.channels||(y.channels={});if(e in s){s[e].listeners.push(i);let t=await s[e].result;return i.onlisten&&i.onlisten(),{state:t.state,unlisten:c}}s[e]={result:a`listen ${a.unsafe('"'+e.replace(/"/g,'""')+'"')}`,listeners:[i]};let o=await s[e].result;return i.onlisten&&i.onlisten(),{state:o.state,unlisten:c};async function c(){if(e in s!=!1&&(s[e].listeners=s[e].listeners.filter(e=>e!==i),!s[e].listeners.length))return delete s[e],a`unlisten ${a.unsafe('"'+e.replace(/"/g,'""')+'"')}`}}async function b(e,t){return await g`select pg_notify(${e}, ${""+t})`}async function w(){let e=le(),t=u.length?u.shift():await new Promise((e,t)=>{let r={reserve:e,reject:t};a.push(r),c.length&&P(c.shift(),r)});x(t,o),t.reserved=()=>e.length?t.execute(e.shift()):x(t,o),t.reserved.release=!0;let r=m(function(r){t.queue===f?e.push(r):t.execute(r)||x(t,f)});return r.release=()=>{t.reserved=null,O(t)},r}async function v(e,t){t||(t=e,e="");let r=le(),n=0,i,a=null;try{return await g.unsafe("begin "+e.replace(/[^a-z ]/ig,""),[],{onexecute:function(e){i=e,x(e,o),e.reserved=()=>r.length?e.execute(r.shift()):x(e,o)}}).execute(),await Promise.race([s(i,t),new Promise((e,t)=>i.onclose=t)])}catch(e){throw e}async function s(e,t,i){let o,c;let l=m(function(t){t.catch(e=>o||(o=e)),e.queue===f?r.push(t):e.execute(t)||x(e,f)});l.savepoint=function t(r,i){return r&&Array.isArray(r.raw)?t(e=>e.apply(e,arguments)):(1==arguments.length&&(i=r,r=null),s(e,i,"s"+n+++(r?"_"+r:"")))},l.prepare=e=>a=e.replace(/[^a-z0-9$-_. ]/gi),i&&await l`savepoint ${l(i)}`;try{if(c=await new Promise((e,r)=>{let n=t(l);Promise.resolve(Array.isArray(n)?Promise.all(n):n).then(e,r)}),o)throw o}catch(e){throw await (i?l`rollback to ${l(i)}`:l`rollback`),e instanceof cv&&"25P02"===e.code&&o||e}return i||(a?await l`prepare transaction '${l.unsafe(a)}'`:await l`commit`),c}}function x(e,t){return e.queue.remove(e),t.push(e),e.queue=t,t===u?e.idleTimer.start():e.idleTimer.cancel(),e}function _(e){return new cE(e,3802)}function S(e,t){return e.execute(t)?x(e,d):x(e,f)}function k(e){return new Promise((t,n)=>{e.state?e.active?ls(r).cancel(e.state,t,n):e.cancelled={resolve:t,reject:n}:(a.remove(e),e.cancelled=!0,e.reject(cx.generic("57014","canceling statement due to user request")),t())})}async function E({timeout:e=null}={}){let t;return i||(await 1,i=Promise.race([new Promise(r=>null!==e&&(t=setTimeout(T,1e3*e,r))),Promise.all(p.map(e=>e.end()).concat(y.sql?y.sql.end({timeout:0}):[],n.sql?n.sql.end({timeout:0}):[]))]).then(()=>clearTimeout(t)))}async function A(){await Promise.all(p.map(e=>e.end()))}async function T(e){for(await Promise.all(p.map(e=>e.terminate()));a.length;)a.shift().reject(cx.connection("CONNECTION_DESTROYED",r));e()}function P(e,t){return x(e,s),e.connect(t),e}function C(e){x(e,l)}function O(e){if(0===a.length)return x(e,u);let t=Math.ceil(a.length/(s.length+1)),r=!0;for(;r&&a.length&&t-- >0;){let t=a.shift();if(t.reserve)return t.reserve(e);r=e.execute(t)}r?x(e,d):x(e,f)}function N(e,t){x(e,c),e.reserved=null,e.onclose&&(e.onclose(t),e.onclose=null),r.onclose&&r.onclose(e.id),a.length&&P(e,a.shift())}}function lA(e){return(.5+Math.random()/2)*Math.min(3**e/100,20)}function lT(){return 60*(30+30*Math.random())}let lP=Symbol.for("drizzle:entityKind");function lC(e,t){if(!e||"object"!=typeof e)return!1;if(e instanceof t)return!0;if(!Object.prototype.hasOwnProperty.call(t,lP))throw Error(`Class "${t.name??"<unknown>"}" doesn't look like a Drizzle entity. If this is incorrect and the class is provided by Drizzle, please report this as a bug.`);let r=Object.getPrototypeOf(e).constructor;if(r)for(;r;){if(lP in r&&r[lP]===t[lP])return!0;r=Object.getPrototypeOf(r)}return!1}Symbol.for("drizzle:hasOwnEntityKind");class lO{static [lP]="ConsoleLogWriter";write(e){console.log(e)}}class lN{static [lP]="DefaultLogger";writer;constructor(e){this.writer=e?.writer??new lO}logQuery(e,t){let r=t.map(e=>{try{return JSON.stringify(e)}catch{return String(e)}}),n=r.length?` -- params: [${r.join(", ")}]`:"";this.writer.write(`Query: ${e}${n}`)}}class lR{static [lP]="NoopLogger";logQuery(){}}class lI{constructor(e,t){this.table=e,this.config=t,this.name=t.name,this.keyAsName=t.keyAsName,this.notNull=t.notNull,this.default=t.default,this.defaultFn=t.defaultFn,this.onUpdateFn=t.onUpdateFn,this.hasDefault=t.hasDefault,this.primary=t.primaryKey,this.isUnique=t.isUnique,this.uniqueName=t.uniqueName,this.uniqueType=t.uniqueType,this.dataType=t.dataType,this.columnType=t.columnType,this.generated=t.generated,this.generatedIdentity=t.generatedIdentity}static [lP]="Column";name;keyAsName;primary;notNull;default;defaultFn;onUpdateFn;hasDefault;isUnique;uniqueName;uniqueType;dataType;columnType;enumValues=void 0;generated=void 0;generatedIdentity=void 0;config;mapFromDriverValue(e){return e}mapToDriverValue(e){return e}shouldDisableInsert(){return void 0!==this.config.generated&&"byDefault"!==this.config.generated.type}}class l${static [lP]="ColumnBuilder";config;constructor(e,t,r){this.config={name:e,keyAsName:""===e,notNull:!1,default:void 0,hasDefault:!1,primaryKey:!1,isUnique:!1,uniqueName:void 0,uniqueType:void 0,dataType:t,columnType:r,generated:void 0}}$type(){return this}notNull(){return this.config.notNull=!0,this}default(e){return this.config.default=e,this.config.hasDefault=!0,this}$defaultFn(e){return this.config.defaultFn=e,this.config.hasDefault=!0,this}$default=this.$defaultFn;$onUpdateFn(e){return this.config.onUpdateFn=e,this.config.hasDefault=!0,this}$onUpdate=this.$onUpdateFn;primaryKey(){return this.config.primaryKey=!0,this.config.notNull=!0,this}setName(e){""===this.config.name&&(this.config.name=e)}}let lj=Symbol.for("drizzle:Name");class lU{static [lP]="PgForeignKeyBuilder";reference;_onUpdate="no action";_onDelete="no action";constructor(e,t){this.reference=()=>{let{name:t,columns:r,foreignColumns:n}=e();return{name:t,columns:r,foreignTable:n[0].table,foreignColumns:n}},t&&(this._onUpdate=t.onUpdate,this._onDelete=t.onDelete)}onUpdate(e){return this._onUpdate=void 0===e?"no action":e,this}onDelete(e){return this._onDelete=void 0===e?"no action":e,this}build(e){return new lL(e,this)}}class lL{constructor(e,t){this.table=e,this.reference=t.reference,this.onUpdate=t._onUpdate,this.onDelete=t._onDelete}static [lP]="PgForeignKey";reference;onUpdate;onDelete;getName(){let{name:e,columns:t,foreignColumns:r}=this.reference(),n=t.map(e=>e.name),i=r.map(e=>e.name),a=[this.table[lj],...n,r[0].table[lj],...i];return e??`${a.join("_")}_fk`}}function lD(e,...t){return e(...t)}function lM(e,t){return`${e[lj]}_${t.join("_")}_unique`}class lB{constructor(e,t){this.name=t,this.columns=e}static [lP]=null;columns;nullsNotDistinctConfig=!1;nullsNotDistinct(){return this.nullsNotDistinctConfig=!0,this}build(e){return new lq(e,this.columns,this.nullsNotDistinctConfig,this.name)}}class lH{static [lP]=null;name;constructor(e){this.name=e}on(...e){return new lB(e,this.name)}}class lq{constructor(e,t,r,n){this.table=e,this.columns=t,this.name=n??lM(this.table,this.columns.map(e=>e.name)),this.nullsNotDistinct=r}static [lP]=null;columns;name;nullsNotDistinct=!1;getName(){return this.name}}function lz(e,t,r){for(let n=t;n<e.length;n++){let i=e[n];if("\\"===i){n++;continue}if('"'===i)return[e.slice(t,n).replace(/\\/g,""),n+1];if(!r&&(","===i||"}"===i))return[e.slice(t,n).replace(/\\/g,""),n]}return[e.slice(t).replace(/\\/g,""),e.length]}class lW extends l${foreignKeyConfigs=[];static [lP]="PgColumnBuilder";array(e){return new lJ(this.config.name,this,e)}references(e,t={}){return this.foreignKeyConfigs.push({ref:e,actions:t}),this}unique(e,t){return this.config.isUnique=!0,this.config.uniqueName=e,this.config.uniqueType=t?.nulls,this}generatedAlwaysAs(e){return this.config.generated={as:e,type:"always",mode:"stored"},this}buildForeignKeys(e,t){return this.foreignKeyConfigs.map(({ref:r,actions:n})=>lD((r,n)=>{let i=new lU(()=>({columns:[e],foreignColumns:[r()]}));return n.onUpdate&&i.onUpdate(n.onUpdate),n.onDelete&&i.onDelete(n.onDelete),i.build(t)},r,n))}buildExtraConfigColumn(e){return new lK(e,this.config)}}class lF extends lI{constructor(e,t){t.uniqueName||(t.uniqueName=lM(e,[t.name])),super(e,t),this.table=e}static [lP]="PgColumn"}class lK extends lF{static [lP]="ExtraConfigColumn";getSQLType(){return this.getSQLType()}indexConfig={order:this.config.order??"asc",nulls:this.config.nulls??"last",opClass:this.config.opClass};defaultConfig={order:"asc",nulls:"last",opClass:void 0};asc(){return this.indexConfig.order="asc",this}desc(){return this.indexConfig.order="desc",this}nullsFirst(){return this.indexConfig.nulls="first",this}nullsLast(){return this.indexConfig.nulls="last",this}op(e){return this.indexConfig.opClass=e,this}}class lV{static [lP]=null;constructor(e,t,r,n){this.name=e,this.keyAsName=t,this.type=r,this.indexConfig=n}name;keyAsName;type;indexConfig}class lJ extends lW{static [lP]="PgArrayBuilder";constructor(e,t,r){super(e,"array","PgArray"),this.config.baseBuilder=t,this.config.size=r}build(e){let t=this.config.baseBuilder.build(e);return new lQ(e,this.config,t)}}class lQ extends lF{constructor(e,t,r,n){super(e,t),this.baseColumn=r,this.range=n,this.size=t.size}size;static [lP]="PgArray";getSQLType(){return`${this.baseColumn.getSQLType()}[${"number"==typeof this.size?this.size:""}]`}mapFromDriverValue(e){return"string"==typeof e&&(e=function(e){let[t]=function e(t,r=0){let n=[],i=r,a=!1;for(;i<t.length;){let s=t[i];if(","===s){(a||i===r)&&n.push(""),a=!0,i++;continue}if(a=!1,"\\"===s){i+=2;continue}if('"'===s){let[e,r]=lz(t,i+1,!0);n.push(e),i=r;continue}if("}"===s)return[n,i+1];if("{"===s){let[r,a]=e(t,i+1);n.push(r),i=a;continue}let[o,c]=lz(t,i,!1);n.push(o),i=c}return[n,i]}(e,1);return t}(e)),e.map(e=>this.baseColumn.mapFromDriverValue(e))}mapToDriverValue(e,t=!1){let r=e.map(e=>null===e?null:lC(this.baseColumn,lQ)?this.baseColumn.mapToDriverValue(e,!0):this.baseColumn.mapToDriverValue(e));return t?r:function e(t){return`{${t.map(t=>Array.isArray(t)?e(t):"string"==typeof t?`"${t.replace(/\\/g,"\\\\").replace(/"/g,'\\"')}"`:`${t}`).join(",")}}`}(r)}}class lG extends lW{static [lP]="PgEnumObjectColumnBuilder";constructor(e,t){super(e,"string","PgEnumObjectColumn"),this.config.enum=t}build(e){return new lX(e,this.config)}}class lX extends lF{static [lP]="PgEnumObjectColumn";enum;enumValues=this.config.enum.enumValues;constructor(e,t){super(e,t),this.enum=t.enum}getSQLType(){return this.enum.enumName}}let lY=Symbol.for("drizzle:isPgEnum");class lZ extends lW{static [lP]="PgEnumColumnBuilder";constructor(e,t){super(e,"string","PgEnumColumn"),this.config.enum=t}build(e){return new l0(e,this.config)}}class l0 extends lF{static [lP]="PgEnumColumn";enum=this.config.enum;enumValues=this.config.enum.enumValues;constructor(e,t){super(e,t),this.enum=t.enum}getSQLType(){return this.enum.enumName}}class l1{static [lP]="Subquery";constructor(e,t,r,n=!1){this._={brand:"Subquery",sql:e,selectedFields:t,alias:r,isWith:n}}}class l2 extends l1{static [lP]="WithSubquery"}let l5={startActiveSpan:(e,t)=>c?(l||(l=c.trace.getTracer("drizzle-orm","0.43.1")),lD((r,n)=>n.startActiveSpan(e,e=>{try{return t(e)}catch(t){throw e.setStatus({code:r.SpanStatusCode.ERROR,message:t instanceof Error?t.message:"Unknown error"}),t}finally{e.end()}}),c,l)):t()},l6=Symbol.for("drizzle:ViewBaseConfig"),l3=Symbol.for("drizzle:Schema"),l8=Symbol.for("drizzle:Columns"),l4=Symbol.for("drizzle:ExtraConfigColumns"),l9=Symbol.for("drizzle:OriginalName"),l7=Symbol.for("drizzle:BaseName"),ue=Symbol.for("drizzle:IsAlias"),ut=Symbol.for("drizzle:ExtraConfigBuilder"),ur=Symbol.for("drizzle:IsDrizzleTable");class un{static [lP]="Table";static Symbol={Name:lj,Schema:l3,OriginalName:l9,Columns:l8,ExtraConfigColumns:l4,BaseName:l7,IsAlias:ue,ExtraConfigBuilder:ut};[lj];[l9];[l3];[l8];[l4];[l7];[ue]=!1;[ur]=!0;[ut]=void 0;constructor(e,t,r){this[lj]=this[l9]=e,this[l3]=t,this[l7]=r}}function ui(e){return`${e[l3]??"public"}.${e[lj]}`}class ua{static [lP]=null}function us(e){return null!=e&&"function"==typeof e.getSQL}class uo{static [lP]="StringChunk";value;constructor(e){this.value=Array.isArray(e)?e:[e]}getSQL(){return new uc([this])}}class uc{constructor(e){this.queryChunks=e}static [lP]="SQL";decoder=uu;shouldInlineParams=!1;append(e){return this.queryChunks.push(...e.queryChunks),this}toQuery(e){return l5.startActiveSpan("drizzle.buildSQL",t=>{let r=this.buildQueryFromSourceParams(this.queryChunks,e);return t?.setAttributes({"drizzle.query.text":r.sql,"drizzle.query.params":JSON.stringify(r.params)}),r})}buildQueryFromSourceParams(e,t){let r=Object.assign({},t,{inlineParams:t.inlineParams||this.shouldInlineParams,paramStartIndex:t.paramStartIndex||{value:0}}),{casing:n,escapeName:i,escapeParam:a,prepareTyping:s,inlineParams:o,paramStartIndex:c}=r;return function(e){let t={sql:"",params:[]};for(let r of e)t.sql+=r.sql,t.params.push(...r.params),r.typings?.length&&(t.typings||(t.typings=[]),t.typings.push(...r.typings));return t}(e.map(e=>{if(lC(e,uo))return{sql:e.value.join(""),params:[]};if(lC(e,ul))return{sql:i(e.value),params:[]};if(void 0===e)return{sql:"",params:[]};if(Array.isArray(e)){let t=[new uo("(")];for(let[r,n]of e.entries())t.push(n),r<e.length-1&&t.push(new uo(", "));return t.push(new uo(")")),this.buildQueryFromSourceParams(t,r)}if(lC(e,uc))return this.buildQueryFromSourceParams(e.queryChunks,{...r,inlineParams:o||e.shouldInlineParams});if(lC(e,un)){let t=e[un.Symbol.Schema],r=e[un.Symbol.Name];return{sql:void 0===t||e[ue]?i(r):i(t)+"."+i(r),params:[]}}if(lC(e,lI)){let r=n.getColumnCasing(e);if("indexes"===t.invokeSource)return{sql:i(r),params:[]};let a=e.table[un.Symbol.Schema];return{sql:e.table[ue]||void 0===a?i(e.table[un.Symbol.Name])+"."+i(r):i(a)+"."+i(e.table[un.Symbol.Name])+"."+i(r),params:[]}}if(lC(e,uy)){let t=e[l6].schema,r=e[l6].name;return{sql:void 0===t||e[l6].isAlias?i(r):i(t)+"."+i(r),params:[]}}if(lC(e,uf)){if(lC(e.value,up))return{sql:a(c.value++,e),params:[e],typings:["none"]};let t=null===e.value?null:e.encoder.mapToDriverValue(e.value);if(lC(t,uc))return this.buildQueryFromSourceParams([t],r);if(o)return{sql:this.mapInlineParam(t,r),params:[]};let n=["none"];return s&&(n=[s(e.encoder)]),{sql:a(c.value++,t),params:[t],typings:n}}return lC(e,up)?{sql:a(c.value++,e),params:[e],typings:["none"]}:lC(e,uc.Aliased)&&void 0!==e.fieldAlias?{sql:i(e.fieldAlias),params:[]}:lC(e,l1)?e._.isWith?{sql:i(e._.alias),params:[]}:this.buildQueryFromSourceParams([new uo("("),e._.sql,new uo(") "),new ul(e._.alias)],r):e&&"function"==typeof e&&lY in e&&!0===e[lY]?e.schema?{sql:i(e.schema)+"."+i(e.enumName),params:[]}:{sql:i(e.enumName),params:[]}:us(e)?e.shouldOmitSQLParens?.()?this.buildQueryFromSourceParams([e.getSQL()],r):this.buildQueryFromSourceParams([new uo("("),e.getSQL(),new uo(")")],r):o?{sql:this.mapInlineParam(e,r),params:[]}:{sql:a(c.value++,e),params:[e],typings:["none"]}}))}mapInlineParam(e,{escapeString:t}){if(null===e)return"null";if("number"==typeof e||"boolean"==typeof e)return e.toString();if("string"==typeof e)return t(e);if("object"==typeof e){let r=e.toString();return"[object Object]"===r?t(JSON.stringify(e)):t(r)}throw Error("Unexpected param value: "+e)}getSQL(){return this}as(e){return void 0===e?this:new uc.Aliased(this,e)}mapWith(e){return this.decoder="function"==typeof e?{mapFromDriverValue:e}:e,this}inlineParams(){return this.shouldInlineParams=!0,this}if(e){return e?this:void 0}}class ul{constructor(e){this.value=e}static [lP]="Name";brand;getSQL(){return new uc([this])}}let uu={mapFromDriverValue:e=>e},ud={mapToDriverValue:e=>e};({...uu,...ud});class uf{constructor(e,t=ud){this.value=e,this.encoder=t}static [lP]="Param";brand;getSQL(){return new uc([this])}}function uh(e,...t){let r=[];for(let[n,i]of((t.length>0||e.length>0&&""!==e[0])&&r.push(new uo(e[0])),t.entries()))r.push(i,new uo(e[n+1]));return new uc(r)}(e=>{e.empty=function(){return new uc([])},e.fromList=function(e){return new uc(e)},e.raw=function(e){return new uc([new uo(e)])},e.join=function(e,t){let r=[];for(let[n,i]of e.entries())n>0&&void 0!==t&&r.push(t),r.push(i);return new uc(r)},e.identifier=function(e){return new ul(e)},e.placeholder=function(e){return new up(e)},e.param=function(e,t){return new uf(e,t)}})(uh||(uh={})),(e=>{class t{constructor(e,t){this.sql=e,this.fieldAlias=t}static [lP]="SQL.Aliased";isSelectionField=!1;getSQL(){return this.sql}clone(){return new t(this.sql,this.fieldAlias)}}e.Aliased=t})(uc||(uc={}));class up{constructor(e){this.name=e}static [lP]="Placeholder";getSQL(){return new uc([this])}}function ug(e,t){return e.map(e=>{if(lC(e,up)){if(!(e.name in t))throw Error(`No value for placeholder "${e.name}" was provided`);return t[e.name]}if(lC(e,uf)&&lC(e.value,up)){if(!(e.value.name in t))throw Error(`No value for placeholder "${e.value.name}" was provided`);return e.encoder.mapToDriverValue(t[e.value.name])}return e})}let um=Symbol.for("drizzle:IsDrizzleView");class uy{static [lP]="View";[l6];[um]=!0;constructor({name:e,schema:t,selectedFields:r,query:n}){this[l6]={name:e,originalName:e,schema:t,selectedFields:r,query:n,isExisting:!n,isAlias:!1}}getSQL(){return new uc([this])}}lI.prototype.getSQL=function(){return new uc([this])},un.prototype.getSQL=function(){return new uc([this])},l1.prototype.getSQL=function(){return new uc([this])};class ub{constructor(e){this.table=e}static [lP]="ColumnAliasProxyHandler";get(e,t){return"table"===t?this.table:e[t]}}class uw{constructor(e,t){this.alias=e,this.replaceOriginalName=t}static [lP]="TableAliasProxyHandler";get(e,t){if(t===un.Symbol.IsAlias)return!0;if(t===un.Symbol.Name||this.replaceOriginalName&&t===un.Symbol.OriginalName)return this.alias;if(t===l6)return{...e[l6],name:this.alias,isAlias:!0};if(t===un.Symbol.Columns){let t=e[un.Symbol.Columns];if(!t)return t;let r={};return Object.keys(t).map(n=>{r[n]=new Proxy(t[n],new ub(new Proxy(e,this)))}),r}let r=e[t];return lC(r,lI)?new Proxy(r,new ub(new Proxy(e,this))):r}}class uv{constructor(e){this.alias=e}static [lP]=null;get(e,t){return"sourceTable"===t?ux(e.sourceTable,this.alias):e[t]}}function ux(e,t){return new Proxy(e,new uw(t,!1))}function u_(e,t){return new Proxy(e,new ub(new Proxy(e.table,new uw(t,!1))))}function uS(e,t){return new uc.Aliased(uk(e.sql,t),e.fieldAlias)}function uk(e,t){return uh.join(e.queryChunks.map(e=>lC(e,lI)?u_(e,t):lC(e,uc)?uk(e,t):lC(e,uc.Aliased)?uS(e,t):e))}function uE(e){return(e.replace(/['\u2019]/g,"").match(/[\da-z]+|[A-Z]+(?![a-z])|[A-Z][\da-z]+/g)??[]).map(e=>e.toLowerCase()).join("_")}function uA(e){return(e.replace(/['\u2019]/g,"").match(/[\da-z]+|[A-Z]+(?![a-z])|[A-Z][\da-z]+/g)??[]).reduce((e,t,r)=>e+(0===r?t.toLowerCase():`${t[0].toUpperCase()}${t.slice(1)}`),"")}function uT(e){return e}class uP{static [lP]="CasingCache";cache={};cachedTables={};convert;constructor(e){this.convert="snake_case"===e?uE:"camelCase"===e?uA:uT}getColumnCasing(e){if(!e.keyAsName)return e.name;let t=e.table[un.Symbol.Schema]??"public",r=e.table[un.Symbol.OriginalName],n=`${t}.${r}.${e.name}`;return this.cache[n]||this.cacheTable(e.table),this.cache[n]}cacheTable(e){let t=e[un.Symbol.Schema]??"public",r=e[un.Symbol.OriginalName],n=`${t}.${r}`;if(!this.cachedTables[n]){for(let t of Object.values(e[un.Symbol.Columns])){let e=`${n}.${t.name}`;this.cache[e]=this.convert(t.name)}this.cachedTables[n]=!0}}clearCache(){this.cache={},this.cachedTables={}}}class uC extends Error{static [lP]="DrizzleError";constructor({message:e,cause:t}){super(e),this.name="DrizzleError",this.cause=t}}class uO extends uC{static [lP]="TransactionRollbackError";constructor(){super({message:"Rollback"})}}class uN extends lW{static [lP]="PgJsonbBuilder";constructor(e){super(e,"json","PgJsonb")}build(e){return new uR(e,this.config)}}class uR extends lF{static [lP]="PgJsonb";constructor(e,t){super(e,t)}getSQLType(){return"jsonb"}mapToDriverValue(e){return JSON.stringify(e)}mapFromDriverValue(e){if("string"==typeof e)try{return JSON.parse(e)}catch{}return e}}function uI(e){return new uN(e??"")}class u$ extends lW{static [lP]="PgJsonBuilder";constructor(e){super(e,"json","PgJson")}build(e){return new uj(e,this.config)}}class uj extends lF{static [lP]="PgJson";constructor(e,t){super(e,t)}getSQLType(){return"json"}mapToDriverValue(e){return JSON.stringify(e)}mapFromDriverValue(e){if("string"==typeof e)try{return JSON.parse(e)}catch{}return e}}function uU(e){return new u$(e??"")}function uL(e,t){return Object.entries(e).reduce((e,[r,n])=>{if("string"!=typeof r)return e;let i=t?[...t,r]:[r];return lC(n,lI)||lC(n,uc)||lC(n,uc.Aliased)?e.push({path:i,field:n}):lC(n,un)?e.push(...uL(n[un.Symbol.Columns],i)):e.push(...uL(n,i)),e},[])}function uD(e,t){let r=Object.keys(e),n=Object.keys(t);if(r.length!==n.length)return!1;for(let[e,t]of r.entries())if(t!==n[e])return!1;return!0}function uM(e,t){let r=Object.entries(t).filter(([,e])=>void 0!==e).map(([t,r])=>lC(r,uc)||lC(r,lI)?[t,r]:[t,new uf(r,e[un.Symbol.Columns][t])]);if(0===r.length)throw Error("No values to set");return Object.fromEntries(r)}function uB(e){return lC(e,l1)?e._.alias:lC(e,uy)?e[l6].name:lC(e,uc)?void 0:e[un.Symbol.IsAlias]?e[un.Symbol.Name]:e[un.Symbol.BaseName]}function uH(e,t){return{name:"string"==typeof e&&e.length>0?e:"",config:"object"==typeof e?e:t}}class uq extends lW{static [lP]="PgNumericBuilder";constructor(e,t,r){super(e,"string","PgNumeric"),this.config.precision=t,this.config.scale=r}build(e){return new uz(e,this.config)}}class uz extends lF{static [lP]="PgNumeric";precision;scale;constructor(e,t){super(e,t),this.precision=t.precision,this.scale=t.scale}mapFromDriverValue(e){return"string"==typeof e?e:String(e)}getSQLType(){return void 0!==this.precision&&void 0!==this.scale?`numeric(${this.precision}, ${this.scale})`:void 0===this.precision?"numeric":`numeric(${this.precision})`}}class uW extends lW{static [lP]="PgNumericNumberBuilder";constructor(e,t,r){super(e,"number","PgNumericNumber"),this.config.precision=t,this.config.scale=r}build(e){return new uF(e,this.config)}}class uF extends lF{static [lP]="PgNumericNumber";precision;scale;constructor(e,t){super(e,t),this.precision=t.precision,this.scale=t.scale}mapFromDriverValue(e){return"number"==typeof e?e:Number(e)}mapToDriverValue=String;getSQLType(){return void 0!==this.precision&&void 0!==this.scale?`numeric(${this.precision}, ${this.scale})`:void 0===this.precision?"numeric":`numeric(${this.precision})`}}class uK extends lW{static [lP]="PgNumericBigIntBuilder";constructor(e,t,r){super(e,"bigint","PgNumericBigInt"),this.config.precision=t,this.config.scale=r}build(e){return new uV(e,this.config)}}class uV extends lF{static [lP]="PgNumericBigInt";precision;scale;constructor(e,t){super(e,t),this.precision=t.precision,this.scale=t.scale}mapFromDriverValue=BigInt;mapToDriverValue=String;getSQLType(){return void 0!==this.precision&&void 0!==this.scale?`numeric(${this.precision}, ${this.scale})`:void 0===this.precision?"numeric":`numeric(${this.precision})`}}function uJ(e,t){let{name:r,config:n}=uH(e,t),i=n?.mode;return"number"===i?new uW(r,n?.precision,n?.scale):"bigint"===i?new uK(r,n?.precision,n?.scale):new uq(r,n?.precision,n?.scale)}class uQ extends lW{static [lP]="PgDateColumnBaseBuilder";defaultNow(){return this.default(uh`now()`)}}class uG extends uQ{constructor(e,t,r){super(e,"string","PgTime"),this.withTimezone=t,this.precision=r,this.config.withTimezone=t,this.config.precision=r}static [lP]="PgTimeBuilder";build(e){return new uX(e,this.config)}}class uX extends lF{static [lP]="PgTime";withTimezone;precision;constructor(e,t){super(e,t),this.withTimezone=t.withTimezone,this.precision=t.precision}getSQLType(){let e=void 0===this.precision?"":`(${this.precision})`;return`time${e}${this.withTimezone?" with time zone":""}`}}function uY(e,t={}){let{name:r,config:n}=uH(e,t);return new uG(r,n.withTimezone??!1,n.precision)}class uZ extends uQ{static [lP]="PgTimestampBuilder";constructor(e,t,r){super(e,"date","PgTimestamp"),this.config.withTimezone=t,this.config.precision=r}build(e){return new u0(e,this.config)}}class u0 extends lF{static [lP]="PgTimestamp";withTimezone;precision;constructor(e,t){super(e,t),this.withTimezone=t.withTimezone,this.precision=t.precision}getSQLType(){let e=void 0===this.precision?"":` (${this.precision})`;return`timestamp${e}${this.withTimezone?" with time zone":""}`}mapFromDriverValue=e=>new Date(this.withTimezone?e:e+"+0000");mapToDriverValue=e=>e.toISOString()}class u1 extends uQ{static [lP]="PgTimestampStringBuilder";constructor(e,t,r){super(e,"string","PgTimestampString"),this.config.withTimezone=t,this.config.precision=r}build(e){return new u2(e,this.config)}}class u2 extends lF{static [lP]="PgTimestampString";withTimezone;precision;constructor(e,t){super(e,t),this.withTimezone=t.withTimezone,this.precision=t.precision}getSQLType(){let e=void 0===this.precision?"":`(${this.precision})`;return`timestamp${e}${this.withTimezone?" with time zone":""}`}}function u5(e,t={}){let{name:r,config:n}=uH(e,t);return n?.mode==="string"?new u1(r,n.withTimezone??!1,n.precision):new uZ(r,n?.withTimezone??!1,n?.precision)}class u6 extends uQ{static [lP]="PgDateBuilder";constructor(e){super(e,"date","PgDate")}build(e){return new u3(e,this.config)}}class u3 extends lF{static [lP]="PgDate";getSQLType(){return"date"}mapFromDriverValue(e){return new Date(e)}mapToDriverValue(e){return e.toISOString()}}class u8 extends uQ{static [lP]="PgDateStringBuilder";constructor(e){super(e,"string","PgDateString")}build(e){return new u4(e,this.config)}}class u4 extends lF{static [lP]="PgDateString";getSQLType(){return"date"}}function u9(e,t){let{name:r,config:n}=uH(e,t);return n?.mode==="date"?new u6(r):new u8(r)}class u7 extends lW{static [lP]="PgUUIDBuilder";constructor(e){super(e,"string","PgUUID")}defaultRandom(){return this.default(uh`gen_random_uuid()`)}build(e){return new de(e,this.config)}}class de extends lF{static [lP]="PgUUID";getSQLType(){return"uuid"}}function dt(e){return new u7(e??"")}class dr extends lW{static [lP]="PgIntColumnBaseBuilder";generatedAlwaysAsIdentity(e){if(e){let{name:t,...r}=e;this.config.generatedIdentity={type:"always",sequenceName:t,sequenceOptions:r}}else this.config.generatedIdentity={type:"always"};return this.config.hasDefault=!0,this.config.notNull=!0,this}generatedByDefaultAsIdentity(e){if(e){let{name:t,...r}=e;this.config.generatedIdentity={type:"byDefault",sequenceName:t,sequenceOptions:r}}else this.config.generatedIdentity={type:"byDefault"};return this.config.hasDefault=!0,this.config.notNull=!0,this}}class dn extends dr{static [lP]="PgBigInt53Builder";constructor(e){super(e,"number","PgBigInt53")}build(e){return new di(e,this.config)}}class di extends lF{static [lP]="PgBigInt53";getSQLType(){return"bigint"}mapFromDriverValue(e){return"number"==typeof e?e:Number(e)}}class da extends dr{static [lP]="PgBigInt64Builder";constructor(e){super(e,"bigint","PgBigInt64")}build(e){return new ds(e,this.config)}}class ds extends lF{static [lP]="PgBigInt64";getSQLType(){return"bigint"}mapFromDriverValue(e){return BigInt(e)}}function dc(e,t){let{name:r,config:n}=uH(e,t);return"number"===n.mode?new dn(r):new da(r)}class dl extends lW{static [lP]="PgBigSerial53Builder";constructor(e){super(e,"number","PgBigSerial53"),this.config.hasDefault=!0,this.config.notNull=!0}build(e){return new du(e,this.config)}}class du extends lF{static [lP]="PgBigSerial53";getSQLType(){return"bigserial"}mapFromDriverValue(e){return"number"==typeof e?e:Number(e)}}class dd extends lW{static [lP]="PgBigSerial64Builder";constructor(e){super(e,"bigint","PgBigSerial64"),this.config.hasDefault=!0}build(e){return new df(e,this.config)}}class df extends lF{static [lP]="PgBigSerial64";getSQLType(){return"bigserial"}mapFromDriverValue(e){return BigInt(e)}}function dh(e,t){let{name:r,config:n}=uH(e,t);return"number"===n.mode?new dl(r):new dd(r)}class dp extends lW{static [lP]="PgBooleanBuilder";constructor(e){super(e,"boolean","PgBoolean")}build(e){return new dg(e,this.config)}}class dg extends lF{static [lP]="PgBoolean";getSQLType(){return"boolean"}}function dm(e){return new dp(e??"")}class dy extends lW{static [lP]="PgCharBuilder";constructor(e,t){super(e,"string","PgChar"),this.config.length=t.length,this.config.enumValues=t.enum}build(e){return new db(e,this.config)}}class db extends lF{static [lP]="PgChar";length=this.config.length;enumValues=this.config.enumValues;getSQLType(){return void 0===this.length?"char":`char(${this.length})`}}function dw(e,t={}){let{name:r,config:n}=uH(e,t);return new dy(r,n)}class dv extends lW{static [lP]="PgCidrBuilder";constructor(e){super(e,"string","PgCidr")}build(e){return new dx(e,this.config)}}class dx extends lF{static [lP]="PgCidr";getSQLType(){return"cidr"}}function d_(e){return new dv(e??"")}class dS extends lW{static [lP]="PgCustomColumnBuilder";constructor(e,t,r){super(e,"custom","PgCustomColumn"),this.config.fieldConfig=t,this.config.customTypeParams=r}build(e){return new dk(e,this.config)}}class dk extends lF{static [lP]="PgCustomColumn";sqlName;mapTo;mapFrom;constructor(e,t){super(e,t),this.sqlName=t.customTypeParams.dataType(t.fieldConfig),this.mapTo=t.customTypeParams.toDriver,this.mapFrom=t.customTypeParams.fromDriver}getSQLType(){return this.sqlName}mapFromDriverValue(e){return"function"==typeof this.mapFrom?this.mapFrom(e):e}mapToDriverValue(e){return"function"==typeof this.mapTo?this.mapTo(e):e}}function dE(e){return(t,r)=>{let{name:n,config:i}=uH(t,r);return new dS(n,i,e)}}class dA extends lW{static [lP]="PgDoublePrecisionBuilder";constructor(e){super(e,"number","PgDoublePrecision")}build(e){return new dT(e,this.config)}}class dT extends lF{static [lP]="PgDoublePrecision";getSQLType(){return"double precision"}mapFromDriverValue(e){return"string"==typeof e?Number.parseFloat(e):e}}function dP(e){return new dA(e??"")}class dC extends lW{static [lP]="PgInetBuilder";constructor(e){super(e,"string","PgInet")}build(e){return new dO(e,this.config)}}class dO extends lF{static [lP]="PgInet";getSQLType(){return"inet"}}function dN(e){return new dC(e??"")}class dR extends dr{static [lP]="PgIntegerBuilder";constructor(e){super(e,"number","PgInteger")}build(e){return new dI(e,this.config)}}class dI extends lF{static [lP]="PgInteger";getSQLType(){return"integer"}mapFromDriverValue(e){return"string"==typeof e?Number.parseInt(e):e}}function d$(e){return new dR(e??"")}class dj extends lW{static [lP]="PgIntervalBuilder";constructor(e,t){super(e,"string","PgInterval"),this.config.intervalConfig=t}build(e){return new dU(e,this.config)}}class dU extends lF{static [lP]="PgInterval";fields=this.config.intervalConfig.fields;precision=this.config.intervalConfig.precision;getSQLType(){let e=this.fields?` ${this.fields}`:"",t=this.precision?`(${this.precision})`:"";return`interval${e}${t}`}}function dL(e,t={}){let{name:r,config:n}=uH(e,t);return new dj(r,n)}class dD extends lW{static [lP]="PgLineBuilder";constructor(e){super(e,"array","PgLine")}build(e){return new dM(e,this.config)}}class dM extends lF{static [lP]="PgLine";getSQLType(){return"line"}mapFromDriverValue(e){let[t,r,n]=e.slice(1,-1).split(",");return[Number.parseFloat(t),Number.parseFloat(r),Number.parseFloat(n)]}mapToDriverValue(e){return`{${e[0]},${e[1]},${e[2]}}`}}class dB extends lW{static [lP]="PgLineABCBuilder";constructor(e){super(e,"json","PgLineABC")}build(e){return new dH(e,this.config)}}class dH extends lF{static [lP]="PgLineABC";getSQLType(){return"line"}mapFromDriverValue(e){let[t,r,n]=e.slice(1,-1).split(",");return{a:Number.parseFloat(t),b:Number.parseFloat(r),c:Number.parseFloat(n)}}mapToDriverValue(e){return`{${e.a},${e.b},${e.c}}`}}function dq(e,t){let{name:r,config:n}=uH(e,t);return n?.mode&&"tuple"!==n.mode?new dB(r):new dD(r)}class dz extends lW{static [lP]="PgMacaddrBuilder";constructor(e){super(e,"string","PgMacaddr")}build(e){return new dW(e,this.config)}}class dW extends lF{static [lP]="PgMacaddr";getSQLType(){return"macaddr"}}function dF(e){return new dz(e??"")}class dK extends lW{static [lP]="PgMacaddr8Builder";constructor(e){super(e,"string","PgMacaddr8")}build(e){return new dV(e,this.config)}}class dV extends lF{static [lP]="PgMacaddr8";getSQLType(){return"macaddr8"}}function dJ(e){return new dK(e??"")}class dQ extends lW{static [lP]="PgPointTupleBuilder";constructor(e){super(e,"array","PgPointTuple")}build(e){return new dG(e,this.config)}}class dG extends lF{static [lP]="PgPointTuple";getSQLType(){return"point"}mapFromDriverValue(e){if("string"==typeof e){let[t,r]=e.slice(1,-1).split(",");return[Number.parseFloat(t),Number.parseFloat(r)]}return[e.x,e.y]}mapToDriverValue(e){return`(${e[0]},${e[1]})`}}class dX extends lW{static [lP]="PgPointObjectBuilder";constructor(e){super(e,"json","PgPointObject")}build(e){return new dY(e,this.config)}}class dY extends lF{static [lP]="PgPointObject";getSQLType(){return"point"}mapFromDriverValue(e){if("string"==typeof e){let[t,r]=e.slice(1,-1).split(",");return{x:Number.parseFloat(t),y:Number.parseFloat(r)}}return e}mapToDriverValue(e){return`(${e.x},${e.y})`}}function dZ(e,t){let{name:r,config:n}=uH(e,t);return n?.mode&&"tuple"!==n.mode?new dX(r):new dQ(r)}function d0(e,t){let r=new DataView(new ArrayBuffer(8));for(let n=0;n<8;n++)r.setUint8(n,e[t+n]);return r.getFloat64(0,!0)}function d1(e){let t=function(e){let t=[];for(let r=0;r<e.length;r+=2)t.push(Number.parseInt(e.slice(r,r+2),16));return new Uint8Array(t)}(e),r=0,n=t[0];r+=1;let i=new DataView(t.buffer),a=i.getUint32(r,1===n);if(r+=4,0x20000000&a&&(i.getUint32(r,1===n),r+=4),(65535&a)==1){let e=d0(t,r),n=d0(t,r+=8);return r+=8,[e,n]}throw Error("Unsupported geometry type")}class d2 extends lW{static [lP]="PgGeometryBuilder";constructor(e){super(e,"array","PgGeometry")}build(e){return new d5(e,this.config)}}class d5 extends lF{static [lP]="PgGeometry";getSQLType(){return"geometry(point)"}mapFromDriverValue(e){return d1(e)}mapToDriverValue(e){return`point(${e[0]} ${e[1]})`}}class d6 extends lW{static [lP]="PgGeometryObjectBuilder";constructor(e){super(e,"json","PgGeometryObject")}build(e){return new d3(e,this.config)}}class d3 extends lF{static [lP]="PgGeometryObject";getSQLType(){return"geometry(point)"}mapFromDriverValue(e){let t=d1(e);return{x:t[0],y:t[1]}}mapToDriverValue(e){return`point(${e.x} ${e.y})`}}function d8(e,t){let{name:r,config:n}=uH(e,t);return n?.mode&&"tuple"!==n.mode?new d6(r):new d2(r)}class d4 extends lW{static [lP]="PgRealBuilder";constructor(e,t){super(e,"number","PgReal"),this.config.length=t}build(e){return new d9(e,this.config)}}class d9 extends lF{static [lP]="PgReal";constructor(e,t){super(e,t)}getSQLType(){return"real"}mapFromDriverValue=e=>"string"==typeof e?Number.parseFloat(e):e}function d7(e){return new d4(e??"")}class fe extends lW{static [lP]="PgSerialBuilder";constructor(e){super(e,"number","PgSerial"),this.config.hasDefault=!0,this.config.notNull=!0}build(e){return new ft(e,this.config)}}class ft extends lF{static [lP]="PgSerial";getSQLType(){return"serial"}}function fr(e){return new fe(e??"")}class fn extends dr{static [lP]="PgSmallIntBuilder";constructor(e){super(e,"number","PgSmallInt")}build(e){return new fi(e,this.config)}}class fi extends lF{static [lP]="PgSmallInt";getSQLType(){return"smallint"}mapFromDriverValue=e=>"string"==typeof e?Number(e):e}function fa(e){return new fn(e??"")}class fs extends lW{static [lP]="PgSmallSerialBuilder";constructor(e){super(e,"number","PgSmallSerial"),this.config.hasDefault=!0,this.config.notNull=!0}build(e){return new fo(e,this.config)}}class fo extends lF{static [lP]="PgSmallSerial";getSQLType(){return"smallserial"}}function fc(e){return new fs(e??"")}class fl extends lW{static [lP]="PgTextBuilder";constructor(e,t){super(e,"string","PgText"),this.config.enumValues=t.enum}build(e){return new fu(e,this.config)}}class fu extends lF{static [lP]="PgText";enumValues=this.config.enumValues;getSQLType(){return"text"}}function fd(e,t={}){let{name:r,config:n}=uH(e,t);return new fl(r,n)}class ff extends lW{static [lP]="PgVarcharBuilder";constructor(e,t){super(e,"string","PgVarchar"),this.config.length=t.length,this.config.enumValues=t.enum}build(e){return new fh(e,this.config)}}class fh extends lF{static [lP]="PgVarchar";length=this.config.length;enumValues=this.config.enumValues;getSQLType(){return void 0===this.length?"varchar":`varchar(${this.length})`}}function fp(e,t={}){let{name:r,config:n}=uH(e,t);return new ff(r,n)}class fg extends lW{static [lP]="PgBinaryVectorBuilder";constructor(e,t){super(e,"string","PgBinaryVector"),this.config.dimensions=t.dimensions}build(e){return new fm(e,this.config)}}class fm extends lF{static [lP]="PgBinaryVector";dimensions=this.config.dimensions;getSQLType(){return`bit(${this.dimensions})`}}function fy(e,t){let{name:r,config:n}=uH(e,t);return new fg(r,n)}class fb extends lW{static [lP]="PgHalfVectorBuilder";constructor(e,t){super(e,"array","PgHalfVector"),this.config.dimensions=t.dimensions}build(e){return new fw(e,this.config)}}class fw extends lF{static [lP]="PgHalfVector";dimensions=this.config.dimensions;getSQLType(){return`halfvec(${this.dimensions})`}mapToDriverValue(e){return JSON.stringify(e)}mapFromDriverValue(e){return e.slice(1,-1).split(",").map(e=>Number.parseFloat(e))}}function fv(e,t){let{name:r,config:n}=uH(e,t);return new fb(r,n)}class fx extends lW{static [lP]="PgSparseVectorBuilder";constructor(e,t){super(e,"string","PgSparseVector"),this.config.dimensions=t.dimensions}build(e){return new f_(e,this.config)}}class f_ extends lF{static [lP]="PgSparseVector";dimensions=this.config.dimensions;getSQLType(){return`sparsevec(${this.dimensions})`}}function fS(e,t){let{name:r,config:n}=uH(e,t);return new fx(r,n)}class fk extends lW{static [lP]="PgVectorBuilder";constructor(e,t){super(e,"array","PgVector"),this.config.dimensions=t.dimensions}build(e){return new fE(e,this.config)}}class fE extends lF{static [lP]="PgVector";dimensions=this.config.dimensions;getSQLType(){return`vector(${this.dimensions})`}mapToDriverValue(e){return JSON.stringify(e)}mapFromDriverValue(e){return e.slice(1,-1).split(",").map(e=>Number.parseFloat(e))}}function fA(e,t){let{name:r,config:n}=uH(e,t);return new fk(r,n)}let fT=Symbol.for("drizzle:PgInlineForeignKeys"),fP=Symbol.for("drizzle:EnableRLS");class fC extends un{static [lP]="PgTable";static Symbol=Object.assign({},un.Symbol,{InlineForeignKeys:fT,EnableRLS:fP});[fT]=[];[fP]=!1;[un.Symbol.ExtraConfigBuilder]=void 0;[un.Symbol.ExtraConfigColumns]={}}let fO=(e,t,r)=>(function(e,t,r,n,i=e){let a=new fC(e,n,i),s="function"==typeof t?t({bigint:dc,bigserial:dh,boolean:dm,char:dw,cidr:d_,customType:dE,date:u9,doublePrecision:dP,inet:dN,integer:d$,interval:dL,json:uU,jsonb:uI,line:dq,macaddr:dF,macaddr8:dJ,numeric:uJ,point:dZ,geometry:d8,real:d7,serial:fr,smallint:fa,smallserial:fc,text:fd,time:uY,timestamp:u5,uuid:dt,varchar:fp,bit:fy,halfvec:fv,sparsevec:fS,vector:fA}):t,o=Object.fromEntries(Object.entries(s).map(([e,t])=>{t.setName(e);let r=t.build(a);return a[fT].push(...t.buildForeignKeys(r,a)),[e,r]})),c=Object.fromEntries(Object.entries(s).map(([e,t])=>(t.setName(e),[e,t.buildExtraConfigColumn(a)]))),l=Object.assign(a,o);return l[un.Symbol.Columns]=o,l[un.Symbol.ExtraConfigColumns]=c,r&&(l[fC.Symbol.ExtraConfigBuilder]=r),Object.assign(l,{enableRLS:()=>(l[fC.Symbol.EnableRLS]=!0,l)})})(e,t,r,void 0);class fN{static [lP]="PgPrimaryKeyBuilder";columns;name;constructor(e,t){this.columns=e,this.name=t}build(e){return new fR(e,this.columns,this.name)}}class fR{constructor(e,t,r){this.table=e,this.columns=t,this.name=r}static [lP]="PgPrimaryKey";columns;name;getName(){return this.name??`${this.table[fC.Symbol.Name]}_${this.columns.map(e=>e.name).join("_")}_pk`}}function fI(e,t){return"object"!=typeof t||null===t||!("mapToDriverValue"in t)||"function"!=typeof t.mapToDriverValue||us(e)||lC(e,uf)||lC(e,up)||lC(e,lI)||lC(e,un)||lC(e,uy)?e:new uf(e,t)}let f$=(e,t)=>uh`${e} = ${fI(t,e)}`,fj=(e,t)=>uh`${e} <> ${fI(t,e)}`;function fU(...e){let t=e.filter(e=>void 0!==e);return 0===t.length?void 0:new uc(1===t.length?t:[new uo("("),uh.join(t,new uo(" and ")),new uo(")")])}function fL(...e){let t=e.filter(e=>void 0!==e);return 0===t.length?void 0:new uc(1===t.length?t:[new uo("("),uh.join(t,new uo(" or ")),new uo(")")])}function fD(e){return uh`not ${e}`}let fM=(e,t)=>uh`${e} > ${fI(t,e)}`,fB=(e,t)=>uh`${e} >= ${fI(t,e)}`,fH=(e,t)=>uh`${e} < ${fI(t,e)}`,fq=(e,t)=>uh`${e} <= ${fI(t,e)}`;function fz(e,t){return Array.isArray(t)?0===t.length?uh`false`:uh`${e} in ${t.map(t=>fI(t,e))}`:uh`${e} in ${fI(t,e)}`}function fW(e,t){return Array.isArray(t)?0===t.length?uh`true`:uh`${e} not in ${t.map(t=>fI(t,e))}`:uh`${e} not in ${fI(t,e)}`}function fF(e){return uh`${e} is null`}function fK(e){return uh`${e} is not null`}function fV(e){return uh`exists ${e}`}function fJ(e){return uh`not exists ${e}`}function fQ(e,t,r){return uh`${e} between ${fI(t,e)} and ${fI(r,e)}`}function fG(e,t,r){return uh`${e} not between ${fI(t,e)} and ${fI(r,e)}`}function fX(e,t){return uh`${e} like ${t}`}function fY(e,t){return uh`${e} not like ${t}`}function fZ(e,t){return uh`${e} ilike ${t}`}function f0(e,t){return uh`${e} not ilike ${t}`}function f1(e){return uh`${e} asc`}function f2(e){return uh`${e} desc`}class f5{constructor(e,t,r){this.sourceTable=e,this.referencedTable=t,this.relationName=r,this.referencedTableName=t[un.Symbol.Name]}static [lP]="Relation";referencedTableName;fieldName}class f6{constructor(e,t){this.table=e,this.config=t}static [lP]="Relations"}class f3 extends f5{constructor(e,t,r,n){super(e,t,r?.relationName),this.config=r,this.isNullable=n}static [lP]="One";withFieldName(e){let t=new f3(this.sourceTable,this.referencedTable,this.config,this.isNullable);return t.fieldName=e,t}}class f8 extends f5{constructor(e,t,r){super(e,t,r?.relationName),this.config=r}static [lP]="Many";withFieldName(e){let t=new f8(this.sourceTable,this.referencedTable,this.config);return t.fieldName=e,t}}function f4(e){return{one:function(t,r){return new f3(e,t,r,r?.fields.reduce((e,t)=>e&&t.notNull,!0)??!1)},many:function(t,r){return new f8(e,t,r)}}}class f9 extends uy{static [lP]="PgViewBase"}class f7{static [lP]="PgDialect";casing;constructor(e){this.casing=new uP(e?.casing)}async migrate(e,t,r){let n="string"==typeof r?"__drizzle_migrations":r.migrationsTable??"__drizzle_migrations",i="string"==typeof r?"drizzle":r.migrationsSchema??"drizzle",a=uh`
			CREATE TABLE IF NOT EXISTS ${uh.identifier(i)}.${uh.identifier(n)} (
				id SERIAL PRIMARY KEY,
				hash text NOT NULL,
				created_at bigint
			)
		`;await t.execute(uh`CREATE SCHEMA IF NOT EXISTS ${uh.identifier(i)}`),await t.execute(a);let s=(await t.all(uh`select id, hash, created_at from ${uh.identifier(i)}.${uh.identifier(n)} order by created_at desc limit 1`))[0];await t.transaction(async t=>{for await(let r of e)if(!s||Number(s.created_at)<r.folderMillis){for(let e of r.sql)await t.execute(uh.raw(e));await t.execute(uh`insert into ${uh.identifier(i)}.${uh.identifier(n)} ("hash", "created_at") values(${r.hash}, ${r.folderMillis})`)}})}escapeName(e){return`"${e}"`}escapeParam(e){return`$${e+1}`}escapeString(e){return`'${e.replace(/'/g,"''")}'`}buildWithCTE(e){if(!e?.length)return;let t=[uh`with `];for(let[r,n]of e.entries())t.push(uh`${uh.identifier(n._.alias)} as (${n._.sql})`),r<e.length-1&&t.push(uh`, `);return t.push(uh` `),uh.join(t)}buildDeleteQuery({table:e,where:t,returning:r,withList:n}){let i=this.buildWithCTE(n),a=r?uh` returning ${this.buildSelection(r,{isSingleTable:!0})}`:void 0,s=t?uh` where ${t}`:void 0;return uh`${i}delete from ${e}${s}${a}`}buildUpdateSet(e,t){let r=e[un.Symbol.Columns],n=Object.keys(r).filter(e=>void 0!==t[e]||r[e]?.onUpdateFn!==void 0),i=n.length;return uh.join(n.flatMap((e,n)=>{let a=r[e],s=t[e]??uh.param(a.onUpdateFn(),a),o=uh`${uh.identifier(this.casing.getColumnCasing(a))} = ${s}`;return n<i-1?[o,uh.raw(", ")]:[o]}))}buildUpdateQuery({table:e,set:t,where:r,returning:n,withList:i,from:a,joins:s}){let o=this.buildWithCTE(i),c=e[fC.Symbol.Name],l=e[fC.Symbol.Schema],u=e[fC.Symbol.OriginalName],d=c===u?void 0:c,f=uh`${l?uh`${uh.identifier(l)}.`:void 0}${uh.identifier(u)}${d&&uh` ${uh.identifier(d)}`}`,h=this.buildUpdateSet(e,t),p=a&&uh.join([uh.raw(" from "),this.buildFromTable(a)]),g=this.buildJoins(s),m=n?uh` returning ${this.buildSelection(n,{isSingleTable:!a})}`:void 0,y=r?uh` where ${r}`:void 0;return uh`${o}update ${f} set ${h}${p}${g}${y}${m}`}buildSelection(e,{isSingleTable:t=!1}={}){let r=e.length,n=e.flatMap(({field:e},n)=>{let i=[];if(lC(e,uc.Aliased)&&e.isSelectionField)i.push(uh.identifier(e.fieldAlias));else if(lC(e,uc.Aliased)||lC(e,uc)){let r=lC(e,uc.Aliased)?e.sql:e;t?i.push(new uc(r.queryChunks.map(e=>lC(e,lF)?uh.identifier(this.casing.getColumnCasing(e)):e))):i.push(r),lC(e,uc.Aliased)&&i.push(uh` as ${uh.identifier(e.fieldAlias)}`)}else lC(e,lI)&&(t?i.push(uh.identifier(this.casing.getColumnCasing(e))):i.push(e));return n<r-1&&i.push(uh`, `),i});return uh.join(n)}buildJoins(e){if(!e||0===e.length)return;let t=[];for(let[r,n]of e.entries()){0===r&&t.push(uh` `);let i=n.table,a=n.lateral?uh` lateral`:void 0,s=n.on?uh` on ${n.on}`:void 0;if(lC(i,fC)){let e=i[fC.Symbol.Name],r=i[fC.Symbol.Schema],o=i[fC.Symbol.OriginalName],c=e===o?void 0:n.alias;t.push(uh`${uh.raw(n.joinType)} join${a} ${r?uh`${uh.identifier(r)}.`:void 0}${uh.identifier(o)}${c&&uh` ${uh.identifier(c)}`}${s}`)}else if(lC(i,uy)){let e=i[l6].name,r=i[l6].schema,o=i[l6].originalName,c=e===o?void 0:n.alias;t.push(uh`${uh.raw(n.joinType)} join${a} ${r?uh`${uh.identifier(r)}.`:void 0}${uh.identifier(o)}${c&&uh` ${uh.identifier(c)}`}${s}`)}else t.push(uh`${uh.raw(n.joinType)} join${a} ${i}${s}`);r<e.length-1&&t.push(uh` `)}return uh.join(t)}buildFromTable(e){if(lC(e,un)&&e[un.Symbol.IsAlias]){let t=uh`${uh.identifier(e[un.Symbol.OriginalName])}`;return e[un.Symbol.Schema]&&(t=uh`${uh.identifier(e[un.Symbol.Schema])}.${t}`),uh`${t} ${uh.identifier(e[un.Symbol.Name])}`}return e}buildSelectQuery({withList:e,fields:t,fieldsFlat:r,where:n,having:i,table:a,joins:s,orderBy:o,groupBy:c,limit:l,offset:u,lockingClause:d,distinct:f,setOperators:h}){let p,g,m;let y=r??uL(t);for(let e of y){let t;if(lC(e.field,lI)&&e.field.table[lj]!==(lC(a,l1)?a._.alias:lC(a,f9)?a[l6].name:lC(a,uc)?void 0:a[lj])&&(t=e.field.table,!s?.some(({alias:e})=>e===(t[un.Symbol.IsAlias]?t[lj]:t[un.Symbol.BaseName])))){let t=e.field.table[lj];throw Error(`Your "${e.path.join("->")}" field references a column "${t}"."${e.field.name}", but the table "${t}" is not part of the query! Did you forget to join it?`)}}let b=!s||0===s.length,w=this.buildWithCTE(e);f&&(p=!0===f?uh` distinct`:uh` distinct on (${uh.join(f.on,uh`, `)})`);let v=this.buildSelection(y,{isSingleTable:b}),x=this.buildFromTable(a),_=this.buildJoins(s),S=n?uh` where ${n}`:void 0,k=i?uh` having ${i}`:void 0;o&&o.length>0&&(g=uh` order by ${uh.join(o,uh`, `)}`),c&&c.length>0&&(m=uh` group by ${uh.join(c,uh`, `)}`);let E="object"==typeof l||"number"==typeof l&&l>=0?uh` limit ${l}`:void 0,A=u?uh` offset ${u}`:void 0,T=uh.empty();if(d){let e=uh` for ${uh.raw(d.strength)}`;d.config.of&&e.append(uh` of ${uh.join(Array.isArray(d.config.of)?d.config.of:[d.config.of],uh`, `)}`),d.config.noWait?e.append(uh` nowait`):d.config.skipLocked&&e.append(uh` skip locked`),T.append(e)}let P=uh`${w}select${p} ${v} from ${x}${_}${S}${m}${k}${g}${E}${A}${T}`;return h.length>0?this.buildSetOperations(P,h):P}buildSetOperations(e,t){let[r,...n]=t;if(!r)throw Error("Cannot pass undefined values to any set operator");return 0===n.length?this.buildSetOperationQuery({leftSelect:e,setOperator:r}):this.buildSetOperations(this.buildSetOperationQuery({leftSelect:e,setOperator:r}),n)}buildSetOperationQuery({leftSelect:e,setOperator:{type:t,isAll:r,rightSelect:n,limit:i,orderBy:a,offset:s}}){let o;let c=uh`(${e.getSQL()}) `,l=uh`(${n.getSQL()})`;if(a&&a.length>0){let e=[];for(let t of a)if(lC(t,lF))e.push(uh.identifier(t.name));else if(lC(t,uc)){for(let e=0;e<t.queryChunks.length;e++){let r=t.queryChunks[e];lC(r,lF)&&(t.queryChunks[e]=uh.identifier(r.name))}e.push(uh`${t}`)}else e.push(uh`${t}`);o=uh` order by ${uh.join(e,uh`, `)} `}let u="object"==typeof i||"number"==typeof i&&i>=0?uh` limit ${i}`:void 0,d=uh.raw(`${t} ${r?"all ":""}`),f=s?uh` offset ${s}`:void 0;return uh`${c}${d}${l}${o}${u}${f}`}buildInsertQuery({table:e,values:t,onConflict:r,returning:n,withList:i,select:a,overridingSystemValue_:s}){let o=[],c=Object.entries(e[un.Symbol.Columns]).filter(([e,t])=>!t.shouldDisableInsert()),l=c.map(([,e])=>uh.identifier(this.casing.getColumnCasing(e)));if(a)lC(t,uc)?o.push(t):o.push(t.getSQL());else for(let[e,r]of(o.push(uh.raw("values ")),t.entries())){let n=[];for(let[e,t]of c){let i=r[e];if(void 0===i||lC(i,uf)&&void 0===i.value){if(void 0!==t.defaultFn){let e=t.defaultFn(),r=lC(e,uc)?e:uh.param(e,t);n.push(r)}else if(t.default||void 0===t.onUpdateFn)n.push(uh`default`);else{let e=t.onUpdateFn(),r=lC(e,uc)?e:uh.param(e,t);n.push(r)}}else n.push(i)}o.push(n),e<t.length-1&&o.push(uh`, `)}let u=this.buildWithCTE(i),d=uh.join(o),f=n?uh` returning ${this.buildSelection(n,{isSingleTable:!0})}`:void 0,h=r?uh` on conflict ${r}`:void 0,p=!0===s?uh`overriding system value `:void 0;return uh`${u}insert into ${e} ${l} ${p}${d}${h}${f}`}buildRefreshMaterializedViewQuery({view:e,concurrently:t,withNoData:r}){let n=t?uh` concurrently`:void 0,i=r?uh` with no data`:void 0;return uh`refresh materialized view${n} ${e}${i}`}prepareTyping(e){return lC(e,uR)||lC(e,uj)?"json":lC(e,uz)?"decimal":lC(e,uX)?"time":lC(e,u0)||lC(e,u2)?"timestamp":lC(e,u3)||lC(e,u4)?"date":lC(e,de)?"uuid":"none"}sqlToQuery(e,t){return e.toQuery({casing:this.casing,escapeName:this.escapeName,escapeParam:this.escapeParam,escapeString:this.escapeString,prepareTyping:this.prepareTyping,invokeSource:t})}buildRelationalQueryWithoutPK({fullSchema:e,schema:t,tableNamesMap:r,table:n,tableConfig:i,queryConfig:a,tableAlias:s,nestedQueryRelation:o,joinOn:c}){let l,u=[],d,f,h=[],p,g=[];if(!0===a)u=Object.entries(i.columns).map(([e,t])=>({dbKey:t.name,tsKey:e,field:u_(t,s),relationTableTsKey:void 0,isJson:!1,selection:[]}));else{let n=Object.fromEntries(Object.entries(i.columns).map(([e,t])=>[e,u_(t,s)]));if(a.where){let e="function"==typeof a.where?a.where(n,{and:fU,between:fQ,eq:f$,exists:fV,gt:fM,gte:fB,ilike:fZ,inArray:fz,isNull:fF,isNotNull:fK,like:fX,lt:fH,lte:fq,ne:fj,not:fD,notBetween:fG,notExists:fJ,notLike:fY,notIlike:f0,notInArray:fW,or:fL,sql:uh}):a.where;p=e&&uk(e,s)}let o=[],c=[];if(a.columns){let e=!1;for(let[t,r]of Object.entries(a.columns))void 0!==r&&t in i.columns&&(e||!0!==r||(e=!0),c.push(t));c.length>0&&(c=e?c.filter(e=>a.columns?.[e]===!0):Object.keys(i.columns).filter(e=>!c.includes(e)))}else c=Object.keys(i.columns);for(let e of c){let t=i.columns[e];o.push({tsKey:e,value:t})}let l=[];if(a.with&&(l=Object.entries(a.with).filter(e=>!!e[1]).map(([e,t])=>({tsKey:e,queryConfig:t,relation:i.relations[e]}))),a.extras)for(let[e,t]of Object.entries("function"==typeof a.extras?a.extras(n,{sql:uh}):a.extras))o.push({tsKey:e,value:uS(t,s)});for(let{tsKey:e,value:t}of o)u.push({dbKey:lC(t,uc.Aliased)?t.fieldAlias:i.columns[e].name,tsKey:e,field:lC(t,lI)?u_(t,s):t,relationTableTsKey:void 0,isJson:!1,selection:[]});let m="function"==typeof a.orderBy?a.orderBy(n,{sql:uh,asc:f1,desc:f2}):a.orderBy??[];for(let{tsKey:n,queryConfig:i,relation:o}of(Array.isArray(m)||(m=[m]),h=m.map(e=>lC(e,lI)?u_(e,s):uk(e,s)),d=a.limit,f=a.offset,l)){let a=function(e,t,r){if(lC(r,f3)&&r.config)return{fields:r.config.fields,references:r.config.references};let n=t[ui(r.referencedTable)];if(!n)throw Error(`Table "${r.referencedTable[un.Symbol.Name]}" not found in schema`);let i=e[n];if(!i)throw Error(`Table "${n}" not found in schema`);let a=r.sourceTable,s=t[ui(a)];if(!s)throw Error(`Table "${a[un.Symbol.Name]}" not found in schema`);let o=[];for(let e of Object.values(i.relations))(r.relationName&&r!==e&&e.relationName===r.relationName||!r.relationName&&e.referencedTable===r.sourceTable)&&o.push(e);if(o.length>1)throw r.relationName?Error(`There are multiple relations with name "${r.relationName}" in table "${n}"`):Error(`There are multiple relations between "${n}" and "${r.sourceTable[un.Symbol.Name]}". Please specify relation name`);if(o[0]&&lC(o[0],f3)&&o[0].config)return{fields:o[0].config.references,references:o[0].config.fields};throw Error(`There is not enough information to infer relation "${s}.${r.fieldName}"`)}(t,r,o),c=r[ui(o.referencedTable)],l=`${s}_${n}`,d=fU(...a.fields.map((e,t)=>f$(u_(a.references[t],l),u_(e,s)))),f=this.buildRelationalQueryWithoutPK({fullSchema:e,schema:t,tableNamesMap:r,table:e[c],tableConfig:t[c],queryConfig:lC(o,f3)?!0===i?{limit:1}:{...i,limit:1}:i,tableAlias:l,joinOn:d,nestedQueryRelation:o}),h=uh`${uh.identifier(l)}.${uh.identifier("data")}`.as(n);g.push({on:uh`true`,table:new l1(f.sql,{},l),alias:l,joinType:"left",lateral:!0}),u.push({dbKey:n,tsKey:n,field:h,relationTableTsKey:c,isJson:!0,selection:f.selection})}}if(0===u.length)throw new uC({message:`No fields selected for table "${i.tsName}" ("${s}")`});if(p=fU(c,p),o){let e=uh`json_build_array(${uh.join(u.map(({field:e,tsKey:t,isJson:r})=>r?uh`${uh.identifier(`${s}_${t}`)}.${uh.identifier("data")}`:lC(e,uc.Aliased)?e.sql:e),uh`, `)})`;lC(o,f8)&&(e=uh`coalesce(json_agg(${e}${h.length>0?uh` order by ${uh.join(h,uh`, `)}`:void 0}), '[]'::json)`);let t=[{dbKey:"data",tsKey:"data",field:e.as("data"),isJson:!0,relationTableTsKey:i.tsName,selection:u}];void 0!==d||void 0!==f||h.length>0?(l=this.buildSelectQuery({table:ux(n,s),fields:{},fieldsFlat:[{path:[],field:uh.raw("*")}],where:p,limit:d,offset:f,orderBy:h,setOperators:[]}),p=void 0,d=void 0,f=void 0,h=[]):l=ux(n,s),l=this.buildSelectQuery({table:lC(l,fC)?l:new l1(l,{},s),fields:{},fieldsFlat:t.map(({field:e})=>({path:[],field:lC(e,lI)?u_(e,s):e})),joins:g,where:p,limit:d,offset:f,orderBy:h,setOperators:[]})}else l=this.buildSelectQuery({table:ux(n,s),fields:{},fieldsFlat:u.map(({field:e})=>({path:[],field:lC(e,lI)?u_(e,s):e})),joins:g,where:p,limit:d,offset:f,orderBy:h,setOperators:[]});return{tableTsKey:i.tsName,sql:l,selection:u}}}class he{static [lP]="SelectionProxyHandler";config;constructor(e){this.config={...e}}get(e,t){if("_"===t)return{...e._,selectedFields:new Proxy(e._.selectedFields,this)};if(t===l6)return{...e[l6],selectedFields:new Proxy(e[l6].selectedFields,this)};if("symbol"==typeof t)return e[t];let r=(lC(e,l1)?e._.selectedFields:lC(e,uy)?e[l6].selectedFields:e)[t];if(lC(r,uc.Aliased)){if("sql"===this.config.sqlAliasedBehavior&&!r.isSelectionField)return r.sql;let e=r.clone();return e.isSelectionField=!0,e}if(lC(r,uc)){if("sql"===this.config.sqlBehavior)return r;throw Error(`You tried to reference "${t}" field from a subquery, which is a raw SQL field, but it doesn't have an alias declared. Please add an alias to the field using ".as('alias')" method.`)}return lC(r,lI)?this.config.alias?new Proxy(r,new ub(new Proxy(r.table,new uw(this.config.alias,this.config.replaceOriginalName??!1)))):r:"object"!=typeof r||null===r?r:new Proxy(r,new he(this.config))}}class ht{static [lP]="TypedQueryBuilder";getSelectedFields(){return this._.selectedFields}}class hr{static [lP]="QueryPromise";[Symbol.toStringTag]="QueryPromise";catch(e){return this.then(void 0,e)}finally(e){return this.then(t=>(e?.(),t),t=>{throw e?.(),t})}then(e,t){return this.execute().then(e,t)}}class hn{static [lP]="PgSelectBuilder";fields;session;dialect;withList=[];distinct;constructor(e){this.fields=e.fields,this.session=e.session,this.dialect=e.dialect,e.withList&&(this.withList=e.withList),this.distinct=e.distinct}authToken;setToken(e){return this.authToken=e,this}from(e){let t;let r=!!this.fields;return t=this.fields?this.fields:lC(e,l1)?Object.fromEntries(Object.keys(e._.selectedFields).map(t=>[t,e[t]])):lC(e,f9)?e[l6].selectedFields:lC(e,uc)?{}:e[un.Symbol.Columns],new ha({table:e,fields:t,isPartialSelect:r,session:this.session,dialect:this.dialect,withList:this.withList,distinct:this.distinct}).setToken(this.authToken)}}class hi extends ht{static [lP]="PgSelectQueryBuilder";_;config;joinsNotNullableMap;tableName;isPartialSelect;session;dialect;constructor({table:e,fields:t,isPartialSelect:r,session:n,dialect:i,withList:a,distinct:s}){super(),this.config={withList:a,table:e,fields:{...t},distinct:s,setOperators:[]},this.isPartialSelect=r,this.session=n,this.dialect=i,this._={selectedFields:t},this.tableName=uB(e),this.joinsNotNullableMap="string"==typeof this.tableName?{[this.tableName]:!0}:{}}createJoin(e,t){return(r,n)=>{let i=this.tableName,a=uB(r);if("string"==typeof a&&this.config.joins?.some(e=>e.alias===a))throw Error(`Alias "${a}" is already used in this query`);if(!this.isPartialSelect&&(1===Object.keys(this.joinsNotNullableMap).length&&"string"==typeof i&&(this.config.fields={[i]:this.config.fields}),"string"==typeof a&&!lC(r,uc))){let e=lC(r,l1)?r._.selectedFields:lC(r,uy)?r[l6].selectedFields:r[un.Symbol.Columns];this.config.fields[a]=e}if("function"==typeof n&&(n=n(new Proxy(this.config.fields,new he({sqlAliasedBehavior:"sql",sqlBehavior:"sql"})))),this.config.joins||(this.config.joins=[]),this.config.joins.push({on:n,table:r,joinType:e,alias:a,lateral:t}),"string"==typeof a)switch(e){case"left":this.joinsNotNullableMap[a]=!1;break;case"right":this.joinsNotNullableMap=Object.fromEntries(Object.entries(this.joinsNotNullableMap).map(([e])=>[e,!1])),this.joinsNotNullableMap[a]=!0;break;case"cross":case"inner":this.joinsNotNullableMap[a]=!0;break;case"full":this.joinsNotNullableMap=Object.fromEntries(Object.entries(this.joinsNotNullableMap).map(([e])=>[e,!1])),this.joinsNotNullableMap[a]=!1}return this}}leftJoin=this.createJoin("left",!1);leftJoinLateral=this.createJoin("left",!0);rightJoin=this.createJoin("right",!1);innerJoin=this.createJoin("inner",!1);innerJoinLateral=this.createJoin("inner",!0);fullJoin=this.createJoin("full",!1);crossJoin=this.createJoin("cross",!1);crossJoinLateral=this.createJoin("cross",!0);createSetOperator(e,t){return r=>{let n="function"==typeof r?r(ho()):r;if(!uD(this.getSelectedFields(),n.getSelectedFields()))throw Error("Set operator error (union / intersect / except): selected fields are not the same or are in a different order");return this.config.setOperators.push({type:e,isAll:t,rightSelect:n}),this}}union=this.createSetOperator("union",!1);unionAll=this.createSetOperator("union",!0);intersect=this.createSetOperator("intersect",!1);intersectAll=this.createSetOperator("intersect",!0);except=this.createSetOperator("except",!1);exceptAll=this.createSetOperator("except",!0);addSetOperators(e){return this.config.setOperators.push(...e),this}where(e){return"function"==typeof e&&(e=e(new Proxy(this.config.fields,new he({sqlAliasedBehavior:"sql",sqlBehavior:"sql"})))),this.config.where=e,this}having(e){return"function"==typeof e&&(e=e(new Proxy(this.config.fields,new he({sqlAliasedBehavior:"sql",sqlBehavior:"sql"})))),this.config.having=e,this}groupBy(...e){if("function"==typeof e[0]){let t=e[0](new Proxy(this.config.fields,new he({sqlAliasedBehavior:"alias",sqlBehavior:"sql"})));this.config.groupBy=Array.isArray(t)?t:[t]}else this.config.groupBy=e;return this}orderBy(...e){if("function"==typeof e[0]){let t=e[0](new Proxy(this.config.fields,new he({sqlAliasedBehavior:"alias",sqlBehavior:"sql"}))),r=Array.isArray(t)?t:[t];this.config.setOperators.length>0?this.config.setOperators.at(-1).orderBy=r:this.config.orderBy=r}else this.config.setOperators.length>0?this.config.setOperators.at(-1).orderBy=e:this.config.orderBy=e;return this}limit(e){return this.config.setOperators.length>0?this.config.setOperators.at(-1).limit=e:this.config.limit=e,this}offset(e){return this.config.setOperators.length>0?this.config.setOperators.at(-1).offset=e:this.config.offset=e,this}for(e,t={}){return this.config.lockingClause={strength:e,config:t},this}getSQL(){return this.dialect.buildSelectQuery(this.config)}toSQL(){let{typings:e,...t}=this.dialect.sqlToQuery(this.getSQL());return t}as(e){return new Proxy(new l1(this.getSQL(),this.config.fields,e),new he({alias:e,sqlAliasedBehavior:"alias",sqlBehavior:"error"}))}getSelectedFields(){return new Proxy(this.config.fields,new he({alias:this.tableName,sqlAliasedBehavior:"alias",sqlBehavior:"error"}))}$dynamic(){return this}}class ha extends hi{static [lP]="PgSelect";_prepare(e){let{session:t,config:r,dialect:n,joinsNotNullableMap:i,authToken:a}=this;if(!t)throw Error("Cannot execute a query on a query builder. Please use a database instance instead.");return l5.startActiveSpan("drizzle.prepareQuery",()=>{let s=uL(r.fields),o=t.prepareQuery(n.sqlToQuery(this.getSQL()),s,e,!0);return o.joinsNotNullableMap=i,o.setToken(a)})}prepare(e){return this._prepare(e)}authToken;setToken(e){return this.authToken=e,this}execute=e=>l5.startActiveSpan("drizzle.operation",()=>this._prepare().execute(e,this.authToken))}function hs(e,t){return(r,n,...i)=>{let a=[n,...i].map(r=>({type:e,isAll:t,rightSelect:r}));for(let e of a)if(!uD(r.getSelectedFields(),e.rightSelect.getSelectedFields()))throw Error("Set operator error (union / intersect / except): selected fields are not the same or are in a different order");return r.addSetOperators(a)}}!function(e,t){for(let r of t)for(let t of Object.getOwnPropertyNames(r.prototype))"constructor"!==t&&Object.defineProperty(e.prototype,t,Object.getOwnPropertyDescriptor(r.prototype,t)||Object.create(null))}(ha,[hr]);let ho=()=>({union:hc,unionAll:hl,intersect:hu,intersectAll:hd,except:hf,exceptAll:hh}),hc=hs("union",!1),hl=hs("union",!0),hu=hs("intersect",!1),hd=hs("intersect",!0),hf=hs("except",!1),hh=hs("except",!0);class hp{static [lP]="PgQueryBuilder";dialect;dialectConfig;constructor(e){this.dialect=lC(e,f7)?e:void 0,this.dialectConfig=lC(e,f7)?void 0:e}$with=(e,t)=>{let r=this;return{as:n=>("function"==typeof n&&(n=n(r)),new Proxy(new l2(n.getSQL(),t??("getSelectedFields"in n?n.getSelectedFields()??{}:{}),e,!0),new he({alias:e,sqlAliasedBehavior:"alias",sqlBehavior:"error"})))}};with(...e){let t=this;return{select:function(r){return new hn({fields:r??void 0,session:void 0,dialect:t.getDialect(),withList:e})},selectDistinct:function(e){return new hn({fields:e??void 0,session:void 0,dialect:t.getDialect(),distinct:!0})},selectDistinctOn:function(e,r){return new hn({fields:r??void 0,session:void 0,dialect:t.getDialect(),distinct:{on:e}})}}}select(e){return new hn({fields:e??void 0,session:void 0,dialect:this.getDialect()})}selectDistinct(e){return new hn({fields:e??void 0,session:void 0,dialect:this.getDialect(),distinct:!0})}selectDistinctOn(e,t){return new hn({fields:t??void 0,session:void 0,dialect:this.getDialect(),distinct:{on:e}})}getDialect(){return this.dialect||(this.dialect=new f7(this.dialectConfig)),this.dialect}}class hg{constructor(e,t,r,n){this.table=e,this.session=t,this.dialect=r,this.withList=n}static [lP]="PgUpdateBuilder";authToken;setToken(e){return this.authToken=e,this}set(e){return new hm(this.table,uM(this.table,e),this.session,this.dialect,this.withList).setToken(this.authToken)}}class hm extends hr{constructor(e,t,r,n,i){super(),this.session=r,this.dialect=n,this.config={set:t,table:e,withList:i,joins:[]},this.tableName=uB(e),this.joinsNotNullableMap="string"==typeof this.tableName?{[this.tableName]:!0}:{}}static [lP]="PgUpdate";config;tableName;joinsNotNullableMap;from(e){let t=uB(e);return"string"==typeof t&&(this.joinsNotNullableMap[t]=!0),this.config.from=e,this}getTableLikeFields(e){return lC(e,fC)?e[un.Symbol.Columns]:lC(e,l1)?e._.selectedFields:e[l6].selectedFields}createJoin(e){return(t,r)=>{let n=uB(t);if("string"==typeof n&&this.config.joins.some(e=>e.alias===n))throw Error(`Alias "${n}" is already used in this query`);if("function"==typeof r){let e=this.config.from&&!lC(this.config.from,uc)?this.getTableLikeFields(this.config.from):void 0;r=r(new Proxy(this.config.table[un.Symbol.Columns],new he({sqlAliasedBehavior:"sql",sqlBehavior:"sql"})),e&&new Proxy(e,new he({sqlAliasedBehavior:"sql",sqlBehavior:"sql"})))}if(this.config.joins.push({on:r,table:t,joinType:e,alias:n}),"string"==typeof n)switch(e){case"left":this.joinsNotNullableMap[n]=!1;break;case"right":this.joinsNotNullableMap=Object.fromEntries(Object.entries(this.joinsNotNullableMap).map(([e])=>[e,!1])),this.joinsNotNullableMap[n]=!0;break;case"inner":this.joinsNotNullableMap[n]=!0;break;case"full":this.joinsNotNullableMap=Object.fromEntries(Object.entries(this.joinsNotNullableMap).map(([e])=>[e,!1])),this.joinsNotNullableMap[n]=!1}return this}}leftJoin=this.createJoin("left");rightJoin=this.createJoin("right");innerJoin=this.createJoin("inner");fullJoin=this.createJoin("full");where(e){return this.config.where=e,this}returning(e){if(!e&&(e=Object.assign({},this.config.table[un.Symbol.Columns]),this.config.from)){let t=uB(this.config.from);if("string"==typeof t&&this.config.from&&!lC(this.config.from,uc)){let r=this.getTableLikeFields(this.config.from);e[t]=r}for(let t of this.config.joins){let r=uB(t.table);if("string"==typeof r&&!lC(t.table,uc)){let n=this.getTableLikeFields(t.table);e[r]=n}}}return this.config.returningFields=e,this.config.returning=uL(e),this}getSQL(){return this.dialect.buildUpdateQuery(this.config)}toSQL(){let{typings:e,...t}=this.dialect.sqlToQuery(this.getSQL());return t}_prepare(e){let t=this.session.prepareQuery(this.dialect.sqlToQuery(this.getSQL()),this.config.returning,e,!0);return t.joinsNotNullableMap=this.joinsNotNullableMap,t}prepare(e){return this._prepare(e)}authToken;setToken(e){return this.authToken=e,this}execute=e=>this._prepare().execute(e,this.authToken);getSelectedFields(){return this.config.returningFields?new Proxy(this.config.returningFields,new he({alias:this.config.table[lj],sqlAliasedBehavior:"alias",sqlBehavior:"error"})):void 0}$dynamic(){return this}}class hy{constructor(e,t,r,n,i){this.table=e,this.session=t,this.dialect=r,this.withList=n,this.overridingSystemValue_=i}static [lP]="PgInsertBuilder";authToken;setToken(e){return this.authToken=e,this}overridingSystemValue(){return this.overridingSystemValue_=!0,this}values(e){if(0===(e=Array.isArray(e)?e:[e]).length)throw Error("values() must be called with at least one value");let t=e.map(e=>{let t={},r=this.table[un.Symbol.Columns];for(let n of Object.keys(e)){let i=e[n];t[n]=lC(i,uc)?i:new uf(i,r[n])}return t});return new hb(this.table,t,this.session,this.dialect,this.withList,!1,this.overridingSystemValue_).setToken(this.authToken)}select(e){let t="function"==typeof e?e(new hp):e;if(!lC(t,uc)&&!uD(this.table[l8],t._.selectedFields))throw Error("Insert select error: selected fields are not the same or are in a different order compared to the table definition");return new hb(this.table,t,this.session,this.dialect,this.withList,!0)}}class hb extends hr{constructor(e,t,r,n,i,a,s){super(),this.session=r,this.dialect=n,this.config={table:e,values:t,withList:i,select:a,overridingSystemValue_:s}}static [lP]="PgInsert";config;returning(e=this.config.table[un.Symbol.Columns]){return this.config.returningFields=e,this.config.returning=uL(e),this}onConflictDoNothing(e={}){if(void 0===e.target)this.config.onConflict=uh`do nothing`;else{let t="";t=Array.isArray(e.target)?e.target.map(e=>this.dialect.escapeName(this.dialect.casing.getColumnCasing(e))).join(","):this.dialect.escapeName(this.dialect.casing.getColumnCasing(e.target));let r=e.where?uh` where ${e.where}`:void 0;this.config.onConflict=uh`(${uh.raw(t)})${r} do nothing`}return this}onConflictDoUpdate(e){if(e.where&&(e.targetWhere||e.setWhere))throw Error('You cannot use both "where" and "targetWhere"/"setWhere" at the same time - "where" is deprecated, use "targetWhere" or "setWhere" instead.');let t=e.where?uh` where ${e.where}`:void 0,r=e.targetWhere?uh` where ${e.targetWhere}`:void 0,n=e.setWhere?uh` where ${e.setWhere}`:void 0,i=this.dialect.buildUpdateSet(this.config.table,uM(this.config.table,e.set)),a="";return a=Array.isArray(e.target)?e.target.map(e=>this.dialect.escapeName(this.dialect.casing.getColumnCasing(e))).join(","):this.dialect.escapeName(this.dialect.casing.getColumnCasing(e.target)),this.config.onConflict=uh`(${uh.raw(a)})${r} do update set ${i}${t}${n}`,this}getSQL(){return this.dialect.buildInsertQuery(this.config)}toSQL(){let{typings:e,...t}=this.dialect.sqlToQuery(this.getSQL());return t}_prepare(e){return l5.startActiveSpan("drizzle.prepareQuery",()=>this.session.prepareQuery(this.dialect.sqlToQuery(this.getSQL()),this.config.returning,e,!0))}prepare(e){return this._prepare(e)}authToken;setToken(e){return this.authToken=e,this}execute=e=>l5.startActiveSpan("drizzle.operation",()=>this._prepare().execute(e,this.authToken));getSelectedFields(){return this.config.returningFields?new Proxy(this.config.returningFields,new he({alias:this.config.table[lj],sqlAliasedBehavior:"alias",sqlBehavior:"error"})):void 0}$dynamic(){return this}}class hw extends hr{constructor(e,t,r,n){super(),this.session=t,this.dialect=r,this.config={table:e,withList:n}}static [lP]="PgDelete";config;where(e){return this.config.where=e,this}returning(e=this.config.table[un.Symbol.Columns]){return this.config.returningFields=e,this.config.returning=uL(e),this}getSQL(){return this.dialect.buildDeleteQuery(this.config)}toSQL(){let{typings:e,...t}=this.dialect.sqlToQuery(this.getSQL());return t}_prepare(e){return l5.startActiveSpan("drizzle.prepareQuery",()=>this.session.prepareQuery(this.dialect.sqlToQuery(this.getSQL()),this.config.returning,e,!0))}prepare(e){return this._prepare(e)}authToken;setToken(e){return this.authToken=e,this}execute=e=>l5.startActiveSpan("drizzle.operation",()=>this._prepare().execute(e,this.authToken));getSelectedFields(){return this.config.returningFields?new Proxy(this.config.returningFields,new he({alias:this.config.table[lj],sqlAliasedBehavior:"alias",sqlBehavior:"error"})):void 0}$dynamic(){return this}}class hv extends uc{constructor(e){super(hv.buildEmbeddedCount(e.source,e.filters).queryChunks),this.params=e,this.mapWith(Number),this.session=e.session,this.sql=hv.buildCount(e.source,e.filters)}sql;token;static [lP]="PgCountBuilder";[Symbol.toStringTag]="PgCountBuilder";session;static buildEmbeddedCount(e,t){return uh`(select count(*) from ${e}${uh.raw(" where ").if(t)}${t})`}static buildCount(e,t){return uh`select count(*) as count from ${e}${uh.raw(" where ").if(t)}${t};`}setToken(e){return this.token=e,this}then(e,t){return Promise.resolve(this.session.count(this.sql,this.token)).then(e,t)}catch(e){return this.then(void 0,e)}finally(e){return this.then(t=>(e?.(),t),t=>{throw e?.(),t})}}class hx{constructor(e,t,r,n,i,a,s){this.fullSchema=e,this.schema=t,this.tableNamesMap=r,this.table=n,this.tableConfig=i,this.dialect=a,this.session=s}static [lP]="PgRelationalQueryBuilder";findMany(e){return new h_(this.fullSchema,this.schema,this.tableNamesMap,this.table,this.tableConfig,this.dialect,this.session,e||{},"many")}findFirst(e){return new h_(this.fullSchema,this.schema,this.tableNamesMap,this.table,this.tableConfig,this.dialect,this.session,e?{...e,limit:1}:{limit:1},"first")}}class h_ extends hr{constructor(e,t,r,n,i,a,s,o,c){super(),this.fullSchema=e,this.schema=t,this.tableNamesMap=r,this.table=n,this.tableConfig=i,this.dialect=a,this.session=s,this.config=o,this.mode=c}static [lP]="PgRelationalQuery";_prepare(e){return l5.startActiveSpan("drizzle.prepareQuery",()=>{let{query:t,builtQuery:r}=this._toSQL();return this.session.prepareQuery(r,void 0,e,!0,(e,r)=>{let n=e.map(e=>(function e(t,r,n,i,a=e=>e){let s={};for(let[o,c]of i.entries())if(c.isJson){let i=r.relations[c.tsKey],l=n[o],u="string"==typeof l?JSON.parse(l):l;s[c.tsKey]=lC(i,f3)?u&&e(t,t[c.relationTableTsKey],u,c.selection,a):u.map(r=>e(t,t[c.relationTableTsKey],r,c.selection,a))}else{let e;let t=a(n[o]),r=c.field;e=lC(r,lI)?r:lC(r,uc)?r.decoder:r.sql.decoder,s[c.tsKey]=null===t?null:e.mapFromDriverValue(t)}return s})(this.schema,this.tableConfig,e,t.selection,r));return"first"===this.mode?n[0]:n})})}prepare(e){return this._prepare(e)}_getQuery(){return this.dialect.buildRelationalQueryWithoutPK({fullSchema:this.fullSchema,schema:this.schema,tableNamesMap:this.tableNamesMap,table:this.table,tableConfig:this.tableConfig,queryConfig:this.config,tableAlias:this.tableConfig.tsName})}getSQL(){return this._getQuery().sql}_toSQL(){let e=this._getQuery(),t=this.dialect.sqlToQuery(e.sql);return{query:e,builtQuery:t}}toSQL(){return this._toSQL().builtQuery}authToken;setToken(e){return this.authToken=e,this}execute(){return l5.startActiveSpan("drizzle.operation",()=>this._prepare().execute(void 0,this.authToken))}}class hS extends hr{constructor(e,t,r,n){super(),this.execute=e,this.sql=t,this.query=r,this.mapBatchResult=n}static [lP]="PgRaw";getSQL(){return this.sql}getQuery(){return this.query}mapResult(e,t){return t?this.mapBatchResult(e):e}_prepare(){return this}isResponseInArrayMode(){return!1}}class hk extends hr{constructor(e,t,r){super(),this.session=t,this.dialect=r,this.config={view:e}}static [lP]="PgRefreshMaterializedView";config;concurrently(){if(void 0!==this.config.withNoData)throw Error("Cannot use concurrently and withNoData together");return this.config.concurrently=!0,this}withNoData(){if(void 0!==this.config.concurrently)throw Error("Cannot use concurrently and withNoData together");return this.config.withNoData=!0,this}getSQL(){return this.dialect.buildRefreshMaterializedViewQuery(this.config)}toSQL(){let{typings:e,...t}=this.dialect.sqlToQuery(this.getSQL());return t}_prepare(e){return l5.startActiveSpan("drizzle.prepareQuery",()=>this.session.prepareQuery(this.dialect.sqlToQuery(this.getSQL()),void 0,e,!0))}prepare(e){return this._prepare(e)}authToken;setToken(e){return this.authToken=e,this}execute=e=>l5.startActiveSpan("drizzle.operation",()=>this._prepare().execute(e,this.authToken))}class hE{constructor(e,t,r){if(this.dialect=e,this.session=t,this._=r?{schema:r.schema,fullSchema:r.fullSchema,tableNamesMap:r.tableNamesMap,session:t}:{schema:void 0,fullSchema:{},tableNamesMap:{},session:t},this.query={},this._.schema)for(let[n,i]of Object.entries(this._.schema))this.query[n]=new hx(r.fullSchema,this._.schema,this._.tableNamesMap,r.fullSchema[n],i,e,t)}static [lP]="PgDatabase";query;$with=(e,t)=>{let r=this;return{as:n=>("function"==typeof n&&(n=n(new hp(r.dialect))),new Proxy(new l2(n.getSQL(),t??("getSelectedFields"in n?n.getSelectedFields()??{}:{}),e,!0),new he({alias:e,sqlAliasedBehavior:"alias",sqlBehavior:"error"})))}};$count(e,t){return new hv({source:e,filters:t,session:this.session})}with(...e){let t=this;return{select:function(r){return new hn({fields:r??void 0,session:t.session,dialect:t.dialect,withList:e})},selectDistinct:function(r){return new hn({fields:r??void 0,session:t.session,dialect:t.dialect,withList:e,distinct:!0})},selectDistinctOn:function(r,n){return new hn({fields:n??void 0,session:t.session,dialect:t.dialect,withList:e,distinct:{on:r}})},update:function(r){return new hg(r,t.session,t.dialect,e)},insert:function(r){return new hy(r,t.session,t.dialect,e)},delete:function(r){return new hw(r,t.session,t.dialect,e)}}}select(e){return new hn({fields:e??void 0,session:this.session,dialect:this.dialect})}selectDistinct(e){return new hn({fields:e??void 0,session:this.session,dialect:this.dialect,distinct:!0})}selectDistinctOn(e,t){return new hn({fields:t??void 0,session:this.session,dialect:this.dialect,distinct:{on:e}})}update(e){return new hg(e,this.session,this.dialect)}insert(e){return new hy(e,this.session,this.dialect)}delete(e){return new hw(e,this.session,this.dialect)}refreshMaterializedView(e){return new hk(e,this.session,this.dialect)}authToken;execute(e){let t="string"==typeof e?uh.raw(e):e.getSQL(),r=this.dialect.sqlToQuery(t),n=this.session.prepareQuery(r,void 0,void 0,!1);return new hS(()=>n.execute(void 0,this.authToken),t,r,e=>n.mapResult(e,!0))}transaction(e,t){return this.session.transaction(e,t)}}class hA{constructor(e){this.query=e}authToken;getQuery(){return this.query}mapResult(e,t){return e}setToken(e){return this.authToken=e,this}static [lP]="PgPreparedQuery";joinsNotNullableMap}class hT{constructor(e){this.dialect=e}static [lP]="PgSession";execute(e,t){return l5.startActiveSpan("drizzle.operation",()=>l5.startActiveSpan("drizzle.prepareQuery",()=>this.prepareQuery(this.dialect.sqlToQuery(e),void 0,void 0,!1)).setToken(t).execute(void 0,t))}all(e){return this.prepareQuery(this.dialect.sqlToQuery(e),void 0,void 0,!1).all()}async count(e,t){return Number((await this.execute(e,t))[0].count)}}class hP extends hE{constructor(e,t,r,n=0){super(e,t,r),this.schema=r,this.nestedIndex=n}static [lP]="PgTransaction";rollback(){throw new uO}getTransactionConfigSQL(e){let t=[];return e.isolationLevel&&t.push(`isolation level ${e.isolationLevel}`),e.accessMode&&t.push(e.accessMode),"boolean"==typeof e.deferrable&&t.push(e.deferrable?"deferrable":"not deferrable"),uh.raw(t.join(" "))}setTransaction(e){return this.session.execute(uh`set transaction ${this.getTransactionConfigSQL(e)}`)}}class hC extends hA{constructor(e,t,r,n,i,a,s){super({sql:t,params:r}),this.client=e,this.queryString=t,this.params=r,this.logger=n,this.fields=i,this._isResponseInArrayMode=a,this.customResultMapper=s}static [lP]="PostgresJsPreparedQuery";async execute(e={}){return l5.startActiveSpan("drizzle.execute",async t=>{let r=ug(this.params,e);t?.setAttributes({"drizzle.query.text":this.queryString,"drizzle.query.params":JSON.stringify(r)}),this.logger.logQuery(this.queryString,r);let{fields:n,queryString:i,client:a,joinsNotNullableMap:s,customResultMapper:o}=this;if(!n&&!o)return l5.startActiveSpan("drizzle.driver.execute",()=>a.unsafe(i,r));let c=await l5.startActiveSpan("drizzle.driver.execute",()=>(t?.setAttributes({"drizzle.query.text":i,"drizzle.query.params":JSON.stringify(r)}),a.unsafe(i,r).values()));return l5.startActiveSpan("drizzle.mapResponse",()=>o?o(c):c.map(e=>(function(e,t,r){let n={},i=e.reduce((e,{path:i,field:a},s)=>{let o;o=lC(a,lI)?a:lC(a,uc)?a.decoder:a.sql.decoder;let c=e;for(let[e,l]of i.entries())if(e<i.length-1)l in c||(c[l]={}),c=c[l];else{let e=t[s],u=c[l]=null===e?null:o.mapFromDriverValue(e);if(r&&lC(a,lI)&&2===i.length){let e=i[0];e in n?"string"==typeof n[e]&&n[e]!==a.table[lj]&&(n[e]=!1):n[e]=null===u&&a.table[lj]}}return e},{});if(r&&Object.keys(n).length>0)for(let[e,t]of Object.entries(n))"string"!=typeof t||r[t]||(i[e]=null);return i})(n,e,s)))})}all(e={}){return l5.startActiveSpan("drizzle.execute",async t=>{let r=ug(this.params,e);return t?.setAttributes({"drizzle.query.text":this.queryString,"drizzle.query.params":JSON.stringify(r)}),this.logger.logQuery(this.queryString,r),l5.startActiveSpan("drizzle.driver.execute",()=>(t?.setAttributes({"drizzle.query.text":this.queryString,"drizzle.query.params":JSON.stringify(r)}),this.client.unsafe(this.queryString,r)))})}isResponseInArrayMode(){return this._isResponseInArrayMode}}class hO extends hT{constructor(e,t,r,n={}){super(t),this.client=e,this.schema=r,this.options=n,this.logger=n.logger??new lR}static [lP]="PostgresJsSession";logger;prepareQuery(e,t,r,n,i){return new hC(this.client,e.sql,e.params,this.logger,t,n,i)}query(e,t){return this.logger.logQuery(e,t),this.client.unsafe(e,t).values()}queryObjects(e,t){return this.client.unsafe(e,t)}transaction(e,t){return this.client.begin(async r=>{let n=new hO(r,this.dialect,this.schema,this.options),i=new hN(this.dialect,n,this.schema);return t&&await i.setTransaction(t),e(i)})}}class hN extends hP{constructor(e,t,r,n=0){super(e,t,r,n),this.session=t}static [lP]="PostgresJsTransaction";transaction(e){return this.session.client.savepoint(t=>{let r=new hO(t,this.dialect,this.schema,this.session.options);return e(new hN(this.dialect,r,this.schema))})}}class hR extends hE{static [lP]="PostgresJsDatabase"}function hI(e,t={}){let r,n;let i=e=>e;for(let t of["1184","1082","1083","1114","1182","1185","1115","1231"])e.options.parsers[t]=i,e.options.serializers[t]=i;e.options.serializers["114"]=i,e.options.serializers["3802"]=i;let a=new f7({casing:t.casing});if(!0===t.logger?r=new lN:!1!==t.logger&&(r=t.logger),t.schema){let e=function(e,t){1===Object.keys(e).length&&"default"in e&&!lC(e.default,un)&&(e=e.default);let r={},n={},i={};for(let[a,s]of Object.entries(e))if(lC(s,un)){let e=ui(s),t=n[e];for(let n of(r[e]=a,i[a]={tsName:a,dbName:s[un.Symbol.Name],schema:s[un.Symbol.Schema],columns:s[un.Symbol.Columns],relations:t?.relations??{},primaryKey:t?.primaryKey??[]},Object.values(s[un.Symbol.Columns])))n.primary&&i[a].primaryKey.push(n);let o=s[un.Symbol.ExtraConfigBuilder]?.(s[un.Symbol.ExtraConfigColumns]);if(o)for(let e of Object.values(o))lC(e,fN)&&i[a].primaryKey.push(...e.columns)}else if(lC(s,f6)){let e;let a=ui(s.table),o=r[a];for(let[r,c]of Object.entries(s.config(t(s.table))))if(o){let t=i[o];t.relations[r]=c,e&&t.primaryKey.push(...e)}else a in n||(n[a]={relations:{},primaryKey:e}),n[a].relations[r]=c}return{tables:i,tableNamesMap:r}}(t.schema,f4);n={fullSchema:t.schema,schema:e.tables,tableNamesMap:e.tableNamesMap}}let s=new hO(e,a,n,{logger:r}),o=new hR(a,s,n);return o.$client=e,o}function h$(...e){if("string"==typeof e[0])return hI(lk(e[0]),e[1]);if(function(e){if("object"!=typeof e||null===e||"Object"!==e.constructor.name)return!1;if("logger"in e){let t=typeof e.logger;return"boolean"===t||"object"===t&&"function"==typeof e.logger.logQuery||"undefined"===t}if("schema"in e){let t=typeof e.schema;return"object"===t||"undefined"===t}if("casing"in e){let t=typeof e.casing;return"string"===t||"undefined"===t}if("mode"in e)return"default"===e.mode&&"planetscale"===e.mode&&void 0===e.mode;if("connection"in e){let t=typeof e.connection;return"string"===t||"object"===t||"undefined"===t}if("client"in e){let t=typeof e.client;return"object"===t||"function"===t||"undefined"===t}return 0===Object.keys(e).length}(e[0])){let{connection:t,client:r,...n}=e[0];if(r)return hI(r,n);if("object"==typeof t&&void 0!==t.url){let{url:e,...r}=t;return hI(lk(e,r),n)}return hI(lk(t),n)}return hI(e[0],e[1])}(h$||(h$={})).mock=function(e){return hI({options:{parsers:{},serializers:{}}},e)};var hj=r(791);let hU=fO("users",{id:fd("id").primaryKey().$defaultFn(()=>(0,hj.sX)()),name:fd("name"),email:fd("email").notNull().unique(),emailVerified:u5("emailVerified",{mode:"date"}),image:fd("image"),password:fd("password"),role:fd("role",{enum:["admin","test_checker"]}).notNull().default("test_checker"),createdAt:u5("created_at").defaultNow().notNull(),updatedAt:u5("updated_at").defaultNow().notNull()}),hL=fO("accounts",{userId:fd("userId").notNull().references(()=>hU.id,{onDelete:"cascade"}),type:fd("type").notNull(),provider:fd("provider").notNull(),providerAccountId:fd("providerAccountId").notNull(),refresh_token:fd("refresh_token"),access_token:fd("access_token"),expires_at:d$("expires_at"),token_type:fd("token_type"),scope:fd("scope"),id_token:fd("id_token"),session_state:fd("session_state")}),hD=fO("sessions",{sessionToken:fd("sessionToken").primaryKey(),userId:fd("userId").notNull().references(()=>hU.id,{onDelete:"cascade"}),expires:u5("expires",{mode:"date"}).notNull()}),hM=fO("verificationTokens",{identifier:fd("identifier").notNull(),token:fd("token").notNull(),expires:u5("expires",{mode:"date"}).notNull()}),hB=fO("candidates",{id:fd("id").primaryKey().$defaultFn(()=>(0,hj.sX)()),fullName:fd("full_name").notNull(),email:fd("email").notNull().unique(),phoneNumber:fd("phone_number").notNull(),dateOfBirth:u5("date_of_birth",{mode:"date"}).notNull(),nationality:fd("nationality").notNull(),passportNumber:fd("passport_number").notNull().unique(),testDate:u5("test_date",{mode:"date"}).notNull(),testCenter:fd("test_center").notNull(),photoUrl:fd("photo_url"),createdAt:u5("created_at").defaultNow().notNull(),updatedAt:u5("updated_at").defaultNow().notNull()}),hH=fO("test_results",{id:fd("id").primaryKey().$defaultFn(()=>(0,hj.sX)()),candidateId:fd("candidate_id").notNull().references(()=>hB.id,{onDelete:"cascade"}),listeningScore:uJ("listening_score",{precision:3,scale:1}),listeningBandScore:uJ("listening_band_score",{precision:2,scale:1}),readingScore:uJ("reading_score",{precision:3,scale:1}),readingBandScore:uJ("reading_band_score",{precision:2,scale:1}),writingTask1Score:uJ("writing_task1_score",{precision:2,scale:1}),writingTask2Score:uJ("writing_task2_score",{precision:2,scale:1}),writingBandScore:uJ("writing_band_score",{precision:2,scale:1}),speakingFluencyScore:uJ("speaking_fluency_score",{precision:2,scale:1}),speakingLexicalScore:uJ("speaking_lexical_score",{precision:2,scale:1}),speakingGrammarScore:uJ("speaking_grammar_score",{precision:2,scale:1}),speakingPronunciationScore:uJ("speaking_pronunciation_score",{precision:2,scale:1}),speakingBandScore:uJ("speaking_band_score",{precision:2,scale:1}),overallBandScore:uJ("overall_band_score",{precision:2,scale:1}),status:fd("status",{enum:["pending","completed","verified"]}).notNull().default("pending"),enteredBy:fd("entered_by").references(()=>hU.id),verifiedBy:fd("verified_by").references(()=>hU.id),certificateGenerated:dm("certificate_generated").default(!1),certificateSerial:fd("certificate_serial").unique(),certificateUrl:fd("certificate_url"),aiFeedbackGenerated:dm("ai_feedback_generated").default(!1),testDate:u5("test_date",{mode:"date"}),createdAt:u5("created_at").defaultNow().notNull(),updatedAt:u5("updated_at").defaultNow().notNull()}),hq=fO("ai_feedback",{id:fd("id").primaryKey().$defaultFn(()=>(0,hj.sX)()),testResultId:fd("test_result_id").notNull().references(()=>hH.id,{onDelete:"cascade"}),listeningFeedback:fd("listening_feedback"),readingFeedback:fd("reading_feedback"),writingFeedback:fd("writing_feedback"),speakingFeedback:fd("speaking_feedback"),overallFeedback:fd("overall_feedback"),studyRecommendations:fd("study_recommendations"),strengths:uU("strengths").$type(),weaknesses:uU("weaknesses").$type(),studyPlan:uU("study_plan").$type(),generatedAt:u5("generated_at").defaultNow().notNull()}),hz=h$(lk(process.env.DATABASE_URL,{prepare:!1}),{schema:d});var hW=r(825),hF=null;function hK(e,t){if("number"!=typeof(e=e||h6))throw Error("Illegal arguments: "+typeof e+", "+typeof t);e<4?e=4:e>31&&(e=31);var r=[];return r.push("$2b$"),e<10&&r.push("0"),r.push(e.toString()),r.push("$"),r.push(h1(function(e){try{return crypto.getRandomValues(new Uint8Array(e))}catch{}try{return hW.randomBytes(e)}catch{}if(!hF)throw Error("Neither WebCryptoAPI nor a crypto module is available. Use bcrypt.setRandomFallback to set an alternative");return hF(e)}(h5),h5)),r.join("")}function hV(e,t,r){if("function"==typeof t&&(r=t,t=void 0),"function"==typeof e&&(r=e,e=void 0),void 0===e)e=h6;else if("number"!=typeof e)throw Error("illegal arguments: "+typeof e);function n(t){hX(function(){try{t(null,hK(e))}catch(e){t(e)}})}if(!r)return new Promise(function(e,t){n(function(r,n){if(r){t(r);return}e(n)})});if("function"!=typeof r)throw Error("Illegal callback: "+typeof r);n(r)}function hJ(e,t){if(void 0===t&&(t=h6),"number"==typeof t&&(t=hK(t)),"string"!=typeof e||"string"!=typeof t)throw Error("Illegal arguments: "+typeof e+", "+typeof t);return pr(e,t)}function hQ(e,t,r,n){function i(r){"string"==typeof e&&"number"==typeof t?hV(t,function(t,i){pr(e,i,r,n)}):"string"==typeof e&&"string"==typeof t?pr(e,t,r,n):hX(r.bind(this,Error("Illegal arguments: "+typeof e+", "+typeof t)))}if(!r)return new Promise(function(e,t){i(function(r,n){if(r){t(r);return}e(n)})});if("function"!=typeof r)throw Error("Illegal callback: "+typeof r);i(r)}function hG(e,t){for(var r=e.length^t.length,n=0;n<e.length;++n)r|=e.charCodeAt(n)^t.charCodeAt(n);return 0===r}var hX="undefined"!=typeof process&&process&&"function"==typeof process.nextTick?"function"==typeof setImmediate?setImmediate:process.nextTick:setTimeout;function hY(e){for(var t=0,r=0,n=0;n<e.length;++n)(r=e.charCodeAt(n))<128?t+=1:r<2048?t+=2:(64512&r)==55296&&(64512&e.charCodeAt(n+1))==56320?(++n,t+=4):t+=3;return t}var hZ="./ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),h0=[-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,0,1,54,55,56,57,58,59,60,61,62,63,-1,-1,-1,-1,-1,-1,-1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,-1,-1,-1,-1,-1,-1,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,-1,-1,-1,-1,-1];function h1(e,t){var r,n,i=0,a=[];if(t<=0||t>e.length)throw Error("Illegal len: "+t);for(;i<t;){if(r=255&e[i++],a.push(hZ[r>>2&63]),r=(3&r)<<4,i>=t||(r|=(n=255&e[i++])>>4&15,a.push(hZ[63&r]),r=(15&n)<<2,i>=t)){a.push(hZ[63&r]);break}r|=(n=255&e[i++])>>6&3,a.push(hZ[63&r]),a.push(hZ[63&n])}return a.join("")}function h2(e,t){var r,n,i,a,s,o=0,c=e.length,l=0,u=[];if(t<=0)throw Error("Illegal len: "+t);for(;o<c-1&&l<t&&(r=(s=e.charCodeAt(o++))<h0.length?h0[s]:-1,n=(s=e.charCodeAt(o++))<h0.length?h0[s]:-1,-1!=r&&-1!=n)&&(a=r<<2>>>0|(48&n)>>4,u.push(String.fromCharCode(a)),!(++l>=t||o>=c||-1==(i=(s=e.charCodeAt(o++))<h0.length?h0[s]:-1)||(a=(15&n)<<4>>>0|(60&i)>>2,u.push(String.fromCharCode(a)),++l>=t||o>=c)));)a=(3&i)<<6>>>0|((s=e.charCodeAt(o++))<h0.length?h0[s]:-1),u.push(String.fromCharCode(a)),++l;var d=[];for(o=0;o<l;o++)d.push(u[o].charCodeAt(0));return d}var h5=16,h6=10,h3=[0x243f6a88,0x85a308d3,0x13198a2e,0x3707344,0xa4093822,0x299f31d0,0x82efa98,0xec4e6c89,0x452821e6,0x38d01377,0xbe5466cf,0x34e90c6c,0xc0ac29b7,0xc97c50dd,0x3f84d5b5,0xb5470917,0x9216d5d9,0x8979fb1b],h8=[0xd1310ba6,0x98dfb5ac,0x2ffd72db,0xd01adfb7,0xb8e1afed,0x6a267e96,0xba7c9045,0xf12c7f99,0x24a19947,0xb3916cf7,0x801f2e2,0x858efc16,0x636920d8,0x71574e69,0xa458fea3,0xf4933d7e,0xd95748f,0x728eb658,0x718bcd58,0x82154aee,0x7b54a41d,0xc25a59b5,0x9c30d539,0x2af26013,0xc5d1b023,0x286085f0,0xca417918,0xb8db38ef,0x8e79dcb0,0x603a180e,0x6c9e0e8b,0xb01e8a3e,0xd71577c1,0xbd314b27,0x78af2fda,0x55605c60,0xe65525f3,0xaa55ab94,0x57489862,0x63e81440,0x55ca396a,0x2aab10b6,0xb4cc5c34,0x1141e8ce,0xa15486af,0x7c72e993,0xb3ee1411,0x636fbc2a,0x2ba9c55d,0x741831f6,0xce5c3e16,0x9b87931e,0xafd6ba33,0x6c24cf5c,0x7a325381,0x28958677,0x3b8f4898,0x6b4bb9af,0xc4bfe81b,0x66282193,0x61d809cc,0xfb21a991,0x487cac60,0x5dec8032,0xef845d5d,0xe98575b1,0xdc262302,0xeb651b88,0x23893e81,0xd396acc5,0xf6d6ff3,0x83f44239,0x2e0b4482,0xa4842004,0x69c8f04a,0x9e1f9b5e,0x21c66842,0xf6e96c9a,0x670c9c61,0xabd388f0,0x6a51a0d2,0xd8542f68,0x960fa728,0xab5133a3,0x6eef0b6c,0x137a3be4,0xba3bf050,0x7efb2a98,0xa1f1651d,0x39af0176,0x66ca593e,0x82430e88,0x8cee8619,0x456f9fb4,0x7d84a5c3,0x3b8b5ebe,0xe06f75d8,0x85c12073,0x401a449f,0x56c16aa6,0x4ed3aa62,0x363f7706,0x1bfedf72,0x429b023d,0x37d0d724,0xd00a1248,0xdb0fead3,0x49f1c09b,0x75372c9,0x80991b7b,0x25d479d8,0xf6e8def7,0xe3fe501a,0xb6794c3b,0x976ce0bd,0x4c006ba,0xc1a94fb6,0x409f60c4,0x5e5c9ec2,0x196a2463,0x68fb6faf,0x3e6c53b5,0x1339b2eb,0x3b52ec6f,0x6dfc511f,0x9b30952c,0xcc814544,0xaf5ebd09,0xbee3d004,0xde334afd,0x660f2807,0x192e4bb3,0xc0cba857,0x45c8740f,0xd20b5f39,0xb9d3fbdb,0x5579c0bd,0x1a60320a,0xd6a100c6,0x402c7279,0x679f25fe,0xfb1fa3cc,0x8ea5e9f8,0xdb3222f8,0x3c7516df,0xfd616b15,0x2f501ec8,0xad0552ab,0x323db5fa,0xfd238760,0x53317b48,0x3e00df82,0x9e5c57bb,0xca6f8ca0,0x1a87562e,0xdf1769db,0xd542a8f6,0x287effc3,0xac6732c6,0x8c4f5573,0x695b27b0,0xbbca58c8,0xe1ffa35d,0xb8f011a0,0x10fa3d98,0xfd2183b8,0x4afcb56c,0x2dd1d35b,0x9a53e479,0xb6f84565,0xd28e49bc,0x4bfb9790,0xe1ddf2da,0xa4cb7e33,0x62fb1341,0xcee4c6e8,0xef20cada,0x36774c01,0xd07e9efe,0x2bf11fb4,0x95dbda4d,0xae909198,0xeaad8e71,0x6b93d5a0,0xd08ed1d0,0xafc725e0,0x8e3c5b2f,0x8e7594b7,0x8ff6e2fb,0xf2122b64,0x8888b812,0x900df01c,0x4fad5ea0,0x688fc31c,0xd1cff191,0xb3a8c1ad,0x2f2f2218,0xbe0e1777,0xea752dfe,0x8b021fa1,0xe5a0cc0f,0xb56f74e8,0x18acf3d6,0xce89e299,0xb4a84fe0,0xfd13e0b7,0x7cc43b81,0xd2ada8d9,0x165fa266,0x80957705,0x93cc7314,0x211a1477,0xe6ad2065,0x77b5fa86,0xc75442f5,0xfb9d35cf,0xebcdaf0c,0x7b3e89a0,0xd6411bd3,0xae1e7e49,2428461,0x2071b35e,0x226800bb,0x57b8e0af,0x2464369b,0xf009b91e,0x5563911d,0x59dfa6aa,0x78c14389,0xd95a537f,0x207d5ba2,0x2e5b9c5,0x83260376,0x6295cfa9,0x11c81968,0x4e734a41,0xb3472dca,0x7b14a94a,0x1b510052,0x9a532915,0xd60f573f,0xbc9bc6e4,0x2b60a476,0x81e67400,0x8ba6fb5,0x571be91f,0xf296ec6b,0x2a0dd915,0xb6636521,0xe7b9f9b6,0xff34052e,0xc5855664,0x53b02d5d,0xa99f8fa1,0x8ba4799,0x6e85076a,0x4b7a70e9,0xb5b32944,0xdb75092e,0xc4192623,290971e4,0x49a7df7d,0x9cee60b8,0x8fedb266,0xecaa8c71,0x699a17ff,0x5664526c,0xc2b19ee1,0x193602a5,0x75094c29,0xa0591340,0xe4183a3e,0x3f54989a,0x5b429d65,0x6b8fe4d6,0x99f73fd6,0xa1d29c07,0xefe830f5,0x4d2d38e6,0xf0255dc1,0x4cdd2086,0x8470eb26,0x6382e9c6,0x21ecc5e,0x9686b3f,0x3ebaefc9,0x3c971814,0x6b6a70a1,0x687f3584,0x52a0e286,0xb79c5305,0xaa500737,0x3e07841c,0x7fdeae5c,0x8e7d44ec,0x5716f2b8,0xb03ada37,0xf0500c0d,0xf01c1f04,0x200b3ff,0xae0cf51a,0x3cb574b2,0x25837a58,0xdc0921bd,0xd19113f9,0x7ca92ff6,0x94324773,0x22f54701,0x3ae5e581,0x37c2dadc,0xc8b57634,0x9af3dda7,0xa9446146,0xfd0030e,0xecc8c73e,0xa4751e41,0xe238cd99,0x3bea0e2f,0x3280bba1,0x183eb331,0x4e548b38,0x4f6db908,0x6f420d03,0xf60a04bf,0x2cb81290,0x24977c79,0x5679b072,0xbcaf89af,0xde9a771f,0xd9930810,0xb38bae12,0xdccf3f2e,0x5512721f,0x2e6b7124,0x501adde6,0x9f84cd87,0x7a584718,0x7408da17,0xbc9f9abc,0xe94b7d8c,0xec7aec3a,0xdb851dfa,0x63094366,0xc464c3d2,0xef1c1847,0x3215d908,0xdd433b37,0x24c2ba16,0x12a14d43,0x2a65c451,0x50940002,0x133ae4dd,0x71dff89e,0x10314e55,0x81ac77d6,0x5f11199b,0x43556f1,0xd7a3c76b,0x3c11183b,0x5924a509,0xf28fe6ed,0x97f1fbfa,0x9ebabf2c,0x1e153c6e,0x86e34570,0xeae96fb1,0x860e5e0a,0x5a3e2ab3,0x771fe71c,0x4e3d06fa,0x2965dcb9,0x99e71d0f,0x803e89d6,0x5266c825,0x2e4cc978,0x9c10b36a,0xc6150eba,0x94e2ea78,0xa5fc3c53,0x1e0a2df4,0xf2f74ea7,0x361d2b3d,0x1939260f,0x19c27960,0x5223a708,0xf71312b6,0xebadfe6e,0xeac31f66,0xe3bc4595,0xa67bc883,0xb17f37d1,0x18cff28,0xc332ddef,0xbe6c5aa5,0x65582185,0x68ab9802,0xeecea50f,0xdb2f953b,0x2aef7dad,0x5b6e2f84,0x1521b628,0x29076170,0xecdd4775,0x619f1510,0x13cca830,0xeb61bd96,0x334fe1e,0xaa0363cf,0xb5735c90,0x4c70a239,0xd59e9e0b,0xcbaade14,0xeecc86bc,0x60622ca7,0x9cab5cab,0xb2f3846e,0x648b1eaf,0x19bdf0ca,0xa02369b9,0x655abb50,0x40685a32,0x3c2ab4b3,0x319ee9d5,0xc021b8f7,0x9b540b19,0x875fa099,0x95f7997e,0x623d7da8,0xf837889a,0x97e32d77,0x11ed935f,0x16681281,0xe358829,0xc7e61fd6,0x96dedfa1,0x7858ba99,0x57f584a5,0x1b227263,0x9b83c3ff,0x1ac24696,0xcdb30aeb,0x532e3054,0x8fd948e4,0x6dbc3128,0x58ebf2ef,0x34c6ffea,0xfe28ed61,0xee7c3c73,0x5d4a14d9,0xe864b7e3,0x42105d14,0x203e13e0,0x45eee2b6,0xa3aaabea,0xdb6c4f15,0xfacb4fd0,0xc742f442,0xef6abbb5,0x654f3b1d,0x41cd2105,0xd81e799e,0x86854dc7,0xe44b476a,0x3d816250,0xcf62a1f2,0x5b8d2646,0xfc8883a0,0xc1c7b6a3,0x7f1524c3,0x69cb7492,0x47848a0b,0x5692b285,0x95bbf00,0xad19489d,0x1462b174,0x23820e00,0x58428d2a,0xc55f5ea,0x1dadf43e,0x233f7061,0x3372f092,0x8d937e41,0xd65fecf1,0x6c223bdb,0x7cde3759,0xcbee7460,0x4085f2a7,0xce77326e,0xa6078084,0x19f8509e,0xe8efd855,0x61d99735,0xa969a7aa,0xc50c06c2,0x5a04abfc,0x800bcadc,0x9e447a2e,0xc3453484,0xfdd56705,0xe1e9ec9,0xdb73dbd3,0x105588cd,0x675fda79,0xe3674340,0xc5c43465,0x713e38d8,0x3d28f89e,0xf16dff20,0x153e21e7,0x8fb03d4a,0xe6e39f2b,0xdb83adf7,0xe93d5a68,0x948140f7,0xf64c261c,0x94692934,0x411520f7,0x7602d4f7,0xbcf46b2e,0xd4a20068,0xd4082471,0x3320f46a,0x43b7d4b7,0x500061af,0x1e39f62e,0x97244546,0x14214f74,0xbf8b8840,0x4d95fc1d,0x96b591af,0x70f4ddd3,0x66a02f45,0xbfbc09ec,0x3bd9785,0x7fac6dd0,0x31cb8504,0x96eb27b3,0x55fd3941,0xda2547e6,0xabca0a9a,0x28507825,0x530429f4,0xa2c86da,0xe9b66dfb,0x68dc1462,0xd7486900,0x680ec0a4,0x27a18dee,0x4f3ffea2,0xe887ad8c,0xb58ce006,0x7af4d6b6,0xaace1e7c,0xd3375fec,0xce78a399,0x406b2a42,0x20fe9e35,0xd9f385b9,0xee39d7ab,0x3b124e8b,0x1dc9faf7,0x4b6d1856,0x26a36631,0xeae397b2,0x3a6efa74,0xdd5b4332,0x6841e7f7,0xca7820fb,0xfb0af54e,0xd8feb397,0x454056ac,0xba489527,0x55533a3a,0x20838d87,0xfe6ba9b7,0xd096954b,0x55a867bc,0xa1159a58,0xcca92963,0x99e1db33,0xa62a4a56,0x3f3125f9,0x5ef47e1c,0x9029317c,0xfdf8e802,0x4272f70,0x80bb155c,0x5282ce3,0x95c11548,0xe4c66d22,0x48c1133f,0xc70f86dc,0x7f9c9ee,0x41041f0f,0x404779a4,0x5d886e17,0x325f51eb,0xd59bc0d1,0xf2bcc18f,0x41113564,0x257b7834,0x602a9c60,0xdff8e8a3,0x1f636c1b,0xe12b4c2,0x2e1329e,0xaf664fd1,0xcad18115,0x6b2395e0,0x333e92e1,0x3b240b62,0xeebeb922,0x85b2a20e,0xe6ba0d99,0xde720c8c,0x2da2f728,0xd0127845,0x95b794fd,0x647d0862,0xe7ccf5f0,0x5449a36f,0x877d48fa,0xc39dfd27,0xf33e8d1e,0xa476341,0x992eff74,0x3a6f6eab,0xf4f8fd37,0xa812dc60,0xa1ebddf8,0x991be14c,0xdb6e6b0d,0xc67b5510,0x6d672c37,0x2765d43b,0xdcd0e804,0xf1290dc7,0xcc00ffa3,0xb5390f92,0x690fed0b,0x667b9ffb,0xcedb7d9c,0xa091cf0b,0xd9155ea3,0xbb132f88,0x515bad24,0x7b9479bf,0x763bd6eb,0x37392eb3,0xcc115979,0x8026e297,0xf42e312d,0x6842ada7,0xc66a2b3b,0x12754ccc,0x782ef11c,0x6a124237,0xb79251e7,0x6a1bbe6,0x4bfb6350,0x1a6b1018,0x11caedfa,0x3d25bdd8,0xe2e1c3c9,0x44421659,0xa121386,0xd90cec6e,0xd5abea2a,0x64af674e,0xda86a85f,0xbebfe988,0x64e4c3fe,0x9dbc8057,0xf0f7c086,0x60787bf8,0x6003604d,0xd1fd8346,0xf6381fb0,0x7745ae04,0xd736fccc,0x83426b33,0xf01eab71,0xb0804187,0x3c005e5f,0x77a057be,0xbde8ae24,0x55464299,0xbf582e61,0x4e58f48f,0xf2ddfda2,0xf474ef38,0x8789bdc2,0x5366f9c3,0xc8b38e74,0xb475f255,0x46fcd9b9,0x7aeb2661,0x8b1ddf84,0x846a0e79,0x915f95e2,0x466e598e,0x20b45770,0x8cd55591,0xc902de4c,0xb90bace1,0xbb8205d0,0x11a86248,0x7574a99e,0xb77f19b6,0xe0a9dc09,0x662d09a1,0xc4324633,0xe85a1f02,0x9f0be8c,0x4a99a025,0x1d6efe10,0x1ab93d1d,0xba5a4df,0xa186f20f,0x2868f169,0xdcb7da83,0x573906fe,0xa1e2ce9b,0x4fcd7f52,0x50115e01,0xa70683fa,0xa002b5c4,0xde6d027,0x9af88c27,0x773f8641,0xc3604c06,0x61a806b5,0xf0177a28,0xc0f586e0,6314154,0x30dc7d62,0x11e69ed7,0x2338ea63,0x53c2dd94,0xc2c21634,0xbbcbee56,0x90bcb6de,0xebfc7da1,0xce591d76,0x6f05e409,0x4b7c0188,0x39720a3d,0x7c927c24,0x86e3725f,0x724d9db9,0x1ac15bb4,0xd39eb8fc,0xed545578,0x8fca5b5,0xd83d7cd3,0x4dad0fc4,0x1e50ef5e,0xb161e6f8,0xa28514d9,0x6c51133c,0x6fd5c7e7,0x56e14ec4,0x362abfce,0xddc6c837,0xd79a3234,0x92638212,0x670efa8e,0x406000e0,0x3a39ce37,0xd3faf5cf,0xabc27737,0x5ac52d1b,0x5cb0679e,0x4fa33742,0xd3822740,0x99bc9bbe,0xd5118e9d,0xbf0f7315,0xd62d1c7e,0xc700c47b,0xb78c1b6b,0x21a19045,0xb26eb1be,0x6a366eb4,0x5748ab2f,0xbc946e79,0xc6a376d2,0x6549c2c8,0x530ff8ee,0x468dde7d,0xd5730a1d,0x4cd04dc6,0x2939bbdb,0xa9ba4650,0xac9526e8,0xbe5ee304,0xa1fad5f0,0x6a2d519a,0x63ef8ce2,0x9a86ee22,0xc089c2b8,0x43242ef6,0xa51e03aa,0x9cf2d0a4,0x83c061ba,0x9be96a4d,0x8fe51550,0xba645bd6,0x2826a2f9,0xa73a3ae1,0x4ba99586,0xef5562e9,0xc72fefd3,0xf752f7da,0x3f046f69,0x77fa0a59,0x80e4a915,0x87b08601,0x9b09e6ad,0x3b3ee593,0xe990fd5a,0x9e34d797,0x2cf0b7d9,0x22b8b51,0x96d5ac3a,0x17da67d,0xd1cf3ed6,0x7c7d2d28,0x1f9f25cf,0xadf2b89b,0x5ad6b472,0x5a88f54c,0xe029ac71,0xe019a5e6,0x47b0acfd,0xed93fa9b,0xe8d3c48d,0x283b57cc,0xf8d56629,0x79132e28,0x785f0191,0xed756055,0xf7960e44,0xe3d35e8c,0x15056dd4,0x88f46dba,0x3a16125,0x564f0bd,0xc3eb9e15,0x3c9057a2,0x97271aec,0xa93a072a,0x1b3f6d9b,0x1e6321f5,0xf59c66fb,0x26dcf319,0x7533d928,0xb155fdf5,0x3563482,0x8aba3cbb,0x28517711,0xc20ad9f8,0xabcc5167,0xccad925f,0x4de81751,0x3830dc8e,0x379d5862,0x9320f991,0xea7a90c2,0xfb3e7bce,0x5121ce64,0x774fbe32,0xa8b6e37e,0xc3293d46,0x48de5369,0x6413e680,0xa2ae0810,0xdd6db224,0x69852dfd,0x9072166,0xb39a460a,0x6445c0dd,0x586cdecf,0x1c20c8ae,0x5bbef7dd,0x1b588d40,0xccd2017f,0x6bb4e3bb,0xdda26a7e,0x3a59ff45,0x3e350a44,0xbcb4cdd5,0x72eacea8,0xfa6484bb,0x8d6612ae,0xbf3c6f47,0xd29be463,0x542f5d9e,0xaec2771b,0xf64e6370,0x740e0d8d,0xe75b1357,0xf8721671,0xaf537d5d,0x4040cb08,0x4eb4e2cc,0x34d2466a,0x115af84,3786409e3,0x95983a1d,0x6b89fb4,0xce6ea048,0x6f3f3b82,0x3520ab82,0x11a1d4b,0x277227f8,0x611560b1,0xe7933fdc,0xbb3a792b,0x344525bd,0xa08839e1,0x51ce794b,0x2f32c9b7,0xa01fbac9,0xe01cc87e,0xbcc7d1f6,0xcf0111c3,0xa1e8aac7,0x1a908749,0xd44fbd9a,0xd0dadecb,0xd50ada38,0x339c32a,0xc6913667,0x8df9317c,0xe0b12b4f,0xf79e59b7,0x43f5bb3a,0xf2d519ff,0x27d9459c,0xbf97222c,0x15e6fc2a,0xf91fc71,0x9b941525,0xfae59361,0xceb69ceb,0xc2a86459,0x12baa8d1,0xb6c1075e,0xe3056a0c,0x10d25065,0xcb03a442,0xe0ec6e0e,0x1698db3b,0x4c98a0be,0x3278e964,0x9f1f9532,0xe0d392df,0xd3a0342b,0x8971f21e,0x1b0a7441,0x4ba3348c,0xc5be7120,0xc37632d8,0xdf359f8d,0x9b992f2e,0xe60b6f47,0xfe3f11d,0xe54cda54,0x1edad891,0xce6279cf,0xcd3e7e6f,0x1618b166,0xfd2c1d05,0x848fd2c5,0xf6fb2299,0xf523f357,0xa6327623,0x93a83531,0x56cccd02,0xacf08162,0x5a75ebb5,0x6e163697,0x88d273cc,0xde966292,0x81b949d0,0x4c50901b,0x71c65614,0xe6c6c7bd,0x327a140a,0x45e1d006,0xc3f27b9a,0xc9aa53fd,0x62a80f00,0xbb25bfe2,0x35bdd2f6,0x71126905,0xb2040222,0xb6cbcf7c,0xcd769c2b,0x53113ec0,0x1640e3d3,0x38abbd60,0x2547adf0,0xba38209c,0xf746ce76,0x77afa1c5,0x20756060,0x85cbfe4e,0x8ae88dd8,0x7aaaf9b0,0x4cf9aa7e,0x1948c25c,0x2fb8a8c,0x1c36ae4,0xd6ebe1f9,0x90d4f869,0xa65cdea0,0x3f09252d,0xc208e69f,0xb74e6132,0xce77e25b,0x578fdfe3,0x3ac372e6],h4=[0x4f727068,0x65616e42,0x65686f6c,0x64657253,0x63727944,0x6f756274];function h9(e,t,r,n){var i=e[t],a=e[t+1];return i^=r[0],a^=(n[i>>>24]+n[256|i>>16&255]^n[512|i>>8&255])+n[768|255&i]^r[1],i^=(n[a>>>24]+n[256|a>>16&255]^n[512|a>>8&255])+n[768|255&a]^r[2],a^=(n[i>>>24]+n[256|i>>16&255]^n[512|i>>8&255])+n[768|255&i]^r[3],i^=(n[a>>>24]+n[256|a>>16&255]^n[512|a>>8&255])+n[768|255&a]^r[4],a^=(n[i>>>24]+n[256|i>>16&255]^n[512|i>>8&255])+n[768|255&i]^r[5],i^=(n[a>>>24]+n[256|a>>16&255]^n[512|a>>8&255])+n[768|255&a]^r[6],a^=(n[i>>>24]+n[256|i>>16&255]^n[512|i>>8&255])+n[768|255&i]^r[7],i^=(n[a>>>24]+n[256|a>>16&255]^n[512|a>>8&255])+n[768|255&a]^r[8],a^=(n[i>>>24]+n[256|i>>16&255]^n[512|i>>8&255])+n[768|255&i]^r[9],i^=(n[a>>>24]+n[256|a>>16&255]^n[512|a>>8&255])+n[768|255&a]^r[10],a^=(n[i>>>24]+n[256|i>>16&255]^n[512|i>>8&255])+n[768|255&i]^r[11],i^=(n[a>>>24]+n[256|a>>16&255]^n[512|a>>8&255])+n[768|255&a]^r[12],a^=(n[i>>>24]+n[256|i>>16&255]^n[512|i>>8&255])+n[768|255&i]^r[13],i^=(n[a>>>24]+n[256|a>>16&255]^n[512|a>>8&255])+n[768|255&a]^r[14],a^=(n[i>>>24]+n[256|i>>16&255]^n[512|i>>8&255])+n[768|255&i]^r[15],i^=(n[a>>>24]+n[256|a>>16&255]^n[512|a>>8&255])+n[768|255&a]^r[16],e[t]=a^r[17],e[t+1]=i,e}function h7(e,t){for(var r=0,n=0;r<4;++r)n=n<<8|255&e[t],t=(t+1)%e.length;return{key:n,offp:t}}function pe(e,t,r){for(var n,i=0,a=[0,0],s=t.length,o=r.length,c=0;c<s;c++)i=(n=h7(e,i)).offp,t[c]=t[c]^n.key;for(c=0;c<s;c+=2)a=h9(a,0,t,r),t[c]=a[0],t[c+1]=a[1];for(c=0;c<o;c+=2)a=h9(a,0,t,r),r[c]=a[0],r[c+1]=a[1]}function pt(e,t,r,n,i){var a,s,o=h4.slice(),c=o.length;if(r<4||r>31){if(s=Error("Illegal number of rounds (4-31): "+r),n){hX(n.bind(this,s));return}throw s}if(t.length!==h5){if(s=Error("Illegal salt length: "+t.length+" != "+h5),n){hX(n.bind(this,s));return}throw s}r=1<<r>>>0;var l,u,d,f=0;function h(){if(i&&i(f/r),f<r)for(var a=Date.now();f<r&&(f+=1,pe(e,l,u),pe(t,l,u),!(Date.now()-a>100)););else{for(f=0;f<64;f++)for(d=0;d<c>>1;d++)h9(o,d<<1,l,u);var s=[];for(f=0;f<c;f++)s.push((o[f]>>24&255)>>>0),s.push((o[f]>>16&255)>>>0),s.push((o[f]>>8&255)>>>0),s.push((255&o[f])>>>0);return n?void n(null,s):s}n&&hX(h)}if("function"==typeof Int32Array?(l=new Int32Array(h3),u=new Int32Array(h8)):(l=h3.slice(),u=h8.slice()),!function(e,t,r,n){for(var i,a=0,s=[0,0],o=r.length,c=n.length,l=0;l<o;l++)a=(i=h7(t,a)).offp,r[l]=r[l]^i.key;for(l=0,a=0;l<o;l+=2)a=(i=h7(e,a)).offp,s[0]^=i.key,a=(i=h7(e,a)).offp,s[1]^=i.key,s=h9(s,0,r,n),r[l]=s[0],r[l+1]=s[1];for(l=0;l<c;l+=2)a=(i=h7(e,a)).offp,s[0]^=i.key,a=(i=h7(e,a)).offp,s[1]^=i.key,s=h9(s,0,r,n),n[l]=s[0],n[l+1]=s[1]}(t,e,l,u),void 0!==n)h();else for(;;)if(void 0!==(a=h()))return a||[]}function pr(e,t,r,n){if("string"!=typeof e||"string"!=typeof t){if(i=Error("Invalid string / salt: Not a string"),r){hX(r.bind(this,i));return}throw i}if("$"!==t.charAt(0)||"2"!==t.charAt(1)){if(i=Error("Invalid salt version: "+t.substring(0,2)),r){hX(r.bind(this,i));return}throw i}if("$"===t.charAt(2))a="\0",s=3;else{if("a"!==(a=t.charAt(2))&&"b"!==a&&"y"!==a||"$"!==t.charAt(3)){if(i=Error("Invalid salt revision: "+t.substring(2,4)),r){hX(r.bind(this,i));return}throw i}s=4}if(t.charAt(s+2)>"$"){if(i=Error("Missing salt rounds"),r){hX(r.bind(this,i));return}throw i}var i,a,s,o=10*parseInt(t.substring(s,s+1),10)+parseInt(t.substring(s+1,s+2),10),c=t.substring(s+3,s+25),l=function(e){for(var t,r,n=0,i=Array(hY(e)),a=0,s=e.length;a<s;++a)(t=e.charCodeAt(a))<128?i[n++]=t:(t<2048?i[n++]=t>>6|192:((64512&t)==55296&&(64512&(r=e.charCodeAt(a+1)))==56320?(t=65536+((1023&t)<<10)+(1023&r),++a,i[n++]=t>>18|240,i[n++]=t>>12&63|128):i[n++]=t>>12|224,i[n++]=t>>6&63|128),i[n++]=63&t|128);return i}(e+=a>="a"?"\0":""),u=h2(c,h5);function d(e){var t=[];return t.push("$2"),a>="a"&&t.push(a),t.push("$"),o<10&&t.push("0"),t.push(o.toString()),t.push("$"),t.push(h1(u,u.length)),t.push(h1(e,4*h4.length-1)),t.join("")}if(void 0===r)return d(pt(l,u,o));pt(l,u,o,function(e,t){e?r(e,null):r(null,d(t))},n)}let pn={setRandomFallback:function(e){hF=e},genSaltSync:hK,genSalt:hV,hashSync:hJ,hash:hQ,compareSync:function(e,t){if("string"!=typeof e||"string"!=typeof t)throw Error("Illegal arguments: "+typeof e+", "+typeof t);return 60===t.length&&hG(hJ(e,t.substring(0,t.length-31)),t)},compare:function(e,t,r,n){function i(r){if("string"!=typeof e||"string"!=typeof t){hX(r.bind(this,Error("Illegal arguments: "+typeof e+", "+typeof t)));return}if(60!==t.length){hX(r.bind(this,null,!1));return}hQ(e,t.substring(0,29),function(e,n){e?r(e):r(null,hG(n,t))},n)}if(!r)return new Promise(function(e,t){i(function(r,n){if(r){t(r);return}e(n)})});if("function"!=typeof r)throw Error("Illegal callback: "+typeof r);i(r)},getRounds:function(e){if("string"!=typeof e)throw Error("Illegal arguments: "+typeof e);return parseInt(e.split("$")[2],10)},getSalt:function(e){if("string"!=typeof e)throw Error("Illegal arguments: "+typeof e);if(60!==e.length)throw Error("Illegal hash length: "+e.length+" != 60");return e.substring(0,29)},truncates:function(e){if("string"!=typeof e)throw Error("Illegal arguments: "+typeof e);return hY(e)>72},encodeBase64:function(e,t){return h1(e,t)},decodeBase64:function(e,t){return h2(e,t)}},{handlers:pi,auth:pa,signIn:ps,signOut:po}=function(e){if("function"==typeof e){let t=async t=>{let r=await e(t);return oW(r),oN(oz(t),r)};return{handlers:{GET:t,POST:t},auth:cr(e,e=>oW(e)),signIn:async(t,r,n)=>{let i=await e(void 0);return oW(i),cu(t,r,n,i)},signOut:async t=>{let r=await e(void 0);return oW(r),cd(t,r)},unstable_update:async t=>{let r=await e(void 0);return oW(r),cf(t,r)}}}oW(e);let t=t=>oN(oz(t),e);return{handlers:{GET:t,POST:t},auth:cr(e),signIn:(t,r,n)=>cu(t,r,n,e),signOut:t=>cd(t,e),unstable_update:t=>cf(t,e)}}({session:{strategy:"jwt"},providers:[{id:"credentials",name:"Credentials",type:"credentials",credentials:{},authorize:()=>null,options:{name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;try{let t=await hz.select().from(hU).where(f$(hU.email,e.email)).limit(1);if(0===t.length)return null;let r=t[0];if(!r.password||!await pn.compare(e.password,r.password))return null;return{id:r.id,email:r.email,name:r.name,role:r.role}}catch(e){return console.error("Authentication error:",e),null}}}}],callbacks:{jwt:async({token:e,user:t})=>(t&&(e.id=t.id,e.role=t.role,e.email=t.email,e.name=t.name),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.id,e.user.role=t.role,e.user.email=t.email,e.user.name=t.name),e)},pages:{signIn:"/auth/signin"}}),pc=pa(e=>{let{pathname:t}=e.nextUrl;if(["/","/search","/auth/signin"].includes(t))return Q.next();if(!e.auth){let r=new URL("/auth/signin",e.url);return r.searchParams.set("callbackUrl",t),Q.redirect(r)}return["/admin"].some(e=>t.startsWith(e))&&e.auth.user?.role!=="admin"?Q.redirect(new URL("/dashboard",e.url)):["/dashboard"].some(e=>t.startsWith(e))&&!["admin","test_checker"].includes(e.auth.user?.role)?Q.redirect(new URL("/auth/signin",e.url)):Q.next()}),pl={matcher:["/((?!api|_next/static|_next/image|favicon.ico).*)"]},pu={...f},pd=pu.middleware||pu.default,pf="/src/middleware";if("function"!=typeof pd)throw Error(`The Middleware "${pf}" must export a \`middleware\` or a \`default\` function`);function ph(e){return ti({...e,page:pf,handler:async(...e)=>{try{return await pd(...e)}catch(i){let t=e[0],r=new URL(t.url),n=r.pathname+r.search;throw await m(i,{path:n,method:t.method,headers:Object.fromEntries(t.headers.entries())},{routerKind:"Pages Router",routePath:"/middleware",routeType:"middleware",revalidateReason:void 0}),i}}})}}},e=>{var t=e(e.s=43);(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_src/middleware"]=t}]);
//# sourceMappingURL=middleware.js.map