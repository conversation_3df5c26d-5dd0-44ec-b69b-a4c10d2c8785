(()=>{var e={};e.id=5199,e.ids=[5199],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},55511:e=>{"use strict";e.exports=require("crypto")},2113:e=>{"use strict";e.exports=import("postgres")},77598:e=>{"use strict";e.exports=require("node:crypto")},49236:(e,t,a)=>{"use strict";a.a(e,async(e,s)=>{try{a.r(t),a.d(t,{patchFetch:()=>c,routeModule:()=>l,serverHooks:()=>m,workAsyncStorage:()=>u,workUnitAsyncStorage:()=>p});var r=a(42706),n=a(28203),i=a(45994),o=a(30924),d=e([o]);o=(d.then?(await d)():d)[0];let l=new r.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/admin/reports/route",pathname:"/api/admin/reports",filename:"route",bundlePath:"app/api/admin/reports/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\api\\admin\\reports\\route.ts",nextConfigOutput:"",userland:o}),{workAsyncStorage:u,workUnitAsyncStorage:p,serverHooks:m}=l;function c(){return(0,i.patchFetch)({workAsyncStorage:u,workUnitAsyncStorage:p})}s()}catch(e){s(e)}})},96487:()=>{},78335:()=>{},30924:(e,t,a)=>{"use strict";a.a(e,async(e,s)=>{try{a.r(t),a.d(t,{POST:()=>p});var r=a(39187),n=a(37702),i=a(62693),o=a(48590),d=a(59252),c=a(47579),l=a(91990),u=e([n,i]);async function p(e){try{let t=await (0,n.j2)();if(!t||t.user?.role!=="admin")return r.NextResponse.json({error:"Unauthorized"},{status:401});let{from:a,to:s}=await e.json(),u=new Date(a),p=new Date(s),m=await i.db.select({count:(0,d.U9)()}).from(o.candidates).where((0,c.Uo)((0,c.RO)(o.candidates.createdAt,u),(0,c.wJ)(o.candidates.createdAt,p))),_=await i.db.select({count:(0,d.U9)()}).from(o.testResults).where((0,c.Uo)((0,c.RO)(o.testResults.createdAt,u),(0,c.wJ)(o.testResults.createdAt,p))),f=await i.db.select({avg:(0,d.Zf)(o.testResults.overallBandScore)}).from(o.testResults).where((0,c.Uo)((0,c.RO)(o.testResults.createdAt,u),(0,c.wJ)(o.testResults.createdAt,p))),g=await i.db.select({count:(0,d.U9)()}).from(o.testResults).where((0,c.Uo)((0,c.eq)(o.testResults.certificateGenerated,!0),(0,c.RO)(o.testResults.createdAt,u),(0,c.wJ)(o.testResults.createdAt,p))),w=await i.db.select({score:o.testResults.overallBandScore,count:(0,d.U9)()}).from(o.testResults).where((0,c.Uo)((0,c.RO)(o.testResults.createdAt,u),(0,c.wJ)(o.testResults.createdAt,p))).groupBy(o.testResults.overallBandScore).orderBy(o.testResults.overallBandScore),y=w.reduce((e,t)=>e+t.count,0),q=w.filter(e=>null!==e.score).map(e=>({score:e.score.toString(),count:e.count,percentage:y>0?e.count/y*100:0})),v=await i.db.select({center:o.candidates.testCenter,candidates:(0,d.U9)(),avgScore:(0,d.Zf)(o.testResults.overallBandScore)}).from(o.candidates).leftJoin(o.testResults,(0,c.eq)(o.candidates.id,o.testResults.candidateId)).where((0,c.Uo)((0,c.RO)(o.candidates.createdAt,u),(0,c.wJ)(o.candidates.createdAt,p))).groupBy(o.candidates.testCenter).orderBy(o.candidates.testCenter),k=await i.db.select({month:(0,l.ll)`TO_CHAR(${o.candidates.createdAt}, 'YYYY-MM')`,candidates:(0,d.U9)(o.candidates.id),results:(0,d.U9)(o.testResults.id),avgScore:(0,d.Zf)(o.testResults.overallBandScore)}).from(o.candidates).leftJoin(o.testResults,(0,c.eq)(o.candidates.id,o.testResults.candidateId)).where((0,c.Uo)((0,c.RO)(o.candidates.createdAt,u),(0,c.wJ)(o.candidates.createdAt,p))).groupBy((0,l.ll)`TO_CHAR(${o.candidates.createdAt}, 'YYYY-MM')`).orderBy((0,l.ll)`TO_CHAR(${o.candidates.createdAt}, 'YYYY-MM')`),h=[];for(let e=6;e>=0;e--){let t=new Date(Date.now()-864e5*e),a=new Date(t.getFullYear(),t.getMonth(),t.getDate()),s=new Date(t.getFullYear(),t.getMonth(),t.getDate()+1),r=await i.db.select({count:(0,d.U9)()}).from(o.candidates).where((0,c.Uo)((0,c.RO)(o.candidates.createdAt,a),(0,c.wJ)(o.candidates.createdAt,s))),n=await i.db.select({count:(0,d.U9)()}).from(o.testResults).where((0,c.Uo)((0,c.RO)(o.testResults.createdAt,a),(0,c.wJ)(o.testResults.createdAt,s)));h.push({date:a.toISOString().split("T")[0],candidates:r[0]?.count||0,results:n[0]?.count||0})}let R={overview:{totalCandidates:m[0]?.count||0,totalResults:_[0]?.count||0,averageBandScore:Number(f[0]?.avg)||0,certificatesGenerated:g[0]?.count||0},monthlyStats:k.map(e=>({month:e.month,candidates:e.candidates,results:e.results,avgScore:Number(e.avgScore)||0})),bandScoreDistribution:q,testCenterStats:v.map(e=>({center:e.center,candidates:e.candidates,avgScore:Number(e.avgScore)||0})),recentActivity:h};return r.NextResponse.json(R)}catch(e){return console.error("Reports error:",e),r.NextResponse.json({error:"Failed to generate reports"},{status:500})}}[n,i]=u.then?(await u)():u,s()}catch(e){s(e)}})},37702:(e,t,a)=>{"use strict";a.a(e,async(e,s)=>{try{a.d(t,{Y9:()=>u,j2:()=>p});var r=a(32221),n=a(31648),i=a(62693),o=a(48590),d=a(47579),c=a(34926),l=e([i]);i=(l.then?(await l)():l)[0];let{handlers:u,auth:p,signIn:m,signOut:_}=(0,r.Ay)({session:{strategy:"jwt"},providers:[(0,n.A)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;try{let t=await i.db.select().from(o.users).where((0,d.eq)(o.users.email,e.email)).limit(1);if(0===t.length)return null;let a=t[0];if(!a.password||!await c.Ay.compare(e.password,a.password))return null;return{id:a.id,email:a.email,name:a.name,role:a.role}}catch(e){return console.error("Authentication error:",e),null}}})],callbacks:{jwt:async({token:e,user:t})=>(t&&(e.role=t.role),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role),e)},pages:{signIn:"/auth/signin"}});s()}catch(e){s(e)}})},62693:(e,t,a)=>{"use strict";a.a(e,async(e,s)=>{try{a.d(t,{db:()=>l});var r=a(10072),n=a(2113),i=a(48590),o=e([n,r]);[n,r]=o.then?(await o)():o;let d=process.env.DATABASE_URL,c=(0,n.default)(d,{prepare:!1}),l=(0,r.f)(c,{schema:i});s()}catch(e){s(e)}})},48590:(e,t,a)=>{"use strict";a.r(t),a.d(t,{accounts:()=>p,aiFeedback:()=>w,candidates:()=>f,sessions:()=>m,testResults:()=>g,users:()=>u,verificationTokens:()=>_});var s=a(87858),r=a(44799),n=a(32590),i=a(9848),o=a(70009),d=a(27390),c=a(32190),l=a(4502);let u=(0,s.cJ)("users",{id:(0,r.Qq)("id").primaryKey().$defaultFn(()=>(0,l.sX)()),name:(0,r.Qq)("name"),email:(0,r.Qq)("email").notNull().unique(),emailVerified:(0,n.vE)("emailVerified",{mode:"date"}),image:(0,r.Qq)("image"),password:(0,r.Qq)("password"),role:(0,r.Qq)("role",{enum:["admin","test_checker"]}).notNull().default("test_checker"),createdAt:(0,n.vE)("created_at").defaultNow().notNull(),updatedAt:(0,n.vE)("updated_at").defaultNow().notNull()}),p=(0,s.cJ)("accounts",{userId:(0,r.Qq)("userId").notNull().references(()=>u.id,{onDelete:"cascade"}),type:(0,r.Qq)("type").notNull(),provider:(0,r.Qq)("provider").notNull(),providerAccountId:(0,r.Qq)("providerAccountId").notNull(),refresh_token:(0,r.Qq)("refresh_token"),access_token:(0,r.Qq)("access_token"),expires_at:(0,i.nd)("expires_at"),token_type:(0,r.Qq)("token_type"),scope:(0,r.Qq)("scope"),id_token:(0,r.Qq)("id_token"),session_state:(0,r.Qq)("session_state")}),m=(0,s.cJ)("sessions",{sessionToken:(0,r.Qq)("sessionToken").primaryKey(),userId:(0,r.Qq)("userId").notNull().references(()=>u.id,{onDelete:"cascade"}),expires:(0,n.vE)("expires",{mode:"date"}).notNull()}),_=(0,s.cJ)("verificationTokens",{identifier:(0,r.Qq)("identifier").notNull(),token:(0,r.Qq)("token").notNull(),expires:(0,n.vE)("expires",{mode:"date"}).notNull()}),f=(0,s.cJ)("candidates",{id:(0,r.Qq)("id").primaryKey().$defaultFn(()=>(0,l.sX)()),fullName:(0,r.Qq)("full_name").notNull(),email:(0,r.Qq)("email").notNull().unique(),phoneNumber:(0,r.Qq)("phone_number").notNull(),dateOfBirth:(0,n.vE)("date_of_birth",{mode:"date"}).notNull(),nationality:(0,r.Qq)("nationality").notNull(),passportNumber:(0,r.Qq)("passport_number").notNull().unique(),testDate:(0,n.vE)("test_date",{mode:"date"}).notNull(),testCenter:(0,r.Qq)("test_center").notNull(),photoUrl:(0,r.Qq)("photo_url"),createdAt:(0,n.vE)("created_at").defaultNow().notNull(),updatedAt:(0,n.vE)("updated_at").defaultNow().notNull()}),g=(0,s.cJ)("test_results",{id:(0,r.Qq)("id").primaryKey().$defaultFn(()=>(0,l.sX)()),candidateId:(0,r.Qq)("candidate_id").notNull().references(()=>f.id,{onDelete:"cascade"}),listeningScore:(0,o._)("listening_score",{precision:3,scale:1}),listeningBandScore:(0,o._)("listening_band_score",{precision:2,scale:1}),readingScore:(0,o._)("reading_score",{precision:3,scale:1}),readingBandScore:(0,o._)("reading_band_score",{precision:2,scale:1}),writingTask1Score:(0,o._)("writing_task1_score",{precision:2,scale:1}),writingTask2Score:(0,o._)("writing_task2_score",{precision:2,scale:1}),writingBandScore:(0,o._)("writing_band_score",{precision:2,scale:1}),speakingFluencyScore:(0,o._)("speaking_fluency_score",{precision:2,scale:1}),speakingLexicalScore:(0,o._)("speaking_lexical_score",{precision:2,scale:1}),speakingGrammarScore:(0,o._)("speaking_grammar_score",{precision:2,scale:1}),speakingPronunciationScore:(0,o._)("speaking_pronunciation_score",{precision:2,scale:1}),speakingBandScore:(0,o._)("speaking_band_score",{precision:2,scale:1}),overallBandScore:(0,o._)("overall_band_score",{precision:2,scale:1}),status:(0,r.Qq)("status",{enum:["pending","completed","verified"]}).notNull().default("pending"),enteredBy:(0,r.Qq)("entered_by").references(()=>u.id),verifiedBy:(0,r.Qq)("verified_by").references(()=>u.id),certificateGenerated:(0,d.zM)("certificate_generated").default(!1),certificateSerial:(0,r.Qq)("certificate_serial").unique(),certificateUrl:(0,r.Qq)("certificate_url"),aiFeedbackGenerated:(0,d.zM)("ai_feedback_generated").default(!1),testDate:(0,n.vE)("test_date",{mode:"date"}),createdAt:(0,n.vE)("created_at").defaultNow().notNull(),updatedAt:(0,n.vE)("updated_at").defaultNow().notNull()}),w=(0,s.cJ)("ai_feedback",{id:(0,r.Qq)("id").primaryKey().$defaultFn(()=>(0,l.sX)()),testResultId:(0,r.Qq)("test_result_id").notNull().references(()=>g.id,{onDelete:"cascade"}),listeningFeedback:(0,r.Qq)("listening_feedback"),readingFeedback:(0,r.Qq)("reading_feedback"),writingFeedback:(0,r.Qq)("writing_feedback"),speakingFeedback:(0,r.Qq)("speaking_feedback"),overallFeedback:(0,r.Qq)("overall_feedback"),studyRecommendations:(0,r.Qq)("study_recommendations"),strengths:(0,c.Pq)("strengths").$type(),weaknesses:(0,c.Pq)("weaknesses").$type(),studyPlan:(0,c.Pq)("study_plan").$type(),generatedAt:(0,n.vE)("generated_at").defaultNow().notNull()})},59252:(e,t,a)=>{"use strict";a.d(t,{U9:()=>r,Zf:()=>n});var s=a(91990);function r(e){return(0,s.ll)`count(${e||s.ll.raw("*")})`.mapWith(Number)}function n(e){return(0,s.ll)`avg(${e})`.mapWith(String)}}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),s=t.X(0,[638,5452,9757,4681],()=>a(49236));module.exports=s})();