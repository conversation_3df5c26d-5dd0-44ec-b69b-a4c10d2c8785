(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3698],{4538:(e,t,s)=>{Promise.resolve().then(s.bind(s,7929))},7401:(e,t,s)=>{"use strict";s.d(t,{A:()=>h});var a=s(2115);let r=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,s)=>s?s.toUpperCase():t.toLowerCase()),d=e=>{let t=l(e);return t.charAt(0).toUpperCase()+t.slice(1)},i=function(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return t.filter((e,t,s)=>!!e&&""!==e.trim()&&s.indexOf(e)===t).join(" ").trim()},c=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var n={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=(0,a.forwardRef)((e,t)=>{let{color:s="currentColor",size:r=24,strokeWidth:l=2,absoluteStrokeWidth:d,className:o="",children:h,iconNode:m,...x}=e;return(0,a.createElement)("svg",{ref:t,...n,width:r,height:r,stroke:s,strokeWidth:d?24*Number(l)/Number(r):l,className:i("lucide",o),...!h&&!c(x)&&{"aria-hidden":"true"},...x},[...m.map(e=>{let[t,s]=e;return(0,a.createElement)(t,s)}),...Array.isArray(h)?h:[h]])}),h=(e,t)=>{let s=(0,a.forwardRef)((s,l)=>{let{className:c,...n}=s;return(0,a.createElement)(o,{ref:l,iconNode:t,className:i("lucide-".concat(r(d(e))),"lucide-".concat(e),c),...n})});return s.displayName=d(e),s}},9136:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(7401).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},6889:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(7401).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},4857:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(7401).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},4081:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(7401).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},853:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(7401).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},6878:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(7401).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},7517:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(7401).A)("user-plus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},2823:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(7401).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},7929:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>g});var a=s(5155),r=s(2115),l=s(8173),d=s.n(l),i=s(2823),c=s(4081),n=s(6889),o=s(6878),h=s(7517),m=s(9136),x=s(853),u=s(4857);function g(){let[e,t]=(0,r.useState)({totalCandidates:0,totalResults:0,pendingResults:0,completedResults:0,recentCandidates:[],recentResults:[]}),[s,l]=(0,r.useState)(!0);(0,r.useEffect)(()=>{g()},[]);let g=async()=>{try{let e=await fetch("/api/admin/dashboard");if(e.ok){let s=await e.json();t(s)}}catch(e){console.error("Error fetching dashboard stats:",e)}finally{l(!1)}},p=[{name:"Total Candidates",value:e.totalCandidates,icon:i.A,color:"bg-blue-500",href:"/admin/candidates"},{name:"Test Results",value:e.totalResults,icon:c.A,color:"bg-green-500",href:"/admin/results"},{name:"Pending Results",value:e.pendingResults,icon:n.A,color:"bg-yellow-500",href:"/admin/results?status=pending"},{name:"Completed Results",value:e.completedResults,icon:o.A,color:"bg-purple-500",href:"/admin/results?status=completed"}],y=[{name:"Add New Candidate",description:"Register a new test candidate",href:"/admin/candidates/new",icon:h.A,color:"bg-blue-600 hover:bg-blue-700"},{name:"View All Results",description:"Browse all test results",href:"/admin/results",icon:m.A,color:"bg-green-600 hover:bg-green-700"},{name:"Search System",description:"Search candidates and results",href:"/admin/search",icon:x.A,color:"bg-purple-600 hover:bg-purple-700"},{name:"Export Data",description:"Download system reports",href:"/admin/export",icon:u.A,color:"bg-gray-600 hover:bg-gray-700"}];return s?(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Admin Dashboard"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Overview of the IELTS Certification System"})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:p.map(e=>{let t=e.icon;return(0,a.jsx)(d(),{href:e.href,className:"bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow",children:(0,a.jsx)("div",{className:"p-5",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"".concat(e.color," p-3 rounded-md"),children:(0,a.jsx)(t,{className:"h-6 w-6 text-white"})})}),(0,a.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,a.jsxs)("dl",{children:[(0,a.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:e.name}),(0,a.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:e.value.toLocaleString()})]})})]})})},e.name)})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-lg font-medium text-gray-900 mb-4",children:"Quick Actions"}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:y.map(e=>{let t=e.icon;return(0,a.jsx)(d(),{href:e.href,className:"".concat(e.color," text-white p-6 rounded-lg shadow hover:shadow-md transition-all"),children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(t,{className:"h-8 w-8 mr-4"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium",children:e.name}),(0,a.jsx)("p",{className:"text-sm opacity-90",children:e.description})]})]})},e.name)})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"bg-white shadow rounded-lg",children:[(0,a.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Recent Candidates"})}),(0,a.jsxs)("div",{className:"p-6",children:[e.recentCandidates.length>0?(0,a.jsx)("div",{className:"space-y-4",children:e.recentCandidates.slice(0,5).map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-gray-900",children:e.fullName}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:e.email})]}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.createdAt?new Date(e.createdAt).toLocaleDateString():"N/A"})]},e.id))}):(0,a.jsx)("p",{className:"text-gray-500 text-center py-4",children:"No recent candidates"}),(0,a.jsx)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:(0,a.jsx)(d(),{href:"/admin/candidates",className:"text-blue-600 hover:text-blue-700 text-sm font-medium",children:"View all candidates →"})})]})]}),(0,a.jsxs)("div",{className:"bg-white shadow rounded-lg",children:[(0,a.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Recent Test Results"})}),(0,a.jsxs)("div",{className:"p-6",children:[e.recentResults.length>0?(0,a.jsx)("div",{className:"space-y-4",children:e.recentResults.slice(0,5).map(e=>{var t;return(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-gray-900",children:null===(t=e.candidate)||void 0===t?void 0:t.fullName}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["Overall: ",e.overallBandScore||"Pending"]})]}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.createdAt?new Date(e.createdAt).toLocaleDateString():"N/A"})]},e.id)})}):(0,a.jsx)("p",{className:"text-gray-500 text-center py-4",children:"No recent results"}),(0,a.jsx)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:(0,a.jsx)(d(),{href:"/admin/results",className:"text-blue-600 hover:text-blue-700 text-sm font-medium",children:"View all results →"})})]})]})]})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[8173,8441,1517,7358],()=>t(4538)),_N_E=e.O()}]);