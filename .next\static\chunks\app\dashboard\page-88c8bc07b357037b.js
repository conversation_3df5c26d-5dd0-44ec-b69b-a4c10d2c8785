(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5105],{2821:(e,s,t)=>{Promise.resolve().then(t.bind(t,3686))},7401:(e,s,t)=>{"use strict";t.d(s,{A:()=>h});var r=t(2115);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,s,t)=>t?t.toUpperCase():s.toLowerCase()),d=e=>{let s=l(e);return s.charAt(0).toUpperCase()+s.slice(1)},i=function(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return s.filter((e,s,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===s).join(" ").trim()},c=e=>{for(let s in e)if(s.startsWith("aria-")||"role"===s||"title"===s)return!0};var n={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=(0,r.forwardRef)((e,s)=>{let{color:t="currentColor",size:a=24,strokeWidth:l=2,absoluteStrokeWidth:d,className:o="",children:h,iconNode:m,...u}=e;return(0,r.createElement)("svg",{ref:s,...n,width:a,height:a,stroke:t,strokeWidth:d?24*Number(l)/Number(a):l,className:i("lucide",o),...!h&&!c(u)&&{"aria-hidden":"true"},...u},[...m.map(e=>{let[s,t]=e;return(0,r.createElement)(s,t)}),...Array.isArray(h)?h:[h]])}),h=(e,s)=>{let t=(0,r.forwardRef)((t,l)=>{let{className:c,...n}=t;return(0,r.createElement)(o,{ref:l,iconNode:s,className:i("lucide-".concat(a(d(e))),"lucide-".concat(e),c),...n})});return t.displayName=d(e),t}},8207:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(7401).A)("brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]])},9136:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(7401).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},1594:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(7401).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},3239:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(7401).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},4836:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(7401).A)("clipboard-list",[["rect",{width:"8",height:"4",x:"8",y:"2",rx:"1",ry:"1",key:"tgr4d6"}],["path",{d:"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2",key:"116196"}],["path",{d:"M12 11h4",key:"1jrz19"}],["path",{d:"M12 16h4",key:"n85exb"}],["path",{d:"M8 11h.01",key:"1dfujw"}],["path",{d:"M8 16h.01",key:"18s6g9"}]])},6889:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(7401).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},4081:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(7401).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},853:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(7401).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},3686:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>p});var r=t(5155),a=t(2115),l=t(8173),d=t.n(l),i=t(4836),c=t(6889),n=t(3239),o=t(853),h=t(9136),m=t(8207),u=t(1594),x=t(4081);function p(){let[e,s]=(0,a.useState)({totalResultsEntered:0,pendingResults:0,completedResults:0,recentResults:[]}),[t,l]=(0,a.useState)(!0);(0,a.useEffect)(()=>{p()},[]);let p=async()=>{try{let e=await fetch("/api/checker/dashboard");if(e.ok){let t=await e.json();s(t)}}catch(e){console.error("Error fetching checker stats:",e)}finally{l(!1)}},g=[{name:"Results Entered",value:e.totalResultsEntered,icon:i.A,color:"bg-blue-500",href:"/dashboard/results/list"},{name:"Pending Review",value:e.pendingResults,icon:c.A,color:"bg-yellow-500",href:"/dashboard/results/list?status=pending"},{name:"Completed",value:e.completedResults,icon:n.A,color:"bg-green-500",href:"/dashboard/results/list?status=completed"}],y=[{name:"Search Candidates",description:"Find candidates to enter results",href:"/dashboard/search",icon:o.A,color:"bg-blue-600 hover:bg-blue-700"},{name:"Enter Test Results",description:"Add new test scores",href:"/dashboard/results",icon:i.A,color:"bg-green-600 hover:bg-green-700"},{name:"View All Results",description:"Browse entered results",href:"/dashboard/results/list",icon:h.A,color:"bg-purple-600 hover:bg-purple-700"},{name:"AI Feedback",description:"Generate AI feedback",href:"/dashboard/feedback",icon:m.A,color:"bg-indigo-600 hover:bg-indigo-700"}];return t?(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Test Checker Dashboard"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Manage IELTS test results and candidate information"})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:g.map(e=>{let s=e.icon;return(0,r.jsx)(d(),{href:e.href,className:"bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow",children:(0,r.jsx)("div",{className:"p-5",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"".concat(e.color," p-3 rounded-md"),children:(0,r.jsx)(s,{className:"h-6 w-6 text-white"})})}),(0,r.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,r.jsxs)("dl",{children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:e.name}),(0,r.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:e.value.toLocaleString()})]})})]})})},e.name)})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-lg font-medium text-gray-900 mb-4",children:"Quick Actions"}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:y.map(e=>{let s=e.icon;return(0,r.jsx)(d(),{href:e.href,className:"".concat(e.color," text-white p-6 rounded-lg shadow hover:shadow-md transition-all"),children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(s,{className:"h-8 w-8 mr-4"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-medium",children:e.name}),(0,r.jsx)("p",{className:"text-sm opacity-90",children:e.description})]})]})},e.name)})})]}),(0,r.jsxs)("div",{className:"bg-white shadow rounded-lg",children:[(0,r.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Recent Test Results"})}),(0,r.jsxs)("div",{className:"p-6",children:[e.recentResults.length>0?(0,r.jsx)("div",{className:"space-y-4",children:e.recentResults.slice(0,5).map(e=>{var s;return(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border border-gray-200 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:"completed"===e.status?(0,r.jsx)(n.A,{className:"h-8 w-8 text-green-500"}):"pending"===e.status?(0,r.jsx)(c.A,{className:"h-8 w-8 text-yellow-500"}):(0,r.jsx)(u.A,{className:"h-8 w-8 text-red-500"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"font-medium text-gray-900",children:null===(s=e.candidate)||void 0===s?void 0:s.fullName}),(0,r.jsxs)("p",{className:"text-sm text-gray-500",children:["Overall Band: ",e.overallBandScore||"Pending"]})]})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)("p",{className:"text-sm text-gray-500",children:new Date(e.createdAt).toLocaleDateString()}),(0,r.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat("completed"===e.status?"bg-green-100 text-green-800":"pending"===e.status?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"),children:e.status})]})]},e.id)})}):(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)(x.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-500",children:"No recent results"}),(0,r.jsx)(d(),{href:"/dashboard/results",className:"mt-2 inline-flex items-center text-blue-600 hover:text-blue-700",children:"Enter your first result →"})]}),e.recentResults.length>0&&(0,r.jsx)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:(0,r.jsx)(d(),{href:"/dashboard/results/list",className:"text-blue-600 hover:text-blue-700 text-sm font-medium",children:"View all results →"})})]})]}),(0,r.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-blue-900 mb-4",children:"Test Checker Guidelines"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-800",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium mb-2",children:"Scoring Guidelines:"}),(0,r.jsxs)("ul",{className:"space-y-1",children:[(0,r.jsx)("li",{children:"• Listening: 0-40 raw score → 1-9 band score"}),(0,r.jsx)("li",{children:"• Reading: 0-40 raw score → 1-9 band score"}),(0,r.jsx)("li",{children:"• Writing: Direct band scores (1-9)"}),(0,r.jsx)("li",{children:"• Speaking: Direct band scores (1-9)"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium mb-2",children:"Quality Assurance:"}),(0,r.jsxs)("ul",{className:"space-y-1",children:[(0,r.jsx)("li",{children:"• Double-check all scores before submission"}),(0,r.jsx)("li",{children:"• Ensure candidate details match test papers"}),(0,r.jsx)("li",{children:"• Generate AI feedback for improvement areas"}),(0,r.jsx)("li",{children:"• Mark results as completed when verified"})]})]})]})]})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[8173,8441,1517,7358],()=>s(2821)),_N_E=e.O()}]);