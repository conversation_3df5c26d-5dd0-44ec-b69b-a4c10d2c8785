import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { testResults, candidates } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: resultId } = await params;

    // Validate result ID format
    if (!resultId || isNaN(Number(resultId))) {
      return NextResponse.json(
        { error: 'Invalid result ID format' },
        { status: 400 }
      );
    }

    // Get test result with candidate info - PUBLIC ACCESS (no authentication required)
    const result = await db
      .select({
        id: testResults.id,
        candidateId: testResults.candidateId,
        testDate: testResults.testDate,
        listeningScore: testResults.listeningScore,
        listeningBandScore: testResults.listeningBandScore,
        readingScore: testResults.readingScore,
        readingBandScore: testResults.readingBandScore,
        writingTask1Score: testResults.writingTask1Score,
        writingTask2Score: testResults.writingTask2Score,
        writingBandScore: testResults.writingBandScore,
        speakingFluencyScore: testResults.speakingFluencyScore,
        speakingLexicalScore: testResults.speakingLexicalScore,
        speakingGrammarScore: testResults.speakingGrammarScore,
        speakingPronunciationScore: testResults.speakingPronunciationScore,
        speakingBandScore: testResults.speakingBandScore,
        overallBandScore: testResults.overallBandScore,
        status: testResults.status,
        certificateSerial: testResults.certificateSerial,
        certificateGenerated: testResults.certificateGenerated,
        aiFeedbackGenerated: testResults.aiFeedbackGenerated,
        createdAt: testResults.createdAt,
        updatedAt: testResults.updatedAt,
        candidate: {
          fullName: candidates.fullName,
          nationality: candidates.nationality,
          testDate: candidates.testDate,
          testCenter: candidates.testCenter,
          photoUrl: candidates.photoUrl,
        },
      })
      .from(testResults)
      .innerJoin(candidates, eq(testResults.candidateId, candidates.id))
      .where(eq(testResults.id, resultId))
      .limit(1);

    if (!result.length) {
      return NextResponse.json(
        { error: 'Test result not found' },
        { status: 404 }
      );
    }

    const testResult = result[0];

    // Only return results that are completed or verified (not pending)
    if (testResult.status === 'pending') {
      return NextResponse.json(
        { error: 'Test result is not yet available' },
        { status: 403 }
      );
    }

    // Calculate performance metrics
    const scores = [
      testResult.listeningBandScore,
      testResult.readingBandScore,
      testResult.writingBandScore,
      testResult.speakingBandScore
    ].filter(score => score !== null) as number[];

    const performanceMetrics = {
      averageScore: scores.length > 0 ? scores.reduce((a, b) => a + b, 0) / scores.length : null,
      highestScore: scores.length > 0 ? Math.max(...scores) : null,
      lowestScore: scores.length > 0 ? Math.min(...scores) : null,
      scoreDistribution: {
        listening: testResult.listeningBandScore,
        reading: testResult.readingBandScore,
        writing: testResult.writingBandScore,
        speaking: testResult.speakingBandScore
      }
    };

    return NextResponse.json({
      ...testResult,
      performanceMetrics
    });

  } catch (error) {
    console.error('Error fetching public test result:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
