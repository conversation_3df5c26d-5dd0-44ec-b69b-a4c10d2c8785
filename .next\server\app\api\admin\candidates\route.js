(()=>{var e={};e.id=2304,e.ids=[2304],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},55511:e=>{"use strict";e.exports=require("crypto")},2113:e=>{"use strict";e.exports=import("postgres")},77598:e=>{"use strict";e.exports=require("node:crypto")},22182:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{patchFetch:()=>l,routeModule:()=>c,serverHooks:()=>m,workAsyncStorage:()=>u,workUnitAsyncStorage:()=>p});var s=r(42706),n=r(28203),i=r(45994),o=r(61025),d=e([o]);o=(d.then?(await d)():d)[0];let c=new s.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/admin/candidates/route",pathname:"/api/admin/candidates",filename:"route",bundlePath:"app/api/admin/candidates/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\api\\admin\\candidates\\route.ts",nextConfigOutput:"",userland:o}),{workAsyncStorage:u,workUnitAsyncStorage:p,serverHooks:m}=c;function l(){return(0,i.patchFetch)({workAsyncStorage:u,workUnitAsyncStorage:p})}a()}catch(e){a(e)}})},96487:()=>{},78335:()=>{},61025:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{GET:()=>p,POST:()=>m});var s=r(39187),n=r(37702),i=r(62693),o=r(48590),d=r(47579),l=r(59252),c=r(92489),u=e([n,i]);async function p(e){try{let t=await (0,n.j2)();if(!t||t.user?.role!=="admin")return s.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:r}=new URL(e.url),a=parseInt(r.get("page")||"1"),u=parseInt(r.get("limit")||"20"),p=r.get("search")||"",m=p?(0,d.or)((0,d.B3)(o.candidates.fullName,`%${p}%`),(0,d.B3)(o.candidates.email,`%${p}%`),(0,d.B3)(o.candidates.passportNumber,`%${p}%`)):void 0,f=await i.db.select({count:(0,l.U9)()}).from(o.candidates).where(m),_=f[0]?.count||0,g=await i.db.select().from(o.candidates).where(m).orderBy((0,c.i)(o.candidates.createdAt)).limit(u).offset((a-1)*u);return s.NextResponse.json({candidates:g,total:_,page:a,limit:u,totalPages:Math.ceil(_/u)})}catch(e){return console.error("Error fetching candidates:",e),s.NextResponse.json({error:"Internal server error"},{status:500})}}async function m(e){try{let t=await (0,n.j2)();if(!t||t.user?.role!=="admin")return s.NextResponse.json({error:"Unauthorized"},{status:401});let r=await e.json();for(let e of["fullName","email","phoneNumber","dateOfBirth","nationality","passportNumber","testDate","testCenter"])if(!r[e])return s.NextResponse.json({error:`${e} is required`},{status:400});let a=await i.db.insert(o.candidates).values({fullName:r.fullName,email:r.email,phoneNumber:r.phoneNumber,dateOfBirth:new Date(r.dateOfBirth),nationality:r.nationality,passportNumber:r.passportNumber,testDate:new Date(r.testDate),testCenter:r.testCenter,photoUrl:r.photoUrl}).returning();return s.NextResponse.json(a[0],{status:201})}catch(e){if(console.error("Error creating candidate:",e),e instanceof Error&&e.message.includes("unique")){if(e.message.includes("email"))return s.NextResponse.json({error:"Email address already exists"},{status:409});if(e.message.includes("passport"))return s.NextResponse.json({error:"Passport number already exists"},{status:409})}return s.NextResponse.json({error:"Internal server error"},{status:500})}}[n,i]=u.then?(await u)():u,a()}catch(e){a(e)}})},37702:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.d(t,{Y9:()=>u,j2:()=>p});var s=r(32221),n=r(31648),i=r(62693),o=r(48590),d=r(47579),l=r(34926),c=e([i]);i=(c.then?(await c)():c)[0];let{handlers:u,auth:p,signIn:m,signOut:f}=(0,s.Ay)({session:{strategy:"jwt"},providers:[(0,n.A)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;try{let t=await i.db.select().from(o.users).where((0,d.eq)(o.users.email,e.email)).limit(1);if(0===t.length)return null;let r=t[0];if(!r.password||!await l.Ay.compare(e.password,r.password))return null;return{id:r.id,email:r.email,name:r.name,role:r.role}}catch(e){return console.error("Authentication error:",e),null}}})],callbacks:{jwt:async({token:e,user:t})=>(t&&(e.id=t.id,e.role=t.role,e.email=t.email,e.name=t.name),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.id,e.user.role=t.role,e.user.email=t.email,e.user.name=t.name),e)},pages:{signIn:"/auth/signin"}});a()}catch(e){a(e)}})},62693:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.d(t,{db:()=>c});var s=r(10072),n=r(2113),i=r(48590),o=e([n,s]);[n,s]=o.then?(await o)():o;let d=process.env.DATABASE_URL,l=(0,n.default)(d,{prepare:!1}),c=(0,s.f)(l,{schema:i});a()}catch(e){a(e)}})},48590:(e,t,r)=>{"use strict";r.r(t),r.d(t,{accounts:()=>p,aiFeedback:()=>y,candidates:()=>_,sessions:()=>m,testResults:()=>g,users:()=>u,verificationTokens:()=>f});var a=r(87858),s=r(44799),n=r(32590),i=r(9848),o=r(70009),d=r(27390),l=r(32190),c=r(4502);let u=(0,a.cJ)("users",{id:(0,s.Qq)("id").primaryKey().$defaultFn(()=>(0,c.sX)()),name:(0,s.Qq)("name"),email:(0,s.Qq)("email").notNull().unique(),emailVerified:(0,n.vE)("emailVerified",{mode:"date"}),image:(0,s.Qq)("image"),password:(0,s.Qq)("password"),role:(0,s.Qq)("role",{enum:["admin","test_checker"]}).notNull().default("test_checker"),createdAt:(0,n.vE)("created_at").defaultNow().notNull(),updatedAt:(0,n.vE)("updated_at").defaultNow().notNull()}),p=(0,a.cJ)("accounts",{userId:(0,s.Qq)("userId").notNull().references(()=>u.id,{onDelete:"cascade"}),type:(0,s.Qq)("type").notNull(),provider:(0,s.Qq)("provider").notNull(),providerAccountId:(0,s.Qq)("providerAccountId").notNull(),refresh_token:(0,s.Qq)("refresh_token"),access_token:(0,s.Qq)("access_token"),expires_at:(0,i.nd)("expires_at"),token_type:(0,s.Qq)("token_type"),scope:(0,s.Qq)("scope"),id_token:(0,s.Qq)("id_token"),session_state:(0,s.Qq)("session_state")}),m=(0,a.cJ)("sessions",{sessionToken:(0,s.Qq)("sessionToken").primaryKey(),userId:(0,s.Qq)("userId").notNull().references(()=>u.id,{onDelete:"cascade"}),expires:(0,n.vE)("expires",{mode:"date"}).notNull()}),f=(0,a.cJ)("verificationTokens",{identifier:(0,s.Qq)("identifier").notNull(),token:(0,s.Qq)("token").notNull(),expires:(0,n.vE)("expires",{mode:"date"}).notNull()}),_=(0,a.cJ)("candidates",{id:(0,s.Qq)("id").primaryKey().$defaultFn(()=>(0,c.sX)()),fullName:(0,s.Qq)("full_name").notNull(),email:(0,s.Qq)("email").notNull().unique(),phoneNumber:(0,s.Qq)("phone_number").notNull(),dateOfBirth:(0,n.vE)("date_of_birth",{mode:"date"}).notNull(),nationality:(0,s.Qq)("nationality").notNull(),passportNumber:(0,s.Qq)("passport_number").notNull().unique(),testDate:(0,n.vE)("test_date",{mode:"date"}).notNull(),testCenter:(0,s.Qq)("test_center").notNull(),photoUrl:(0,s.Qq)("photo_url"),createdAt:(0,n.vE)("created_at").defaultNow().notNull(),updatedAt:(0,n.vE)("updated_at").defaultNow().notNull()}),g=(0,a.cJ)("test_results",{id:(0,s.Qq)("id").primaryKey().$defaultFn(()=>(0,c.sX)()),candidateId:(0,s.Qq)("candidate_id").notNull().references(()=>_.id,{onDelete:"cascade"}),listeningScore:(0,o._)("listening_score",{precision:3,scale:1}),listeningBandScore:(0,o._)("listening_band_score",{precision:2,scale:1}),readingScore:(0,o._)("reading_score",{precision:3,scale:1}),readingBandScore:(0,o._)("reading_band_score",{precision:2,scale:1}),writingTask1Score:(0,o._)("writing_task1_score",{precision:2,scale:1}),writingTask2Score:(0,o._)("writing_task2_score",{precision:2,scale:1}),writingBandScore:(0,o._)("writing_band_score",{precision:2,scale:1}),speakingFluencyScore:(0,o._)("speaking_fluency_score",{precision:2,scale:1}),speakingLexicalScore:(0,o._)("speaking_lexical_score",{precision:2,scale:1}),speakingGrammarScore:(0,o._)("speaking_grammar_score",{precision:2,scale:1}),speakingPronunciationScore:(0,o._)("speaking_pronunciation_score",{precision:2,scale:1}),speakingBandScore:(0,o._)("speaking_band_score",{precision:2,scale:1}),overallBandScore:(0,o._)("overall_band_score",{precision:2,scale:1}),status:(0,s.Qq)("status",{enum:["pending","completed","verified"]}).notNull().default("pending"),enteredBy:(0,s.Qq)("entered_by").references(()=>u.id),verifiedBy:(0,s.Qq)("verified_by").references(()=>u.id),certificateGenerated:(0,d.zM)("certificate_generated").default(!1),certificateSerial:(0,s.Qq)("certificate_serial").unique(),certificateUrl:(0,s.Qq)("certificate_url"),aiFeedbackGenerated:(0,d.zM)("ai_feedback_generated").default(!1),testDate:(0,n.vE)("test_date",{mode:"date"}),createdAt:(0,n.vE)("created_at").defaultNow().notNull(),updatedAt:(0,n.vE)("updated_at").defaultNow().notNull()}),y=(0,a.cJ)("ai_feedback",{id:(0,s.Qq)("id").primaryKey().$defaultFn(()=>(0,c.sX)()),testResultId:(0,s.Qq)("test_result_id").notNull().references(()=>g.id,{onDelete:"cascade"}),listeningFeedback:(0,s.Qq)("listening_feedback"),readingFeedback:(0,s.Qq)("reading_feedback"),writingFeedback:(0,s.Qq)("writing_feedback"),speakingFeedback:(0,s.Qq)("speaking_feedback"),overallFeedback:(0,s.Qq)("overall_feedback"),studyRecommendations:(0,s.Qq)("study_recommendations"),strengths:(0,l.Pq)("strengths").$type(),weaknesses:(0,l.Pq)("weaknesses").$type(),studyPlan:(0,l.Pq)("study_plan").$type(),generatedAt:(0,n.vE)("generated_at").defaultNow().notNull()})},59252:(e,t,r)=>{"use strict";r.d(t,{U9:()=>s,Zf:()=>n});var a=r(91990);function s(e){return(0,a.ll)`count(${e||a.ll.raw("*")})`.mapWith(Number)}function n(e){return(0,a.ll)`avg(${e})`.mapWith(String)}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[638,5452,9757,4681],()=>r(22182));module.exports=a})();