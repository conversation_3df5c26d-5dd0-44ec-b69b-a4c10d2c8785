(()=>{var e={};e.id=4067,e.ids=[4067],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},87730:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var t=s(70260),a=s(28203),n=s(25155),i=s.n(n),o=s(67292),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(r,l);let d=["",{children:["dashboard",{children:["results",{children:["[id]",{children:["edit",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,20792)),"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\dashboard\\results\\[id]\\edit\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,33405)),"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,71354)),"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\dashboard\\results\\[id]\\edit\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/results/[id]/edit/page",pathname:"/dashboard/results/[id]/edit",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},48700:(e,r,s)=>{Promise.resolve().then(s.bind(s,20792))},35132:(e,r,s)=>{Promise.resolve().then(s.bind(s,53684))},41680:(e,r,s)=>{"use strict";s.d(r,{A:()=>u});var t=s(58009);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),n=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,r,s)=>s?s.toUpperCase():r.toLowerCase()),i=e=>{let r=n(e);return r.charAt(0).toUpperCase()+r.slice(1)},o=(...e)=>e.filter((e,r,s)=>!!e&&""!==e.trim()&&s.indexOf(e)===r).join(" ").trim(),l=e=>{for(let r in e)if(r.startsWith("aria-")||"role"===r||"title"===r)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,t.forwardRef)(({color:e="currentColor",size:r=24,strokeWidth:s=2,absoluteStrokeWidth:a,className:n="",children:i,iconNode:c,...u},m)=>(0,t.createElement)("svg",{ref:m,...d,width:r,height:r,stroke:e,strokeWidth:a?24*Number(s)/Number(r):s,className:o("lucide",n),...!i&&!l(u)&&{"aria-hidden":"true"},...u},[...c.map(([e,r])=>(0,t.createElement)(e,r)),...Array.isArray(i)?i:[i]])),u=(e,r)=>{let s=(0,t.forwardRef)(({className:s,...n},l)=>(0,t.createElement)(c,{ref:l,iconNode:r,className:o(`lucide-${a(i(e))}`,`lucide-${e}`,s),...n}));return s.displayName=i(e),s}},67497:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(41680).A)("calculator",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",key:"1nb95v"}],["line",{x1:"8",x2:"16",y1:"6",y2:"6",key:"x4nwl0"}],["line",{x1:"16",x2:"16",y1:"14",y2:"18",key:"wjye3r"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M8 14h.01",key:"6423bh"}],["path",{d:"M12 18h.01",key:"mhygvu"}],["path",{d:"M8 18h.01",key:"lrp35t"}]])},45037:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(41680).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},61075:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(41680).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},33680:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(41680).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},79334:(e,r,s)=>{"use strict";var t=s(58686);s.o(t,"useParams")&&s.d(r,{useParams:function(){return t.useParams}}),s.o(t,"useRouter")&&s.d(r,{useRouter:function(){return t.useRouter}}),s.o(t,"useSearchParams")&&s.d(r,{useSearchParams:function(){return t.useSearchParams}})},53684:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>b});var t=s(45512),a=s(58009),n=s(79334),i=s(28531),o=s.n(i),l=s(45037),d=s(35668),c=s(87798),u=s(64290),m=s(4269),x=s(42417),p=s(18814),g=s(67497),h=s(33680);function b(){let e=(0,n.useParams)(),r=(0,n.useRouter)(),s=e.id,[i,b]=(0,a.useState)(null),[f,y]=(0,a.useState)(!0),[S,v]=(0,a.useState)(!1),[k,j]=(0,a.useState)(""),[w,N]=(0,a.useState)({listeningScore:"",listeningBandScore:"",readingScore:"",readingBandScore:"",writingTask1Score:"",writingTask2Score:"",writingBandScore:"",speakingFluencyScore:"",speakingLexicalScore:"",speakingGrammarScore:"",speakingPronunciationScore:"",speakingBandScore:"",overallBandScore:"",status:"pending"});(0,a.useCallback)(async()=>{try{let e=await fetch(`/api/checker/results/${s}`);if(e.ok){let r=await e.json();b(r),N({listeningScore:r.listeningScore?.toString()||"",listeningBandScore:r.listeningBandScore?.toString()||"",readingScore:r.readingScore?.toString()||"",readingBandScore:r.readingBandScore?.toString()||"",writingTask1Score:r.writingTask1Score?.toString()||"",writingTask2Score:r.writingTask2Score?.toString()||"",writingBandScore:r.writingBandScore?.toString()||"",speakingFluencyScore:r.speakingFluencyScore?.toString()||"",speakingLexicalScore:r.speakingLexicalScore?.toString()||"",speakingGrammarScore:r.speakingGrammarScore?.toString()||"",speakingPronunciationScore:r.speakingPronunciationScore?.toString()||"",speakingBandScore:r.speakingBandScore?.toString()||"",overallBandScore:r.overallBandScore?.toString()||"",status:r.status})}else j("Result not found")}catch(e){console.error("Error fetching result:",e),j("Failed to load result")}finally{y(!1)}},[s]);let C=e=>{let{name:r,value:s}=e.target;N(e=>({...e,[r]:s}))};(0,a.useCallback)(()=>{let{listeningBandScore:e,readingBandScore:r,writingBandScore:s,speakingBandScore:t}=w;if(e&&r&&s&&t){let a=Math.round(2*((parseFloat(e)+parseFloat(r)+parseFloat(s)+parseFloat(t))/4))/2;N(e=>({...e,overallBandScore:a.toString()}))}},[w]);let B=async e=>{e.preventDefault(),v(!0),j("");try{let e=await fetch(`/api/checker/results/${s}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(w)});if(e.ok)r.push(`/dashboard/results/${s}`);else{let r=await e.json();j(r.error||"Failed to update results")}}catch{j("An error occurred. Please try again.")}finally{v(!1)}};return f?(0,t.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):k&&!i?(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)(l.A,{className:"h-12 w-12 text-red-400 mx-auto mb-4"}),(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Error Loading Result"}),(0,t.jsx)("p",{className:"text-gray-600",children:k}),(0,t.jsxs)(o(),{href:"/dashboard/results/list",className:"mt-4 inline-flex items-center text-blue-600 hover:text-blue-700",children:[(0,t.jsx)(d.A,{className:"h-4 w-4 mr-1"}),"Back to Results"]})]}):(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("div",{className:"flex items-center justify-between",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(o(),{href:`/dashboard/results/${s}`,className:"mr-4 p-2 text-gray-400 hover:text-gray-600",children:(0,t.jsx)(d.A,{className:"h-5 w-5"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Edit Test Results"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Update IELTS test scores"})]})]})}),i&&(0,t.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(c.A,{className:"h-5 w-5 text-blue-600 mr-2"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium text-blue-900",children:i.candidate.fullName}),(0,t.jsxs)("p",{className:"text-sm text-blue-700",children:[i.candidate.passportNumber," • Test Date: ",new Date(i.candidate.testDate).toLocaleDateString()]})]})]})}),(0,t.jsx)("div",{className:"bg-white shadow rounded-lg",children:(0,t.jsxs)("form",{onSubmit:B,className:"p-6 space-y-8",children:[k&&(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm",children:k}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"status",className:"block text-sm font-medium text-gray-700 mb-2",children:"Result Status"}),(0,t.jsxs)("select",{id:"status",name:"status",value:w.status,onChange:C,className:"w-full max-w-xs px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,t.jsx)("option",{value:"pending",children:"Pending"}),(0,t.jsx)("option",{value:"completed",children:"Completed"}),(0,t.jsx)("option",{value:"verified",children:"Verified"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("h3",{className:"text-lg font-medium text-gray-900 mb-4 flex items-center",children:[(0,t.jsx)(u.A,{className:"h-5 w-5 mr-2 text-blue-600"}),"Listening"]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"listeningScore",className:"block text-sm font-medium text-gray-700 mb-2",children:"Raw Score (0-40)"}),(0,t.jsx)("input",{type:"number",id:"listeningScore",name:"listeningScore",value:w.listeningScore,onChange:C,min:"0",max:"40",step:"1",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"listeningBandScore",className:"block text-sm font-medium text-gray-700 mb-2",children:"Band Score (1-9)"}),(0,t.jsx)("input",{type:"number",id:"listeningBandScore",name:"listeningBandScore",value:w.listeningBandScore,onChange:C,min:"1",max:"9",step:"0.5",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("h3",{className:"text-lg font-medium text-gray-900 mb-4 flex items-center",children:[(0,t.jsx)(m.A,{className:"h-5 w-5 mr-2 text-green-600"}),"Reading"]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"readingScore",className:"block text-sm font-medium text-gray-700 mb-2",children:"Raw Score (0-40)"}),(0,t.jsx)("input",{type:"number",id:"readingScore",name:"readingScore",value:w.readingScore,onChange:C,min:"0",max:"40",step:"1",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"readingBandScore",className:"block text-sm font-medium text-gray-700 mb-2",children:"Band Score (1-9)"}),(0,t.jsx)("input",{type:"number",id:"readingBandScore",name:"readingBandScore",value:w.readingBandScore,onChange:C,min:"1",max:"9",step:"0.5",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("h3",{className:"text-lg font-medium text-gray-900 mb-4 flex items-center",children:[(0,t.jsx)(x.A,{className:"h-5 w-5 mr-2 text-purple-600"}),"Writing"]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"writingTask1Score",className:"block text-sm font-medium text-gray-700 mb-2",children:"Task 1 Score (1-9)"}),(0,t.jsx)("input",{type:"number",id:"writingTask1Score",name:"writingTask1Score",value:w.writingTask1Score,onChange:C,min:"1",max:"9",step:"0.5",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"writingTask2Score",className:"block text-sm font-medium text-gray-700 mb-2",children:"Task 2 Score (1-9)"}),(0,t.jsx)("input",{type:"number",id:"writingTask2Score",name:"writingTask2Score",value:w.writingTask2Score,onChange:C,min:"1",max:"9",step:"0.5",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"writingBandScore",className:"block text-sm font-medium text-gray-700 mb-2",children:"Overall Band Score (1-9)"}),(0,t.jsx)("input",{type:"number",id:"writingBandScore",name:"writingBandScore",value:w.writingBandScore,onChange:C,min:"1",max:"9",step:"0.5",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("h3",{className:"text-lg font-medium text-gray-900 mb-4 flex items-center",children:[(0,t.jsx)(p.A,{className:"h-5 w-5 mr-2 text-red-600"}),"Speaking"]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"speakingFluencyScore",className:"block text-sm font-medium text-gray-700 mb-2",children:"Fluency & Coherence"}),(0,t.jsx)("input",{type:"number",id:"speakingFluencyScore",name:"speakingFluencyScore",value:w.speakingFluencyScore,onChange:C,min:"1",max:"9",step:"0.5",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"speakingLexicalScore",className:"block text-sm font-medium text-gray-700 mb-2",children:"Lexical Resource"}),(0,t.jsx)("input",{type:"number",id:"speakingLexicalScore",name:"speakingLexicalScore",value:w.speakingLexicalScore,onChange:C,min:"1",max:"9",step:"0.5",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"speakingGrammarScore",className:"block text-sm font-medium text-gray-700 mb-2",children:"Grammar & Accuracy"}),(0,t.jsx)("input",{type:"number",id:"speakingGrammarScore",name:"speakingGrammarScore",value:w.speakingGrammarScore,onChange:C,min:"1",max:"9",step:"0.5",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"speakingPronunciationScore",className:"block text-sm font-medium text-gray-700 mb-2",children:"Pronunciation"}),(0,t.jsx)("input",{type:"number",id:"speakingPronunciationScore",name:"speakingPronunciationScore",value:w.speakingPronunciationScore,onChange:C,min:"1",max:"9",step:"0.5",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"speakingBandScore",className:"block text-sm font-medium text-gray-700 mb-2",children:"Overall Band Score"}),(0,t.jsx)("input",{type:"number",id:"speakingBandScore",name:"speakingBandScore",value:w.speakingBandScore,onChange:C,min:"1",max:"9",step:"0.5",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]})]})]}),(0,t.jsxs)("div",{className:"bg-gray-50 p-6 rounded-lg",children:[(0,t.jsxs)("h3",{className:"text-lg font-medium text-gray-900 mb-4 flex items-center",children:[(0,t.jsx)(g.A,{className:"h-5 w-5 mr-2 text-indigo-600"}),"Overall Band Score"]}),(0,t.jsxs)("div",{className:"max-w-xs",children:[(0,t.jsx)("label",{htmlFor:"overallBandScore",className:"block text-sm font-medium text-gray-700 mb-2",children:"Calculated Overall Score"}),(0,t.jsx)("input",{type:"number",id:"overallBandScore",name:"overallBandScore",value:w.overallBandScore,onChange:C,min:"1",max:"9",step:"0.5",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white",readOnly:!0}),(0,t.jsx)("p",{className:"mt-1 text-xs text-gray-500",children:"Automatically calculated from individual band scores"})]})]}),(0,t.jsxs)("div",{className:"flex justify-end space-x-4 pt-6 border-t border-gray-200",children:[(0,t.jsx)(o(),{href:`/dashboard/results/${s}`,className:"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50",children:"Cancel"}),(0,t.jsx)("button",{type:"submit",disabled:S,className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50",children:S?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Saving..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Save Changes"]})})]})]})})]})}},20792:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\[id]\\\\edit\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\codes\\IELTS-Certification-System\\src\\app\\dashboard\\results\\[id]\\edit\\page.tsx","default")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[638,8338,2367,7897],()=>s(87730));module.exports=t})();